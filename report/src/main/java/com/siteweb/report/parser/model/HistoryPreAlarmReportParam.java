package com.siteweb.report.parser.model;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.report.entity.ReportParameterPreset;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Collection;
import java.util.Date;
import java.util.Set;

@Data
public class HistoryPreAlarmReportParam {
    private Date startDate;
    private Date endDate;
    private Integer preAlarmCategory; //预警分类id
    private Integer preAlarmSeverity;  //预警等级id
    private String levelOfPath;  //资源层级key
    private String resourceName; //资源名称key

    public HistoryPreAlarmReportParam(Collection<ReportParameterPreset> reportParameterPresetList) {
        if (CollUtil.isEmpty(reportParameterPresetList)) {
            return;
        }
        try {
            for (ReportParameterPreset reportParameterPreset : reportParameterPresetList) {
                if (reportParameterPreset.getValue() == null || reportParameterPreset.getValue().equals("")) {
                    continue;
                }
                switch (reportParameterPreset.getReportSchemaQueryParameter().getReportSchemaQueryParameterName()) {
                    case "startDate":
                        this.startDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "endDate":
                        this.endDate = DateUtil.stringToDate(reportParameterPreset.getValue());
                        break;
                    case "preAlarmCategory":
                        this.preAlarmCategory = Integer.parseInt(reportParameterPreset.getValue());
                        break;
                    case "preAlarmSeverity":
                        this.preAlarmSeverity = Integer.parseInt(reportParameterPreset.getValue());
                        break;
                    case "levelOfPath":
                        this.levelOfPath = reportParameterPreset.getValue();
                        break;
                    case "resourceName":
                        this.resourceName = reportParameterPreset.getValue();
                        break;
                    default:
                        break;
                }
            }
        } catch (Exception e) {
            e.getMessage();
        }
    }
}
