package com.siteweb.report.parser;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.siteweb.airconditioncontrol.dto.*;
import com.siteweb.airconditioncontrol.enumeration.EquipmentSelectType;
import com.siteweb.airconditioncontrol.enumeration.EquipmentType;
import com.siteweb.airconditioncontrol.enumeration.StdSignalType;
import com.siteweb.airconditioncontrol.service.AirEquipTemplateService;
import com.siteweb.airconditioncontrol.service.AirGroupViewService;
import com.siteweb.common.influxdb.InfluxDBManager;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.model.ReportCount;
import com.siteweb.report.enums.ReportDataSourceEnum;
import com.siteweb.report.enums.ReportStructureEnum;
import com.siteweb.report.parser.model.EquipHistoryStateReportParam;
import com.siteweb.report.parser.model.EquipHistoryStateReturnParam;
import com.siteweb.report.vo.ReportVO;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BoundParameterQuery;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBResultMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

import static com.siteweb.common.util.DateUtil.dateToString;

@Slf4j
@Component
public class EquipHistoryStateParser extends ReportParser {
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    InfluxDBManager influxDBManager;

    private static final String measurementName = "historydatas";
    @Value("${spring.influx.database}")
    private String database;
    @Autowired
    private InfluxDB influxDB;
    @Autowired
    private AirGroupViewService airGroupViewService;
    @Autowired
    private AirEquipTemplateService airEquipTemplateService;
    private static final String VIRTUAL_ID = "virtualEquipmentId";
    private static final String STATION_NAME = "position";
    private static final String MONITOR_UNIT_NAME = "nodeName";
    private static final String VIRTUAL_NAME = "groupName";

    protected EquipHistoryStateParser() {
        super(ReportDataSourceEnum.EQUIPSTATE_REPORT.getValue());
    }

    @Override
    public JSONObject parser(ReportVO reportVO) {
        EquipHistoryStateReportParam reportParam = new EquipHistoryStateReportParam(reportVO.getReportParameterPresetList());
        String virtualInfo = reportParam.getVirtualInfo();
        JSONObject jsonObject = new JSONObject(true);
        if (CharSequenceUtil.isEmpty(virtualInfo)) {
            return jsonObject;
        }
        JSONArray virtualInfoJsonArray = JSONUtil.parseArray(virtualInfo);

        List<TempQueryResult> equipHistoryStateList = new ArrayList<>();

        List<EquipHistoryStateReturnParam> airEquip = getAllEquipSignal(virtualInfoJsonArray);

        Map<Integer,EquipHistoryStateReturnParam> idToAirInfo = airEquip.stream().collect(Collectors.toMap(EquipHistoryStateReturnParam::getEquipmentId,equipHistoryStateReturnParam -> equipHistoryStateReturnParam,(r1, r2) -> r1));
//        Map<Integer, String> idToStationNameMap = airEquip.stream().collect(Collectors.toMap(EquipHistoryStateReturnParam::getStationId, EquipHistoryStateReturnParam::getStationName, (r1, r2) -> r1));
//        Map<Integer, String> idToEquipNameMap = airEquip.stream().collect(Collectors.toMap(EquipHistoryStateReturnParam::getEquipmentId, EquipHistoryStateReturnParam::getEquipmentName, (r1, r2) -> r1));
//        假数据
//        idToStationNameMap.put(22000002, "6_基站");
//        idToEquipNameMap.put(573004470, "BA依米康风冷空调");
//        idToEquipNameMap.put(573004444, "列间空调_英维克_A2-EVO-CY-DB_协议标准化");


        Pageable pageable = reportVO.getPageable();
        equipHistoryStateList = findEquipHistoryState(reportParam, pageable, jsonObject, airEquip);
        if (pageable != null && pageable.isPaged()) {
            Integer tempTotalElements = getResultCount(reportParam, airEquip);
            // 更新信号总数和总页数
            ReportStructureEnum.updateTotalElement(jsonObject, tempTotalElements, pageable);
        }
        // 设置表头
        JSONObject titleJsonObject = new JSONObject(true);
        titleJsonObject.set(ReportStructureEnum.COLUMN1.value(), messageSourceUtil.getMessage("airConditionControl.report.equipStateForm.station"));//"局站"
        titleJsonObject.set(ReportStructureEnum.COLUMN2.value(), messageSourceUtil.getMessage("airConditionControl.report.equipStateForm.monitorUnit"));//"监控单元"
        titleJsonObject.set(ReportStructureEnum.COLUMN3.value(), messageSourceUtil.getMessage("airConditionControl.report.equipStateForm.virtualEquipment"));//"群控设备"
        titleJsonObject.set(ReportStructureEnum.COLUMN4.value(), messageSourceUtil.getMessage("airConditionControl.report.equipStateForm.equipment"));//"设备"
        titleJsonObject.set(ReportStructureEnum.COLUMN5.value(), messageSourceUtil.getMessage("airConditionControl.report.equipStateForm.state"));//"开关状态"
        titleJsonObject.set(ReportStructureEnum.COLUMN6.value(), messageSourceUtil.getMessage("airConditionControl.report.equipStateForm.stateNum"));//"状态值"
        titleJsonObject.set(ReportStructureEnum.COLUMN7.value(), messageSourceUtil.getMessage("airConditionControl.report.equipStateForm.time"));//"时间"
        jsonObject.set(ReportStructureEnum.TITLE.value(), titleJsonObject);
        JSONArray jsonArray = new JSONArray();

        try {
            // 设置内容
            JSONObject contentJsonObject;
            for (TempQueryResult tempQueryResult : equipHistoryStateList) {
                contentJsonObject = new JSONObject(true);
                contentJsonObject.set(ReportStructureEnum.COLUMN1.value(), idToAirInfo.get(Integer.parseInt(tempQueryResult.getDeviceId())).getStationName());
                contentJsonObject.set(ReportStructureEnum.COLUMN2.value(), idToAirInfo.get(Integer.parseInt(tempQueryResult.getDeviceId())).getMonitorUnitName());
                contentJsonObject.set(ReportStructureEnum.COLUMN3.value(), idToAirInfo.get(Integer.parseInt(tempQueryResult.getDeviceId())).getVirtualEquipment());
                contentJsonObject.set(ReportStructureEnum.COLUMN4.value(), idToAirInfo.get(Integer.parseInt(tempQueryResult.getDeviceId())).getEquipmentName());
                contentJsonObject.set(ReportStructureEnum.COLUMN5.value(), Objects.equals(tempQueryResult.getPointValue(), "1.0") ? messageSourceUtil.getMessage("airConditionControl.view.airStateOn") : messageSourceUtil.getMessage("airConditionControl.view.airStateOff"));
                contentJsonObject.set(ReportStructureEnum.COLUMN6.value(), tempQueryResult.getPointValue());
                contentJsonObject.set(ReportStructureEnum.COLUMN7.value(), tempQueryResult.getTime());
                jsonArray.add(contentJsonObject);
            }
        } catch (Exception e) {
            log.error("EquipHistoryStateParser-parser error {}", e.getMessage());
        }
        jsonObject.set(ReportStructureEnum.RESULT.value(), jsonArray);
        return jsonObject;
    }


    private List<TempQueryResult> findEquipHistoryState(EquipHistoryStateReportParam reportParam, Pageable pageable, JSONObject jsonObject, List<EquipHistoryStateReturnParam> airEquip) {
        List<TempQueryResult> historyResultQuery = new ArrayList<>();
        try {
            String resultSql;
            if (pageable != null && pageable.isPaged()) {
                resultSql = "select * from historydatas where time >=$startTime and time <= $endTime and ( $someSignalId ) order by time asc limit $limit offset $offset";
            } else {
                resultSql = "select * from historydatas where time >=$startTime and time <= $endTime and ( $someSignalId ) ";
            }
            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
            StringBuilder bsWhereSignalId = new StringBuilder();
            int count = 0;
            for (EquipHistoryStateReturnParam equipHistoryStateReturnParam : airEquip) {
                if (bsWhereSignalId.length() == 0) {
//               假数据
//                    bsWhereSignalId.append(" SignalId='573004444.110000021' ");
                    bsWhereSignalId.append(" SignalId='" + equipHistoryStateReturnParam.getEquipmentId() + "." + equipHistoryStateReturnParam.getStateSignal() + "' ");
                } else {
//                    bsWhereSignalId.append(" or SignalId='573004446.110000021' ");
                    bsWhereSignalId.append(" or SignalId='" + equipHistoryStateReturnParam.getEquipmentId() + "." + equipHistoryStateReturnParam.getStateSignal() + "' ");
                }
                count++;
                if (count == airEquip.size() || count % 500 == 0) {
                    resultSql = resultSql.replace("$someSignalId", bsWhereSignalId.toString());
                    if (pageable != null && pageable.isPaged()) {
                        Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(resultSql)
                                .forDatabase(database)
                                .bind("startTime", dateToString(reportParam.getStartDate()))
                                .bind("endTime", dateToString(reportParam.getEndDate()))
                                .bind("limit", pageable.getPageSize())
                                .bind("offset", pageable.getPageNumber() * pageable.getPageSize())
                                .create();
                        query = influxDB.query(queryBuilder);
                    } else {
                        Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(resultSql)
                                .forDatabase(database)
                                .bind("startTime", dateToString(reportParam.getStartDate()))
                                .bind("endTime", dateToString(reportParam.getEndDate()))
                                .create();
                        query = influxDB.query(queryBuilder);
                    }
                    if (query != null) {
                        historyResultQuery = resultMapper.toPOJO(query, TempQueryResult.class);
                    }
                }
            }
        } catch (Exception e) {
            log.error("EquipHistoryStateParser-findEquipHistoryState error {}", e.getMessage());
            return new ArrayList<>();
        }

        if (historyResultQuery == null || historyResultQuery.size() == 0)
            return new ArrayList<>();
        return historyResultQuery;
    }

    private Integer getResultCount(EquipHistoryStateReportParam reportParam, List<EquipHistoryStateReturnParam> airEquip) {
        List<ReportCount> resultCount = new ArrayList<>();
        try {
            String countSql;
            countSql = "select count(PointValue) from historydatas where time >=$startTime and time <= $endTime and ( $someSignalId )";
            StringBuilder bsWhereSignalId = new StringBuilder();
            int count = 0;

            for (EquipHistoryStateReturnParam equipHistoryStateReturnParam : airEquip) {
                if (bsWhereSignalId.length() == 0) {
                    //               假数据
//                    bsWhereSignalId.append(" SignalId='573004444.110000021' ");
                    bsWhereSignalId.append(" SignalId='" + equipHistoryStateReturnParam.getEquipmentId() + "." + equipHistoryStateReturnParam.getStateSignal() + "' ");
                } else {
//                    bsWhereSignalId.append(" or SignalId='573004446.110000021' ");
                    bsWhereSignalId.append(" or SignalId='" + equipHistoryStateReturnParam.getEquipmentId() + "." + equipHistoryStateReturnParam.getStateSignal() + "' ");
                }
                count++;
                if (count == airEquip.size() || count % 500 == 0) {
                    countSql = countSql.replace("$someSignalId", bsWhereSignalId.toString());
                    Map<String, Object> params = new HashMap<>();
                    params.put("startTime", DateUtil.dateToString(reportParam.getStartDate()));
                    params.put("endTime", DateUtil.dateToString(reportParam.getEndDate()));
                    resultCount = influxDBManager.list(countSql, ReportCount.class, measurementName, params);
                    return CollUtil.isEmpty(resultCount) ? 0 : resultCount.get(0).getCount();
                }
            }
        } catch (Exception e) {
            log.error("EquipHistoryStateParser-getResultCount error {}", e.getMessage());
        }
        return resultCount.size() == 0 ? 0 : resultCount.get(0).getCount();
    }

    private List<EquipHistoryStateReturnParam> getAllEquipSignal(JSONArray virtualInfoJsonArray) {
        //到时候前端会把虚拟空调设备分组的信息传过来，包括分组id，局站信息（可以不用后端进行局站查询）
        List<EquipHistoryStateReturnParam> airEquip = new ArrayList<>();
        for (int i = 0; i < virtualInfoJsonArray.size(); i++) {
            JSONObject object = virtualInfoJsonArray.getJSONObject(i);
            String virtualEquipmentId = object.getStr(VIRTUAL_ID);
            String stationName = object.getStr(STATION_NAME);
            String monitorUnit = object.getStr(MONITOR_UNIT_NAME);
            String virtualEquipment = object.getStr(VIRTUAL_NAME);
            List<EquipSample> air = airGroupViewService.getAllEquipmentsByVirtualEquipmentId(Integer.valueOf(virtualEquipmentId), EquipmentSelectType.ONLYAIR.value());
            for (EquipSample equipSample : air) {//需要设备id，设备局站信息，设备信号id，返回信息里面还包含时间，状态值，和状态信息
                List<SignalInfo> signalInfoList = getEquipStateSignalList(equipSample);//映射的空调开关状态信号
                if (signalInfoList.size() == 0) {
                    airEquip.add(new EquipHistoryStateReturnParam(equipSample.getEquipmentName(), equipSample.getEquipmentId(), equipSample.getStationId(), stationName, 0,virtualEquipment,monitorUnit));
                }
                for (SignalInfo signalInfo : signalInfoList) {
                    airEquip.add(new EquipHistoryStateReturnParam(equipSample.getEquipmentName(), equipSample.getEquipmentId(), equipSample.getStationId(), stationName, signalInfo.getSwSignalId(),virtualEquipment,monitorUnit));
                }
            }
        }

        return airEquip;
    }

    private List<SignalInfo> getEquipStateSignalList(EquipSample equipSample) {
        AirStdTypeMap airStdTypeMap = airEquipTemplateService.getCache(equipSample.getEquipmentTemplateId());
        List<AirStdSignalMap> allSignalList = airStdTypeMap.getSignalList();
        List<SignalInfo> needSignalsList = new ArrayList<>();//需要展示的信号的标准信号ID（数据库前面的序号）
        if (allSignalList == null || allSignalList.size() == 0) {//如果该设备没有映射信号
            return new ArrayList<>();
        }
        Integer equipmentType = allSignalList.get(0).getTypeId();
        Map<Integer, Integer> stdIdToSwId = allSignalList.stream().collect(Collectors.toMap(AirStdSignalMap::getStdSignalId, airStdSignalMap -> airStdSignalMap.getSwSignalId() == null ? 0 : airStdSignalMap.getSwSignalId()));
        try {
            //根据空调的分类通过信号的标准id找到要显示的信号列表
            //普通空调4，5
            if (equipmentType.equals(EquipmentType.NORMALAIRCONDITION.value())) {
                needSignalsList.add(new SignalInfo(StdSignalType.NORMALAIRSTATE.value(), stdIdToSwId.get(StdSignalType.NORMALAIRSTATE.value()), messageSourceUtil.getMessage("airConditionControl.view.signalNameStateOnOrOff")));
            }
            //专用空调3，4
            if (equipmentType.equals(EquipmentType.SPECIALAIRCONDITION.value())) {
                needSignalsList.add(new SignalInfo(StdSignalType.SPECIALAIRSTATE.value(), stdIdToSwId.get(StdSignalType.SPECIALAIRSTATE.value()), messageSourceUtil.getMessage("airConditionControl.view.signalNameStateOnOrOff")));
            }
            //一拖二空调3，4，19，20
            if (equipmentType.equals(EquipmentType.ONETOTWOAIRCONDITION.value())) {
                needSignalsList.add(new SignalInfo(StdSignalType.ONETOTWOAIR1STATE.value(), stdIdToSwId.get(StdSignalType.ONETOTWOAIR1STATE.value()), messageSourceUtil.getMessage("airConditionControl.view.signalNameStateOnOrOff")));
                needSignalsList.add(new SignalInfo(StdSignalType.ONETOTWOAIR2STATE.value(), stdIdToSwId.get(StdSignalType.ONETOTWOAIR2STATE.value()), messageSourceUtil.getMessage("airConditionControl.view.signalNameStateOnOrOff")));
            }
        } catch (Exception e) {
            log.error("getNeedSignalsList error in getAirStateInfo{}", e.getMessage());
        }
        return needSignalsList;
    }
}
