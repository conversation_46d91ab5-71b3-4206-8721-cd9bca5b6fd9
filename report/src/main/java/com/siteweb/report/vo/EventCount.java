package com.siteweb.report.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 告警统计类
 * <AUTHOR>
 * @date 2022/05/27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EventCount {
    /**
     * 层级id
     */
    private Integer  resourceStructureId;
    /**
     * 层级名称
     */
    private String  resourceStructureName;
    /**
     * 告警等级
     */
    private Integer  eventLevel;
    /**
     * 告警数量
     */
    private Integer count;
}
