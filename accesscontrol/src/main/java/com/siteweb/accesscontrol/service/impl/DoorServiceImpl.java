package com.siteweb.accesscontrol.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.accesscontrol.dto.DoorDTO;
import com.siteweb.accesscontrol.dto.DoorEquipmentVO;
import com.siteweb.accesscontrol.dto.DoorParamItem;
import com.siteweb.accesscontrol.entity.Door;
import com.siteweb.accesscontrol.entity.TimeGroup;
import com.siteweb.accesscontrol.mapper.DoorMapper;
import com.siteweb.accesscontrol.service.DoorParamService;
import com.siteweb.accesscontrol.service.DoorService;
import com.siteweb.accesscontrol.service.TimeGroupService;
import com.siteweb.accesscontrol.vo.EquipmentDoor;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.monitoring.service.EquipmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class DoorServiceImpl implements DoorService {
    @Autowired
    private DoorMapper doorMapper;
    @Autowired
    private DoorParamService doorParamService;
    @Autowired
    private TimeGroupService timeGroupService;
    @Autowired
    private EquipmentService equipmentService;
    @Override
    public List<Integer> findCardAuthEquipment(Integer cardId) {
        return doorMapper.findCardAuthEquipment(cardId);
    }

    @Override
    public Integer findDoorCategoryByEquipmentId(Integer equipmentId) {
        return doorMapper.findDoorCategoryByEquipmentId(equipmentId);
    }

    @Override
    public EquipmentDoor findInfoByEquipmentId(Integer equipmentId) {
        return doorMapper.findInfoByEquipmentId(equipmentId);
    }

    @Override
    public List<Integer> findDoorProperties(Integer equipmentId) {
         return doorMapper.findDoorProperties(equipmentId);
    }

    @Override
    public DoorDTO findDoorInfo(Integer equipmentId) {
        DoorDTO doorDTO = doorMapper.findDoorInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(doorDTO)) {
            return null;
        }
        List<DoorParamItem> doorParamItemList = doorParamService.findDoorParamByEquipmentId(equipmentId);
        doorDTO.setParamList(doorParamItemList);
        List<TimeGroup> timeGroupList = timeGroupService.findByEquipmentId(equipmentId);
        doorDTO.setTimeGroupList(timeGroupList);
        return doorDTO;
    }

    @Override
    public List<DoorEquipmentVO> findDoorEquipment() {
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(TokenUserUtil.getLoginUserId());
        if (CollUtil.isEmpty(equipmentIds)) {
            return Collections.emptyList();
        }
        return doorMapper.findDoorEquipmentByEquipmentIds(equipmentIds);
    }

    @Override
    public Integer findDoorIdByEquipmentId(Integer equipmentId) {
        Door door = doorMapper.selectOne(Wrappers.lambdaQuery(Door.class)
                                                 .select(Door::getDoorId)
                                                 .eq(Door::getEquipmentId, equipmentId));
        return Optional.ofNullable(door)
                       .map(Door::getDoorId)
                       .orElse(null);
    }

    @Override
    public List<Door> findByDoorIds(List<Integer> doorIds) {
        if (CollUtil.isEmpty(doorIds)) {
            return Collections.emptyList();
        }
        return doorMapper.selectList(Wrappers.lambdaQuery(Door.class).in(Door::getDoorId, doorIds));
    }

    @Override
    public List<Door> findByEquipmentIds(List<Integer> equipmentIds) {
        if (CollUtil.isEmpty(equipmentIds)) {
            return Collections.emptyList();
        }
        return doorMapper.selectList(Wrappers.lambdaQuery(Door.class).in(Door::getEquipmentId, equipmentIds));
    }
}
