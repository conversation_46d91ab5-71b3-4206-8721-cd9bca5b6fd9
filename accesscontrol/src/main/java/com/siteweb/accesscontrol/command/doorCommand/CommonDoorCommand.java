package com.siteweb.accesscontrol.command.doorCommand;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.accesscontrol.command.FingerPrintCommandFactory;
import com.siteweb.accesscontrol.dto.AccessCardInfoDTO;
import com.siteweb.accesscontrol.dto.ChildEquipment;
import com.siteweb.accesscontrol.entity.FingerPrint;
import com.siteweb.accesscontrol.enums.CommandCategoryEnum;
import com.siteweb.accesscontrol.enums.EquipmentTypeEnum;
import com.siteweb.accesscontrol.enums.VendorEnum;
import com.siteweb.accesscontrol.vo.EquipmentDoor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * DC505通用门禁控制实现
 *
 * <AUTHOR>
 * @date 2022/09/16
 */
@Service("CommonDoorCommand")
@Slf4j
public class CommonDoorCommand extends AbstractDoorCommand {
    @Autowired
    private FingerPrintCommandFactory fingerPrintCommandFactory;
    @Override
    public Boolean deleteAuthFingerprint(Integer userId, Integer equipmentId, Integer cardId) {
         //删除指纹（纽贝尔指纹机）
         //控制命令种类41,命令号19
         //8888888888,11	   权限密码,用户id指纹号 （指纹标志为用户ID和指纹编号拼接，例子中用户ID为1，指纹号为1）
        List<FingerPrint> fingerPrintList = fingerprintService.findCardFingerprintByVendor(cardId, VendorEnum.NEWBEL.getVendorId());
        if (CollUtil.isEmpty(fingerPrintList)) {
            log.info("AddOrUpdateFingerprint: not find fingerprint by equipmentId:{}, cardId: {}", equipmentId, cardId);
            return Boolean.TRUE;
        }
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("SetFingerprint: cannot find equipment or childEquipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        List<ChildEquipment> childEquipmentList = fingerprintService.findChildEquipment(equipmentId, EquipmentTypeEnum.FINGER_READER.getEquipmentCategory(), VendorEnum.NEWBEL.getVendorId());
        if (CollUtil.isEmpty(childEquipmentList)) {
            return Boolean.TRUE;
        }
        //发送控制及命令
        for (ChildEquipment childEquipment : childEquipmentList) {
            Integer childStationId = childEquipment.getStationId();
            Integer childEquipmentId = childEquipment.getEquipmentId();
            Integer commandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.CLEAR_FINGERPRINT.getCategory());
            for (FingerPrint fingerprint : fingerPrintList) {
                String command = fingerPrintCommandFactory.getAbstractFingerPrintCommandInstance(childEquipmentId)
                                                          .deleteAuthFingerprint(childEquipment, fingerprint, equipmentDoor, cardId);
                log.info("deleteAuthFingerprint: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, childEquipmentId, commandId, command);
                activeControlManager.sendControl(childStationId, childEquipmentId, commandId, command, userId);

            }
            //删除指纹到数据库
            fingerprintAuthService.deleteFingerprintAuth(equipmentDoor.getStationId(),equipmentId,cardId,
                    fingerPrintList.get(0).getFingerPrintId(),VendorEnum.NEWBEL.getVendorId());
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean delManualDoorCard(Integer userId, Integer equipmentId, Integer cardId) {
        //删除门禁卡
        //控制命令种类13,命令号37
        //000000,0+0002148D03	门密码,0+卡号
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("delManualDoorCard: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("delManualDoorCard: cannot find card by cardId:{}", cardId);
        }

        Integer stationId = equipmentDoor.getStationId();
        String password = super.getDoorPassword(equipmentDoor.getPassword());
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.DEL_MANUAL_DOOR_CARD.getCategory());
        String cardCode = super.getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(),accessCardInfo.getCardCodeConv());
        String command = String.format("%s,%s+%s", password, 0, cardCode);
        log.info("delManualDoorCard: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return delFingerReaderUser(userId, equipmentId, cardId);
    }

    public Boolean delFingerReaderUser(Integer userId, Integer equipmentId, Integer cardId) {
         //删除用户(纽贝尔指纹读头)
         //控制命令种类36,命令号20
         //8888888888,00000010	   权限密码,用户ID(8位的用户id，不足8位向前补0)
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("delFingerReaderUser: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        List<ChildEquipment> childEquipmentList = fingerprintService.findChildEquipment(equipmentId, EquipmentTypeEnum.FINGER_READER.getEquipmentCategory(),VendorEnum.NEWBEL.getVendorId());
        if (CollUtil.isEmpty(childEquipmentList)) {
            return Boolean.TRUE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (Objects.isNull(accessCardInfo)) {
            log.error("delFingerReaderUser: cannot find card by cardId:{}", cardId);
        }
        for (ChildEquipment childEquipment : childEquipmentList) {
            Integer commandId = controlService.findCommandIdByCommandCategory(childEquipment.getStationId(), childEquipment.getEquipmentId(), CommandCategoryEnum.DELETE_USER.getCategory());
            String userNo = getUserNo(getCardId(cardId));
            String command = String.format("%s,%s", FINGERPRINT_DEFAULT_PASSWORD, userNo);
            log.info("delFingerReaderUser: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childEquipment.getStationId(), equipmentId, commandId, command);
            activeControlManager.sendControl(childEquipment.getStationId(), childEquipment.getEquipmentId(), commandId, command, userId);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean editManualDoorCard(Integer userId, Integer equipmentId, Integer timeGroupNo, Integer cardId, Date validTime) {
        //修改门禁卡
        //控制命令种类14,命令号38
        //000000,0+0002148D03,1,7777,20501231	门密码,卡编号+卡号,时间组号,卡密码,卡有效期
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("editManualDoorCard: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("editManualDoorCard: cannot find card by cardId:{}", cardId);
        }
        Integer stationId = equipmentDoor.getStationId();
        String password = super.getDoorPassword(equipmentDoor.getPassword());
        Integer doorNo = equipmentDoor.getDoorNo();
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.EDIT_MANUAL_DOOR_CARD.getCategory());
        String cardCode = super.getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(),accessCardInfo.getCardCodeConv());
        Integer id = super.getCardId(accessCardInfo.getCardId());
        String command = String.format("%s,%s+%s,%s,%s,%s", password, id, cardCode, super.getTimeGroupNo(timeGroupNo,doorNo), accessCardInfo.getPassword(), super.defaultTimeFormat(validTime));
        log.info("editManualDoorCard: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean addManualDoorCard(Integer userId, Integer equipmentId, Integer timeGroupNo, Integer cardId) {
        //增加门禁卡
        //控制命令种类12,命令号36
        //000000,0+0002148D03,1,7777,20501231	门密码,卡编号+卡号,时间组号,卡密码,卡有效期
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("addManualDoorCard: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("addManualDoorCard: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        //发送命令获取参数
        Integer stationId = equipmentDoor.getStationId();
        Integer doorNo = equipmentDoor.getDoorNo();
        String doorPassword = CharSequenceUtil.isBlank(equipmentDoor.getPassword()) ? DOOR_DEFAULT_PASSWORD : equipmentDoor.getPassword();
        String cardPassword = CharSequenceUtil.isBlank(accessCardInfo.getPassword()) ? CARD_DEFAULT_PASSWORD : accessCardInfo.getPassword();
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.ADD_MANUAL_DOOR_CARD.getCategory());
        String cardCode = getCardCode(equipmentDoor.getCardCodeType(),accessCardInfo.getCardCodeType(),accessCardInfo.getCardCode(),accessCardInfo.getCardCodeConv());
        Integer id = getCardId(cardId);
        String cardEndTime = defaultTimeFormat(accessCardInfo.getEndTime());
        String command = String.format("%s,%s+%s,%s,%s,%s", doorPassword, id, cardCode,super.getTimeGroupNo(timeGroupNo,doorNo),cardPassword,cardEndTime);
        log.info("addManualDoorCard: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return addFingerReaderUser(userId, equipmentId, cardId);
    }

    @Override
    public Boolean addOrUpdateAuthFingerprint(Integer userId, Integer equipmentId, Integer cardId) {
        //添加或修改指纹(纽贝尔）
        //控制命令种类40,命令号18
        //8888888888,200,1302,1	   权限密码,【卡编号】【指纹编号】,指纹长度,指纹指纹信息ID
        List<FingerPrint> fingerPrintList = fingerprintService.findCardFingerprintByVendor(cardId, VendorEnum.NEWBEL.getVendorId());
        if (CollUtil.isEmpty(fingerPrintList)) {
            log.info("AddOrUpdateFingerprint: not find fingerprint by equipmentId:{}, cardId: {}", equipmentId, cardId);
            return Boolean.TRUE;
        }
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("addOrUpdateAuthFingerprint: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("addOrUpdateAuthFingerprint: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        List<ChildEquipment> childEquipmentList = fingerprintService.findChildEquipment(equipmentId, EquipmentTypeEnum.FINGER_READER.getEquipmentCategory(),VendorEnum.NEWBEL.getVendorId());
        if (CollUtil.isEmpty(childEquipmentList)) {
            return Boolean.TRUE;
        }
        //发送控制及命令
        for (ChildEquipment childEquipment : childEquipmentList) {
            Integer childStationId = childEquipment.getStationId();
            Integer childEquipmentId = childEquipment.getEquipmentId();
            Integer commandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.SET_FINGERPRINT.getCategory());
            for (FingerPrint fingerprint : fingerPrintList) {
                String command = fingerPrintCommandFactory.getAbstractFingerPrintCommandInstance(childEquipmentId)
                                                          .addOrUpdateAuthFingerprint(childEquipment, fingerprint, equipmentDoor, cardId);
                log.info("addOrUpdateAuthFingerprint: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, childEquipmentId, commandId, command);
                activeControlManager.sendControl(childStationId, childEquipmentId, commandId, command, userId);
            }
            //添加指纹授权到数据库
            fingerprintAuthService.saveAuthFingerPrint(equipmentDoor.getStationId(),equipmentId,cardId, fingerPrintList.get(0).getFingerPrintId(),VendorEnum.NEWBEL.getVendorId());
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean addFingerReaderUser(Integer userId, Integer equipmentId, Integer cardId) {
        /*****************************************************************************************************
         * 添加或修改用户(纽贝尔指纹读头)
         * 控制命令种类35,命令号17
         * 8888888888,00000001,10,11,12,0000,009F20A8	   权限密码,用户ID,第一枚指纹编号,第二枚指纹编号,第三枚指纹编号,密码,卡号（仅支持8位卡号）
         * ****************************************************************************************************/
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("addFingerReaderUser: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (ObjectUtil.isNull(accessCardInfo)) {
            log.error("addFingerReaderUser: cannot find card by cardId:{}", cardId);
            return Boolean.FALSE;
        }
        List<ChildEquipment> childEquipmentList = fingerprintService.findChildEquipment(equipmentId, EquipmentTypeEnum.FINGER_READER.getEquipmentCategory(),VendorEnum.NEWBEL.getVendorId());
        if (Objects.isNull(childEquipmentList)) {
            return Boolean.TRUE;
        }
        for (ChildEquipment childEquipment : childEquipmentList) {
            Integer childStationId = childEquipment.getStationId();
            Integer childEquipmentId = childEquipment.getEquipmentId();
            Integer commandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.ADD_OR_UPDATE_USER.getCategory());
            String command = fingerPrintCommandFactory.getAbstractFingerPrintCommandInstance(childEquipmentId)
                                                      .addFingerReaderUser(childEquipment, equipmentDoor, accessCardInfo);
            log.info("addFingerReaderUser: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, childEquipmentId, commandId, command);
            activeControlManager.sendControl(childStationId, childEquipmentId, commandId, command, userId);
        }
        return Boolean.TRUE;
    }

    @Override
    public Boolean setDoorOpenDelay(Integer userId, Integer equipmentId, Integer openDelay) {
        //门开延迟
        //000000,1,160	门密码,门编号,延时时间(单位为0.1秒)； 本例：设置门开延时为160（0.1秒）
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("OpenDelay: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        Integer stationId = equipmentDoor.getStationId();
        Integer doorNo = equipmentDoor.getDoorNo();
        String doorPassword = super.getDoorPassword(equipmentDoor.getPassword());
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.OPEN_DELAY.getCategory());
        String command = String.format("%s,%s,%s", doorPassword, doorNo, openDelay);
        log.info("addOrUpdateAuthFingerprint: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean setTime(Integer userId, Integer equipmentId, String timeStr) {
        //门禁校时
        //控制命令种类22,命令号32
        //000000,200610131047035	 门密码,时间(yyyymmddhhmmssd)；本例：设置时间为：2006-10-13 10:47:03 Fri
        //cn.hutool.core.date.Week对应星期值，需要处理
        int week = DateUtil.thisDayOfWeek() - 1;
        week = week == 0 ? 7 : week;
        String command = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN) + week;
        return super.sendSimpleDoorCommand(userId, equipmentId, CommandCategoryEnum.SET_TIME, command);
    }

    @Override
    public Boolean setOpenMode(Integer userId, Integer equipmentId, Integer openMode) {
        //刷卡进门密码工作方式
        //000000,1,1	门密码,门编号,门开方式(0:不用密码；1:要密码)； 本例：进门要密码
        //000000,1,0	门密码,门编号,门开方式(0:不用密码；1:要密码)； 本例：进门不用密码
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("setOpenMode: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        Integer stationId = equipmentDoor.getStationId();
        Integer doorNo = equipmentDoor.getDoorNo();
        String doorPassword = super.getDoorPassword(equipmentDoor.getPassword());
        Integer commandId = controlService.findCommandIdByCommandCategory(stationId, equipmentId, CommandCategoryEnum.MANUAL_DOOR_WORK_MODE.getCategory());
        String command = String.format("%s,%s,%s", doorPassword, doorNo, openMode);
        log.info("setOpenMode: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", stationId, equipmentId, commandId, command);
        activeControlManager.sendControl(stationId, equipmentId, commandId, command, userId);
        return Boolean.TRUE;
    }

    @Override
    public Boolean clearCard(Integer userId, Integer equipmentId) {
        //删除所有门禁卡
        // 控制命令种类17,命令号45,
        //000000,1	门密码,门编号；本例：删除门禁中所有门禁卡
        String command = "1";
        Boolean result = super.sendSimpleDoorCommand(userId, equipmentId, CommandCategoryEnum.DEL_ALL_MANUAL_DOOR_CARD, command);
        if (Boolean.FALSE.equals(result)) {
            return result;
        }
        boolean clearFingerReaderResult = this.clearFingerReader(userId,equipmentId);
        boolean clearUserResult = this.clearUser(userId, equipmentId);
        return clearFingerReaderResult && clearUserResult;
    }

    @Override
    public Boolean clearFingerReader(Integer userId, Integer equipmentId) {
        //删除所有指纹（纽贝尔指纹机）
        //控制命令种类41,命令号19
        //8888888888,0   权限密码,0
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("ClearFingerprint: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        List<ChildEquipment> childEquipmentList = fingerprintService.findChildEquipment(equipmentId, EquipmentTypeEnum.FINGER_READER.getEquipmentCategory(), VendorEnum.NEWBEL.getVendorId());
        if (CollUtil.isEmpty(childEquipmentList)) {
            return Boolean.TRUE;
        }
        for (ChildEquipment childEquipment : childEquipmentList) {
            Integer childStationId = childEquipment.getStationId();
            Integer childEquipmentId = childEquipment.getEquipmentId();
            Integer clearFingerCommandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.CLEAR_FINGERPRINT.getCategory());
            String clearFingerCommand = String.format("%s,0", FINGERPRINT_DEFAULT_PASSWORD);
            log.info("ClearFingerprint: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, childEquipmentId, clearFingerCommandId, clearFingerCommand);
            activeControlManager.sendControl(childStationId, childEquipmentId, clearFingerCommandId, clearFingerCommand, userId);
        }
        return Boolean.TRUE;
    }


    public Boolean clearUser(Integer userId, Integer equipmentId) {
        //删除所有用户（纽贝尔指纹机）
        //控制命令种类36,命令号20
        //8888888888,0   权限密码,0
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("ClearFingerprint: cannot find equipment by equipmentId:{}", equipmentId);
            return Boolean.FALSE;
        }
        List<ChildEquipment> childEquipmentList = fingerprintService.findChildEquipment(equipmentId, EquipmentTypeEnum.FINGER_READER.getEquipmentCategory(), VendorEnum.NEWBEL.getVendorId());
        if (CollUtil.isEmpty(childEquipmentList)) {
            return Boolean.TRUE;
        }
        for (ChildEquipment childEquipment : childEquipmentList) {
            Integer childStationId = childEquipment.getStationId();
            Integer childEquipmentId = childEquipment.getEquipmentId();
            Integer clearUserCommandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.DELETE_USER.getCategory());
            String clearUserCommand = String.format("%s,0", FINGERPRINT_DEFAULT_PASSWORD);
            log.info("ClearFingerprint: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}", childStationId, childEquipmentId, clearUserCommandId, clearUserCommand);
            activeControlManager.sendControl(childStationId, childEquipmentId, clearUserCommandId, clearUserCommand, userId);
        }
        return Boolean.TRUE;
    }


    @Override
    public boolean remoteCollectFingerprint(Integer userId, Integer equipmentId, Integer cardId, Integer fingerNo) {
        //采集指纹（纽贝尔）
        //控制命令种类38,命令号60
        //8888888888,0,00000001,009F20A8	   权限密码,指纹编号,用户ID(8位),卡号
        EquipmentDoor equipmentDoor = doorService.findInfoByEquipmentId(equipmentId);
        if (ObjectUtil.isNull(equipmentDoor)) {
            log.error("remoteCollectFingerprint: cannot find equipment by equipmentId:{}", equipmentId);
            return false;
        }
        List<ChildEquipment> childEquipmentList = fingerprintService.findChildEquipment(equipmentId, EquipmentTypeEnum.FINGER_READER.getEquipmentCategory(), VendorEnum.NEWBEL.getVendorId());
        if (CollUtil.isEmpty(childEquipmentList)) {
            log.error("SampleFingerprint: cannot childEquipment by equipmentId:{}",equipmentId);
            return true;
        }
        AccessCardInfoDTO accessCardInfo = cardService.findInfoByCardId(cardId);
        if (Objects.isNull(accessCardInfo)) {
            log.error("SampleFingerprint: cannot find card by cardId:{}",cardId);
            return false;
        }
        for (ChildEquipment childEquipment : childEquipmentList) {
            Integer childStationId = childEquipment.getStationId();
            Integer childEquipmentId = childEquipment.getEquipmentId();
            Integer commandId = controlService.findCommandIdByCommandCategory(childStationId, childEquipmentId, CommandCategoryEnum.SAMPLE_FINGERPRINT.getCategory());
            String userNo = getUserNo(getCardId(cardId));
            String cardCode = super.getCardCode(equipmentDoor.getCardCodeType(), accessCardInfo.getCardCodeType(), accessCardInfo.getCardCode(),accessCardInfo.getCardCodeConv());
            String command = String.format("%s,%s,%s,%S", FINGERPRINT_DEFAULT_PASSWORD, fingerNo, userNo, cardCode);
            log.info("SampleFingerprint: StationId:{},EquipmentId:{}，CommandId{}，CommandString:{}",childStationId,childEquipmentId,commandId,command);
            activeControlManager.sendControl(childStationId, childEquipmentId, commandId, command, userId);
        }
        return true;
    }
}
