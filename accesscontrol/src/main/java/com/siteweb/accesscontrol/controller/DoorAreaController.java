package com.siteweb.accesscontrol.controller;

import com.siteweb.accesscontrol.entity.DoorArea;
import com.siteweb.accesscontrol.service.DoorAreaService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api")
@Api(value = "DoorAreaController",tags = "门区域管理")
public class DoorAreaController {
    @Autowired
    private DoorAreaService doorAreaService;

    @ApiOperation("获取所有门区域")
    @GetMapping("/doorarea")
    public ResponseEntity<ResponseResult> getDoorAreas(){
        return ResponseHelper.successful(doorAreaService.findAll());
    }

    @ApiOperation(value = "获取门禁区域报表")
    @GetMapping("/doorareas")
    public ResponseEntity<ResponseResult> findDoorAreas() {
        return ResponseHelper.successful(doorAreaService.findDoorAreas(), HttpStatus.OK);
    }

    @ApiOperation("创建门区域")
    @PostMapping("/doorarea")
    public ResponseEntity<ResponseResult> createDoorArea(@RequestBody DoorArea doorArea){
        return ResponseHelper.successful(doorAreaService.createDoorArea(doorArea));
    }

    @ApiOperation("修改门区域")
    @PutMapping("/doorarea")
    public ResponseEntity<ResponseResult> updateDoorArea(@RequestBody DoorArea doorArea){
        return ResponseHelper.successful(doorAreaService.updateDoorArea(doorArea));
    }
    @ApiOperation("删除门区域")
    @DeleteMapping("/doorarea")
    public ResponseEntity<ResponseResult> deleteDoorAreaById(@RequestBody List<Integer> ids){
        for (Integer id : ids) {
            doorAreaService.deleteById(id);
        }
        return ResponseHelper.successful(true);
    }

    @ApiOperation("获取门区域及门设备树结构数据")
    @GetMapping("/doorareatree")
    public ResponseEntity<ResponseResult> getDoorAreaTree(){
        return ResponseHelper.successful(doorAreaService.findDoorAreaTree());
    }
}
