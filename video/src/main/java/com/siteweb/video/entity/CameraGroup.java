package com.siteweb.video.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@TableName("CameraGroup")
@AllArgsConstructor
@NoArgsConstructor
public class CameraGroup implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 摄像头组主键id
     */
    @TableId(type = IdType.AUTO)
    private Integer cameraGroupId;
    /**
     * 摄像机名称
     */
    private String cameraGroupName;

    /**
     * 父级id
     */
    private Integer parentId;

    /**
     * 父级名称
     */
    @TableField(exist = false)
    private String parentName;

    /**
     * 子集
     */
    @TableField(exist = false)
    @JsonIgnore
    private List<CameraGroup> children;

    /**
     * 描述
     */
    private String description;
}
