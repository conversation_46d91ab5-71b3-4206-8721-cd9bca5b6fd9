<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.jsops.mapper.FaultLocationMapper">
    <select id="findFaultAlarmList" resultType="com.siteweb.jsops.dto.FaultEventDTO">
        SELECT
        a.StationId,a.StationName,a.EquipmentId,a.EquipmentName,a.EventId,a.EventName,a.EventConditionId,a.EventLevel,a.EventSeverityId,a.StartTime,a.EndTime,a.EventValue,a.Meanings
        FROM tbl_activeevent a where a.eventseverityid in (2, 3) and a.StartTime &gt;= #{startTime} and a.StartTime &lt;= #{endTime}
        and a.StationId in
        <foreach collection="stationIdList" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
        union
        SELECT
        a.StationId,a.StationName,a.EquipmentId,a.EquipmentName,a.EventId,a.EventName,a.EventConditionId,a.EventLevel,a.EventSeverityId,a.StartTime,a.EndTime,a.EventValue,a.Meanings
        FROM tbl_historyevent a where a.eventseverityid in (2, 3) and a.StartTime &gt;= #{startTime} and a.StartTime &lt;= #{endTime}
        and a.StationId in
        <foreach collection="stationIdList" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
    </select>
    <select id="losePowerSupply" resultType="com.siteweb.jsops.dto.CauseAnalysisDTO">
        SELECT b.StartTime as occurrenceTime,a.SWEquipmentId as equipmentId,a.PDDeviceName as equipmentName,b.EventName as eventName,b.EventId
        FROM tbms_pddevice a INNER JOIN tbl_activeevent b on a.SWEquipmentId = b.EquipmentId
        where b.EndTime is null and b.BaseTypeId in (4013130001, 401111001) AND a.SWStationId IN
        <foreach collection="stationIdList" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
    </select>
    <select id="reducePowerSupply" resultType="com.siteweb.jsops.dto.CauseAnalysisDTO">
        SELECT b.StartTime as occurrenceTime,a.SWEquipmentId as equipmentId,a.PDDeviceName as equipmentName,b.EventName as eventName,b.EventId
        FROM tbms_pddevice a INNER JOIN tbl_activeevent b on a.SWEquipmentId = b.EquipmentId
        where b.EndTime is null and b.BaseTypeId in (101312001, 401307001) AND a.SWStationId IN
        <foreach collection="stationIdList" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
    </select>
</mapper>