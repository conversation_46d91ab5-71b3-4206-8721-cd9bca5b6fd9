# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
/dist
/tmp
/out-tsc
/target
/core/src/main/resources/static

# dependencies
/node_modules
/upload-dir
# IDEs and editors
.idea
src/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
testem.log
/typings

# e2e
/e2e/*.js
/e2e/*.map

# System Files
.DS_Store
Thumbs.db

target/
!.mvn/wrapper/maven-wrapper.jar

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans

### IntelliJ IDEA ###
*.iws
*.iml
*.ipr

### NetBeans ###
nbproject/private/
build/
nbbuild/
nbdist/
.nb-gradle/

### log###
.log
[Ll]og/
[Ll]ogs/

### properties file ###
/src/main/resources/application.properties

##File upload##
##upload-dir/##
##MQTT Temp dir##
data/
Cov-Sub_Pub-tcplocalhost1883/
activemq-data/mapfiles
"mapfiles"
