INSERT INTO eventfiltermember (EventFilterMemberId, EventFilterMemberName, Description, LastUpdateDate) VALUES (1,'中心',NULL,'2024-06-11 16:21:47');
INSERT INTO eventfiltermember (EventFilterMemberId, EventFilterMemberName, Description, LastUpdateDate) VALUES (2,'局站分组',NULL,'2024-06-11 16:21:47');
INSERT INTO eventfiltermember (EventFilterMemberId, EventFilterMemberName, Description, LastUpdateDate) VALUES (3,'片区',NULL,'2024-06-11 16:21:47');
INSERT INTO eventfiltermember (EventFilterMemberId, EventFilterMemberName, Description, LastUpdateDate) VALUES (4,'局站等级',NULL,'2024-06-11 16:21:47');
INSERT INTO eventfiltermember (EventFilterMemberId, EventFilterMemberName, Description, LastUpdateDate) VALUES (5,'局站',NULL,'2024-06-11 16:21:47');
INSERT INTO eventfiltermember (EventFilterMemberId, EventFilterMemberName, Description, LastUpdateDate) VALUES (6,'设备种类',NULL,'2024-06-11 16:21:47');
INSERT INTO eventfiltermember (EventFilterMemberId, EventFilterMemberName, Description, LastUpdateDate) VALUES (7,'设备',NULL,'2024-06-11 16:21:47');
INSERT INTO eventfiltermember (EventFilterMemberId, EventFilterMemberName, Description, LastUpdateDate) VALUES (8,'事件种类',NULL,'2024-06-11 16:21:47');
INSERT INTO eventfiltermember (EventFilterMemberId, EventFilterMemberName, Description, LastUpdateDate) VALUES (9,'事件',NULL,'2024-06-11 16:21:47');
INSERT INTO eventfiltermember (EventFilterMemberId, EventFilterMemberName, Description, LastUpdateDate) VALUES (10,'事件等级',NULL,'2024-06-11 16:21:47');
INSERT INTO eventfiltermember (EventFilterMemberId, EventFilterMemberName, Description, LastUpdateDate) VALUES (11,'事件状态',NULL,'2024-06-11 16:21:47');
INSERT INTO eventfiltermember (EventFilterMemberId, EventFilterMemberName, Description, LastUpdateDate) VALUES (12,'设备逻辑分类',NULL,'2024-06-11 16:21:47');
INSERT INTO eventfiltermember (EventFilterMemberId, EventFilterMemberName, Description, LastUpdateDate) VALUES (13,'事件逻辑分类',NULL,'2024-06-11 16:21:47');
INSERT INTO eventfiltermember (EventFilterMemberId, EventFilterMemberName, Description, LastUpdateDate) VALUES (14,'告警标准名',NULL,'2024-06-11 16:21:47');

INSERT INTO notifymode (NotifyModeId, NotifyModeName, NotifyModeFormat, Description, LastUpdateDate) VALUES (1,'SMTP',NULL,'ENPC.Monitoring.EventNotification.EventNotifier.EmailSender,EmailSender','2024-06-11 16:21:47');
INSERT INTO notifymode (NotifyModeId, NotifyModeName, NotifyModeFormat, Description, LastUpdateDate) VALUES (2,'SMEI',NULL,'ENPC.Monitoring.EventNotification.EventNotifier.SMEINotifier,SMEINotifier','2024-06-11 16:21:47');
INSERT INTO notifymode (NotifyModeId, NotifyModeName, NotifyModeFormat, Description, LastUpdateDate) VALUES (3,'TTS',NULL,'ENPC.Monitoring.EventNotification.EventNotifier.TextToSpeechNotifier,TTSSender','2024-06-11 16:21:47');
INSERT INTO notifymode (NotifyModeId, NotifyModeName, NotifyModeFormat, Description, LastUpdateDate) VALUES (4,'Printer',NULL,'ENPC.Monitoring.EventNotification.EventNotifier.PrintNotifier,PrintSender','2024-06-11 16:21:47');
INSERT INTO notifymode (NotifyModeId, NotifyModeName, NotifyModeFormat, Description, LastUpdateDate) VALUES (5,'Logger',NULL,'ENPC.Monitoring.EventNotification.EventNotifier.LogNotifier,EventNotifier','2024-06-11 16:21:47');
INSERT INTO notifymode (NotifyModeId, NotifyModeName, NotifyModeFormat, Description, LastUpdateDate) VALUES (6,'WeChatSend',NULL,'ENPC.Monitoring.EventNotification.EventNotifier.WeChatSend,WeChatSend','2024-06-11 16:21:47');
INSERT INTO notifymode (NotifyModeId, NotifyModeName, NotifyModeFormat, Description, LastUpdateDate) VALUES (7,'Notes',NULL,'ENPC.Monitoring.EventNotification.EventNotifier.NotesMailNotifier,NotesMailNotifier','2024-06-11 16:21:47');
INSERT INTO notifymode (NotifyModeId, NotifyModeName, NotifyModeFormat, Description, LastUpdateDate) VALUES (8,'Dispatch-Inspur',NULL,'ENPC.Monitoring.EventNotification.EventNotifier.EomsNotifier,EomsNotifier','2024-06-11 16:21:47');
INSERT INTO notifymode (NotifyModeId, NotifyModeName, NotifyModeFormat, Description, LastUpdateDate) VALUES (9,'GSMModem',NULL,'ENPC.Monitoring.EventNotification.EventNotifier.GSMModemNotifier,GSMModemNotifier','2024-06-11 16:21:47');
INSERT INTO notifymode (NotifyModeId, NotifyModeName, NotifyModeFormat, Description, LastUpdateDate) VALUES (10,'InfoX-MAS',NULL,'ENPC.Monitoring.EventNotification.EventNotifier.infoX_MAS_DLL_Notifier,infoX_MAS_DLL_Notifier','2024-06-11 16:21:47');
INSERT INTO notifymode (NotifyModeId, NotifyModeName, NotifyModeFormat, Description, LastUpdateDate) VALUES (11,'FTP',NULL,'ENPC.Monitoring.EventNotification.EventNotifier.FTPNotifier,FTPNotifier','2024-06-11 16:21:47');
INSERT INTO notifymode (NotifyModeId, NotifyModeName, NotifyModeFormat, Description, LastUpdateDate) VALUES (12,'SNMP',NULL,'ENPC.Monitoring.EventNotification.EventNotifier.SNMPNotifier,SNMPNotifier','2024-06-11 16:21:47');
INSERT INTO notifymode (NotifyModeId, NotifyModeName, NotifyModeFormat, Description, LastUpdateDate) VALUES (13,'DInterface',NULL,'ENPC.Monitoring.EventNotification.EventNotifier.DInterface,DInterface','2024-06-11 16:21:47');
INSERT INTO notifymode (NotifyModeId, NotifyModeName, NotifyModeFormat, Description, LastUpdateDate) VALUES (14,'DInterface4SZ',NULL,'ENPC.Monitoring.EventNotification.EventNotifier.DInterface,DInterface4SZ','2024-06-11 16:21:47');
INSERT INTO notifymode (NotifyModeId, NotifyModeName, NotifyModeFormat, Description, LastUpdateDate) VALUES (15,'VoicePhone',NULL,'ENPC.Monitoring.EventNotification.EventNotifier.VoicePhoneNotifier,VoicePhone','2024-06-11 16:21:47');
INSERT INTO notifymode (NotifyModeId, NotifyModeName, NotifyModeFormat, Description, LastUpdateDate) VALUES (16,'SmsSend',NULL,'ENPC.Monitoring.EventNotification.EventNotifier.SmsSend,SmsSend','2024-06-11 16:21:47');
INSERT INTO notifymode (NotifyModeId, NotifyModeName, NotifyModeFormat, Description, LastUpdateDate) VALUES (17,'VoicePhone',NULL,'ENPC.Monitoring.EventNotification.EventNotifier.PhoneTTS,PhoneTTS','2024-06-11 16:21:47');
INSERT INTO notifymode (NotifyModeId, NotifyModeName, NotifyModeFormat, Description, LastUpdateDate) VALUES (19,'TTSEx',NULL,'ENPC.Monitoring.EventNotification.EventNotifier.TextToSpeechExNotifier,TTSExSender','2024-06-11 16:21:47');
INSERT INTO notifymode (NotifyModeId, NotifyModeName, NotifyModeFormat, Description, LastUpdateDate) VALUES (21,'AlarmLightBox',NULL,'ENPC.Monitoring.EventNotification.EventNotifier.AlarmLightBox,AlarmLightBox','2024-06-11 16:21:47');

INSERT INTO tbl_area (AreaId, AreaName, Description) VALUES (-1,'所有片区权限组','拥有片区所有权限');

INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,11,'高压配电','High Voltage Distribution',1,0,1,'','Equipment.png','1',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,12,'低压配电','Low Voltage Distribution',1,0,1,'','Equipment.png','2',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,13,'柴油发电机组','Diesel Generator',1,0,1,'','Generator.png','5',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,21,'交流配电屏','AC Distribution Board',1,0,1,'','Equipment.png','3',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,22,'开关电源','Rectifier',1,0,1,'','Equipment.png','6',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,23,'直流配电屏','DC Distribution Board',1,0,1,'','Equipment.png','4',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,24,'蓄电池组','Battery',1,0,1,'','Battery.png','7',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,31,'UPS设备','UPS',1,0,1,'','Ups.png','8',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,35,'UPS配电屏','UPS Distribution Board',1,0,1,'',NULL,'9',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,36,'UPS电池','UPS Battery',1,0,1,'',NULL,'10',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,37,'模块化UPS','Module UPS',1,0,1,'',NULL,'30',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,42,'中央空调(水冷)','Central Air-Condition (Water Cooling)',1,0,1,'','AirCondition.png','13',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,43,'专用空调(风冷)','Special Air-Condition (Wind Cooling)',1,0,1,'','AirCondition.png','12',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,44,'专用空调(水冷)','Special Air-Condition (Water Cooling)',1,0,1,'','AirCondition.png','14',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,45,'普通空调','Split Air-Condition',1,0,1,'','AirCondition.png','15',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,51,'机房/基站环境','Station Environment',1,0,1,'','Equipment.png','18',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,62,'太阳能/风能设备','Solar/Wind Energy Equipment',1,0,1,'','Equipment.png','20',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,65,'燃气轮机发电机组','Gas Turbine Generator Set',1,0,1,'',NULL,'21',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,66,'风力发电设备','Wind Power Generator',1,0,1,'',NULL,'22',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,67,'新风设备','Fresh Air Systems',1,0,1,'',NULL,'24',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,68,'热交换设备','Heat Exchange Device',1,0,1,'',NULL,'25',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,69,'热管设备','Heat Pipe',1,0,1,'',NULL,'26',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,72,'防雷设备/防雷箱','Lightning Protection Equipment',1,0,1,'','Equipment.png','28',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,73,'240V直流系统','240V DC',1,0,1,'',NULL,'11',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,74,'蓄电池温控柜','Battery Cabinet with Temperature Control System',1,0,1,'',NULL,'27',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,75,'240V电池','240V Battery',1,0,1,'',NULL,'31',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,76,'燃料电池','Fuel Cell',1,0,1,'',NULL,'29',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,81,'监控设备','240V DC',1,0,1,'',NULL,'19',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,82,'门禁系统','Door Access Control',1,0,1,'','Door.png','17',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,85,'智能电表','Ammeter',1,0,1,'','ATMKey.png','16',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,86,'智能通风系统','Aeration System',1,0,1,'','ATMKey.png','23',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,90,'图像设备','Image Equipment',1,0,1,'',NULL,'80',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,7,99,'自诊断设备','SelfDiagnostics',1,0,1,'',NULL,'99',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,71,3,'虚拟局站','Virtual Station',1,0,1,'',NULL,'9',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,71,101,'A级局站','A Station',1,0,1,'','SmallStation.png','1',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,71,102,'B级局站','B Station',1,0,1,'',NULL,'2',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,71,103,'C级局站','C Station',1,0,1,'',NULL,'3',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,71,104,'D级局站','D Station',1,0,1,'','BigStation.png','4',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,71,105,'J级基站','CTCC',1,0,1,'',NULL,'5',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,71,106,'L级基站','WITH CNCC',1,0,1,'',NULL,'6',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,71,107,'Y级基站','WITH CMCC',1,0,1,'',NULL,'7',NULL,NULL,NULL);
INSERT INTO tbl_businessdataitem (BusinessId, ParentEntryId, ParentItemId, EntryId, ItemId, ItemValue, ItemAlias, IsSystem, IsDefault, Enable, Description, ExtendField1, ExtendField2, ExtendField3, ExtendField4, ExtendField5) VALUES (2,0,0,71,108,'S级基站','With CNCC&CMCC',1,0,1,'',NULL,'8',NULL,NULL,NULL);

INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,11,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,14,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,16,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,25,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,11,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,14,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,16,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,17,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,18,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,25,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,11,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,14,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,16,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,25,11);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,12,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,15,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,26,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,27,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,28,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,29,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,12,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,15,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,26,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,27,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,28,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,29,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,12,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,15,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,26,12);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,13,13);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,13,13);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,13,13);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,21,14);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,22,15);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,23,16);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,17,17);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,17,17);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,18,18);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,18,18);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,24,20);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,22,20);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,21,21);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,21,21);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,21,22);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,22,22);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,23,22);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,22,22);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,22,22);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,22,23);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,23,23);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,23,23);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,24,24);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,24,24);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,24,24);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,24,26);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,27,27);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,28,28);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,29,29);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,30,30);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,31,31);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,31,31);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,31,31);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,32,32);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,33,33);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,34,34);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,31,35);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,21,35);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,35,35);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,24,36);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,24,36);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,31,37);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,12,37);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,12,38);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,23,39);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,43,40);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,44,40);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,23,40);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,41,41);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,42,41);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,41,41);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,41,42);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,42,42);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,42,42);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,42,42);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,43,43);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,43,43);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,44,44);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,44,44);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,45,45);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,41,45);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,45,45);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,41,45);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,45,45);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,51,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,52,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,53,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,54,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,57,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,61,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,62,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,71,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,72,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,81,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,83,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,84,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,87,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,90,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,51,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,52,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,53,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,54,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,55,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,61,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,83,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,87,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,51,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,52,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,53,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,54,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,55,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,57,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,61,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,71,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,81,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,83,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,84,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,87,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,90,51);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,55,55);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,62,62);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,62,62);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,13,65);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,13,65);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,13,66);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,86,67);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,67,67);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,86,68);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,68,68);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,86,69);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,69,69);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,72,72);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,72,72);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,22,73);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,86,74);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,24,75);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,24,76);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,82,82);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,82,82);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,82,82);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,85,85);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,85,85);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,85,85);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,86,86);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,86,86);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,86,86);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,88,88);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,90,90);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,97,97);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,97,97);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,97,97);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,98,98);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,98,98);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,98,98);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,99,99);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,99,99);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,99,99);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (1,2,101,101);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (2,2,101,101);
INSERT INTO tbl_categoryidmap (BusinessId, CategoryTypeId, OriginalCategoryId, BusinessCategoryId) VALUES (3,2,101,101);

INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (0,'CfgStationStructureOP','TBL_StationStructure','StructureId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (1,'CfgStationOP','TBL_Station','StationId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (2,'NA','TBL_StationMask','StationId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (3,'CfgEquipmentOP','TBL_Equipment','StationId.EquipmentId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (4,'NA','TBL_EquipmentMask','StationId.EquipmentId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (5,'CfgHouseOP','TBL_House','StationId.HouseId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (6,'EquipmentTemplateOP','TBL_EquipmentTemplate','EquipmentTemplateId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (7,'EquipmentSignalOP','TBL_Signal','EquipmentTemplateId.SignalId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (8,'SignalMeaningsOP','TBL_SignalMeanings','EquipmentTemplateId.SignalId.StateValue');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (9,'EquipmentSignalPropertyOP','TBL_SignalProperty','EquipmentTemplateId.SignalId.SignalPropertyId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (10,'EquipmentEventOP','TBL_Event','EquipmentTemplateId.EventId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (11,'EventConditionOP','TBL_EventCondition','EquipmentTemplateId.EventId.EventConditionId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (12,'NA','TBL_EventMask','StationId.EquipmentId.EventId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (13,'EquipmentCommandOP','TBL_Control','EquipmentTemplateId.ControlId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (14,'CommandMeaningsOP','TBL_ControlMeanings','EquipmentTemplateId.ControlId.ParameterValue');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (15,'CfgMonitorUnitOP','TBL_MonitorUnit','StationId.MonitorUnitId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (16,'CfgPortOP','TBL_Port','MonitorUnitId.PortId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (17,'CfgSamplerUnitOP','TBL_SamplerUnit','MonitorUnitId.PortId.SamplerUnitId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (18,'NA','TBL_ChannelMap','MonitorUnitId.SamplerUnitId.OriginalChannelNo.StandardChannelNo');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (19,'MonitorUnitSignalOP','TBL_MonitorUnitSignal','StationId.MonitorUnitId.EquipmentId.SignalId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (20,'MonitorUnitEventOP','TBL_MonitorUnitEvent','StationId.MonitorUnitId.EquipmentId.EventId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (21,'MonitorUnitControlOP','TBL_MonitorUnitControl','StationId.MonitorUnitId.EquipmentId.ControlId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (22,'NA','TBL_DataItem','EntryId.ItemId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (23,'NA','TBL_DataEntry','EntryId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (24,'NA','TBL_StandardDic','StandardDicId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (25,'NA','TBL_EventBaseDic','BaseTypeId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (26,'SamplerOP','TBL_Sampler','SamplerId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (27,'ResourceStructureOP','ResourceStructure','ResourceStructureId');
INSERT INTO tbl_configchangedefine (ConfigId, EntityName, TableName, IdDefine) VALUES (28,'ResourceStructureOP','tbl_equipment','ResourceStructureId.EquipmentId');

INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (0,0,0,0,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (0,1,0,1,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (0,2,0,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (0,3,0,3,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (1,0,1,0,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (1,1,1,1,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (1,2,1,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (1,3,1,3,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (6,0,6,0,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (6,1,6,1,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (6,2,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (6,3,6,3,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (15,0,15,0,'1.2');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (15,1,15,1,'1.2');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (15,2,15,2,'1.2');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (15,3,15,3,'1.2');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (23,0,23,0,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (23,1,23,1,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (23,2,23,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (23,3,23,3,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (25,0,25,0,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (25,1,25,1,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (25,2,25,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (25,3,25,3,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (26,0,26,0,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (26,1,26,1,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (26,2,26,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (26,3,26,3,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (3,1,1,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (3,2,1,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (3,3,1,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (5,1,1,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (5,2,1,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (5,3,1,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (7,1,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (7,2,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (7,3,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (8,1,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (8,2,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (8,3,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (9,1,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (9,2,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (9,3,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (10,1,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (10,2,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (10,3,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (11,1,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (11,2,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (11,3,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (13,1,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (13,2,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (13,3,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (14,1,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (14,2,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (14,3,6,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (16,1,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (16,2,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (16,3,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (17,1,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (17,2,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (17,3,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (18,1,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (18,2,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (18,3,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (19,1,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (19,2,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (19,3,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (20,1,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (20,2,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (20,3,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (21,1,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (21,2,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (21,3,15,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (22,1,23,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (22,2,23,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (22,3,23,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (27,1,27,1,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (27,2,27,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (27,3,27,3,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (28,1,28,1,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (28,2,28,2,'1');
INSERT INTO tbl_configchangemap (MicroConfigId, MicroEditType, MacroConfigId, MacroEditType, IdConvertRule) VALUES (28,3,28,3,'1');

INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (1,1,'TTime','时间的结构',1,'Years','short','年',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (2,1,'TTime','时间的结构',2,'Month','Char','月',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (3,1,'TTime','时间的结构',3,'Day','Char','日',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (4,1,'TTime','时间的结构',4,'Hour','Char','时',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (5,1,'TTime','时间的结构',5,'Minute','Char','分',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (6,1,'TTime','时间的结构',6,'Second','Char','秒',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (7,2,'TSemaphore','信号量的值的结构',1,'TSignalId','Sizeof(TSignalMeasurementId)','设备采集点标识',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (8,2,'TSemaphore','信号量的值的结构',2,'Type','EnumType','数据类型',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (9,2,'TSemaphore','信号量的值的结构',3,'MeasuredVal','Float','实测值',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (10,2,'TSemaphore','信号量的值的结构',4,'SetupVal','Float','设置值',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (11,2,'TSemaphore','信号量的值的结构',5,'Status','EnumState','状态',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (12,2,'TSemaphore','信号量的值的结构',6,'Time','Char[TIME_LEN]','时间，格式YYYY-MM-DD<SPACE键>hh:mm:ss（采用24小时的时间制式）',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (13,3,'TThreshold','信号量的门限值的结构',1,'TSignalId','Sizeof(TSignalMeasurementId)','设备采集点标识',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (14,3,'TThreshold','信号量的门限值的结构',2,'Type','EnumType','数据类型',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (15,3,'TThreshold','信号量的门限值的结构',3,'Threshold','Float','告警门限值',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (16,3,'TThreshold','信号量的门限值的结构',4,'AlarmLevel','EnumLevel','告警等级',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (17,3,'TThreshold','信号量的门限值的结构',5,'NMAlarmID','Char[NMALARMID_LEN]','网管告警编号',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (18,4,'TAlarm','告警消息的结构',1,'SerialNo','Char[SERIALNO_LEN]','告警序号',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (19,4,'TAlarm','告警消息的结构',2,'NMAlarmID','Char[NMALARMID_LEN]','网管告警编号',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (20,4,'TAlarm','告警消息的结构',3,'DeviceID','Char[DEVICEID_LEN]','设备ID',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (21,4,'TAlarm','告警消息的结构',4,'TSignalId','Sizeof(TSignalMeasurementId)','设备采集点标识',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (22,4,'TAlarm','告警消息的结构',5,'AlarmTime','Char [TIME_LEN]','告警时间，YYYY-MM-DD<SPACE键>hh:mm:ss（采用24小时的时间制式）',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (23,4,'TAlarm','告警消息的结构',6,'AlarmLevel','EnumLevel','告警级别',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (24,4,'TAlarm','告警消息的结构',7,'AlarmFlag','EnumFlag','告警标志',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (25,4,'TAlarm','告警消息的结构',8,'AlarmDesc','Char [DES_LENGTH]','告警的事件描述',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (26,4,'TAlarm','告警消息的结构',9,'EventValue','Float','告警触发值',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (27,4,'TAlarm','告警消息的结构',10,'AlarmRemark','Char[ALARMREMARK_LEN]','预留字段',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (28,5,'TFSUStatus','FSU状态参数',1,'CPUUsage','Float','CPU使用率',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (29,5,'TFSUStatus','FSU状态参数',2,'MEMUsage','Float','内存使用率',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (30,5,'TFSUStatus','FSU状态参数',3,'HardDiskUsage','Float','FSU硬盘占用率',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (31,6,'TDevConf','监控对象配置信息',1,'DeviceID','Char[DEVICEID_LEN]','设备ID',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (32,6,'TDevConf','监控对象配置信息',2,'DeviceName','Char[NAME_LENGTH]','设备名称',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (33,6,'TDevConf','监控对象配置信息',3,'SiteID','Char[ID_LENGTH]','所属站点编码',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (34,6,'TDevConf','监控对象配置信息',4,'RoomID','Char[n* ID_LENGTH]','FSU物理机房编码',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (35,6,'TDevConf','监控对象配置信息',5,'SiteName','Char[NAME_LENGTH]','设备所在的站点名称',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (36,6,'TDevConf','监控对象配置信息',6,'RoomName','Char[NAME_LENGTH]','设备所在的机房名称',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (37,6,'TDevConf','监控对象配置信息',7,'DeviceType','EnumDeviceType','设备类型',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (38,6,'TDevConf','监控对象配置信息',8,'DeviceSubType','EnumDeviceSubType?','设备子类型',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (39,6,'TDevConf','监控对象配置信息',9,'Model','Char [DES_LENGTH]','设备型号',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (40,6,'TDevConf','监控对象配置信息',10,'Brand','Char [DES_LENGTH]','设备品牌',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (41,6,'TDevConf','监控对象配置信息',11,'RatedCapacity','Float','额定容量',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (42,6,'TDevConf','监控对象配置信息',12,'Version','Char [VER_LENGTH]','版本',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (43,6,'TDevConf','监控对象配置信息',13,'BeginRunTime','Char [TIME_LEN]','启用时间',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (44,6,'TDevConf','监控对象配置信息',14,'DevDescribe','Char [DES_LENGTH]','设备描述信息',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (45,6,'TDevConf','监控对象配置信息',15,'Signals','N*TSignal','一个或多个监控点信号配置信息',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (46,6,'TDevConf','监控对象配置信息',16,'ConfRemark','Char[CONFREMARK_LEN]','配置预留字段',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (47,7,'TFSULoginInfo','FSU注册信息',1,'FSUID','Char[FSUID_LEN]','FSU ID',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (48,7,'TFSULoginInfo','FSU注册信息',2,'FSUName','Char[FSUName_LEN]','FSU 名称',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (49,7,'TFSULoginInfo','FSU注册信息',3,'UserName','Char[USER_LENGTH]','用户名',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (50,7,'TFSULoginInfo','FSU注册信息',4,'PassWord','Char[PASSWORD_LEN]','口令',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (51,7,'TFSULoginInfo','FSU注册信息',5,'FSUIP','IP_LENGTH','FSU IP',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (52,7,'TFSULoginInfo','FSU注册信息',6,'FSUMAC','Char[MAC_LENGTH]','FSU MAC地址',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (53,7,'TFSULoginInfo','FSU注册信息',7,'FSUVER','Char[VER_LENGTH]','FSU 版本号',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (54,7,'TFSULoginInfo','FSU注册信息',8,'SiteID','Char[ID_LENGTH]','FSU 所属站点编码',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (55,7,'TFSULoginInfo','FSU注册信息',9,'SiteName','Char[ID_LENGTH]','FSU 所属站点名称',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (56,7,'TFSULoginInfo','FSU注册信息',10,'RoomID','Char[n* ID_LENGTH]','FSU 物理机房编码',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (57,7,'TFSULoginInfo','FSU注册信息',11,'RoomName','Char[ID_LENGTH]','FSU 所属机房名称',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (58,8,'TFTPInfo','FTP信息',1,'FTPUserName','USER_LENGTH','用户登录名',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (59,8,'TFTPInfo','FTP信息',2,'FTPPassWord','PASSWORD_LEN','密码',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (60,9,'TSUStatus','SU状态',1,'CPUUsage','Float','CPU使用率',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (61,9,'TSUStatus','SU状态',2,'MEMUsage','Float','内存使用率',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (62,10,'TFTPInfo','FTP信息',1,'FTPUserName','USER_LENGTH','用户登录名',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (63,10,'TFTPInfo','FTP信息',2,'FTPPassWord','PASSWORD_LEN','密码',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (64,11,'TSignalMeasurementId','设备采集点标识',1,'ID','Char[ID_LENGTH]','监控点ID',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (65,11,'TSignalMeasurementId','设备采集点标识',2,'SignalNumber','short','同设备同类监控点顺序号',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (66,12,'TStorageRule','信号数据存储规则',1,'TSignalId','Sizeof(TSignalMeasurementId)','设备采集点标识',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (67,12,'TStorageRule','信号数据存储规则',2,'Type','EnumType','数据类型',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (68,12,'TStorageRule','信号数据存储规则',3,'AbsoluteVal','Float','绝对阀值',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (69,12,'TStorageRule','信号数据存储规则',4,'RelativeVal','Float','百分比阀值',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (70,12,'TStorageRule','信号数据存储规则',5,'StorageInterval','long','存储时间间隔',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (71,12,'TStorageRule','信号数据存储规则',6,'StorageRefTime','Char[TIME_LEN]','存储参考时间',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (72,13,'TSignal','监控点信号配置信息',1,'TSignalId','Sizeof(TSignalMeasurementId)','设备采集点标识',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (73,13,'TSignal','监控点信号配置信息',2,'SignalName','Char[NAME_LENGTH]','信号名称',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (74,13,'TSignal','监控点信号配置信息',3,'Type','EnumType','数据类型',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (75,13,'TSignal','监控点信号配置信息',4,'Threshold','Float','门限值',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (76,13,'TSignal','监控点信号配置信息',5,'AlarmLevel','EnumLevel','告警级别',NULL,NULL);
INSERT INTO tbl_datastruct (EntryId, StructType, StructName, StructDescription, AttributeId, AttributeName, AttributeType, TypeDefine, Description, ExtendField1) VALUES (77,13,'TSignal','监控点信号配置信息',6,'NMAlarmID','Char[NMALARMID_LEN]','网管告警编号',NULL,NULL);

INSERT INTO tbl_department (DepartmentId, DepartmentName, DepartmentLevel, DepartmentFunction, ParentDeprtId, Description, LastUpdateDate) VALUES (0,'默认部门',NULL,NULL,NULL,NULL,'2024-06-11 16:21:46');

INSERT INTO tbl_doorcontroller (DoorControlId, DoorControlName, LicenseKey, Display, CardLength, MaxDoorCount, DLLPath, Description) VALUES (1,'DC505A门禁',NULL,16,10,1,'DC505A.OCX',NULL);
INSERT INTO tbl_doorcontroller (DoorControlId, DoorControlName, LicenseKey, Display, CardLength, MaxDoorCount, DLLPath, Description) VALUES (2,'CHD806D2M3',NULL,16,10,2,'',NULL);
INSERT INTO tbl_doorcontroller (DoorControlId, DoorControlName, LicenseKey, Display, CardLength, MaxDoorCount, DLLPath, Description) VALUES (3,'ES2000门禁',NULL,16,10,1,'ES2000.OCX',NULL);
INSERT INTO tbl_doorcontroller (DoorControlId, DoorControlName, LicenseKey, Display, CardLength, MaxDoorCount, DLLPath, Description) VALUES (4,'CHD806D4M3',NULL,16,10,4,'',NULL);
INSERT INTO tbl_doorcontroller (DoorControlId, DoorControlName, LicenseKey, Display, CardLength, MaxDoorCount, DLLPath, Description) VALUES (5,'DDS门禁',NULL,10,8,4,'',NULL);
INSERT INTO tbl_doorcontroller (DoorControlId, DoorControlName, LicenseKey, Display, CardLength, MaxDoorCount, DLLPath, Description) VALUES (11,'海康门禁',NULL,10,10,1,'',NULL);
INSERT INTO tbl_doorcontroller (DoorControlId, DoorControlName, LicenseKey, Display, CardLength, MaxDoorCount, DLLPath, Description) VALUES (12,'纽贝尔门禁(腾讯HTTP协议)',NULL,10,10,1,'',NULL);
INSERT INTO tbl_doorcontroller (DoorControlId, DoorControlName, LicenseKey, Display, CardLength, MaxDoorCount, DLLPath, Description) VALUES (14,'纽贝尔500E人脸刷卡一体机',NULL,16,10,1,'',NULL);
INSERT INTO tbl_doorcontroller (DoorControlId, DoorControlName, LicenseKey, Display, CardLength, MaxDoorCount, DLLPath, Description) VALUES (15,'CHD200D7门禁',NULL,16,10,1,'',NULL);
INSERT INTO tbl_doorcontroller (DoorControlId, DoorControlName, LicenseKey, Display, CardLength, MaxDoorCount, DLLPath, Description) VALUES (16,'纽贝尔人脸门禁一体机500F7E',NULL,10,10,1,'',NULL);
INSERT INTO tbl_doorcontroller (DoorControlId, DoorControlName, LicenseKey, Display, CardLength, MaxDoorCount, DLLPath, Description) VALUES (17, 'CHD200D7门禁(子父设备合并)', NULL, 16, 10, 1, '', NULL);
INSERT INTO tbl_doorcontroller (DoorControlId, DoorControlName, LicenseKey, Display, CardLength, MaxDoorCount, DLLPath, Description) VALUES (20,'vertiv门禁',NULL,16,10,1,'',NULL);

INSERT INTO tbl_doorproperty (category, propertytype) VALUES (1,6);
INSERT INTO tbl_doorproperty (category, propertytype) VALUES (1,10);
INSERT INTO tbl_doorproperty (category, propertytype) VALUES (3,6);
INSERT INTO tbl_doorproperty (category, propertytype) VALUES (3,10);
INSERT INTO tbl_doorproperty (category, propertytype) VALUES (11,1);
INSERT INTO tbl_doorproperty (category, propertytype) VALUES (11,4);
INSERT INTO tbl_doorproperty (category, propertytype) VALUES (11,10);
INSERT INTO tbl_doorproperty (category, propertytype) VALUES (12,1);
INSERT INTO tbl_doorproperty (category, propertytype) VALUES (12,2);
INSERT INTO tbl_doorproperty (category, propertytype) VALUES (12,3);
INSERT INTO tbl_doorproperty (category, propertytype) VALUES (12,4);
INSERT INTO tbl_doorproperty (category, propertytype) VALUES (12,5);
INSERT INTO tbl_doorproperty (category, propertytype) VALUES (12,6);
INSERT INTO tbl_doorproperty (category, propertytype) VALUES (12,7);
INSERT INTO tbl_doorproperty (category, propertytype) VALUES (12,8);
INSERT INTO tbl_doorproperty (category, propertytype) VALUES (12,9);
INSERT INTO tbl_doorproperty (category, propertytype) VALUES (12,10);

INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (1,4,0,'进门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (1,4,1,'出门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (2,4,0,'进门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (2,4,1,'出门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (3,4,0,'进门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (3,4,1,'出门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (4,4,0,'进门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (4,4,1,'出门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (11,1,2,'刷卡+密码');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (11,1,3,'刷卡');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (11,1,4,'刷卡/（工号+密码）');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (11,1,5,'指纹');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (11,1,6,'指纹+密码');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (11,1,7,'刷卡/指纹');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (11,1,8,'刷卡+指纹');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (11,1,9,'刷卡+指纹+密码');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (11,1,10,'刷卡/指纹/人脸/(工号+密码)');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (11,1,11,'指纹+人脸');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (11,1,13,'刷卡+人脸');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (11,1,14,'人脸');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (11,1,15,'工号+密码');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (11,1,16,'(工号+密码)/指纹');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (11,1,17,'工号+指纹');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (11,1,18,'工号+密码+指纹');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (11,1,19,'刷卡+指纹+人脸');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (11,1,20,'指纹+人脸+密码');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (11,1,21,'工号+人脸');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (11,1,23,'指纹/人脸');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (11,1,24,'刷卡/人脸/(工号+密码)');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (11,4,1,'进门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (11,4,2,'出门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (12,1,0,'刷卡');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (12,1,1,'密码(门的密码)');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (12,1,2,'卡+密码(卡的密码)');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (12,1,3,'卡+密码(门的密码)');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (12,2,0,'短路有效');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (12,2,1,'断路有效');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (12,3,-2,'密码无效');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (12,3,-1,'刷卡失败');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (12,3,0,'刷卡/卡+密码开门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (12,3,1,'按钮开门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (12,3,2,'密码开门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (12,3,3,'远程开门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (12,3,11,'机器人开门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (12,3,12,'机器人关门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (12,3,13,'人脸识别开门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (12,4,0,'无标志');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (12,4,1,'进门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (12,4,2,'出门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (15,4,0,'进门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (15,4,1,'出门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (16,4,1,'进门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (16,4,2,'出门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (20,3,-2,'密码无效');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (20,3,-1,'刷卡失败');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (20,3,0,'刷卡/卡+密码开门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (20,3,1,'按钮开门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (20,3,2,'密码开门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (20,3,3,'远程开门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (20,3,11,'机器人开门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (20,3,12,'机器人关门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (20,3,13,'人脸识别开门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (20,4,1,'进门');
INSERT INTO tbl_doorpropertymeaning (category, propertytype, propertyid, meaning) VALUES (20,4,2,'出门');

INSERT INTO tbl_enumdata (EntryId, AttributeType, AttributeName, AttributeDescription, EnumId, EnumType, EnumValue, EnumDefine, Description, ExtendField1) VALUES (1,1,'EnumResult','报文返回结果',1,'FAILURE',0,'失败',NULL,NULL);
INSERT INTO tbl_enumdata (EntryId, AttributeType, AttributeName, AttributeDescription, EnumId, EnumType, EnumValue, EnumDefine, Description, ExtendField1) VALUES (2,1,'EnumResult','报文返回结果',2,'SUCCESS',1,'成功',NULL,NULL);
INSERT INTO tbl_enumdata (EntryId, AttributeType, AttributeName, AttributeDescription, EnumId, EnumType, EnumValue, EnumDefine, Description, ExtendField1) VALUES (3,2,'EnumType','监控系统数据的种类',1,'DI',4,'遥信',NULL,NULL);
INSERT INTO tbl_enumdata (EntryId, AttributeType, AttributeName, AttributeDescription, EnumId, EnumType, EnumValue, EnumDefine, Description, ExtendField1) VALUES (4,2,'EnumType','监控系统数据的种类',2,'AI',3,'遥测',NULL,NULL);
INSERT INTO tbl_enumdata (EntryId, AttributeType, AttributeName, AttributeDescription, EnumId, EnumType, EnumValue, EnumDefine, Description, ExtendField1) VALUES (5,2,'EnumType','监控系统数据的种类',3,'DO',1,'遥控',NULL,NULL);
INSERT INTO tbl_enumdata (EntryId, AttributeType, AttributeName, AttributeDescription, EnumId, EnumType, EnumValue, EnumDefine, Description, ExtendField1) VALUES (6,2,'EnumType','监控系统数据的种类',4,'AO',2,'遥调',NULL,NULL);
INSERT INTO tbl_enumdata (EntryId, AttributeType, AttributeName, AttributeDescription, EnumId, EnumType, EnumValue, EnumDefine, Description, ExtendField1) VALUES (7,2,'EnumType','监控系统数据的种类',5,'ALARM',0,'告警',NULL,NULL);
INSERT INTO tbl_enumdata (EntryId, AttributeType, AttributeName, AttributeDescription, EnumId, EnumType, EnumValue, EnumDefine, Description, ExtendField1) VALUES (8,2,'EnumType','监控系统数据的种类',6,'ALARM',5,'通讯中断',NULL,NULL);
INSERT INTO tbl_enumdata (EntryId, AttributeType, AttributeName, AttributeDescription, EnumId, EnumType, EnumValue, EnumDefine, Description, ExtendField1) VALUES (9,2,'EnumType','监控系统数据的种类',7,'INVALID',-1,'无效值',NULL,NULL);
INSERT INTO tbl_enumdata (EntryId, AttributeType, AttributeName, AttributeDescription, EnumId, EnumType, EnumValue, EnumDefine, Description, ExtendField1) VALUES (10,3,'EnumState','数据值的状态',1,'NOALARM',0,'正常数据',NULL,NULL);
INSERT INTO tbl_enumdata (EntryId, AttributeType, AttributeName, AttributeDescription, EnumId, EnumType, EnumValue, EnumDefine, Description, ExtendField1) VALUES (11,3,'EnumState','数据值的状态',2,'CRITICAL',1,'一级告警',NULL,NULL);
INSERT INTO tbl_enumdata (EntryId, AttributeType, AttributeName, AttributeDescription, EnumId, EnumType, EnumValue, EnumDefine, Description, ExtendField1) VALUES (12,3,'EnumState','数据值的状态',3,'MAJOR',2,'二级告警',NULL,NULL);
INSERT INTO tbl_enumdata (EntryId, AttributeType, AttributeName, AttributeDescription, EnumId, EnumType, EnumValue, EnumDefine, Description, ExtendField1) VALUES (13,3,'EnumState','数据值的状态',4,'MINOR',3,'三级告警',NULL,NULL);
INSERT INTO tbl_enumdata (EntryId, AttributeType, AttributeName, AttributeDescription, EnumId, EnumType, EnumValue, EnumDefine, Description, ExtendField1) VALUES (14,3,'EnumState','数据值的状态',5,'HINT',4,'四级告警',NULL,NULL);
INSERT INTO tbl_enumdata (EntryId, AttributeType, AttributeName, AttributeDescription, EnumId, EnumType, EnumValue, EnumDefine, Description, ExtendField1) VALUES (15,3,'EnumState','数据值的状态',6,'OPEVENT',5,'操作事件',NULL,NULL);
INSERT INTO tbl_enumdata (EntryId, AttributeType, AttributeName, AttributeDescription, EnumId, EnumType, EnumValue, EnumDefine, Description, ExtendField1) VALUES (16,3,'EnumState','数据值的状态',7,'INVALID',6,'无效数据',NULL,NULL);
INSERT INTO tbl_enumdata (EntryId, AttributeType, AttributeName, AttributeDescription, EnumId, EnumType, EnumValue, EnumDefine, Description, ExtendField1) VALUES (17,4,'EnumFlag','告警标志',1,'BEGIN',0,'开始',NULL,NULL);
INSERT INTO tbl_enumdata (EntryId, AttributeType, AttributeName, AttributeDescription, EnumId, EnumType, EnumValue, EnumDefine, Description, ExtendField1) VALUES (18,4,'EnumFlag','告警标志',2,'END',1,'结束',NULL,NULL);
INSERT INTO tbl_enumdata (EntryId, AttributeType, AttributeName, AttributeDescription, EnumId, EnumType, EnumValue, EnumDefine, Description, ExtendField1) VALUES (19,5,'EnumLevel','告警等级',1,'CRITICAL',3,'一级告警',NULL,NULL);
INSERT INTO tbl_enumdata (EntryId, AttributeType, AttributeName, AttributeDescription, EnumId, EnumType, EnumValue, EnumDefine, Description, ExtendField1) VALUES (20,5,'EnumLevel','告警等级',2,'MAJOR',2,'二级告警',NULL,NULL);
INSERT INTO tbl_enumdata (EntryId, AttributeType, AttributeName, AttributeDescription, EnumId, EnumType, EnumValue, EnumDefine, Description, ExtendField1) VALUES (21,5,'EnumLevel','告警等级',3,'MINOR',1,'三级告警',NULL,NULL);
INSERT INTO tbl_enumdata (EntryId, AttributeType, AttributeName, AttributeDescription, EnumId, EnumType, EnumValue, EnumDefine, Description, ExtendField1) VALUES (22,5,'EnumLevel','告警等级',4,'HINT',0,'四级告警',NULL,NULL);
INSERT INTO tbl_enumdata (EntryId, AttributeType, AttributeName, AttributeDescription, EnumId, EnumType, EnumValue, EnumDefine, Description, ExtendField1) VALUES (23,5,'EnumLevel','告警等级',5,'NOALARM',-1,'正常数据',NULL,NULL);

INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (2,1,'生产厂家',1,'艾默生',0,0,'包括力博特、华为电气、安圣、艾默生、克劳瑞德等',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (4,1,'生产厂家',2,'中达',0,0,'包括台达、中达电通等',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (6,1,'生产厂家',3,'中恒',0,0,'包括施威特克、侨兴、中恒等',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (8,1,'生产厂家',4,'中兴',0,0,'中兴通讯',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (10,1,'生产厂家',5,'武汉普天',0,0,'包括洲际电源、电池等',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (12,1,'生产厂家',6,'珠江',0,0,'珠江电源',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (14,1,'生产厂家',7,'动力源',0,0,'北京动力源',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (16,1,'生产厂家',8,'易达',0,0,'东莞易达',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (18,1,'生产厂家',9,'金威源',0,0,'深圳金威源',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (20,1,'生产厂家',10,'通力盛达',0,0,'北京通力盛达',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (22,1,'生产厂家',11,'安耐特',0,0,'深圳安耐特',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (24,1,'生产厂家',12,'双登',0,0,'江苏双登',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (26,1,'生产厂家',13,'汤浅',0,0,'包括广东汤浅、日本汤浅等',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (28,1,'生产厂家',14,'南都',0,0,'浙江南都',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (30,1,'生产厂家',15,'圣阳',0,0,'山东圣阳',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (32,1,'生产厂家',16,'灯塔',0,0,'浙江卧龙灯塔',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (34,1,'生产厂家',17,'光宇',0,0,'哈尔滨光宇',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (36,1,'生产厂家',18,'华达',0,0,'包括深圳华达、艾诺斯华达等',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (38,1,'生产厂家',19,'GNB',0,0,'美国GNB',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (40,1,'生产厂家',20,'长光',0,0,'武汉长光',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (42,1,'生产厂家',21,'火炬',0,0,'淄博火炬',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (44,1,'生产厂家',22,'海霸',0,0,'山东海霸',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (46,1,'生产厂家',23,'大力神',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (48,1,'生产厂家',24,'派能',0,0,'中兴派能',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (50,1,'生产厂家',25,'银泰',0,0,'武汉银泰',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (52,1,'生产厂家',26,'华日',0,0,'山东华日',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (54,1,'生产厂家',27,'丰日',0,0,'湖南丰日',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (56,1,'生产厂家',28,'文隆',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (58,1,'生产厂家',29,'北京汉铭',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (60,1,'生产厂家',30,'联动天翼',0,0,'北京联动天翼',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (62,1,'生产厂家',31,'比亚迪',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (64,1,'生产厂家',32,'汇龙',0,0,'云南汇龙',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (66,1,'生产厂家',33,'阳光',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (68,1,'生产厂家',34,'爱克赛',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (70,1,'生产厂家',35,'施耐德',0,0,'包括施耐德、梅兰日兰、APC等',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (72,1,'生产厂家',36,'GE',0,0,'美国通用',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (74,1,'生产厂家',37,'索科曼',0,0,'包括索科曼、溯高美等',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (76,1,'生产厂家',38,'易事特',0,0,'广东易事特',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (78,1,'生产厂家',39,'史图斯',0,0,'STULZ',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (80,1,'生产厂家',40,'能威',0,0,'瑞士能威',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (82,1,'生产厂家',41,'西门子',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (84,1,'生产厂家',42,'华为',0,0,'华为技术',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (86,1,'生产厂家',43,'海通',0,0,'江苏海通',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (88,1,'生产厂家',44,'科士达',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (90,1,'生产厂家',45,'科华',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (92,1,'生产厂家',46,'爱维达',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (94,1,'生产厂家',47,'伊顿',0,0,'包括Powerware、山特、伊顿等',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (96,1,'生产厂家',48,'志成冠军',0,0,'广东志成冠军',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (98,1,'生产厂家',49,'先控',0,0,'河北先控',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (100,1,'生产厂家',50,'佳力图',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (102,1,'生产厂家',51,'海洛斯',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (104,1,'生产厂家',52,'斯泰科',0,0,'北京斯泰科',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (106,1,'生产厂家',53,'格力',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (108,1,'生产厂家',54,'海尔',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (110,1,'生产厂家',55,'科龙',0,0,'广东科龙',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (112,1,'生产厂家',56,'海信',0,0,'山东海信',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (114,1,'生产厂家',57,'志高',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (116,1,'生产厂家',58,'吉荣',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (118,1,'生产厂家',59,'登高',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (120,1,'生产厂家',60,'美的',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (122,1,'生产厂家',61,'春兰',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (124,1,'生产厂家',62,'大金',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (126,1,'生产厂家',63,'华菱',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (128,1,'生产厂家',64,'盾安',0,0,'浙江盾安',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (130,1,'生产厂家',65,'约顿',0,0,'上海约顿',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (132,1,'生产厂家',66,'铨高',0,0,'珠海铨高',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (134,1,'生产厂家',67,'融和',0,0,'北京融和',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (136,1,'生产厂家',68,'松下',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (138,1,'生产厂家',69,'优力',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (140,1,'生产厂家',70,'阿尔西',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (142,1,'生产厂家',71,'阿特拉斯',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (144,1,'生产厂家',72,'艾苏威尔',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (146,1,'生产厂家',73,'开利',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (148,1,'生产厂家',74,'依米康',0,0,'四川依米康',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (150,1,'生产厂家',75,'十字军',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (152,1,'生产厂家',76,'康明斯',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (154,1,'生产厂家',77,'卡特彼勒',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (156,1,'生产厂家',78,'道依兹',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (158,1,'生产厂家',79,'劳斯莱斯',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (160,1,'生产厂家',80,'威尔信',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (162,1,'生产厂家',81,'科泰',0,0,'上海科泰',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (164,1,'生产厂家',82,'泰豪',0,0,'泰豪科技',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (166,1,'生产厂家',83,'威能',0,0,'番禺威能',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (168,1,'生产厂家',84,'济柴',0,0,'济南柴油机',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (170,1,'生产厂家',85,'怡昌',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (172,1,'生产厂家',86,'开普',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (174,1,'生产厂家',87,'三菱',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (176,1,'生产厂家',88,'德锋',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (178,1,'生产厂家',89,'南柴',0,0,'南昌柴油机',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (180,1,'生产厂家',90,'科勒',0,0,'含常州科勒',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (182,1,'生产厂家',91,'BEST',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (184,1,'生产厂家',92,'IMV',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (186,1,'生产厂家',93,'波利',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (188,1,'生产厂家',94,'丹科',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (190,1,'生产厂家',95,'上柴',0,0,'上海柴油机',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (192,1,'生产厂家',96,'正和',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (194,1,'生产厂家',97,'威尔逊',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (196,1,'生产厂家',98,'辛普森',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (198,1,'生产厂家',99,'福发',0,0,'福州柴油机',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (200,2,'设备类型',13,'油机发电机组',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (202,2,'设备类型',18,'变压器',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (204,2,'设备类型',22,'48V开关电源',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (206,2,'设备类型',24,'蓄电池组',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (208,2,'设备类型',31,'交流不间断电源系统(UPS)',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (210,2,'设备类型',42,'空调设备',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (212,2,'设备类型',73,'240V直流供电系统',0,0,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (214,3,'变压器类型',1,'油浸式变压器',2,18,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (216,3,'变压器类型',2,'干式变压器',2,18,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (218,3,'变压器类型',3,'其他专用变压器',2,18,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (220,4,'不间断电源类型',1,'塔式UPS电源',2,31,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (222,4,'不间断电源类型',2,'模块化UPS电源',2,31,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (224,5,'开关电源类型',1,'分立式',2,22,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (226,5,'开关电源类型',2,'组合式',2,22,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (228,6,'蓄电池组类型',1,'铁锂电池',2,24,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (230,6,'蓄电池组类型',2,'阀控铅酸蓄电池',2,24,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (232,7,'空调类型',1,'机房专用空调设备',2,42,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (234,7,'空调类型',2,'普通空调设备',2,42,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (236,7,'空调类型',3,'中央空调设备',2,42,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (238,7,'空调类型',4,'其他节能空调',2,42,'',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (240,8,'模块容量',1,'200',0,0,'A',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (242,8,'模块容量',2,'100',0,0,'A',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (244,8,'模块容量',3,'50',0,0,'A',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (246,8,'模块容量',4,'30',0,0,'A',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (248,8,'模块容量',5,'25',0,0,'A',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (250,8,'模块容量',6,'10',0,0,'A',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (252,9,'单体电池标称电压',1,'2',0,0,'V',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (254,9,'单体电池标称电压',2,'6',0,0,'V',NULL,NULL);
INSERT INTO tbl_equipmentresourceitem (EntryItemId, EntryId, EntryValue, ItemId, ItemValue, ParentEntryId, ParentItemId, Description, ExtendField1, ExtendField2) VALUES (256,9,'单体电池标称电压',3,'12',0,0,'V',NULL,NULL);

INSERT INTO tbl_expert (ExpertId, StationCategoryId, StationCategoryName, BaseEquipmentTypeId, BaseEquipmentTypeName, BaseAlarmId, BaseAlarmName, StandardEquipmentTypeId, StandardEquipmentTypeName, StandardAlarmId, StandardAlarmName, Reason, Solution) VALUES (1,0,'默认',1101,'蓄电池',1101172001,'电池组1总电压(240V)过低告警',7,'铅酸电池','0500-002-007-10-007002','电池组总电压过低告警','1、市电停电，电池放电，电压下降\n2、市电正常，电池采样线缆或电池夹松动或断线','1、恢复供电\n2、检查接线');
INSERT INTO tbl_expert (ExpertId, StationCategoryId, StationCategoryName, BaseEquipmentTypeId, BaseEquipmentTypeName, BaseAlarmId, BaseAlarmName, StandardEquipmentTypeId, StandardEquipmentTypeName, StandardAlarmId, StandardAlarmName, Reason, Solution) VALUES (2,0,'默认',1101,'蓄电池',1101310001,'电池组单体内阻1过高告警',0,NULL,NULL,NULL,'1、电池故障，内阻升高\n2、电池监控接线虚接，接触内阻增加。电池放电会造成局部温度升高，有燃烧风险','1、使用内阻工具核对，故障电池及时更换\n2、检查接线，紧固，避免放电时有燃烧风险');
INSERT INTO tbl_expert (ExpertId, StationCategoryId, StationCategoryName, BaseEquipmentTypeId, BaseEquipmentTypeName, BaseAlarmId, BaseAlarmName, StandardEquipmentTypeId, StandardEquipmentTypeName, StandardAlarmId, StandardAlarmName, Reason, Solution) VALUES (3,0,'默认',1101,'蓄电池',1101899001,'整组容量预警',0,NULL,NULL,NULL,'整组容量低于设定值，如80%','核对性放电测试，更换容量不足单体或整组');
INSERT INTO tbl_expert (ExpertId, StationCategoryId, StationCategoryName, BaseEquipmentTypeId, BaseEquipmentTypeName, BaseAlarmId, BaseAlarmName, StandardEquipmentTypeId, StandardEquipmentTypeName, StandardAlarmId, StandardAlarmName, Reason, Solution) VALUES (4,0,'默认',1101,'蓄电池',1101898001,'单体容量预警',0,NULL,NULL,NULL,'单节容量不足设定值，如80%','核对性放电测试，更换容量不足单体');
INSERT INTO tbl_expert (ExpertId, StationCategoryId, StationCategoryName, BaseEquipmentTypeId, BaseEquipmentTypeName, BaseAlarmId, BaseAlarmName, StandardEquipmentTypeId, StandardEquipmentTypeName, StandardAlarmId, StandardAlarmName, Reason, Solution) VALUES (5,0,'默认',1101,'蓄电池',1101897001,'热失控预警',0,NULL,NULL,NULL,'电池故障，温度失控','更换温度时空单体或整组');
INSERT INTO tbl_expert (ExpertId, StationCategoryId, StationCategoryName, BaseEquipmentTypeId, BaseEquipmentTypeName, BaseAlarmId, BaseAlarmName, StandardEquipmentTypeId, StandardEquipmentTypeName, StandardAlarmId, StandardAlarmName, Reason, Solution) VALUES (6,0,'默认',1101,'蓄电池',1101896001,'单体故障预警',0,NULL,NULL,NULL,'AI引擎分析单体故障概率大于设定值','核对性测试，更换故障电池，避免安全隐患');
INSERT INTO tbl_expert (ExpertId, StationCategoryId, StationCategoryName, BaseEquipmentTypeId, BaseEquipmentTypeName, BaseAlarmId, BaseAlarmName, StandardEquipmentTypeId, StandardEquipmentTypeName, StandardAlarmId, StandardAlarmName, Reason, Solution) VALUES (7,0,'默认',1101,'蓄电池',1101894001,'内阻不均预警',0,NULL,NULL,NULL,'1、电池故障，内阻升高\n2、电池监控接线虚接，接触内阻增加。电池放电会造成局部温度升高，有燃烧风险\n3、落后电池','1、使用内阻工具核对，故障电池及时更换\n2、检查接线，紧固，避免放电时有燃烧风险\n3、落后电池，核对性测试，及时更换');
INSERT INTO tbl_expert (ExpertId, StationCategoryId, StationCategoryName, BaseEquipmentTypeId, BaseEquipmentTypeName, BaseAlarmId, BaseAlarmName, StandardEquipmentTypeId, StandardEquipmentTypeName, StandardAlarmId, StandardAlarmName, Reason, Solution) VALUES (8,0,'默认',1101,'蓄电池',1101895001,'电压不均预警',0,NULL,NULL,NULL,'落后电池','核对性测试，更换落后电池');
INSERT INTO tbl_expert (ExpertId, StationCategoryId, StationCategoryName, BaseEquipmentTypeId, BaseEquipmentTypeName, BaseAlarmId, BaseAlarmName, StandardEquipmentTypeId, StandardEquipmentTypeName, StandardAlarmId, StandardAlarmName, Reason, Solution) VALUES (9,0,'默认',1101,'蓄电池',1101893001,'温度不均预警',0,NULL,NULL,NULL,'1、电池故障，存在热失控风险\n','1、核对性测试，及时更换故障电池');
INSERT INTO tbl_expert (ExpertId, StationCategoryId, StationCategoryName, BaseEquipmentTypeId, BaseEquipmentTypeName, BaseAlarmId, BaseAlarmName, StandardEquipmentTypeId, StandardEquipmentTypeName, StandardAlarmId, StandardAlarmName, Reason, Solution) VALUES (10,0,'默认',1101,'蓄电池',1101892001,'容量不均预警',0,NULL,NULL,NULL,'落后电池','核对性测试，及时更换落后电池');

INSERT INTO tbl_homepage (PageId, PageName, FileName, PageType, Description) VALUES (1,'功能导航','Portal.aspx',1,NULL);
INSERT INTO tbl_homepage (PageId, PageName, FileName, PageType, Description) VALUES (2,'事件浏览','CommonContentPage.aspx?SWF=monitoringevents&PageName=monitoringevents2&Title=%25e4%25ba%258b%25e4%25bb%25b6%25e6%25b5%258f%25e8%25a7%2588',1,NULL);
INSERT INTO tbl_homepage (PageId, PageName, FileName, PageType, Description) VALUES (3,'设备监控','CommonContentPage.aspx?SWF=MonitoringEquipments&Tabkey=equipment_signal&PageName=monitoringequipments1&Title=%e8%ae%be%e5%a4%87%e7%9b%91%e6%8e%a7',1,NULL);
INSERT INTO tbl_homepage (PageId, PageName, FileName, PageType, Description) VALUES (4,'KPI主页','CommonContentPage.aspx?SWF=KPIPage&Tabkey=KPIPage&PageName=KPIPage&Title=KPI主页',1,NULL);
INSERT INTO tbl_homepage (PageId, PageName, FileName, PageType, Description) VALUES (5,'电子地图','CommonContentPage.aspx?SWF=MonitoringStations&Tabkey=KPIPage&PageName=MonitoringStation&Title=电子地图',1,NULL);

INSERT INTO tbl_icsequipmenttype (EquipmentTypeId, EquipmentTypeName, IsCanInputId) VALUES (1,'SiteWeb平台',1);
INSERT INTO tbl_icsequipmenttype (EquipmentTypeId, EquipmentTypeName, IsCanInputId) VALUES (2,'FSU',1);
INSERT INTO tbl_icsequipmenttype (EquipmentTypeId, EquipmentTypeName, IsCanInputId) VALUES (3,'交换机',1);
INSERT INTO tbl_icsequipmenttype (EquipmentTypeId, EquipmentTypeName, IsCanInputId) VALUES (4,'路由器',1);
INSERT INTO tbl_icsequipmenttype (EquipmentTypeId, EquipmentTypeName, IsCanInputId) VALUES (5,'防火墙',1);
INSERT INTO tbl_icsequipmenttype (EquipmentTypeId, EquipmentTypeName, IsCanInputId) VALUES (6,'存储',1);
INSERT INTO tbl_icsequipmenttype (EquipmentTypeId, EquipmentTypeName, IsCanInputId) VALUES (7,'服务器',1);
INSERT INTO tbl_icsequipmenttype (EquipmentTypeId, EquipmentTypeName, IsCanInputId) VALUES (8,'视频系统',0);
INSERT INTO tbl_icsequipmenttype (EquipmentTypeId, EquipmentTypeName, IsCanInputId) VALUES (9,'门禁系统',0);
INSERT INTO tbl_icsequipmenttype (EquipmentTypeId, EquipmentTypeName, IsCanInputId) VALUES (10,'北向接口',0);
INSERT INTO tbl_icsequipmenttype (EquipmentTypeId, EquipmentTypeName, IsCanInputId) VALUES (11,'南向接口',0);
INSERT INTO tbl_icsequipmenttype (EquipmentTypeId, EquipmentTypeName, IsCanInputId) VALUES (12,'能耗系统',0);
INSERT INTO tbl_icsequipmenttype (EquipmentTypeId, EquipmentTypeName, IsCanInputId) VALUES (13,'电池管理',0);
INSERT INTO tbl_icsequipmenttype (EquipmentTypeId, EquipmentTypeName, IsCanInputId) VALUES (14,'容量预警',0);
INSERT INTO tbl_icsequipmenttype (EquipmentTypeId, EquipmentTypeName, IsCanInputId) VALUES (15,'自动巡检',0);
INSERT INTO tbl_icsequipmenttype (EquipmentTypeId, EquipmentTypeName, IsCanInputId) VALUES (16,'其他设备',0);

INSERT INTO tbl_kpipage (KPIPageId, UserId, Name, Type, FileName, FileDir, CreateTime, ModifyTime, Description, ThumbImage) VALUES (-2,-1,'区域KPI组态',2,'StationGroupGraphic.xml','\\KPIPages\\','2014-10-20 11:46:14','2014-10-20 12:31:14','区域组态','StationGroupGraphic.jpg');
INSERT INTO tbl_kpipage (KPIPageId, UserId, Name, Type, FileName, FileDir, CreateTime, ModifyTime, Description, ThumbImage) VALUES (-1,-1,'中心KPI组态',2,'CenterGraphic.xml','\\KPIPages\\','2014-10-20 17:28:31','2014-10-20 17:28:31','中心KPI组态','CenterGraphic.jpg');
INSERT INTO tbl_kpipage (KPIPageId, UserId, Name, Type, FileName, FileDir, CreateTime, ModifyTime, Description, ThumbImage) VALUES (1,-1,'系统模板1',0,'2757429b-7878-49b6-bcf1-5ae9618c80e3.xml','\\KPIPages\\','2014-03-12 10:08:35','2014-03-12 10:08:35','描述','1.jpg');
INSERT INTO tbl_kpipage (KPIPageId, UserId, Name, Type, FileName, FileDir, CreateTime, ModifyTime, Description, ThumbImage) VALUES (2,-1,'系统模板2',0,'c3fb214a-2ead-4f2c-925a-56dd9e0d4c66.xml','\\KPIPages\\','2014-03-12 13:44:10','2014-03-12 13:44:10','描述','2.jpg');

INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (1,0,'entry',NULL,'fa fa-bar-chart',0,0,'',0,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (8,1,'s2/devicemonitor','设备监控','iconfont icon-devicemonitor',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (9,1,'s2/focussignal','关注信号','iconfont icon-real-signal',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (10,1,'s2/realtimealarm','事件浏览','iconfont icon-alarm',0,0,'prefix',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (11,1,'s2/shieldevent','屏蔽管理','fa fa-shield',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (12,1,'s2/topologysystem','网络拓扑','iconfont icon-zuzhiguanli',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (13,1,'s2/onlineuser','在线用户','iconfont icon-organization',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (14,1,'s2/versionmanagement','版本管理','iconfont icon-versionmanagement1',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (15,14,'sitewebmanagement','SiteWeb软件信息','iconfont icon-softwareinfo',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (16,14,'servermanagement','服务器信息','iconfont icon-serverinfo',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (17,14,'fsumanagement','FSU信息','iconfont icon-fsu',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (18,17,'fsupie','FSU统计图','',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (19,17,'fsutable','FSU列表','',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (20,14,'contractinfo','合同信息','iconfont icon-hetongxinxi',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (21,20,'stationcontract','局站合同','',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (22,20,'equipmentcontract','设备合同','',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (23,20,'fsucontract','FSU合同','',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (24,14,'contractmanagement','合同管理','iconfont icon-hetongguanli',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (25,24,'installcontract','安装合同','',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (26,24,'maintenancecontract','维保合同','',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (27,14,'qrcode','二维码管理','iconfont icon-erweima',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (28,27,'platformqrcode','平台二维码','',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (29,27,'fsuqrcode','FSU二维码','',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (30,5,'s2/autopatrol','自动巡检','iconfont icon-zidongxunjian',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (31,30,'ruleconfig','规则配置','iconfont icon-xunjianpeizhi',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (32,30,'groupmanagement','分组管理','iconfont icon-manage',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (33,30,'patroltask','巡检任务','iconfont icon-zidongxunjian',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (34,30,'reportmanagement','报表下载','iconfont icon-baobiaoliebiao',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (35,30,'alarminfo','预警信息','iconfont icon-yujingxinxi',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (36,5,'s2/expertadvice','专家系统','iconfont icon-expertrecommendations',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (37,36,'expertmanagement','知识库','fa fa-sitemap',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (38,36,'expertsearch','关联案例','iconfont icon-operation',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (39,5,'s2/accesscontrol','门禁管理','iconfont icon-accesscontrol',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (40,39,'doormanagement','门管理','iconfont icon-doormanagement',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (41,40,'basic','基本信息','',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (42,40,'time','准进时间','',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (43,39,'cardmanagement','卡管理','iconfont icon-cardmanagement',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (44,39,'cardauthorization','卡授权','iconfont icon-cardauthorization',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (45,39,'controlqueue','控制队列','iconfont icon-controlqueue',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (46,39,'featuremanagement','特征管理','iconfont icon-deliveryreader',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (47,39,'featureauthorization','特征授权','iconfont icon-deliveryconf',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (48,39,'datamaintenance','数据维护','iconfont icon-datamaintenance',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (49,48,'cardgroup','卡分组','',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (50,48,'timegroup','准进时间组','',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (51,6,'s2/reportmanagement/reportscan','报表浏览','iconfont icon-baobiaoliebiao',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (52,6,'s2/reportmanagement/reportconfig','报表配置','iconfont icon-baobiaoguanli',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (53,1,'s2/authoritymanagement','权限管理','iconfont icon-quanxianguanli',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (54,53,'departmentmanagement','部门管理','fa fa-sitemap',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (55,53,'employeemanagement','人员管理','iconfont icon-operation',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (56,53,'moduleauthority','模块权限','iconfont icon-ren',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (57,53,'operationauthority','操作权限','iconfont icon-cmdb-organization',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (58,53,'professionalauthority','专业权限','fa fa-key',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (59,53,'areaauthority','片区权限','fa fa-globe',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (60,53,'roleauthority','角色授权','fa fa-list-alt',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (61,1,'s2/system','系统设置','fa fa-cogs',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (63,61,'themelogo','主题及Logo','iconfont icon-themes',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (65,6,'s2/reportmanagement/reporttask','报表任务','iconfont icon-addtimespan',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (66,4,'s2/userinfo','用户信息','iconfont icon-duty',0,0,'',1,0,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (67,2,'s2/stationstate','局站状态','iconfont icon-status',0,0,'',1,0,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (68,1,'s2/reportmanagement/assetManagement','资产管理','fa fa-tasks',0,0,'',1,1,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (69,1,'s2/digitalmap','电子地图','fa fa-futbol-o',0,0,'',1,0,0,0,'');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (71,73,'bmsdashboard','电池看板','',0,0,'',1,1,0,0,'电池管理新增页面');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (72,73,'bmsviewbase','电池监控','',0,0,'',1,1,0,0,'电池管理新增页面');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (73,1,'s2/bms','电池管理','iconfont icon-dianchi',0,0,'',1,1,0,0,'电池管理新增页面');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (74,73,'assetdevice1','资产配置','',0,0,'',1,1,0,0,'电池管理新增页面');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (75,73,'preventionmaintain','预防性维护','',0,0,'',1,1,0,0,'电池管理新增页面');
INSERT INTO tbl_menuitem (MenuItemId, ParentId, Path, Title, Icon, Selected, Expanded, PathMatch, LayoutPosition, IsSystemConfig, IsExternalWeb, MenuHasNavigation, Description) VALUES (76,73,'bmssystemsetting','系统设置','',0,0,'',1,1,0,0,'电池管理新增页面');

INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (20,69,'电子地图',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (21,8,'设备监控',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (22,0,'控制命令',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (25,10,'事件浏览',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (26,11,'屏蔽管理',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (27,0,'门禁刷卡浏览',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (28,67,'局站状态',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (29,0,'设备状态管理',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (30,12,'网络拓扑',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (31,0,'门禁浏览',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (34,36,'专家系统',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (36,7,'报表',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (37,61,'系统设置',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (64,9,'关注信号',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (75,53,'权限配置',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (76,39,'门禁配置',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (77,13,'在线用户',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (78,66,'用户信息',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (79,0,'界面配置',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (80,0,'KPI主页',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (81,0,'B接口日志',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (82,0,'接入管理',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (83,14,'版本管理',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (84,30,'自动巡检',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (85,68,'资产管理',NULL);
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (86,71,'电池看板','电池管理新增页面');
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (87,72,'电池监控','电池管理新增页面');
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (88,74,'资产配置','电池管理新增页面');
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (89,75,'预防性维护','电池管理新增页面');
INSERT INTO tbl_menuitems (MenuItemsId, ParentMenuItemsId, MenuItemsName, Description) VALUES (90,76,'系统设置','电池管理新增页面');

INSERT INTO tbl_menuitemstructuremap (MenuItemStructureMapId, MenuProfileId, MenuStructureId, MenuItemId, SortIndex) VALUES (2,1,3,8,2);
INSERT INTO tbl_menuitemstructuremap (MenuItemStructureMapId, MenuProfileId, MenuStructureId, MenuItemId, SortIndex) VALUES (3,1,3,9,3);
INSERT INTO tbl_menuitemstructuremap (MenuItemStructureMapId, MenuProfileId, MenuStructureId, MenuItemId, SortIndex) VALUES (4,1,4,10,4);
INSERT INTO tbl_menuitemstructuremap (MenuItemStructureMapId, MenuProfileId, MenuStructureId, MenuItemId, SortIndex) VALUES (5,1,4,11,5);
INSERT INTO tbl_menuitemstructuremap (MenuItemStructureMapId, MenuProfileId, MenuStructureId, MenuItemId, SortIndex) VALUES (6,1,5,12,6);
INSERT INTO tbl_menuitemstructuremap (MenuItemStructureMapId, MenuProfileId, MenuStructureId, MenuItemId, SortIndex) VALUES (7,1,5,13,7);
INSERT INTO tbl_menuitemstructuremap (MenuItemStructureMapId, MenuProfileId, MenuStructureId, MenuItemId, SortIndex) VALUES (8,1,6,14,8);
INSERT INTO tbl_menuitemstructuremap (MenuItemStructureMapId, MenuProfileId, MenuStructureId, MenuItemId, SortIndex) VALUES (9,1,6,30,9);
INSERT INTO tbl_menuitemstructuremap (MenuItemStructureMapId, MenuProfileId, MenuStructureId, MenuItemId, SortIndex) VALUES (10,1,6,36,10);
INSERT INTO tbl_menuitemstructuremap (MenuItemStructureMapId, MenuProfileId, MenuStructureId, MenuItemId, SortIndex) VALUES (11,1,6,39,11);
INSERT INTO tbl_menuitemstructuremap (MenuItemStructureMapId, MenuProfileId, MenuStructureId, MenuItemId, SortIndex) VALUES (12,1,7,51,12);
INSERT INTO tbl_menuitemstructuremap (MenuItemStructureMapId, MenuProfileId, MenuStructureId, MenuItemId, SortIndex) VALUES (13,1,7,52,13);
INSERT INTO tbl_menuitemstructuremap (MenuItemStructureMapId, MenuProfileId, MenuStructureId, MenuItemId, SortIndex) VALUES (14,1,2,53,14);
INSERT INTO tbl_menuitemstructuremap (MenuItemStructureMapId, MenuProfileId, MenuStructureId, MenuItemId, SortIndex) VALUES (15,1,2,61,15);
INSERT INTO tbl_menuitemstructuremap (MenuItemStructureMapId, MenuProfileId, MenuStructureId, MenuItemId, SortIndex) VALUES (17,1,7,65,17);
INSERT INTO tbl_menuitemstructuremap (MenuItemStructureMapId, MenuProfileId, MenuStructureId, MenuItemId, SortIndex) VALUES (18,1,5,66,18);
INSERT INTO tbl_menuitemstructuremap (MenuItemStructureMapId, MenuProfileId, MenuStructureId, MenuItemId, SortIndex) VALUES (19,1,3,67,19);
INSERT INTO tbl_menuitemstructuremap (MenuItemStructureMapId, MenuProfileId, MenuStructureId, MenuItemId, SortIndex) VALUES (20,1,6,68,20);
INSERT INTO tbl_menuitemstructuremap (MenuItemStructureMapId, MenuProfileId, MenuStructureId, MenuItemId, SortIndex) VALUES (21,1,3,69,21);
INSERT INTO tbl_menuitemstructuremap (MenuItemStructureMapId, MenuProfileId, MenuStructureId, MenuItemId, SortIndex) VALUES (22,1,6,73,22);

INSERT INTO tbl_menuprofileinfo (MenuProfileId, Name, Checked, Description) VALUES (1,'SiteWeb2',1,NULL);

INSERT INTO tbl_menus (MenusId, MenusName, Description) VALUES (-1,'所有界面权限组','拥有全部界面权限');

INSERT INTO tbl_menustructureinfo (MenuStructureId, MenuProfileId, ParentId, Title, Icon, Selected, Expanded, Hidden, SortIndex, IsSystem, Description) VALUES (2,1,0,'后台配置','fa fa-cog',0,0,1,9999,1,NULL);
INSERT INTO tbl_menustructureinfo (MenuStructureId, MenuProfileId, ParentId, Title, Icon, Selected, Expanded, Hidden, SortIndex, IsSystem, Description) VALUES (3,1,0,'实时监控',NULL,0,0,0,1,1,NULL);
INSERT INTO tbl_menustructureinfo (MenuStructureId, MenuProfileId, ParentId, Title, Icon, Selected, Expanded, Hidden, SortIndex, IsSystem, Description) VALUES (4,1,0,'事件管理',NULL,0,0,0,2,1,NULL);
INSERT INTO tbl_menustructureinfo (MenuStructureId, MenuProfileId, ParentId, Title, Icon, Selected, Expanded, Hidden, SortIndex, IsSystem, Description) VALUES (5,1,0,'系统管理',NULL,0,0,0,3,1,NULL);
INSERT INTO tbl_menustructureinfo (MenuStructureId, MenuProfileId, ParentId, Title, Icon, Selected, Expanded, Hidden, SortIndex, IsSystem, Description) VALUES (6,1,0,'业务管理',NULL,0,0,0,4,1,NULL);
INSERT INTO tbl_menustructureinfo (MenuStructureId, MenuProfileId, ParentId, Title, Icon, Selected, Expanded, Hidden, SortIndex, IsSystem, Description) VALUES (7,1,0,'报表管理',NULL,0,0,0,5,1,NULL);

INSERT INTO tbl_newsystemconfigs (SystemConfigId, SystemConfigKey, SystemConfigValue, Description) VALUES (1,'logo.img','logo.png','Logo图片');
INSERT INTO tbl_newsystemconfigs (SystemConfigId, SystemConfigKey, SystemConfigValue, Description) VALUES (2,'logo.text','Vertiv SiteWeb动力环境监控系统V2.0','Logo文本');
INSERT INTO tbl_newsystemconfigs (SystemConfigId, SystemConfigKey, SystemConfigValue, Description) VALUES (3,'custom_config_modules','dev','模块配置方案');
INSERT INTO tbl_newsystemconfigs (SystemConfigId, SystemConfigKey, SystemConfigValue, Description) VALUES (4,'theme','classic','经典');
INSERT INTO tbl_newsystemconfigs (SystemConfigId, SystemConfigKey, SystemConfigValue, Description) VALUES (5,'menu.collapsible','true','左侧菜单可收缩');
INSERT INTO tbl_newsystemconfigs (SystemConfigId, SystemConfigKey, SystemConfigValue, Description) VALUES (6,'alarmstatistics.show','true','是否显示全局告警统计');
INSERT INTO tbl_newsystemconfigs (SystemConfigId, SystemConfigKey, SystemConfigValue, Description) VALUES (7,'system.tag','s2','系统标识');
INSERT INTO tbl_newsystemconfigs (SystemConfigId, SystemConfigKey, SystemConfigValue, Description) VALUES (8,'batchAlarmWindow.alarm.severity','1,2,3,4','全局告警显示告警等级');
INSERT INTO tbl_newsystemconfigs (SystemConfigId, SystemConfigKey, SystemConfigValue, Description) VALUES (9,'map.online','false','电子地图默认离线');
INSERT INTO tbl_newsystemconfigs (SystemConfigId, SystemConfigKey, SystemConfigValue, Description) VALUES (10,'loading.bar.show','false','请求动画效果');
INSERT INTO tbl_newsystemconfigs (SystemConfigId, SystemConfigKey, SystemConfigValue, Description) VALUES (11,'website.tab.text','Vertiv SiteWeb动力环境监控系统V2.0','选项卡文字');
INSERT INTO tbl_newsystemconfigs (SystemConfigId, SystemConfigKey, SystemConfigValue, Description) VALUES (12,'website.tab.icon','favicon.ico','选项卡图标');
INSERT INTO tbl_newsystemconfigs (SystemConfigId, SystemConfigKey, SystemConfigValue, Description) VALUES (13,'s2.alarm.showAdvice','true','告警页面显示专家建议按钮');

INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (1,NULL,'事件确认',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (2,NULL,'事件强制结束',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (3,NULL,'录入事件注释',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (4,NULL,'锁定事件页面',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (5,NULL,'输出事件',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (7,NULL,'关闭告警发声',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (8,NULL,'设备控制',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (9,NULL,'失败控制命令确认',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (10,NULL,'局站屏蔽',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (11,NULL,'设备屏蔽',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (12,NULL,'事件屏蔽',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (13,NULL,'查看报表',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (14,NULL,'界面个性设置',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (15,NULL,'动态配置',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (16,NULL,'维护专家建议',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (17,NULL,'查看专家建议',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (18,NULL,'所有过滤条件维护',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (19,NULL,'个人过滤条件维护',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (21,NULL,'模板管理',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (22,NULL,'端局管理',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (23,NULL,'采集管理',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (24,NULL,'服务器管理',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (25,NULL,'用户管理',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (26,NULL,'事件通知管理',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (27,NULL,'门禁管理',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (29,NULL,'执行备份',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (30,NULL,'执行复制',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (31,NULL,'执行恢复',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (42,NULL,'服务器Config配置',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (47,NULL,'登录',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (48,NULL,'退出',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (49,NULL,'踢出',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (50,NULL,'发送控制命令',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (51,NULL,'取消控制命令',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (52,NULL,'确认控制命令',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (53,NULL,'开关门命令',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (54,NULL,'专家建议新增',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (55,NULL,'专家建议修改',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (56,NULL,'专家建议删除',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (57,NULL,'过滤条件新增',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (58,NULL,'过滤条件删除',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (59,NULL,'过滤条件修改',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (60,NULL,'配置更改',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (65,NULL,'局站工程状态设置',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (66,NULL,'告警测试',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (67,NULL,'配置工具登录',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (80,NULL,'新增用户',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (81,NULL,'编辑账号',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (82,NULL,'部门管理',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (83,NULL,'人员管理',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (84,NULL,'模块权限',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (85,NULL,'操作权限',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (86,NULL,'专业权限',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (87,NULL,'片区权限',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (88,NULL,'角色授权',NULL,NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (106,NULL,'屏蔽管理','屏蔽管理',NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (109,NULL,'告警模版修改','告警模版修改',NULL);
INSERT INTO tbl_operation (OperationId, OperationCategory, OperationName, Description, MenusItemId) VALUES (110,NULL,'告警模版删除','告警模版删除',NULL);

INSERT INTO tbl_operationgroup (GroupId, GroupName, Description) VALUES (-1,'所有操作权限组','拥有所有操作权限');

INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (1,'ArchiveLogMiner','ScheduleNumber');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (2,'TBL_Area','AreaId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (3,'BatchReportParam','BatchRptParamId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (4,'TBL_Card','CardId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (7,'TBL_Equipment','EquipmentId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (10,'TBL_House','HouseId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (11,'TSL_MonitorUnit','MonitorUnitId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (12,'TSL_Port','PortId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (14,'TSL_SamplerUnit','SamplerUnitId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (16,'TBL_Station','StationId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (17,'TBL_StationStructure','StructureId,');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (18,'TBL_WorkStation','WorkStationId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (22,'TBL_Department','DepartmentId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (23,'TBL_Door','ManualDoorId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (24,'TBL_DoorController','DoorControlId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (25,'TBL_Employee','EmployeeId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (26,'TBL_Control','ControlId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (27,'TBL_Event','EventId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (28,'TBL_Signal','SignalId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (29,'TBL_EquipmentTemplate','EquipmentTemplateId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (31,'TBL_Experience','ExperienceId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (32,'TBL_ExperienceWord','ExperienceKeyId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (35,'TBL_MenuItems','MenuItemsId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (36,'TBL_Menus','MenusId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (37,'NotifyServer','NotifyServerId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (38,'NotifyReceiver','NotifyReceiverId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (39,'EventFilter','EventFilterId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (40,'EventFilterCondition','EventFilterConditionId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (41,'TBL_Operation','OperationId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (42,'TBL_OperationGroup','GroupId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (43,'ReportTask','TaskId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (44,'ReportInstance','ReportId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (45,'TSL_Sampler','SamplerId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (46,'TBL_SpecialtyGroup','SpecialtyGroupId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (52,'TBL_TimeGroup','TimeGroupId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (53,'TBL_TimeGroupSpan','TimeSpanId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (56,'TBL_UserRole','RoleId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (60,'TBL_HistorySelection','HistorySelectionId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (61,'TBL_DataItem','EntryItemId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (64,'TBL_CustomInfo','CustomInfoId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (65,'NotificationLog','NotificationLogId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (69,'NotifyMode','NotifyModeId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (70,'TBL_EventLogAction','LogActioinId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (81,'TBL_DoorGroup','DoorGroupId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (82,'TBL_Building','BuildingId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (83,'TBL_Floor','FloorId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (84,'TBL_Room','RoomId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (85,'MDC','MDCId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (86,'CorePointSECMap','CorePointSECMap');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (100,'tbl_doorarea','areaid');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (208,'TBL_FingerReaderMap','ReaderId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (216,'TBL_FaceData','FaceId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (217,'TBL_Fingerprint','FingerprintId');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (218,'TBL_GraphicPage','Id');
INSERT INTO tbl_primarykeyidentity (TableId, TableName, Description) VALUES (219,'resourcestructure','ResourceStructureId');

INSERT INTO tbl_specialtygroup (SpecialtyGroupId, SpecialtyGroupName, Description) VALUES (-1,'所有专业权限组','拥有所有专业权限');

INSERT INTO tbl_sysconfig (ConfigKey, ConfigValue) VALUES ('BackupHistoryDataDir',NULL);
INSERT INTO tbl_sysconfig (ConfigKey, ConfigValue) VALUES ('BackupConfigDir',NULL);
INSERT INTO tbl_sysconfig (ConfigKey, ConfigValue) VALUES ('EnableConfigSyn','false');
INSERT INTO tbl_sysconfig (ConfigKey, ConfigValue) VALUES ('MUSyncRecordEnable','true');
INSERT INTO tbl_sysconfig (ConfigKey, ConfigValue) VALUES ('DSEditServerName',NULL);
INSERT INTO tbl_sysconfig (ConfigKey, ConfigValue) VALUES ('BDicVersion','1.0.0');

INSERT INTO tbl_timegroup (TimeGroupId, TimeGroupCategory, TimeGroupName, TimeGroupType, TimeGroupException, StartTime, EndTime, LastUpdateDate) VALUES (99999999,1,'准进时间组1',10,0,'2000-01-01 00:00:00','9999-01-01 00:00:00','2012-05-30 00:00:00');
INSERT INTO tbl_timegroup (TimeGroupId, TimeGroupCategory, TimeGroupName, TimeGroupType, TimeGroupException, StartTime, EndTime, LastUpdateDate) VALUES (100000000,1,'准进时间组2',10,0,'2000-01-01 00:00:00','9999-01-01 00:00:00','2025-04-23 00:00:00');
INSERT INTO tbl_timegroup (TimeGroupId, TimeGroupCategory, TimeGroupName, TimeGroupType, TimeGroupException, StartTime, EndTime, LastUpdateDate) VALUES (100000001,1,'准进时间组3',10,0,'2000-01-01 00:00:00','9999-01-01 00:00:00','2025-04-23 00:00:00');
INSERT INTO tbl_timegroup (TimeGroupId, TimeGroupCategory, TimeGroupName, TimeGroupType, TimeGroupException, StartTime, EndTime, LastUpdateDate) VALUES (100000002,1,'准进时间组4',10,0,'2000-01-01 00:00:00','9999-01-01 00:00:00','2025-04-23 00:00:00');

INSERT INTO tbl_timegroupset (TimeGroupSetId, TimeGroupSetName) VALUES (10,'准进时间组1');
INSERT INTO tbl_timegroupset (TimeGroupSetId, TimeGroupSetName) VALUES (11,'准进时间组2');
INSERT INTO tbl_timegroupset (TimeGroupSetId, TimeGroupSetName) VALUES (12,'准进时间组3');
INSERT INTO tbl_timegroupset (TimeGroupSetId, TimeGroupSetName) VALUES (13,'准进时间组4');
INSERT INTO tbl_timegroupset (TimeGroupSetId, TimeGroupSetName) VALUES (14,'准进时间组5');

INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990001,99999999,NULL,NULL,1,'00:00-23:5900:00-00:0000:00-00:00','2012-05-30 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990002,99999999,NULL,NULL,2,'00:00-23:5900:00-00:0000:00-00:00','2012-05-30 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990003,99999999,NULL,NULL,3,'00:00-23:5900:00-00:0000:00-00:00','2012-05-30 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990004,99999999,NULL,NULL,4,'00:00-23:5900:00-00:0000:00-00:00','2012-05-30 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990005,99999999,NULL,NULL,5,'00:00-23:5900:00-00:0000:00-00:00','2012-05-30 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990006,99999999,NULL,NULL,6,'00:00-23:5900:00-00:0000:00-00:00','2012-05-30 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990007,99999999,NULL,NULL,7,'00:00-23:5900:00-00:0000:00-00:00','2012-05-30 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990008,100000000,NULL,NULL,1,'00:00-23:5900:00-00:0000:00-00:00','2025-04-23 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990009,100000000,NULL,NULL,2,'00:00-23:5900:00-00:0000:00-00:00','2025-04-23 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990010,100000000,NULL,NULL,3,'00:00-23:5900:00-00:0000:00-00:00','2025-04-23 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990011,100000000,NULL,NULL,4,'00:00-23:5900:00-00:0000:00-00:00','2025-04-23 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990012,100000000,NULL,NULL,5,'00:00-23:5900:00-00:0000:00-00:00','2025-04-23 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990013,100000000,NULL,NULL,6,'00:00-23:5900:00-00:0000:00-00:00','2025-04-23 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990014,100000000,NULL,NULL,7,'00:00-23:5900:00-00:0000:00-00:00','2025-04-23 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990015,100000001,NULL,NULL,1,'00:00-23:5900:00-00:0000:00-00:00','2025-04-23 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990016,100000001,NULL,NULL,2,'00:00-23:5900:00-00:0000:00-00:00','2025-04-23 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990017,100000001,NULL,NULL,3,'00:00-23:5900:00-00:0000:00-00:00','2025-04-23 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990018,100000001,NULL,NULL,4,'00:00-23:5900:00-00:0000:00-00:00','2025-04-23 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990019,100000001,NULL,NULL,5,'00:00-23:5900:00-00:0000:00-00:00','2025-04-23 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990020,100000001,NULL,NULL,6,'00:00-23:5900:00-00:0000:00-00:00','2025-04-23 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990021,100000001,NULL,NULL,7,'00:00-23:5900:00-00:0000:00-00:00','2025-04-23 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990022,100000002,NULL,NULL,1,'00:00-23:5900:00-00:0000:00-00:00','2025-04-23 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990023,100000002,NULL,NULL,2,'00:00-23:5900:00-00:0000:00-00:00','2025-04-23 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990024,100000002,NULL,NULL,3,'00:00-23:5900:00-00:0000:00-00:00','2025-04-23 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990025,100000002,NULL,NULL,4,'00:00-23:5900:00-00:0000:00-00:00','2025-04-23 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990026,100000002,NULL,NULL,5,'00:00-23:5900:00-00:0000:00-00:00','2025-04-23 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990027,100000002,NULL,NULL,6,'00:00-23:5900:00-00:0000:00-00:00','2025-04-23 00:00:00');
INSERT INTO tbl_timegroupspan (TimeSpanId, TimeGroupId, StartTime, EndTime, Week, TimeSpanChar, LastUpdateDate) VALUES (99990028,100000002,NULL,NULL,7,'00:00-23:5900:00-00:0000:00-00:00','2025-04-23 00:00:00');

INSERT INTO tbl_userrole (RoleId, RoleName, Description) VALUES (-1,'系统管理员','拥有系统所有权限');

INSERT INTO tbl_userrolemap (UserId, RoleId) VALUES (-7,-1);
INSERT INTO tbl_userrolemap (UserId, RoleId) VALUES (-6,-1);
INSERT INTO tbl_userrolemap (UserId, RoleId) VALUES (-5,-1);
INSERT INTO tbl_userrolemap (UserId, RoleId) VALUES (-4,-1);
INSERT INTO tbl_userrolemap (UserId, RoleId) VALUES (-3,-1);
INSERT INTO tbl_userrolemap (UserId, RoleId) VALUES (-2,-1);
INSERT INTO tbl_userrolemap (UserId, RoleId) VALUES (-1,-1);

INSERT INTO tbl_userroleright (RoleId, OperationId, OperationType) VALUES (-1,-1,1);
INSERT INTO tbl_userroleright (RoleId, OperationId, OperationType) VALUES (-1,-1,2);
INSERT INTO tbl_userroleright (RoleId, OperationId, OperationType) VALUES (-1,-1,3);
INSERT INTO tbl_userroleright (RoleId, OperationId, OperationType) VALUES (-1,-1,4);
INSERT INTO tbl_userroleright (RoleId, OperationId, OperationType) VALUES (-1,-1,5);

INSERT INTO tsl_monitorunitconfig (Id, AppConfigId, SiteWebTimeOut, RetryTimes, HeartBeat, EquipmentTimeOut, PortInterruptCount, PortInitializeInternal, MaxPortInitializeTimes, PortQueryTimeOut, DataSaveTimes, HistorySignalSavedTimes, HistoryBatterySavedTimes, HistoryEventSavedTimes, CardRecordSavedCount, ControlLog, IpAddressDS) VALUES (1,1,10,3,30,6,3,30,10,5,500,500,500,500,500,1,'udp://127.0.0.1:9000');



