INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (1,'0 * * * * ? *','每分钟执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (2,'0 0/2 * * * ? *','每2分钟执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (3,'0 0/3 * * * ? *','每3分钟执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (4,'0 0/5 * * * ? *','每5分钟执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (5,'0 0/10 * * * ? *','每10分钟执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (6,'0 0/15 * * * ? *','每15分钟执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (7,'0 0/20 * * * ? *','每20分钟执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (8,'0 0/30 * * * ? *','每30分钟执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (9,'0 0 * * * ? *','每个小时执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (10,'0 0 0/2 * * ? *','每2个小时执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (11,'0 0 0/3 * * ? *','每3个小时执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (12,'0 0 0/4 * * ? *','每4个小时执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (13,'0 0 0/6 * * ? *','每6个小时执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (14,'0 0 0/8 * * ? *','每8个小时执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (15,'0 0 0/12 * * ? *','每12个小时执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (16,'0 0 0 * * ? *','每天0点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (17,'0 0 1 * * ? *','每天1点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (18,'0 0 2 * * ? *','每天2点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (19,'0 0 3 * * ? *','每天3点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (20,'0 0 4 * * ? *','每天4点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (21,'0 0 5 * * ? *','每天5点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (22,'0 0 6 * * ? *','每天6点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (23,'0 0 7 * * ? *','每天7点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (24,'0 0 8 * * ? *','每天8点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (25,'0 0 9 * * ? *','每天9点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (26,'0 0 10 * * ? *','每天10点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (27,'0 0 11 * * ? *','每天11点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (28,'0 0 12 * * ? *','每天12点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (29,'0 0 13 * * ? *','每天13点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (30,'0 0 14 * * ? *','每天14点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (31,'0 0 15 * * ? *','每天15点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (32,'0 0 16 * * ? *','每天16点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (33,'0 0 17 * * ? *','每天17点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (34,'0 0 18 * * ? *','每天18点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (35,'0 0 19 * * ? *','每天19点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (36,'0 0 20 * * ? *','每天20点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (37,'0 0 21 * * ? *','每天21点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (38,'0 0 22 * * ? *','每天22点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (39,'0 0 23 * * ? *','每天23点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (40,'0 0 0 1 * ? *','每月第一天0点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (41,'0 0 1 1 * ? *','每月第一天1点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (42,'0 0 2 1 * ? *','每月第一天2点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (43,'0 0 3 1 * ? *','每月第一天3点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (44,'0 0 4 1 * ? *','每月第一天4点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (45,'0 0 5 1 * ? *','每月第一天5点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (46,'0 0 6 1 * ? *','每月第一天6点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (47,'0 0 7 1 * ? *','每月第一天7点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (48,'0 0 8 1 * ? *','每月第一天8点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (49,'0 0 9 1 * ? *','每月第一天9点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (50,'0 0 10 1 * ? *','每月第一天10点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (51,'0 0 11 1 * ? *','每月第一天11点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (52,'0 0 12 1 * ? *','每月第一天12点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (53,'0 0 13 1 * ? *','每月第一天13点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (54,'0 0 14 1 * ? *','每月第一天14点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (55,'0 0 15 1 * ? *','每月第一天15点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (56,'0 0 16 1 * ? *','每月第一天16点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (57,'0 0 17 1 * ? *','每月第一天17点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (58,'0 0 18 1 * ? *','每月第一天18点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (59,'0 0 19 1 * ? *','每月第一天19点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (60,'0 0 20 1 * ? *','每月第一天20点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (61,'0 0 21 1 * ? *','每月第一天21点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (62,'0 0 22 1 * ? *','每月第一天22点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (63,'0 0 23 1 * ? *','每月第一天23点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (64,'0 0 0 L * ? *','每月最后一天0点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (65,'0 0 1 L * ? *','每月最后一天1点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (66,'0 0 2 L * ? *','每月最后一天2点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (67,'0 0 3 L * ? *','每月最后一天3点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (68,'0 0 4 L * ? *','每月最后一天4点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (69,'0 0 5 L * ? *','每月最后一天5点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (70,'0 0 6 L * ? *','每月最后一天6点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (71,'0 0 7 L * ? *','每月最后一天7点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (72,'0 0 8 L * ? *','每月最后一天8点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (73,'0 0 9 L * ? *','每月最后一天9点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (74,'0 0 10 L * ? *','每月最后一天10点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (75,'0 0 11 L * ? *','每月最后一天11点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (76,'0 0 12 L * ? *','每月最后一天12点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (77,'0 0 13 L * ? *','每月最后一天13点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (78,'0 0 14 L * ? *','每月最后一天14点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (79,'0 0 15 L * ? *','每月最后一天15点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (80,'0 0 16 L * ? *','每月最后一天16点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (81,'0 0 17 L * ? *','每月最后一天17点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (82,'0 0 18 L * ? *','每月最后一天18点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (83,'0 0 19 L * ? *','每月最后一天19点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (84,'0 0 20 L * ? *','每月最后一天20点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (85,'0 0 21 L * ? *','每月最后一天21点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (86,'0 0 22 L * ? *','每月最后一天22点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (87,'0 0 23 L * ? *','每月最后一天23点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (88,'0 0 0 ? * 2 *','每周一0点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (89,'0 0 3 ? * 2 *','每周一3点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (90,'0 0 6 ? * 2 *','每周一6点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (91,'0 0 8 ? * 2 *','每周一8点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (92,'0 0 10 ? * 2 *','每周一10点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (93,'0 0 12 ? * 2 *','每周一12点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (94,'0 0 15 ? * 2 *','每周一15点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (95,'0 0 18 ? * 2 *','每周一18点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (96,'0 0 20 ? * 2 *','每周一20点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (97,'0 0 22 ? * 2 *','每周一22点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (98,'0 0 0 ? * 1 *','每周日0点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (99,'0 0 3 ? * 1 *','每周日3点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (100,'0 0 6 ? * 1 *','每周日6点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (101,'0 0 8 ? * 1 *','每周日8点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (102,'0 0 10 ? * 1 *','每周日10点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (103,'0 0 12 ? * 1 *','每周日12点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (104,'0 0 15 ? * 1 *','每周日15点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (105,'0 0 18 ? * 1 *','每周日18点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (106,'0 0 20 ? * 1 *','每周日20点执行一次');
INSERT INTO tbl_h5reportcronexpression (CronId, CronExpression, Meaning) VALUES (107,'0 0 22 ? * 1 *','每周日22点执行一次');

INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportStationStatistics','CenterId,GroupId,StationCategory,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistorySignal_OP','StartTime,EndTime,StationId,EquipmentId,EquipmentName,SignalId,SignalName,SignalValueFrom,SignalValueTo,DescOrAsc,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistorySignal_2','DescOrAsc，ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistorySignal_1','StartTime,EndTime,EquipmentCategory,EquipmentId,EquipmentName,SignalCategory,SignalId,SignalName,ThresholdType,SignalProperty,ChannelType,SignalValueFrom,SignalValueTo,DescOrAsc,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistorySignal','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,EquipmentName,SignalCategory,SignalId,SignalName,ThresholdType,SignalProperty,ChannelType,SignalValueFrom,SignalValueTo,DescOrAsc,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportUserLoginRecord','StartTime,EndTime,CenterId,UserId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportWorkStationEvent','StartTime,EndTime,CenterId,WorkStationId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_StationEventStatistic','StartTime,EndTime');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_StationRecord','CenterId,GroupId,StationCategory,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_StdEquipmentEventStatistic','StartTime,EndTime');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_StdEventStatistic','StartTime,EndTime');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportEquipment','CenterId,GroupId,StationCategory,StationId,EquipmentCategory,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistoryEvent_2','ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistoryEvent_1','StartTime,EndTime,EquipmentCategory,EquipmentId,EventId,EventName,EventCategory,EventSeverity,NotifyResult,Intervall,IsConfirm,IsEnd,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistoryEvent','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,EventId,EventName,EventCategory,EventSeverity,NotifyResult,Intervall,IsConfirm,IsEnd,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistoryEventStd_2','ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistoryEventStd_1','StartTime,EndTime,StandardAlarmId,NotifyResult,Intervall,IsConfirm,IsEnd,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistoryEventStd','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,StandardAlarmId,NotifyResult,Intervall,IsConfirm,IsEnd,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportEventMask_1','StartTime,EndTime,UserIds,CenterId,GroupId,StationCategoryId,StationId,EquipmentCategory,RowCount,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportEventMask_2','ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportEventMask','StartTime,EndTime,UserIds,CenterId,GroupId,StationCategoryId,Station,EquipmentCategory,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistoryBattery_2','DescOrAsc,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistoryBattery_1','StartTime,EndTime,EquipmentCategory,EquipmentId,EquipmentName,SignalCategory,SignalId,SignalName,ThresholdType,SignalProperty,ChannelType,SignalValueFrom,SignalValueTo,DescOrAsc,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistoryBattery','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,EquipmentName,SignalCategory,SignalId,SignalName,ThresholdType,SignalProperty,ChannelType,SignalValueFrom,SignalValueTo,DescOrAsc,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistoryCommand_3','RowCount,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistoryCommand_2','StartTime,EndTime,EquipmentCategory,EquipmentId,CommandType,ControlId,ControlName,ControlCategory,ControlSeverity,ControlExecuterId,RowCount,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistoryCommand_1','CenterId,GroupId,StationCategory,StationId,QueryUserId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistoryCommand','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,CommandType,ControlId,ControlName,ControlCategory,ControlSeverity,ControlExecuterId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistorySwapCard','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,DoorId,SwapCardOwnerId,ReturnCount,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportMonitoringUnit','DataServerId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_OperationDetailRecord','StartTime,EndTime,UserIds,ObjectName,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_AlarmRecord_SS','StartTime,EndTime,CenterId,StationCategoryId,StationId,EquipmentSubCategoryId,EventSeverity,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_GetNotificationRecord','UserId,NotifyCategory,StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_PowerConsumptionStat','RecordMonth,CenterId,GroupId,StationCategory,StationId,');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_PowerConsumptionStat2','RecordMonth,CenterId,GroupId,StationCategory,StationId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_FetchRPTPowerOffEquipment','EquipmentBaseTypeIds,DestTableName,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_GroupPowerOffMonthSta','RecordMonth,CenterId,GroupId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportUserOperation','DepartmentId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_EngineRunStatistics','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_TemperatureHighAlarmRecord','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,TableName,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_TemperatureHighAlarmStat','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_PowerOffDisconnectRecord','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_StationEngineeringOrMask','CenterId,GroupId,StationCategory,StationId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_StationMonitoredStat','CenterId,GroupId,StationCategory,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_StationMonitoringRateRecord','RecordMonth,CenterId,GroupId,StationCategory,StationId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportEquipmentMask','StartTime,EndTime,UserIds,CenterId,GroupId,StationCategoryId,Station,EquipmentCategory,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_UserSignalPrivilegeNew','CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,EquipmentName,SignalCategory,SignalProperty,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistorySignal_2','DescOrAsc');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistorySignal_1New','StartTime,EndTime,EquipmentCategory,EquipmentId,EquipmentName,SignalCategory,SignalId,SignalName,ThresholdType,SignalProperty,ChannelType,SignalValueFrom,SignalValueTo,DescOrAsc,BaseTypeId,ReturnCount,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistorySignalNew','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,EquipmentName,SignalCategory,SignalId,SignalName,ThresholdType,SignalProperty,ChannelType,SignalValueFrom,SignalValueTo,DescOrAsc,BaseTypeId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistorySignalLine','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,EquipmentName,SignalCategory,SignalId,SignalName,ThresholdType,SignalProperty,ChannelType,SignalValueFrom,SignalValueTo,DescOrAsc,BaseTypeId,');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistoryEventSummary','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EventSeverity,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistoryEventEquipmentTypeSummary','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EventSeverity,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_QueryRealSignalConfig','CenterId,GroupId,StationState,StationCategory,StationId,StationId2,StationId3,EquipmentCategory,EquipmentId,EquipmentId2,EquipmentId3,SignalProperty,SignalCategory,SignalId,SignalId2,SignalId3,SignalName,BaseTypeId,BaseTypeId2,BaseTypeId3,BaseTypeName,BaseTypeEntryId,StandardTypeEntryId,OnlyBaseType,OnlyStandardType,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_PasswordAccountRecord','ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistoryBaseTypeEvent','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,EventType,EventSeverity,Interval,IsConfirm,IsEnd,IsCancel,Reversal,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_StationDisconnectSta','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_StationGradeAlarmSta','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_EquipTypeAlarmStatByGrade','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_StationPowerOffRecord','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,DestTableName,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_StationPowerOffSta','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_StationDisconnectRecord','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,DestTableName,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ACEventStaByFactory','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,Vendor,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistorySignal_OP','StartTime,EndTime,StationId,EquipmentId,EquipmentName,SignalId,SignalName,SignalValueFrom,SignalValueTo,DescOrAsc,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistorySignal_2','DescOrAsc,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistorySignal_1','StartTime,EndTime,EquipmentCategory,EquipmentId,EquipmentName,SignalCategory,SignalId,SignalName,ThresholdType,SignalProperty,ChannelType,SignalValueFrom,SignalValueTo,DescOrAsc,BaseTypeId,ReturnCount,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistorySignal','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,EquipmentName,SignalCategory,SignalId,SignalName,ThresholdType,SignalProperty,ChannelType,SignalValueFrom,SignalValueTo,DescOrAsc,BaseTypeId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_HistorySwapCardRecord','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,DoorId,SwapCardOwnerId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_HistorySwapCardStat','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_EquipAlarmStatByGrade','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_SystemAlarmRecord','StartTime,EndTime,CenterId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_SignalAlarmStatByGrade','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,SignalCategory,SignalId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_BatteryEventStaByFactory','StartTime,EndTime,CenterId,GroupId,StationCategory,Vendor,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_OilEventStaByFactory','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,Vendor,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_EngineRunRecord','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_RectifierEventStaByFactory','StartTime,EndTime,CenterId,GroupId,StationCategory,Vendor,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_UserBaseEquipmentPrivilege','CenterId,GroupId,StationCategory,StationId,EquipmentCategory,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_UserEventBaseDicPrivilege','EquipmentCategory,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_UserSignalPrivilegePro','CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,EquipmentName,SignalCategory,SignalProperty,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_EquipTypeAlarmStatGradeSC','StartTime,EndTime,CenterId,StationCategory,StationId,EquipmentCategory,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_StationGradeAlarmSta_SC','StartTime,EndTime,CenterId,StationCategory,StationId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_StationPowerOffSta_SC','StartTime,EndTime,CenterId,StationCategory,StationId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ACEventStaByFactory_SC','StartTime,EndTime,CenterId,StationCategory,StationId,EquipmentCategory,Vendor,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_BaseAlarmRecord_SC','StartTime,EndTime,CenterId,StationCategory,StationId,EquipmentCategory,EventSeverity,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_UserBaseEquipmentPrivilegeLike','CenterId,GroupId,StationCategory,StationId,StationNameLike,EquipmentCategory,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_UserEventPrivilegeLike','CenterId,GroupId,StationCategory,StationId,StationNameLike,EquipmentCategory,EquipmentId,EquipmentNameLike,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_UserEquipmentPrivilegeLike','CenterId,GroupId,StationCategory,StationId,StationNameLike,EquipmentCategory,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_UserSignalPrivilegeLike','CenterId,GroupId,StationCategory,StationId,StationNameLike,EquipmentCategory,EquipmentId,EquipmentNameLike,SignalCategory,SignalProperty,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_HistorySignalDataRecordResult','DescOrAsc');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_HistorySignalDataRecordCalculation','StartTime,EndTime,StationNameLike,EquipmentCategory,EquipmentId,EquipmentNameLike,SignalCategory,SignalId,SignalNameLike,ThresholdType,SignalProperty,ReturnCount,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_HistorySignalDataRecordMain','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,StationNameLike,EquipmentCategory,EquipmentId,EquipmentNameLike,SignalCategory,SignalId,SignalNameLike,ThresholdType,SignalProperty,DescOrAsc,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistorySignalLineMain','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,StationNameLike,EquipmentCategory,EquipmentId,EquipmentNameLike,SignalCategory,SignalId,SignalNameLike');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_UserBaseEquipmentPrivilegeLike','CenterId,GroupId,StationCategory,StationId,StationNameLike,EquipmentCategory,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_UserEventPrivilegeLike','CenterId,GroupId,StationCategory,StationId,StationNameLike,EquipmentCategory,EquipmentId,EquipmentNameLike,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_HistoryEventDataRecordMain','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,StationNameLike,EquipmentCategory,EquipmentId,EquipmentNameLike,EventId,EventNameLike,EventSeverity,Interval,IsConfirm,IsEnd,IsCancel,Reversal,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistorySignalNew','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,EquipmentName,SignalCategory,SignalId,SignalName,ThresholdType,SignalProperty,ChannelType,SignalValueFrom,SignalValueTo,DescOrAsc,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistorySignal_1New','StartTime,EndTime,EquipmentCategory,EquipmentId,EquipmentName,SignalCategory,SignalId,SignalName,ThresholdType,SignalProperty,ChannelType,SignalValueFrom,SignalValueTo,DescOrAsc,BaseTypeId,ReturnCount,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_GetReportList','ReprotGroupId');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_GetReportStrucuture','ReprotId');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_GetReportParameters','ReprotId');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_GetParameterStructure','ParameterId');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_FetchGroupTree','CenterId,StructureGroupId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_GetEventSeverityOfReport','');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_GetEquipmentTypeOfReport','');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_GetSignalCategoryOfReport','');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_UserSignalPrivilegeLikeOfReport','CenterId,GroupId,StationCategory,StationId,StationNameLike,EquipmentCategory,EquipmentId,EquipmentNameLike,SignalCategory,SignalProperty,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_GetReportChart','ReportId');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5GetReportTimingTask','');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5GetReportTask','TaskId');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5GetReportSCConn','');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5GetReportOperationUser','');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5GetReportOperationObj','');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5UpdateReportGroup','ReportId,ReportGroup,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5DeleteReport','ReportId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistorySignalAvg','StartTime,EndTime,EquipmentId,BaseTypeId,DescOrAsc,ReturnCount,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistorySignalEx','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentBaseType,EquipmentId,EquipmentName,SignalCategory,SignalId,SignalName,ThresholdType,SignalProperty,BaseTypeId,BaseTypeName,ChannelType,SignalValueFrom,SignalValueTo,DescOrAsc,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistorySignalEx_1','StartTime,EndTime,EquipmentCategory,EquipmentBaseTypeId	,EquipmentId,EquipmentName,SignalCategory,SignalId,SignalName,ThresholdType,SignalProperty,BaseTypeId,BaseTypeName,ChannelType,SignalValueFrom,SignalValueTo,DescOrAsc,ReturnCount,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistorySignalEx_2','DescOrAsc,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ReportHistorySwapCardEx','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,DoorId,SwapCardOwnerId,DepartmentId,ReturnCount,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_EquipmentEventStatistic','StartTime,EndTime');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_EventBillBoard','StartTime,EndTime');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_FetchGroup','CenterId,StructureGroupId');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_GetStationGroup','');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_FetchSSCenter','CenterId,OutputColumnsType');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_FetchStandardAlarmName','EquipmentLogicClass');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_FetchStationCategory','');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_FetchUser','CenterId');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_OperationDetailRecord','StartTime,EndTime,UserIds,ObjectName,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_ParseQueryConditions','OutputTableName,QueryConditions,QCType');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_UserControlPrivilege','CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,EquipmentName,CommandType,ControlSeverity');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_UserDoorPrivilege','CenterId,GroupId,StationCategory,StationId');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_UserEquipmentPrivilege','CenterId,GroupId,StationCategory,StationId,EquipmentCategory');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_UserEventPrivilege','CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,EquipmentName,EventCategory,EventSeverity');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_UserSignalPrivilege','CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,EquipmentName,SignalCategory,SignalProperty');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_UserStationPrivilege','CenterId,GroupId,StationCategory');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5UserEquipmentPrivilegeLike','CenterId,GroupId,StationCategory,StationId,StationNameLike,EquipmentCategory');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5GetReportUser','CenterId');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5GetReportACEquipmentCategory','');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5GetReportACEquipmentVendor','');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5GetReportDepartment','');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5GetReportEmployeeUser','');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5UserBaseEquipmentPrivilege','CenterId,GroupId,StationCategory,StationId,EquipmentCategory,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5StaionStatusOperationInfo','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,UserIds,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5HouseStatusOperationInfo','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,UserIds,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5EquipmentStatusOperationInfo','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,UserIds,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5GetNotifyCategory','');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5GetNotifyReceiver','');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5GetCommandType','');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5GetCommandSeverity','');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5GetUser','');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5GetSignalThresholdType','');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5GetSignalBussinessProperty','');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5GetEquipmentCategory','');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_UserSignalPrivilegeLikeRealTime','CenterId,GroupId,StationCategory,StationId,StationNameLike,EquipmentCategory,EquipmentId,EquipmentNameLike,SignalCategory,SignalProperty,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5GetProjectStation','CenterId,GroupId,StationCategory,StationId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5GetProjectHouse','CenterId,GroupId,StationCategory,StationId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5GetProjectEquipment','CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_HistorySignalDataInfluxDb','CenterId,GroupId,StationCategory,StationId,StationNameLike,EquipmentCategory,EquipmentId,EquipmentNameLike,SignalCategory,SignalId,SignalNameLike,ThresholdType,ret');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_FetchComplexIndex','CenterId,GroupId,StationCategory,StationId');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_FetchComplexIndexConfig','CenterId,GroupId,StationCategory,StationId,ComplexIndexId');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5StaionMaskOperationRecord','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,UserIds');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5EquipmentMaskOperationRecord','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,UserIds');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5EventMaskOperationRecord','StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,UserIds');
INSERT INTO tbl_h5reportprocedureparameters (PName, Parameters) VALUES ('PRT_H5StationMask','CenterId,GroupId,StationCategory,StationId');

INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (1,'操作日志历史详细记录表','操作日志历史详细记录表','','操作日志历史详细记录表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (2,'通知发送记录报表','通知发送记录报表','','通知发送记录报表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (3,'高频次告警记录报表','高频次告警记录报表','','高频次告警记录报表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (4,'高温告警明细表','高温告警明细表','','高温告警明细表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (5,'高温告警统计表','高温告警统计表','','高温告警统计表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (6,'告警记录报表','查看您所选择的时间和区域的事件记录','','告警记录报表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (7,'监控断站情况统计报表','统计局站断站情况','','监控断站情况统计报表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (8,'局站按告警级别统计报表','按局站统计不同级别的告警情况','','局站按告警级别统计报表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (9,'局站动环监控工程屏蔽统计表','按局站统计监控工程屏蔽情况','','局站动环监控工程屏蔽统计表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (10,'局站动环监控可用率统计月报表','局站动环监控可用率统计月报表','','局站动环监控可用率统计月报表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (11,'局站记录报表','局站记录','','局站记录报表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (12,'局站设备类告警统计报表','按局站统计不同设备类的告警情况','','局站设备类告警统计报表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (13,'局站市电停电记录表','查询局站的市电停电记录','','局站市电停电记录表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (14,'局站市电停电统计报表','统计局站的市电停电情况','','局站市电停电统计报表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (15,'局站通信中断记录表','查询局站的通信中断记录','','局站通信中断记录表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (16,'局站统计表','统计各个行政区划所有的各类局站个数，并生成柱状图。','','局站统计表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (17,'局站用电量统计','统计各个行政区划所有的各类局站月用电量数据。','','局站用电量统计.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (18,'空调设备故障统计报表','统计空调设备的故障告警信息','','空调设备故障统计报表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (19,'控制命令历史记录表','查询所选控制命令的信息','','控制命令历史记录表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (21,'门禁刷卡记录表','查询门禁刷卡数据','','门禁刷卡记录表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (22,'门禁刷卡统计报表','查询门禁刷卡统计数据','','门禁刷卡统计报表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (23,'区域市电停电按月统计报表','查询区域市电停电按月统计报表','','区域市电停电按月统计报表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (24,'人员登录记录表','查看你选定的某个时间段内系统的登录记录。','','人员登录记录表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (25,'设备告警统计报表','统计设备的告警情况','','设备告警统计报表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (26,'设备清单表','设备清单表','','设备清单表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (27,'市电停电断站局站明细表','市电停电断站局站明细表','','市电停电断站局站明细表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (28,'事件屏蔽状态报表','用来统计所有屏蔽事件记录的报表','','事件屏蔽状态报表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (29,'系统告警记录报表','查询系统告警记录','','系统告警记录报表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (30,'信号告警统计报表','查询信号的告警情况','','信号告警统计报表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (31,'蓄电池故障统计报表','统计蓄电池设备的告警情况','','蓄电池故障统计报表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (32,'已接入动环监控局站数量统计表','已接入动环监控局站数量统计表','','已接入动环监控局站数量统计表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (33,'用户操作权限信息表','用户操作权限信息表','','用户操作权限信息表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (34,'油机发电机组故障统计报表','统计油机设备的告警情况','','油机发电机组故障统计报表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (35,'油机开机运行记录报表','查询油机的开机运行情况','','油机开机运行记录报表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (36,'油机开机运行统计报表','查询油机的开机运行情况','','油机开机运行统计报表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (37,'整流器故障统计报表','统计整流器设备的告警情况','','整流器故障统计报表.rdl','',-1,'admin','2024-06-11 16:21:27',-1,'admin','2024-06-11 16:21:27','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (38,'设备屏蔽状态报表','查询设备屏蔽信息','','设备屏蔽状态报表.rdl','',-1,'admin','2024-06-11 16:21:28',-1,'admin','2024-06-11 16:21:28','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (40,'局站告警统计表','局站告警统计表','','局站告警统计表.rdl','',-1,'admin','2024-06-11 16:21:28',-1,'admin','2024-06-11 16:21:28','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (41,'局站告警统计饼图','局站告警统计饼图','','局站告警统计饼图.rdl','',-1,'admin','2024-06-11 16:21:28',-1,'admin','2024-06-11 16:21:28','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (42,'局站告警统计柱状图','局站告警统计柱状图','','局站告警统计柱状图.rdl','',-1,'admin','2024-06-11 16:21:28',-1,'admin','2024-06-11 16:21:28','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (43,'设备类型告警统计表','设备类型告警统计表','','设备类型告警统计表.rdl','',-1,'admin','2024-06-11 16:21:28',-1,'admin','2024-06-11 16:21:28','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (44,'设备类型告警统计饼图','设备类型告警统计饼图','','设备类型告警统计饼图.rdl','',-1,'admin','2024-06-11 16:21:28',-1,'admin','2024-06-11 16:21:28','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (45,'设备类型告警统计柱状图','设备类型告警统计柱状图','','设备类型告警统计柱状图.rdl','',-1,'admin','2024-06-11 16:21:28',-1,'admin','2024-06-11 16:21:28','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (46,'实时数据记录表','可配置要查询设备的实时信号','','实时数据记录表.rdl','',-1,'admin','2024-06-11 16:21:28',-1,'admin','2024-06-11 16:21:28','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (47,'局站工程状态操作记录表','局站工程状态操作记录表','','局站工程状态操作记录表.rdl','',-1,'admin','2024-06-11 16:21:28',-1,'admin','2024-06-11 16:21:28','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (48,'机房工程状态操作记录表','机房工程状态操作记录表','','机房工程状态操作记录表.rdl','',-1,'admin','2024-06-11 16:21:28',-1,'admin','2024-06-11 16:21:28','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (49,'设备工程状态操作记录表','设备工程状态操作记录表','','设备工程状态操作记录表.rdl','',-1,'admin','2024-06-11 16:21:28',-1,'admin','2024-06-11 16:21:28','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (50,'局站工程状态报表','局站工程状态报表','','局站工程状态报表.rdl','',-1,'admin','2024-06-11 16:21:28',-1,'admin','2024-06-11 16:21:28','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (51,'机房工程状态报表','机房工程状态报表','','机房工程状态报表.rdl','',-1,'admin','2024-06-11 16:21:28',-1,'admin','2024-06-11 16:21:28','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (53,'局站屏蔽操作记录报表','局站屏蔽操作记录报表','','局站屏蔽操作记录报表.rdl','',-1,'admin','2024-06-11 16:21:28',-1,'admin','2024-06-11 16:21:28','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (54,'设备屏蔽操作记录报表','设备屏蔽操作记录报表','','设备屏蔽操作记录报表.rdl','',-1,'admin','2024-06-11 16:21:28',-1,'admin','2024-06-11 16:21:28','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (55,'事件屏蔽操作记录报表','事件屏蔽操作记录报表','','事件屏蔽操作记录报表.rdl','',-1,'admin','2024-06-11 16:21:28',-1,'admin','2024-06-11 16:21:28','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (56,'局站屏蔽状态报表','局站屏蔽状态报表','','局站屏蔽状态报表.rdl','',-1,'admin','2024-06-11 16:21:28',-1,'admin','2024-06-11 16:21:28','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (57,'历史数据记录表InfluxDB','历史数据记录表InfluxDB','','历史数据记录表InfluxDB.rdl','',-1,'admin','2024-06-11 16:21:28',-1,'admin','2024-06-11 16:21:28','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (58,'历史数据曲线表InfluxDB','历史数据曲线表InfluxDB','','历史数据曲线表InfluxDB.rdl','',-1,'admin','2024-06-11 16:21:28',-1,'admin','2024-06-11 16:21:28','1');
INSERT INTO tbl_report (ReportId, ReportName, Description, ReportFileId, ReportFileName, PreviewImageName, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime, Version) VALUES (59,'指标报表InfluxDB','指标报表InfluxDB','','指标报表InfluxDB.rdl','',-1,'admin','2024-06-11 16:21:28',-1,'admin','2024-06-11 16:21:28','1');

INSERT INTO tbl_reportchart (ReportId, ChartId, ChartType, ChartName, XAxis, YAxis, Serials, SerialsName) VALUES (16,1,'bar','局站统计表','GroupName','StationCount','StationCategory','StationCategoryName');
INSERT INTO tbl_reportchart (ReportId, ChartId, ChartType, ChartName, XAxis, YAxis, Serials, SerialsName) VALUES (39,1,'line','历史数据曲线表','RecordTime','SignalValue','GroupLineID','SeriesName');
INSERT INTO tbl_reportchart (ReportId, ChartId, ChartType, ChartName, XAxis, YAxis, Serials, SerialsName) VALUES (41,1,'pie','局站告警统计饼图','StationNameDesc','AlarmCount','StationNameDesc','StationNameDesc');
INSERT INTO tbl_reportchart (ReportId, ChartId, ChartType, ChartName, XAxis, YAxis, Serials, SerialsName) VALUES (42,1,'bar','局站告警统计柱状图','StationNameDesc','AlarmCount','StationNameDesc','StationNameDesc');
INSERT INTO tbl_reportchart (ReportId, ChartId, ChartType, ChartName, XAxis, YAxis, Serials, SerialsName) VALUES (44,1,'pie','设备类型告警统计饼图','EquipmentType','AlarmCount','EquipmentType','EquipmentType');
INSERT INTO tbl_reportchart (ReportId, ChartId, ChartType, ChartName, XAxis, YAxis, Serials, SerialsName) VALUES (45,1,'bar','设备类型告警统计柱状图','EquipmentType','AlarmCount','EquipmentType','EquipmentType');
INSERT INTO tbl_reportchart (ReportId, ChartId, ChartType, ChartName, XAxis, YAxis, Serials, SerialsName) VALUES (58,1,'line','历史数据曲线表InfluxDB','RecordTime','SignalValue','GroupLineID','SeriesName');
INSERT INTO tbl_reportchart (ReportId, ChartId, ChartType, ChartName, XAxis, YAxis, Serials, SerialsName) VALUES (59,1,'line','指标报表InfluxDB','CalTime','IndexValue','ComplexIndexId','ComplexIndexName');

INSERT INTO tbl_reportgroup (ReportGroupId, GroupName, UserId, GroupType, Description) VALUES (1,'统计表',NULL,0,'');
INSERT INTO tbl_reportgroup (ReportGroupId, GroupName, UserId, GroupType, Description) VALUES (2,'记录表',NULL,0,'');
INSERT INTO tbl_reportgroup (ReportGroupId, GroupName, UserId, GroupType, Description) VALUES (3,'典型报表',NULL,0,'');
INSERT INTO tbl_reportgroup (ReportGroupId, GroupName, UserId, GroupType, Description) VALUES (4,'实时报表',NULL,0,'');

INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (5,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (7,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (8,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (9,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (10,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (12,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (14,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (16,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (17,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (18,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (22,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (23,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (25,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (30,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (31,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (32,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (34,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (35,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (36,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (37,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (40,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (41,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (42,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (43,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (44,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (45,1);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (1,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (2,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (3,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (4,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (6,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (11,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (13,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (15,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (19,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (20,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (21,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (24,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (26,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (27,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (28,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (29,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (33,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (38,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (39,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (46,4);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (47,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (48,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (49,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (50,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (51,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (52,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (53,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (54,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (55,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (56,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (57,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (58,2);
INSERT INTO tbl_reportgroupmap (ReportId, ReportGroupId) VALUES (59,2);

INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(1, '查询开始时间', 'StartTime', '', '', '', 'datetime', 'ui', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(2, '查询结束时间', 'EndTime', '', '', '', 'datetime', 'ui', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(3, '查询月份', 'RecordMonth', '', '', '', 'month', 'ui', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(4, '监控中心', 'CenterId', 'PRT_FetchSSCenter', '', '[{"pId":"StructureId", "pName":"StructureName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(5, '行政区划', 'GroupId', 'PRT_FetchGroupTree', 'CenterId', '[{"ppId":"ParentStructureId","pId":"StructureId", "pName":"StructureName"}]', 'tree', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(6, '局站分类', 'StationCategory', 'PRT_FetchStationCategory', '', '[{"pId":"ItemId", "pName":"ItemValue"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(7, '局站', 'StationId', 'PRT_UserStationPrivilege', 'CenterId,GroupId,StationCategory', '[{"pId":"StationId", "pName":"StationName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(8, '局站名(关键字模糊查找)', 'StationNameLike', '', '', '', 'input', 'ui', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(9, '事件等级', 'EventSeverity', 'PRT_GetEventSeverityOfReport', '', '[{"pId":"ItemId", "pName":"ItemValue"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(10, '设备分类', 'EquipmentCategory', 'PRT_GetEquipmentCategoryOfReport', '', '[{"pId":"ItemId", "pName":"ItemValue"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(11, '设备', 'EquipmentId', 'PRT_H5UserEquipmentPrivilegeLike', 'CenterId,GroupId,StationCategory,StationId,StationNameLike,EquipmentCategory', '[{"pId":"EquipmentId", "pName":"EquipmentName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(12, '设备名(关键字模糊查找)', 'EquipmentNameLike', '', '', '', 'input', 'ui', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(13, '信号分类', 'SignalCategory', 'PRT_GetSignalCategoryOfReport', '', '[{"pId":"ItemId", "pName":"ItemValue"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(14, '信号', 'SignalId', 'PRT_UserSignalPrivilegeLikeOfReport', 'CenterId,GroupId,StationCategory,StationId,StationNameLike,EquipmentCategory,EquipmentId,EquipmentNameLike,SignalCategory', '[{"pId":"SignalId", "pName":"SignalName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(15, '信号名(关键字模糊查找)', 'SignalNameLike', '', '', '', 'input', 'ui', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(16, '操作人', 'UserIds', 'PRT_H5GetReportOperationUser', '', '[{"pId":"UserId", "pName":"UserName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(17, '操作对象', 'ObjectName', 'PRT_H5GetReportOperationObj', '', '[{"pId":"ItemId", "pName":"ItemValue"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(18, '设备类型', 'EquipmentCategory', 'PRT_GetEquipmentTypeOfReport', '', '[{"pId":"BaseEquipmentId", "pName":"BaseEquipmentName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(19, '设备', 'EquipmentId', 'PRT_H5UserEquipmentPrivilegeLike', 'CenterId,GroupId,StationCategory,StationId,EquipmentCategory', '[{"pId":"EquipmentId", "pName":"EquipmentName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(20, '信号', 'SignalId', 'PRT_UserSignalPrivilegeLikeOfReport', 'CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,SignalCategory', '[{"pId":"SignalId", "pName":"SignalName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(21, '用户', 'UserId', 'PRT_H5GetReportUser', 'CenterId', '[{"pId":"UserId", "pName":"UserName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(22, '设备', 'EquipmentId', 'PRT_H5UserBaseEquipmentPrivilege', 'CenterId,GroupId,StationCategory,StationId,EquipmentCategory', '[{"pId":"EquipmentId", "pName":"EquipmentName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(23, '设备类型（空调）', 'EquipmentCategory', 'PRT_H5GetReportACEquipmentCategory', '', '[{"pId":"BaseEquipmentId", "pName":"BaseEquipmentName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(24, '设备厂家', 'Vendor', 'PRT_H5GetReportACEquipmentVendor', '', '[{"pId":"ItemId", "pName":"ItemValue"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(25, '部门名称', 'DepartmentId', 'PRT_H5GetReportDepartment', '', '[{"pId":"DepartmentId", "pName":"DepartmentName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(26, '门禁', 'DoorId', 'PRT_UserDoorPrivilege', 'CenterId,GroupId,StationCategory,StationId', '[{"pId":"DoorId", "pName":"DoorName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(27, '刷卡人', 'SwapCardOwnerId', 'PRT_H5GetReportEmployeeUser', '', '[{"pId":"UserId", "pName":"UserName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(28, '设备', 'EquipmentId', 'PRT_UserBaseEquipmentPrivilegeLike', 'CenterId,GroupId,StationCategory,StationId,StationNameLike,EquipmentCategory', '[{"pId":"EquipmentId", "pName":"EquipmentName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(29, '设备名(关键字模糊查找)', 'EquipmentNameLike', '', '', '', 'input', 'ui', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(30, '事件', 'EventId', 'PRT_UserEventPrivilegeLike', 'CenterId,GroupId,StationCategory,StationId,StationNameLike,EquipmentCategory,EquipmentId,EquipmentNameLike', '[{"pId":"EventId", "pName":"EventName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(31, '事件名(关键字模糊查找)', 'EventNameLike', '', '', '', 'input', 'ui', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(32, '事件等级', 'EventSeverity', 'PRT_GetEventSeverityOfReport', '', '[{"pId":"ItemId", "pName":"ItemValue"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(33, '最少间隔(单位:分钟,0:表示无限制)', 'Interval', '', '', '', 'input', 'ui', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(34, '是否确认', 'IsConfirm', '', '', '', 'radio', 'ui', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(35, '是否结束', 'IsEnd', '', '', '', 'radio', 'ui', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(36, '是否强制结束', 'IsCancel', '', '', '', 'radio', 'ui', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(37, '设备', 'EquipmentId', 'PRT_H5UserEquipmentPrivilegeLike', 'CenterId,GroupId,StationCategory,StationId,EquipmentCategory', '[{"pId":"EquipmentId", "pName":"EquipmentName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(38, '通知类型', 'NotifyCategory', 'PRT_H5GetNotifyCategory', '', '[{"pId":"NotifyModeId", "pName":"NotifyModeName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(39, '接收者', 'UserId', 'PRT_H5GetNotifyReceiver', '', '[{"pId":"UserId", "pName":"UserName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(40, '事件类型', 'EventType', 'PRT_UserEventBaseDicPrivilege', 'EquipmentCategory', '[{"pId":"BaseTypeId", "pName":"BaseTypeName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(41, '命令类型', 'CommandType', 'PRT_H5GetCommandType', '', '[{"pId":"ItemId", "pName":"ItemValue"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(42, '命令等级', 'ControlSeverity', 'PRT_H5GetCommandSeverity', '', '[{"pId":"ItemId", "pName":"ItemValue"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(43, '控制命令', 'ControlId', 'PRT_UserControlPrivilege', 'CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,CommandType,ControlSeverity', '[{"pId":"CommandId", "pName":"CommandName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(44, '执行人', 'ControlExecuterId', 'PRT_H5GetUser', '', '[{"pId":"UserId", "pName":"UserName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(45, '信号存盘方式', 'ThresholdType', 'PRT_H5GetSignalThresholdType', '', '[{"pId":"ItemId", "pName":"ItemValue"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(46, '信号业务类型', 'SignalProperty', 'PRT_H5GetSignalBussinessProperty', '', '[{"pId":"ItemId", "pName":"ItemValue"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(47, '局站分类', 'StationCategoryId', 'PRT_FetchStationCategory', '', '[{"pId":"ItemId", "pName":"ItemValue"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(48, '局站', 'Station', 'PRT_UserStationPrivilege', 'CenterId,GroupId,StationCategory', '[{"pId":"StationId", "pName":"StationName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(49, '设备类型', 'EquipmentCategory', 'PRT_H5GetEquipmentCategory', '', '[{"pId":"ItemId", "pName":"ItemValue"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(50, '操作人', 'UserIds', 'PRT_H5GetReportOperationUser', '', '[{"pId":"UserId", "pName":"UserName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(51, '信号分类', 'SignalCategory', 'PRT_GetSignalCategoryOfReport', '', '[{"pId":"ItemId", "pName":"ItemValue"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(52, '信号', 'SignalId', 'PRT_UserSignalPrivilegePro', 'CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,SignalCategory', '[{"pId":"SignalId", "pName":"SignalName"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(53, '指标树', 'StructureId', 'PRT_FetchComplexIndexTree', '', '[{"ppId":"ParentStructureId","pId":"StructureId", "pName":"StructureName"}]', 'tree', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(54, '指标', 'ComplexIndexId', 'PRT_FetchComplexIndex', 'CenterId,GroupId,StationId', '[{"pId":"ItemId", "pName":"ItemValue"}]', 'transfer', 'db', '');
INSERT INTO tbl_reportparameter (ParameterId, ParameterName, ParameterKey, ProcedureName, ParameterString, ParameterField, ParameterType, ParameterFrom, Description) VALUES(55, '时间粒度', 'TimeGranularity', 'PRT_FetchComplexIndexTimeGranularity', '', '[{"pId":"ItemId", "pName":"ItemValue"}]', 'transfer', 'db', '');

INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (1,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (2,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (3,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (4,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (5,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (6,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (7,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (8,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (12,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (13,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (14,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (15,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (18,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (19,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (20,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (21,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (22,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (24,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (25,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (27,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (28,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (29,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (30,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (31,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (34,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (35,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (36,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (37,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (38,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (39,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (40,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (41,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (42,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (43,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (44,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (45,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (47,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (48,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (49,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (53,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (54,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (55,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (57,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (58,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (59,1);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (1,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (2,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (3,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (4,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (5,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (6,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (7,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (8,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (12,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (13,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (14,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (15,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (18,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (19,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (20,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (21,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (22,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (24,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (25,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (27,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (28,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (29,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (30,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (31,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (34,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (35,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (36,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (37,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (38,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (39,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (40,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (41,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (42,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (43,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (44,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (45,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (47,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (48,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (49,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (53,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (54,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (55,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (57,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (58,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (59,2);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (2,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (3,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (4,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (5,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (6,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (7,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (8,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (9,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (11,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (12,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (13,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (14,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (15,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (16,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (18,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (19,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (20,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (21,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (22,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (24,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (25,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (26,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (27,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (28,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (29,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (30,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (31,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (32,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (34,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (35,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (36,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (37,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (38,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (39,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (40,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (41,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (42,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (43,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (44,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (45,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (46,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (47,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (48,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (49,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (50,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (51,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (52,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (53,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (54,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (55,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (56,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (57,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (58,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (59,4);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (2,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (3,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (4,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (5,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (6,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (7,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (8,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (9,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (11,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (12,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (13,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (14,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (15,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (16,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (18,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (19,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (20,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (21,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (22,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (25,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (26,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (27,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (28,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (30,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (31,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (32,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (34,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (35,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (36,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (37,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (38,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (39,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (40,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (41,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (42,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (43,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (44,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (45,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (46,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (47,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (48,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (49,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (50,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (51,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (52,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (53,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (54,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (55,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (56,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (57,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (58,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (59,5);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (2,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (3,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (4,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (5,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (6,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (7,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (8,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (9,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (11,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (12,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (13,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (14,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (15,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (16,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (18,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (19,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (20,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (21,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (22,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (25,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (26,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (27,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (30,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (31,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (32,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (34,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (35,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (36,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (37,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (39,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (40,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (41,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (42,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (43,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (44,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (45,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (46,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (47,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (48,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (49,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (50,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (51,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (52,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (53,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (54,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (55,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (56,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (57,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (58,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (59,6);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (2,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (3,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (4,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (5,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (6,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (7,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (8,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (9,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (12,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (13,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (14,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (15,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (19,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (20,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (21,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (22,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (25,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (26,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (27,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (30,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (35,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (36,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (39,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (40,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (41,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (42,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (43,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (44,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (45,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (46,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (47,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (48,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (49,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (50,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (51,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (52,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (53,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (54,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (55,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (56,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (57,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (58,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (59,7);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (6,8);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (20,8);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (39,8);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (57,8);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (58,8);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (40,9);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (41,9);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (42,9);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (2,10);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (19,10);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (20,10);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (26,10);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (30,10);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (39,10);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (43,10);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (44,10);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (45,10);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (46,10);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (49,10);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (52,10);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (57,10);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (58,10);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (20,11);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (39,11);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (52,11);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (57,11);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (58,11);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (20,12);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (39,12);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (57,12);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (58,12);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (20,13);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (39,13);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (46,13);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (57,13);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (58,13);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (20,14);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (39,14);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (57,14);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (58,14);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (20,15);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (39,15);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (57,15);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (58,15);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (1,16);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (47,16);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (48,16);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (49,16);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (1,17);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (3,18);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (6,18);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (12,18);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (25,18);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (46,19);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (46,20);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (24,21);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (3,22);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (25,22);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (18,23);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (18,24);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (31,24);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (34,24);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (37,24);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (33,25);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (21,26);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (21,27);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (6,28);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (6,29);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (6,30);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (6,31);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (3,32);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (6,32);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (43,32);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (44,32);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (45,32);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (6,33);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (6,34);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (6,35);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (6,36);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (2,37);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (19,37);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (30,37);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (2,38);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (2,39);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (3,40);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (19,41);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (19,42);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (19,43);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (19,44);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (20,45);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (57,45);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (58,45);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (20,46);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (28,47);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (38,47);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (28,48);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (38,48);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (28,49);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (38,49);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (28,50);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (38,50);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (30,51);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (30,52);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (59,54);
INSERT INTO tbl_reportparametermap (ReportId, ParameterId) VALUES (59,55);

INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(1, 'PRT_OperationDetailRecord', 'StartTime,EndTime,UserIds,ObjectName', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"操作人","dbfield":"UserName"},{"no":"3","column":"操作时间","dbfield":"OperationTime"},{"no":"4","column":"操作类型","dbfield":"OperationType"},{"no":"5","column":"操作对象","dbfield":"ObjectName"},{"no":"6","column":"操作对象ID","dbfield":"ObjectId"},{"no":"7","column":"修改字段","dbfield":"PropertyName"},{"no":"8","column":"修改前内容","dbfield":"OldValue"},{"no":"9","column":"修改后内容","dbfield":"NewValue"}]', 1, '操作日志历史详细记录表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(2, 'PRT_GetNotificationRecord', 'StartTime,EndTime,NotifyCategory,UserId,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站","dbfield":"StationName"},{"no":"4","column":"设备","dbfield":"EquipmentName"},{"no":"5","column":"事件名称","dbfield":"EventName"},{"no":"6","column":"事件含义","dbfield":"Meanings"},{"no":"7","column":"事件开始时间","dbfield":"StartTime"},{"no":"8","column":"通知类型","dbfield":"NotifyCategory"},{"no":"9","column":"通知发送时间","dbfield":"SMSSentTime"},{"no":"10","column":"发送标志","dbfield":"SendFlag"},{"no":"11","column":"接收者","dbfield":"UserName"},{"no":"12","column":"发送说明","dbfield":"Description"}]', 1, '通知发送记录报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(3, 'PRT_ReportHistoryBaseTypeEvent', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,EventType,EventSeverity', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站","dbfield":"StationName"},{"no":"4","column":"设备类型","dbfield":"EquipmentCategory"},{"no":"5","column":"设备","dbfield":"EquipmentName"},{"no":"6","column":"事件类型","dbfield":"EventTypeName"},{"no":"7","column":"事件等级","dbfield":"EventSeverity"},{"no":"8","column":"事件说明","dbfield":"Meanings"},{"no":"9","column":"事件","dbfield":"EventName"},{"no":"10","column":"事件标准名","dbfield":"StandardAlarmName"},{"no":"11","column":"开始时间","dbfield":"StartTime"},{"no":"12","column":"结束时间","dbfield":"EndTime"},{"no":"13","column":"持续时长（分）","dbfield":"EventDuration"},{"no":"14","column":"触发值","dbfield":"EventValue"},{"no":"15","column":"确认人","dbfield":"ConfirmerName"},{"no":"16","column":"确认时间","dbfield":"ConfirmTime"},{"no":"17","column":"强制结束人","dbfield":"CancelUserName"},{"no":"18","column":"强制结束时间","dbfield":"CancelTime"},{"no":"19","column":"翻转次数","dbfield":"ReversalNum"}]', 1, '高频次告警记录报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(4, 'PRT_TemperatureHighAlarmRecord', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站","dbfield":"StationName"},{"no":"4","column":"高温告警开始时间","dbfield":"StartTime"},{"no":"5","column":"高温告警结束时间","dbfield":"EndTime"}]', 1, '高温告警明细表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(5, 'PRT_TemperatureHighAlarmStat', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站","dbfield":"StationName"},{"no":"4","column":"高温告警次数","dbfield":"AlarmCount"},{"no":"5","column":"高温告警累计时长(分钟)","dbfield":"AlarmDuringTime"}]', 1, '高温告警统计表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(6, 'PRT_HistoryEventDataRecordMain', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,StationNameLike,EquipmentCategory,EquipmentId,EquipmentNameLike,EventId,EventNameLike,EventSeverity,Interval,IsConfirm,IsEnd,IsCancel', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站","dbfield":"StationName"},{"no":"4","column":"设备类型","dbfield":"EquipmentCategory"},{"no":"5","column":"设备","dbfield":"EquipmentName"},{"no":"6","column":"事件类型","dbfield":"EventTypeName"},{"no":"7","column":"事件等级","dbfield":"EventSeverity"},{"no":"8","column":"事件说明","dbfield":"Meanings"},{"no":"9","column":"事件","dbfield":"EventName"},{"no":"10","column":"事件标准名","dbfield":"StandardAlarmName"},{"no":"11","column":"开始时间","dbfield":"StartTime"},{"no":"12","column":"结束时间","dbfield":"EndTime"},{"no":"13","column":"持续时长(分)","dbfield":"EventDuration"},{"no":"14","column":"触发值","dbfield":"EventValue"},{"no":"15","column":"确认人","dbfield":"ConfirmerName"},{"no":"16","column":"确认时间","dbfield":"ConfirmTime"},{"no":"17","column":"强制结束人","dbfield":"CancelUserName"},{"no":"18","column":"强制结束时间","dbfield":"CancelTime"},{"no":"19","column":"翻转次数","dbfield":"ReversalNum"}]', 1, '告警记录报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(7, 'PRT_StationDisconnectSta', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站","dbfield":"StationName"},{"no":"4","column":"断站次数","dbfield":"SumEventCount"},{"no":"5","column":"断站时长(分钟)","dbfield":"SumDuringTime"},{"no":"6","column":"断站率(%)","dbfield":"EventCountPercent"}]', 1, '监控断站情况统计报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(8, 'PRT_StationGradeAlarmSta', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站","dbfield":"StationName"},{"no":"4","column":"告警级别","dbfield":"EventSeverity"},{"no":"5","column":"告警历时(分钟)","dbfield":"GradeDuringTime"},{"no":"6","column":"告警次数","dbfield":"GradeEventCount"},{"no":"7","column":"历时百分比","dbfield":"DuringTimePercent"},{"no":"8","column":"次数百分比","dbfield":"EventCountPercent"}]', 1, '局站按告警级别统计报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(9, 'PRT_StationEngineeringOrMask', 'CenterId,GroupId,StationCategory,StationId', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站","dbfield":"StationName"},{"no":"4","column":"工程或屏蔽","dbfield":"EngineeringOrMask"},{"no":"5","column":"开始时间","dbfield":"StartTime"},{"no":"6","column":"结束时间","dbfield":"EndTime"}]', 1, '局站动环监控工程屏蔽统计表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(11, 'PRT_StationRecord', 'CenterId,GroupId,StationCategory', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站类型","dbfield":"StationCategoryValue"},{"no":"4","column":"局站等级","dbfield":"StationGradeValue"},{"no":"5","column":"局站名称","dbfield":"StationName"},{"no":"6","column":"经度","dbfield":"Longitude"},{"no":"7","column":"纬度","dbfield":"Latitude"},{"no":"8","column":"Y-坐标","dbfield":"YCoord"},{"no":"9","column":"X-坐标","dbfield":"XCoord"}]', 1, '局站记录报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(12, 'PRT_EquipTypeAlarmStatByGrade', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站","dbfield":"StationName"},{"no":"4","column":"设备类","dbfield":"BaseEquipmentName"},{"no":"5","column":"一级告警","dbfield":"EventSeverity3"},{"no":"6","column":"二级告警","dbfield":"EventSeverity2"},{"no":"7","column":"三级告警","dbfield":"EventSeverity1"},{"no":"8","column":"四级告警","dbfield":"EventSeverity0"},{"no":"9","column":"合计","dbfield":"AllEventCount"}]', 1, '局站设备类告警统计报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(13, 'PRT_StationPowerOffRecord', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站","dbfield":"StationName"},{"no":"4","column":"停电时间","dbfield":"StartTime"},{"no":"5","column":"恢复时间","dbfield":"EndTime"},{"no":"6","column":"停电时长(分钟)","dbfield":"DuringTime"}]', 1, '局站市电停电记录表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(14, 'PRT_StationPowerOffSta', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站","dbfield":"StationName"},{"no":"4","column":"停电次数","dbfield":"SumEventCount"},{"no":"5","column":"停电时长(分钟)","dbfield":"SumDuringTime"},{"no":"6","column":"停电率(%)","dbfield":"EventCountPercent"}]', 1, '局站市电停电统计报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(15, 'PRT_StationDisconnectRecord', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站","dbfield":"StationName"},{"no":"4","column":"中断时间","dbfield":"StartTime"},{"no":"5","column":"恢复时间","dbfield":"EndTime"},{"no":"6","column":"中断时间(分钟)","dbfield":"DuringTime"}]', 1, '局站通信中断记录表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(16, 'PRT_ReportStationStatistics', 'CenterId,GroupId,StationCategory', '[{"no":"1","column":"监控中心名称","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站类型","dbfield":"StationCategoryName"},{"no":"4","column":"局站数量","dbfield":"StationCount"}]', 0, '局站统计表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(18, 'PRT_ACEventStaByFactory', 'StartTime,EndTime,CenterId,GroupId,StationCategory,EquipmentCategory,Vendor', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"局站类型","dbfield":"StationCategory"},{"no":"3","column":"设备厂家","dbfield":"Vendor"},{"no":"4","column":"设备型号","dbfield":"EquipmentStyle"},{"no":"5","column":"故障次数","dbfield":"EventCount"},{"no":"6","column":"故障设备数","dbfield":"EquipEventCount"},{"no":"7","column":"设备总数","dbfield":"EquipCount"},{"no":"8","column":"故障率（%）","dbfield":"EventCountPercent"}]', 1, '空调设备故障统计报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(19, 'PRT_ReportHistoryCommand', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,CommandType,ControlId,ControlSeverity,ControlExecuterId', '[{"no":"1","column":"行政区划","dbfield":"GroupName"},{"no":"2","column":"局站","dbfield":"StationName"},{"no":"3","column":"设备","dbfield":"EquipmentName"},{"no":"4","column":"命令名","dbfield":"CommandName"},{"no":"5","column":"命令类型","dbfield":"CommandType"},{"no":"6","column":"执行结果","dbfield":"CommandResult"},{"no":"7","column":"开始执行时间","dbfield":"StartTime"},{"no":"8","column":"确认时间","dbfield":"ConfirmTime"},{"no":"9","column":"是否执行","dbfield":"CommandResultType"},{"no":"10","column":"执行人","dbfield":"CommandExecuterName"},{"no":"11","column":"确认人","dbfield":"ConfirmerName"},{"no":"12","column":"说明","dbfield":"Description"}]', 1, '控制命令历史记录表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(20, 'PRT_HistorySignalDataRecordMain', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,StationNameLike,EquipmentCategory,EquipmentId,EquipmentNameLike,SignalCategory,SignalId,SignalNameLike,ThresholdType,SignalProperty', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站","dbfield":"StationName"},{"no":"4","column":"设备分类","dbfield":"EquipmentCategory"},{"no":"5","column":"设备名","dbfield":"EquipmentName"},{"no":"6","column":"信号名","dbfield":"SignalName"},{"no":"7","column":"信号值","dbfield":"SignalValue"},{"no":"8","column":"记录时间","dbfield":"RecordTime"},{"no":"9","column":"信号含义","dbfield":"Meanings"},{"no":"10","column":"存盘方式","dbfield":"ThresholdType"}]', 1, '历史数据记录表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(21, 'PRT_HistorySwapCardRecord', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,DoorId,SwapCardOwnerId', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站分类","dbfield":"StationCategoryName"},{"no":"4","column":"局站","dbfield":"StationName"},{"no":"5","column":"门禁设备","dbfield":"DoorEquipmentName"},{"no":"6","column":"门禁","dbfield":"DoorName"},{"no":"7","column":"卡号","dbfield":"CardNumber"},{"no":"8","column":"卡名称","dbfield":"CardName"},{"no":"9","column":"刷卡时间","dbfield":"RecordTime"},{"no":"10","column":"持卡人","dbfield":"CardOnwer"},{"no":"11","column":"刷卡标志","dbfield":"Valid"}]', 1, '门禁刷卡记录表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(22, 'PRT_HistorySwapCardStat', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站","dbfield":"StationName"},{"no":"4","column":"门禁名称","dbfield":"DoorName"},{"no":"5","column":"卡号","dbfield":"CardCode"},{"no":"6","column":"持卡人","dbfield":"CardUserName"},{"no":"7","column":"刷卡次数","dbfield":"SwapCount"}]', 1, '门禁刷卡统计报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(24, 'PRT_ReportUserLoginRecord', 'StartTime,EndTime,CenterId,UserId', '[{"no":"1","column":"监控中心","dbfield":"StructureName"},{"no":"2","column":"用户","dbfield":"UserName"},{"no":"3","column":"登录类型","dbfield":"ItemValue"},{"no":"4","column":"时间","dbfield":"LoginTime"},{"no":"5","column":"客户IP地址","dbfield":"IPAddress"}]', 1, '人员登录记录表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(25, 'PRT_EquipAlarmStatByGrade', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站","dbfield":"StationName"},{"no":"4","column":"设备名称","dbfield":"EquipmentName"},{"no":"5","column":"一级告警","dbfield":"EventSeverity3"},{"no":"6","column":"二级告警","dbfield":"EventSeverity2"},{"no":"7","column":"三级告警","dbfield":"EventSeverity1"},{"no":"8","column":"四级告警","dbfield":"EventSeverity0"},{"no":"9","column":"合计","dbfield":"AllEventCount"}]', 1, '设备告警统计报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(26, 'PRT_ReportEquipment', 'CenterId,GroupId,StationCategory,StationId,EquipmentCategory', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站分类","dbfield":"StationCategoryValue"},{"no":"4","column":"局站名称","dbfield":"StationName"},{"no":"5","column":"设备类型","dbfield":"EquipmentCategoryValue"},{"no":"6","column":"设备名称","dbfield":"EquipmentName"},{"no":"7","column":"厂商","dbfield":"Vendor"},{"no":"8","column":"单位","dbfield":"Unit"},{"no":"9","column":"设备型号","dbfield":"EquipmentStyle"},{"no":"10","column":"模块","dbfield":"EquipmentModule"},{"no":"11","column":"属性","dbfield":"Property"},{"no":"12","column":"资产编号","dbfield":"EquipmentNo"},{"no":"13","column":"资产状态","dbfield":"AssetStateName"},{"no":"14","column":"购买日期","dbfield":"BuyDate"},{"no":"15","column":"投用日期","dbfield":"UsedDate"},{"no":"16","column":"使用年限（年）","dbfield":"UsedLimit"},{"no":"17","column":"价格（元）","dbfield":"Price"},{"no":"18","column":"额定容量","dbfield":"RatedCapacity"},{"no":"19","column":"已安装模块","dbfield":"InstalledModule"},{"no":"20","column":"工程名称","dbfield":"ProjectName"},{"no":"21","column":"合同号","dbfield":"ContractNo"},{"no":"22","column":"说明","dbfield":"Description"},{"no":"23","column":"连接状态","dbfield":"ConnectState"}]', 1, '设备清单表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(27, 'PRT_PowerOffDisconnectRecord', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站","dbfield":"StationName"},{"no":"4","column":"停电时间","dbfield":"PowerOffTime"},{"no":"5","column":"断站时间","dbfield":"DisconnectTime"}]', 1, '市电停电断站局站明细表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(28, 'PRT_ReportEventMask', 'StartTime,EndTime,UserIds,CenterId,GroupId,StationCategoryId,Station,EquipmentCategory', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站分类","dbfield":"StationCategory"},{"no":"4","column":"局站名","dbfield":"StationName"},{"no":"5","column":"设备名","dbfield":"EquipmentName"},{"no":"6","column":"事件名","dbfield":"EventName"},{"no":"7","column":"屏蔽开始时间","dbfield":"StartTime"},{"no":"8","column":"屏蔽结束时间","dbfield":"EndTime"},{"no":"9","column":"屏蔽人","dbfield":"UserName"},{"no":"10","column":"屏蔽原因","dbfield":"Reason"}]', 1, '事件屏蔽状态报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(29, 'PRT_SystemAlarmRecord', 'StartTime,EndTime,CenterId', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"事件名称","dbfield":"EventName"},{"no":"3","column":"开始时间","dbfield":"StartTime"},{"no":"4","column":"结束时间","dbfield":"EndTime"},{"no":"5","column":"持续时间（分钟）","dbfield":"DuringTime"},{"no":"6","column":"确认人","dbfield":"ConfirmerName"},{"no":"7","column":"确认时间","dbfield":"ConfirmTime"}]', 1, '系统告警记录报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(30, 'PRT_SignalAlarmStatByGrade', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,SignalCategory,SignalId', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站","dbfield":"StationName"},{"no":"4","column":"设备名称","dbfield":"EquipmentName"},{"no":"5","column":"信号名称","dbfield":"SignalName"},{"no":"6","column":"一级告警","dbfield":"EventSeverity3"},{"no":"7","column":"二级告警","dbfield":"EventSeverity2"},{"no":"8","column":"三级告警","dbfield":"EventSeverity1"},{"no":"9","column":"四级告警","dbfield":"EventSeverity0"}]', 1, '信号告警统计报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(31, 'PRT_BatteryEventStaByFactory', 'StartTime,EndTime,CenterId,GroupId,StationCategory,Vendor', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"局站类型","dbfield":"StationCategory"},{"no":"3","column":"设备厂家","dbfield":"Vendor"},{"no":"4","column":"设备型号","dbfield":"EquipmentStyle"},{"no":"5","column":"设备数量","dbfield":"EquipCount"},{"no":"6","column":"故障次数","dbfield":"EventCount"},{"no":"7","column":"故障设备数","dbfield":"EquipEventCount"},{"no":"8","column":"故障率（%）","dbfield":"EventCountPercent"}]', 1, '蓄电池故障统计报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(32, 'PRT_StationMonitoredStat', 'CenterId,GroupId,StationCategory', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站分类","dbfield":"StationCategory"},{"no":"4","column":"局站数量","dbfield":"Count"}]', 1, '已接入动环监控局站数量统计表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(33, 'PRT_ReportUserOperation', 'DepartmentId', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"部门","dbfield":"DepartmentName"},{"no":"3","column":"工号","dbfield":"JobNumber"},{"no":"4","column":"帐号","dbfield":"LogonId"},{"no":"5","column":"姓名","dbfield":"UserName"},{"no":"6","column":"操作权限组","dbfield":"Description"}]', 1, '用户操作权限信息表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(34, 'PRT_OilEventStaByFactory', 'StartTime,EndTime,CenterId,GroupId,StationCategory,Vendor', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"局站类型","dbfield":"StationCategory"},{"no":"3","column":"设备厂家","dbfield":"Vendor"},{"no":"4","column":"设备型号","dbfield":"EquipmentStyle"},{"no":"5","column":"故障次数","dbfield":"EventCount"},{"no":"6","column":"故障设备数","dbfield":"EquipEventCount"},{"no":"7","column":"设备总数","dbfield":"EquipCount"},{"no":"8","column":"故障率（%）","dbfield":"EventCountPercent"}]', 1, '油机发电机组故障统计报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(35, 'PRT_EngineRunRecord', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站","dbfield":"StationName"},{"no":"4","column":"油机设备名称","dbfield":"EquipmentName"},{"no":"5","column":"开机时间","dbfield":"StartTime"},{"no":"6","column":"关机时间","dbfield":"EndTime"},{"no":"7","column":"AB线电压","dbfield":"Uab"},{"no":"8","column":"BC线电压","dbfield":"Ubc"},{"no":"9","column":"CA线电压","dbfield":"Uca"},{"no":"10","column":"A相电流","dbfield":"Iab"},{"no":"11","column":"B相电流","dbfield":"Ibc"},{"no":"12","column":"C相电流","dbfield":"Ica"},{"no":"13","column":"启动电池电压","dbfield":"BatVol"},{"no":"14","column":"油温","dbfield":"OilVol"},{"no":"15","column":"油位","dbfield":"OilPos"},{"no":"16","column":"水温","dbfield":"WatTemp"},{"no":"17","column":"频率","dbfield":"Freq"},{"no":"18","column":"运行情况","dbfield":"RunState"}]', 1, '油机开机运行记录报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(36, 'PRT_EngineRunStatistics', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站","dbfield":"StationName"},{"no":"4","column":"油机设备名称","dbfield":"EquipmentName"},{"no":"5","column":"是否运行过","dbfield":"IsRunned"},{"no":"6","column":"运行次数","dbfield":"RunCount"}]', 1, '油机开机运行统计报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(37, 'PRT_RectifierEventStaByFactory', 'StartTime,EndTime,CenterId,GroupId,StationCategory,Vendor', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"局站类型","dbfield":"StationCategory"},{"no":"3","column":"设备厂家","dbfield":"Vendor"},{"no":"4","column":"设备型号","dbfield":"EquipmentStyle"},{"no":"5","column":"模块数量","dbfield":"ModuleCount"},{"no":"6","column":"设备数量","dbfield":"EquipCount"},{"no":"7","column":"模块故障次数","dbfield":"ModuleEventCount"},{"no":"8","column":"设备系统故障次数","dbfield":"SystemEventCount"},{"no":"9","column":"设备系统故障率（%）","dbfield":"EventCountPercent"}]', 1, '整流器故障统计报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(38, 'PRT_ReportEquipmentMask', 'StartTime,EndTime,UserIds,CenterId,GroupId,StationCategoryId,Station,EquipmentCategory', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站分类","dbfield":"StationCategory"},{"no":"4","column":"局站名","dbfield":"StationName"},{"no":"5","column":"设备分类","dbfield":"EquipmentCategory"},{"no":"6","column":"设备名","dbfield":"EquipmentName"},{"no":"7","column":"屏蔽类型","dbfield":"MaskType"},{"no":"8","column":"屏蔽开始时间","dbfield":"StartTime"},{"no":"9","column":"屏蔽结束时间","dbfield":"EndTime"},{"no":"10","column":"屏蔽人","dbfield":"UserName"},{"no":"11","column":"屏蔽原因","dbfield":"Reason"},{"no":"12","column":"星期一","dbfield":"Monday"},{"no":"13","column":"星期二","dbfield":"Tuesday"},{"no":"14","column":"星期三","dbfield":"Wednesday"},{"no":"15","column":"星期四","dbfield":"Thursday"},{"no":"16","column":"星期五","dbfield":"Friday"},{"no":"17","column":"星期六","dbfield":"Saturday"},{"no":"18","column":"星期日","dbfield":"Sunday"}]', 1, '设备屏蔽状态报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(39, 'PRT_ReportHistorySignalLineMain', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,StationNameLike,EquipmentCategory,EquipmentId,EquipmentNameLike,SignalCategory,SignalId,SignalNameLike', '[{"no":"1","column":"监控中心名称","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站名称","dbfield":"StationName"},{"no":"4","column":"设备名称","dbfield":"EquipmentName"},{"no":"5","column":"设备分类","dbfield":"EquipmentCategory"},{"no":"6","column":"信号名称","dbfield":"SignalName"},{"no":"7","column":"信号值","dbfield":"SignalValue"},{"no":"8","column":"记录时间","dbfield":"RecordTime"},{"no":"9","column":"含义","dbfield":"Meanings"},{"no":"10","column":"阀值类型","dbfield":"ThresholdType"}]', 0, '历史数据曲线表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(40, 'PRT_ReportHistoryEventSummary', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EventSeverity', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"StructureName"},{"no":"3","column":"局站类型","dbfield":"StationNameDesc"},{"no":"4","column":"局站名称","dbfield":"StationCategoryDESC"},{"no":"5","column":"告警数量","dbfield":"AlarmCount"}]', 1, '局站告警统计表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(41, 'PRT_ReportHistoryEventSummary', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EventSeverity', '[{"no":"1","column":"监控中心名称","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"StructureName"},{"no":"3","column":"局站名称","dbfield":"StationNameDesc"},{"no":"4","column":"局站分类","dbfield":"StationCategoryDESC"},{"no":"5","column":"警告数","dbfield":"AlarmCount"}]', 0, '局站告警统计饼图');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(42, 'PRT_ReportHistoryEventSummary', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EventSeverity', '[{"no":"1","column":"监控中心名称","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"StructureName"},{"no":"3","column":"局站名称","dbfield":"StationNameDesc"},{"no":"4","column":"局站分类","dbfield":"StationCategoryDESC"},{"no":"5","column":"警告数","dbfield":"AlarmCount"}]', 0, '局站告警统计柱状图');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(43, 'PRT_ReportHistoryEventEquipmentTypeSummary', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EventSeverity', '[{"no":"1","column":"设备类型","dbfield":"EquipmentType"},{"no":"2","column":"告警数量","dbfield":"AlarmCount"}]', 1, '设备类型告警统计表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(44, 'PRT_ReportHistoryEventEquipmentTypeSummary', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EventSeverity', '[{"no":"1","column":"设备类型","dbfield":"EquipmentType"},{"no":"2","column":"告警数量","dbfield":"AlarmCount"}]', 0, '设备类型告警统计饼图');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(45, 'PRT_ReportHistoryEventEquipmentTypeSummary', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EventSeverity', '[{"no":"1","column":"设备类型","dbfield":"EquipmentType"},{"no":"2","column":"告警数量","dbfield":"AlarmCount"}]', 0, '设备类型告警统计柱状图');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(46, 'PRT_QueryRealSignalConfig', 'CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId,SignalCategory,SignalId', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区域","dbfield":"GroupName"},{"no":"3","column":"局站","dbfield":"StationName"},{"no":"4","column":"设备分类","dbfield":"EquipmentCategoryName"},{"no":"5","column":"设备名","dbfield":"EquipmentName"},{"no":"6","column":"信号名","dbfield":"SignalName"},{"no":"7","column":"信号值","dbfield":"SignalValue"},{"no":"8","column":"单位","dbfield":"Unit"},{"no":"9","column":"采集时间","dbfield":"RecordTime"}]', 1, '实时数据记录表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(47, 'PRT_H5StaionStatusOperationInfo', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,UserIds', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区域","dbfield":"GroupName"},{"no":"3","column":"局站分类","dbfield":"StationCategory"},{"no":"4","column":"局站名称","dbfield":"StationName"},{"no":"5","column":"预约开始时间","dbfield":"StartTime"},{"no":"6","column":"预约结束时间","dbfield":"EndTime"},{"no":"7","column":"工程标识","dbfield":"Operation"},{"no":"8","column":"操作人","dbfield":"UserName"},{"no":"9","column":"操作时间","dbfield":"OperationDate"},{"no":"10","column":"工程预约原因","dbfield":"Reason"}]', 1, '局站工程状态操作记录表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(48, 'PRT_H5HouseStatusOperationInfo', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,UserIds', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区域","dbfield":"GroupName"},{"no":"3","column":"局站分类","dbfield":"StationCategory"},{"no":"4","column":"局站名称","dbfield":"StationName"},{"no":"5","column":"机房名称","dbfield":"HouseName"},{"no":"6","column":"预约开始时间","dbfield":"StartTime"},{"no":"7","column":"预约结束时间","dbfield":"EndTime"},{"no":"8","column":"工程标识","dbfield":"Operation"},{"no":"9","column":"操作人","dbfield":"UserName"},{"no":"10","column":"操作时间","dbfield":"OperationDate"},{"no":"11","column":"工程预约原因","dbfield":"Reason"}]', 1, '机房工程状态操作记录表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(49, 'PRT_H5EquipmentStatusOperationInfo', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,EquipmentCategory,UserIds', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区域","dbfield":"GroupName"},{"no":"3","column":"局站名称","dbfield":"StationName"},{"no":"4","column":"设备分类","dbfield":"EquipmentCategory"},{"no":"4","column":"设备名称","dbfield":"EquipmentName"},{"no":"5","column":"预约开始时间","dbfield":"StartTime"},{"no":"6","column":"预约结束时间","dbfield":"EndTime"},{"no":"7","column":"工程标识","dbfield":"Operation"},{"no":"8","column":"操作人","dbfield":"UserName"},{"no":"9","column":"操作时间","dbfield":"OperationDate"},{"no":"10","column":"工程预约原因","dbfield":"Reason"}]', 1, '设备工程状态操作记录表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(50, 'PRT_H5GetProjectStation', 'CenterId,GroupId,StationCategory,StationId', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区域","dbfield":"GroupName"},{"no":"3","column":"局站分类","dbfield":"StationCategoryName"},{"no":"4","column":"局站名称","dbfield":"StationName"},{"no":"5","column":"预约开始时间","dbfield":"StartTime"},{"no":"6","column":"预约结束时间","dbfield":"EndTime"},{"no":"7","column":"工程预约原因","dbfield":"Reason"}]', 1, '局站工程状态报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(51, 'PRT_H5GetProjectHouse', 'CenterId,GroupId,StationCategory,StationId', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区域","dbfield":"GroupName"},{"no":"3","column":"局站分类","dbfield":"StationCategoryName"},{"no":"4","column":"局站名称","dbfield":"StationName"},{"no":"5","column":"机房名称","dbfield":"HouseName"},{"no":"6","column":"预约开始时间","dbfield":"StartTime"},{"no":"7","column":"预约结束时间","dbfield":"EndTime"},{"no":"8","column":"工程预约原因","dbfield":"Reason"}]', 1, '机房工程状态报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(52, 'PRT_H5GetProjectEquipment', 'CenterId,GroupId,StationCategory,StationId,EquipmentCategory,EquipmentId', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站分类","dbfield":"StationCategory"},{"no":"4","column":"局站名称","dbfield":"StationName"},{"no":"5","column":"设备分类","dbfield":"EquipmentCategoryName"},{"no":"6","column":"设备名称","dbfield":"EquipmentName"},{"no":"7","column":"预约开始时间","dbfield":"StartTime"},{"no":"8","column":"预约结束时间","dbfield":"EndTime"},{"no":"9","column":"工程预约原因","dbfield":"Reason"}]', 1, '设备工程状态报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(53, 'PRT_H5StaionMaskOperationRecord', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,UserIds', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区域","dbfield":"GroupName"},{"no":"3","column":"局站分类","dbfield":"StationCategory"},{"no":"4","column":"局站名称","dbfield":"StationName"},{"no":"5","column":"操作人","dbfield":"UserName"},{"no":"6","column":"操作时间","dbfield":"OperationTime"},{"no":"7","column":"操作","dbfield":"OperationName"},{"no":"8","column":"操作内容","dbfield":"OperationContent"}]', 1, '局站屏蔽操作记录报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(54, 'PRT_H5EquipmentMaskOperationRecord', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,UserIds', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区域","dbfield":"GroupName"},{"no":"3","column":"局站分类","dbfield":"StationCategory"},{"no":"4","column":"局站名称","dbfield":"StationName"},{"no":"5","column":"操作人","dbfield":"UserName"},{"no":"6","column":"操作时间","dbfield":"OperationTime"},{"no":"7","column":"操作","dbfield":"OperationName"},{"no":"8","column":"操作内容","dbfield":"OperationContent"}]', 1, '设备屏蔽操作记录报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(55, 'PRT_H5EventMaskOperationRecord', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,UserIds', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区域","dbfield":"GroupName"},{"no":"3","column":"局站分类","dbfield":"StationCategory"},{"no":"4","column":"局站名称","dbfield":"StationName"},{"no":"5","column":"操作人","dbfield":"UserName"},{"no":"6","column":"操作时间","dbfield":"OperationTime"},{"no":"7","column":"操作","dbfield":"OperationName"},{"no":"8","column":"操作内容","dbfield":"OperationContent"}]', 1, '事件屏蔽操作记录报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(56, 'PRT_H5StationMask', 'CenterId,GroupId,StationCategory,StationId', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站","dbfield":"StationName"},{"no":"4","column":"工程或屏蔽","dbfield":"EngineeringOrMask"},{"no":"5","column":"开始时间","dbfield":"StartTime"},{"no":"6","column":"结束时间","dbfield":"EndTime"}]', 1, '局站屏蔽状态报表');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(57, 'InfluxDb,s2history,PRT_HistorySignalDataInfluxDb', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,StationNameLike,EquipmentCategory,EquipmentId,EquipmentNameLike,SignalCategory,SignalId,SignalNameLike,ThresholdType', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站","dbfield":"StationName"},{"no":"4","column":"设备分类","dbfield":"EquipmentCategory"},{"no":"5","column":"设备名","dbfield":"EquipmentName"},{"no":"6","column":"信号ID","dbfield":"SignalId"},{"no":"7","column":"信号名","dbfield":"SignalName"},{"no":"8","column":"信号值","dbfield":"SignalValue"},{"no":"9","column":"记录时间","dbfield":"RecordTime"},{"no":"10","column":"信号含义","dbfield":"Meanings"},{"no":"11","column":"存盘方式","dbfield":"SignalThresholdType"}]', 1, '历史数据记录表InfluxDB');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(58, 'InfluxDb,s2history,PRT_HistorySignalDataInfluxDb', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,StationNameLike,EquipmentCategory,EquipmentId,EquipmentNameLike,SignalCategory,SignalId,SignalNameLike,ThresholdType', '[{"no":"1","column":"监控中心","dbfield":"CenterName"},{"no":"2","column":"行政区划","dbfield":"GroupName"},{"no":"3","column":"局站名称","dbfield":"StationName"},{"no":"4","column":"设备名称","dbfield":"EquipmentName"},{"no":"5","column":"设备分类","dbfield":"EquipmentCategory"},{"no":"6","column":"信号名称","dbfield":"SignalName"},{"no":"7","column":"信号值","dbfield":"SignalValue"},{"no":"8","column":"记录时间","dbfield":"RecordTime"},{"no":"9","column":"信号含义","dbfield":"Meanings"},{"no":"10","column":"存盘方式","dbfield":"SignalThresholdType"}]', 0, '历史数据曲线表InfluxDB');
INSERT INTO tbl_reportprocedure (ReportId, ProcedureName, ParameterString, ReportColumn, ReportType, Description) VALUES(59, 'InfluxDb,phoenixcomplex,PRT_FetchComplexIndexConfig', 'StartTime,EndTime,CenterId,GroupId,StationCategory,StationId,ComplexIndexId,TimeGranularity', '[{"no":"1","column":"指标资源结构","dbfield":"ResourceStructureName"},{"no":"2","column":"指标ID","dbfield":"ComplexIndexId"},{"no":"3","column":"指标名","dbfield":"ComplexIndexName"},{"no":"4","column":"使用差值计算","dbfield":"CalcType"},{"no":"5","column":"单位","dbfield":"Unit"},{"no":"6","column":"值","dbfield":"IndexValue"},{"no":"7","column":"时间","dbfield":"CalTime"}]', 0, '指标报表InfluxDB');

INSERT INTO tbl_reportrole (ReportRoleId, RoleName, Description) VALUES (1,'管理员','');
INSERT INTO tbl_reportrole (ReportRoleId, RoleName, Description) VALUES (2,'分析人员','');
INSERT INTO tbl_reportrole (ReportRoleId, RoleName, Description) VALUES (3,'维护人员','');
INSERT INTO tbl_reportrole (ReportRoleId, RoleName, Description) VALUES (4,'工程人员','');
INSERT INTO tbl_reportrole (ReportRoleId, RoleName, Description) VALUES (5,'值班人员','');

INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (1,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (1,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (1,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (1,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (1,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (2,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (2,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (2,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (2,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (2,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (3,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (3,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (3,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (3,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (3,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (4,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (4,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (4,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (4,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (4,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (5,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (5,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (5,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (5,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (5,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (6,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (6,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (6,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (6,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (6,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (7,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (7,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (7,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (7,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (7,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (8,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (8,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (8,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (8,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (8,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (9,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (9,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (9,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (9,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (9,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (10,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (10,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (10,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (10,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (10,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (11,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (11,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (11,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (11,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (11,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (12,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (12,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (12,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (12,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (12,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (13,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (13,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (13,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (13,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (13,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (14,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (14,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (14,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (14,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (14,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (15,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (15,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (15,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (15,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (15,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (16,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (16,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (16,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (16,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (16,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (17,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (17,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (17,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (17,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (17,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (18,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (18,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (18,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (18,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (18,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (19,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (19,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (19,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (19,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (19,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (20,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (20,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (20,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (20,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (20,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (21,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (21,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (21,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (21,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (21,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (22,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (22,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (22,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (22,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (22,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (23,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (23,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (23,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (23,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (23,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (24,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (24,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (24,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (24,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (24,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (25,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (25,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (25,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (25,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (25,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (26,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (26,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (26,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (26,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (26,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (27,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (27,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (27,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (27,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (27,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (28,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (28,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (28,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (28,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (28,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (29,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (29,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (29,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (29,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (29,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (30,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (30,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (30,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (30,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (30,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (31,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (31,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (31,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (31,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (31,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (32,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (32,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (32,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (32,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (32,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (33,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (33,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (33,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (33,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (33,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (34,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (34,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (34,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (34,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (34,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (35,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (35,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (35,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (35,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (35,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (36,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (36,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (36,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (36,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (36,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (37,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (37,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (37,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (37,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (37,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (38,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (38,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (38,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (38,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (38,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (39,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (39,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (39,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (39,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (39,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (40,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (40,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (40,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (40,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (40,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (41,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (41,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (41,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (41,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (41,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (42,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (42,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (42,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (42,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (42,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (43,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (43,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (43,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (43,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (43,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (44,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (44,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (44,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (44,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (44,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (45,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (45,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (45,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (45,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (45,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (46,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (46,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (46,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (46,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (46,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (47,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (47,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (47,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (47,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (47,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (48,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (48,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (48,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (48,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (48,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (49,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (49,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (49,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (49,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (49,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (50,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (50,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (50,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (50,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (50,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (51,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (51,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (51,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (51,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (51,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (52,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (52,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (52,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (52,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (52,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (53,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (53,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (53,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (53,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (53,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (54,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (54,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (54,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (54,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (54,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (55,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (55,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (55,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (55,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (55,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (56,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (56,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (56,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (56,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (56,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (57,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (57,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (57,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (57,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (57,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (58,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (58,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (58,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (58,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (58,5);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (59,1);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (59,2);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (59,3);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (59,4);
INSERT INTO tbl_reportrolemap (ReportId, ReportRoleId) VALUES (59,5);

INSERT INTO tbl_reportroleusermap (ReportRoleId, UserId) VALUES (1,-1);
INSERT INTO tbl_reportroleusermap (ReportRoleId, UserId) VALUES (2,-1);
INSERT INTO tbl_reportroleusermap (ReportRoleId, UserId) VALUES (3,-1);
INSERT INTO tbl_reportroleusermap (ReportRoleId, UserId) VALUES (4,-1);
INSERT INTO tbl_reportroleusermap (ReportRoleId, UserId) VALUES (5,-1);

INSERT INTO tbl_suitreport (SuitReportId, SuitReportName, Description, CreateUserId, CreateUserName, CreateTime, UpdateUserId, UpdateUserName, UpdateTime) VALUES (1,'考核报表','考核报表',-1,'admin','2024-06-11 16:23:55',-1,'admin','2024-06-11 16:23:55');

INSERT INTO tbl_suitreportmap (SuitReportId, ReportId) VALUES (1,1);
INSERT INTO tbl_suitreportmap (SuitReportId, ReportId) VALUES (1,2);
INSERT INTO tbl_suitreportmap (SuitReportId, ReportId) VALUES (1,3);
INSERT INTO tbl_suitreportmap (SuitReportId, ReportId) VALUES (1,4);
INSERT INTO tbl_suitreportmap (SuitReportId, ReportId) VALUES (1,5);

INSERT INTO tbl_suitreportrolemap (SuitReportId, ReportRoleId) VALUES (1,12);
INSERT INTO tbl_suitreportrolemap (SuitReportId, ReportRoleId) VALUES (1,14);
INSERT INTO tbl_suitreportrolemap (SuitReportId, ReportRoleId) VALUES (1,15);

