CREATE TABLE batterycellmodel
(
    batterycellmodelid          serial NOT NULL PRIMARY KEY,
    vendor                      character varying(128),
    model                       character varying(255),
    voltagetype                 integer,
    ratedvoltage                numeric(5, 2),
    ratedcapacity               numeric(6, 2),
    initialir                   numeric(10, 2),
    floatchargevoltage          numeric(5, 2),
    evenchargevoltage           numeric(5, 2),
    tempcompensationfactor      numeric(3, 2),
    terminationvoltage          numeric(5, 2),
    chargingcurrentlimitfactor  numeric(5, 2),
    dischargecurrentlimitfactor numeric(5, 2),
    length                      integer,
    width                       integer,
    height                      integer,
    weight                      numeric(10, 2)
);
COMMENT ON COLUMN batterycellmodel.vendor IS '电池厂商';
COMMENT ON COLUMN batterycellmodel.model IS '电池型号';
COMMENT ON COLUMN batterycellmodel.voltagetype IS '电压类型';
COMMENT ON COLUMN batterycellmodel.ratedvoltage IS '额定电压';
COMMENT ON COLUMN batterycellmodel.ratedcapacity IS '额定容量';
COMMENT ON COLUMN batterycellmodel.initialir IS '初始内阻';
COMMENT ON COLUMN batterycellmodel.floatchargevoltage IS '浮充电压';
COMMENT ON COLUMN batterycellmodel.evenchargevoltage IS '均充电压';
COMMENT ON COLUMN batterycellmodel.tempcompensationfactor IS '温度补偿系数';
COMMENT ON COLUMN batterycellmodel.terminationvoltage IS '终止电压';
COMMENT ON COLUMN batterycellmodel.chargingcurrentlimitfactor IS '充电限流系数';
COMMENT ON COLUMN batterycellmodel.dischargecurrentlimitfactor IS '放电限流系数';
COMMENT ON COLUMN batterycellmodel.length IS '长度';
COMMENT ON COLUMN batterycellmodel.width IS '宽度';
COMMENT ON COLUMN batterycellmodel.height IS '高度';
COMMENT ON COLUMN batterycellmodel.weight IS '重量';

CREATE TABLE batterydischargerecord
(
    batterydischargerecordid serial NOT NULL PRIMARY KEY,
    batterystringid          integer,
    equipmentid              integer,
    startdischargetime       timestamp,
    enddischargetime         timestamp,
    signalid                 integer
);
COMMENT ON COLUMN batterydischargerecord.batterystringid IS '电池配置Id';
COMMENT ON COLUMN batterydischargerecord.equipmentid IS '设备ID';
COMMENT ON COLUMN batterydischargerecord.startdischargetime IS '放电开始时间';
COMMENT ON COLUMN batterydischargerecord.enddischargetime IS '放电结束时间';
COMMENT ON COLUMN batterydischargerecord.signalid IS '放电触发的信号ID';

CREATE TABLE batterystring
(
    batterystringid        serial NOT NULL PRIMARY KEY,
    batterystringname      character varying(128),
    cellcount              integer,
    equipmentid            integer,
    batterycellmodelid     integer,
    standbypower           integer,
    startusingtime         timestamp,
    currenttransformertype character varying(255),
    vendor                 character varying(255),
    model                  character varying(255),
    ratedvoltage           numeric(5, 2),
    ratedcapacity          numeric(6, 2),
    initialir              numeric(10, 2),
    floatchargevoltage     numeric(5, 2),
    evenchargevoltage      numeric(5, 2),
    tempcompensationfactor numeric(3, 2),
    terminationvoltage     numeric(10, 2),
    maxchargingcurrent     numeric(5, 2),
    maxfloatchargevoltage  numeric(10, 2),
    weight                 numeric(10, 2)
);
COMMENT ON COLUMN batterystring.batterystringname IS '电池配置名称';
COMMENT ON COLUMN batterystring.cellcount IS '电池节数量';
COMMENT ON COLUMN batterystring.equipmentid IS '设备ID';
COMMENT ON COLUMN batterystring.batterycellmodelid IS '电池模型ID';
COMMENT ON COLUMN batterystring.standbypower IS '备电时间';
COMMENT ON COLUMN batterystring.startusingtime IS '启用时间';
COMMENT ON COLUMN batterystring.currenttransformertype IS '电流互感器类型';
COMMENT ON COLUMN batterystring.vendor IS '厂家';
COMMENT ON COLUMN batterystring.model IS '型号';
COMMENT ON COLUMN batterystring.ratedvoltage IS '额定电压';
COMMENT ON COLUMN batterystring.ratedcapacity IS '额定容量';
COMMENT ON COLUMN batterystring.initialir IS '初始内阻';
COMMENT ON COLUMN batterystring.floatchargevoltage IS '浮充电压';
COMMENT ON COLUMN batterystring.evenchargevoltage IS '均充电压';
COMMENT ON COLUMN batterystring.tempcompensationfactor IS '温度补偿系数';
COMMENT ON COLUMN batterystring.terminationvoltage IS '终止电压';
COMMENT ON COLUMN batterystring.maxchargingcurrent IS '最大均充充电电流';
COMMENT ON COLUMN batterystring.maxfloatchargevoltage IS '最大浮充电压';
COMMENT ON COLUMN batterystring.weight IS '重量';