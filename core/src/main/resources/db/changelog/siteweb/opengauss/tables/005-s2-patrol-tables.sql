CREATE TABLE tbl_patrolallrecord_1
(
    centerid              integer               NOT NULL,
    centername            character varying(510),
    groupid               integer               NOT NULL,
    groupname             character varying(510),
    stationid             integer               NOT NULL,
    stationname           character varying(510),
    equipmentcategoryname character varying(510),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(510),
    signalid              integer               NOT NULL,
    signalname            character varying(510),
    signalvalue           character varying(128),
    recordtime            timestamp,
    unit                  character varying(256),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(16) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(72) NOT NULL
);

CREATE TABLE tbl_patrolallrecord_2
(
    centerid              integer               NOT NULL,
    centername            character varying(510),
    groupid               integer               NOT NULL,
    groupname             character varying(510),
    stationid             integer               NOT NULL,
    stationname           character varying(510),
    equipmentcategoryname character varying(510),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(510),
    signalid              integer               NOT NULL,
    signalname            character varying(510),
    signalvalue           character varying(128),
    recordtime            timestamp,
    unit                  character varying(256),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(16) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(72) NOT NULL
);

CREATE TABLE tbl_patrolallrecord_3
(
    centerid              integer               NOT NULL,
    centername            character varying(510),
    groupid               integer               NOT NULL,
    groupname             character varying(510),
    stationid             integer               NOT NULL,
    stationname           character varying(510),
    equipmentcategoryname character varying(510),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(510),
    signalid              integer               NOT NULL,
    signalname            character varying(510),
    signalvalue           character varying(128),
    recordtime            timestamp,
    unit                  character varying(256),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(16) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(72) NOT NULL
);

CREATE TABLE tbl_patrolallrecord_4
(
    centerid              integer               NOT NULL,
    centername            character varying(510),
    groupid               integer               NOT NULL,
    groupname             character varying(510),
    stationid             integer               NOT NULL,
    stationname           character varying(510),
    equipmentcategoryname character varying(510),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(510),
    signalid              integer               NOT NULL,
    signalname            character varying(510),
    signalvalue           character varying(128),
    recordtime            timestamp,
    unit                  character varying(256),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(16) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(72) NOT NULL
);

CREATE TABLE tbl_patrolallrecord_5
(
    centerid              integer               NOT NULL,
    centername            character varying(510),
    groupid               integer               NOT NULL,
    groupname             character varying(510),
    stationid             integer               NOT NULL,
    stationname           character varying(510),
    equipmentcategoryname character varying(510),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(510),
    signalid              integer               NOT NULL,
    signalname            character varying(510),
    signalvalue           character varying(128),
    recordtime            timestamp,
    unit                  character varying(256),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(16) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(72) NOT NULL
);

CREATE TABLE tbl_patrolallrecord_6
(
    centerid              integer               NOT NULL,
    centername            character varying(510),
    groupid               integer               NOT NULL,
    groupname             character varying(510),
    stationid             integer               NOT NULL,
    stationname           character varying(510),
    equipmentcategoryname character varying(510),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(510),
    signalid              integer               NOT NULL,
    signalname            character varying(510),
    signalvalue           character varying(128),
    recordtime            timestamp,
    unit                  character varying(256),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(16) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(72) NOT NULL
);

CREATE TABLE tbl_patrolallrecord_7
(
    centerid              integer               NOT NULL,
    centername            character varying(510),
    groupid               integer               NOT NULL,
    groupname             character varying(510),
    stationid             integer               NOT NULL,
    stationname           character varying(510),
    equipmentcategoryname character varying(510),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(510),
    signalid              integer               NOT NULL,
    signalname            character varying(510),
    signalvalue           character varying(128),
    recordtime            timestamp,
    unit                  character varying(256),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(16) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(72) NOT NULL
);

CREATE TABLE tbl_patrolallrecord_8
(
    centerid              integer               NOT NULL,
    centername            character varying(510),
    groupid               integer               NOT NULL,
    groupname             character varying(510),
    stationid             integer               NOT NULL,
    stationname           character varying(510),
    equipmentcategoryname character varying(510),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(510),
    signalid              integer               NOT NULL,
    signalname            character varying(510),
    signalvalue           character varying(128),
    recordtime            timestamp,
    unit                  character varying(256),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(16) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(72) NOT NULL
);

CREATE TABLE tbl_patrolallrecord_9
(
    centerid              integer               NOT NULL,
    centername            character varying(510),
    groupid               integer               NOT NULL,
    groupname             character varying(510),
    stationid             integer               NOT NULL,
    stationname           character varying(510),
    equipmentcategoryname character varying(510),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(510),
    signalid              integer               NOT NULL,
    signalname            character varying(510),
    signalvalue           character varying(128),
    recordtime            timestamp,
    unit                  character varying(256),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(16) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(72) NOT NULL
);

CREATE TABLE tbl_patrolallrecord_10
(
    centerid              integer               NOT NULL,
    centername            character varying(510),
    groupid               integer               NOT NULL,
    groupname             character varying(510),
    stationid             integer               NOT NULL,
    stationname           character varying(510),
    equipmentcategoryname character varying(510),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(510),
    signalid              integer               NOT NULL,
    signalname            character varying(510),
    signalvalue           character varying(128),
    recordtime            timestamp,
    unit                  character varying(256),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(16) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(72) NOT NULL
);

CREATE TABLE tbl_patrolallrecord_11
(
    centerid              integer               NOT NULL,
    centername            character varying(510),
    groupid               integer               NOT NULL,
    groupname             character varying(510),
    stationid             integer               NOT NULL,
    stationname           character varying(510),
    equipmentcategoryname character varying(510),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(510),
    signalid              integer               NOT NULL,
    signalname            character varying(510),
    signalvalue           character varying(128),
    recordtime            timestamp,
    unit                  character varying(256),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(16) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(72) NOT NULL
);

CREATE TABLE tbl_patrolallrecord_12
(
    centerid              integer               NOT NULL,
    centername            character varying(510),
    groupid               integer               NOT NULL,
    groupname             character varying(510),
    stationid             integer               NOT NULL,
    stationname           character varying(510),
    equipmentcategoryname character varying(510),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(510),
    signalid              integer               NOT NULL,
    signalname            character varying(510),
    signalvalue           character varying(128),
    recordtime            timestamp,
    unit                  character varying(256),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(16) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(72) NOT NULL
);

CREATE TABLE tbl_patrolcalop
(
    calopid     integer                NOT NULL PRIMARY KEY,
    caloperator character varying(256) NOT NULL,
    meaning     character varying(510)
);

CREATE TABLE tbl_patrolcronexpression
(
    cronid         integer NOT NULL PRIMARY KEY,
    cronexpression character varying(256) DEFAULT ''::CHARACTER VARYING NOT NULL,
    meaning        character varying(256) DEFAULT ''::CHARACTER VARYING NOT NULL
);

CREATE TABLE tbl_patrolexrecord
(
    centerid              integer               NOT NULL,
    centername            character varying(510),
    groupid               integer               NOT NULL,
    groupname             character varying(510),
    stationid             integer               NOT NULL,
    stationname           character varying(510),
    equipmentcategoryname character varying(510),
    equipmentid           integer               NOT NULL,
    equipmentname         character varying(510),
    signalid              integer               NOT NULL,
    signalname            character varying(510),
    signalvalue           character varying(128),
    recordtime            timestamp,
    unit                  character varying(256),
    limitdown             double precision,
    limitdowncalopid      integer               NOT NULL,
    limitup               double precision,
    limitupcalopid        integer               NOT NULL,
    bypercentage          integer               NOT NULL,
    ratedvalue            double precision,
    warninglevelid        integer,
    reasonable            integer               NOT NULL,
    ispoweroffalarm       character varying(16) DEFAULT ''::CHARACTER VARYING NOT NULL,
    createtime            timestamp,
    sn                    character varying(72) NOT NULL
);

CREATE TABLE tbl_patrolgroup
(
    groupid         integer                NOT NULL PRIMARY KEY,
    groupname       character varying(256) NOT NULL,
    baseequipmentid integer,
    basetypeid      numeric(12, 0),
    note            character varying(510)
);

CREATE TABLE tbl_patrolgroupbasesignalmap
(
    groupid         integer NOT NULL,
    baseequipmentid integer,
    basetypeid      numeric(10, 0)
);

CREATE TABLE tbl_patrolgroupparameters
(
    groupid        integer NOT NULL,
    stationtypeids character varying(48),
    stationids     text,
    equipmentids   text,
    signalids      text
);

CREATE TABLE tbl_patrolgrouprulemap
(
    groupid integer NOT NULL,
    ruleid  integer NOT NULL
);

CREATE TABLE tbl_patrolrule
(
    ruleid           serial                 NOT NULL PRIMARY KEY,
    rulename         character varying(256) NOT NULL,
    limitdown        double precision,
    limitdowncalopid integer                NOT NULL,
    limitup          double precision,
    limitupcalopid   integer                NOT NULL,
    unitid           integer,
    warninglevelid   integer                NOT NULL,
    bypercentage     integer                NOT NULL,
    ratedvalue       double precision,
    baseequipmentid  integer,
    basetypeid       numeric(12, 0),
    note             character varying(510),
    description      character varying(510),
    equipmentLogicClassId integer,
    standardDicId integer
);

CREATE TABLE tbl_patroltask
(
    taskid         serial                 NOT NULL PRIMARY KEY,
    taskname       character varying(256) NOT NULL,
    cronid         integer                NOT NULL,
    groupid        integer                NOT NULL,
    ispoweroffsave integer DEFAULT 0      NOT NULL,
    note           character varying(510)
);

CREATE TABLE tbl_patrolunit
(
    unitid     integer                NOT NULL PRIMARY KEY,
    unitsymbol character varying(256) NOT NULL,
    meaning    character varying(510)
);

CREATE TABLE tbl_patrolwarninglevel
(
    levelid integer NOT NULL PRIMARY KEY,
    meaning character varying(510)
);

CREATE TABLE tbl_patrolgroupstandardsignalmap (
    groupId integer NOT NULL,
    equipmentLogicClassId integer DEFAULT NULL,
    standardDicId integer DEFAULT NULL
);

CREATE TABLE tbl_patrolstandardgroup (
    groupId serial NOT NULL PRIMARY KEY,
    groupName character varying(256) NOT NULL,
    equipmentLogicClassId integer ,
    standardDicId integer ,
    note character varying(256)
);