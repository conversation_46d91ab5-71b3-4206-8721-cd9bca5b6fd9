INSERT INTO `energy_dataentry` VALUES (1,'FeeControlPeriod','energyfeecontrolperiod',NULL,NULL,_binary '',_binary ''),
(2,'EnergyEfficiencyGradeElectricityRatio','energyefficiencygradeelectricityratio','GBT 40879-2021',NULL,_binary '',_binary ''),
(3,'InnerWorkingCondition','innerworkingcondition','GBT 40879-2021',NULL,_binary '',_binary ''),
(4,'OuterWorkingCondition','outerworkingcondition','GBT 40879-2021',NULL,_binary '',_binary ''),
(5,'TemperatureRange','temperaturerange','GBT 40879-2021',NULL,_binary '',_binary ''),
(7,'EnergyType','energytostandardcoalcoefficient','GBT2589-2020',NULL,_binary '',_binary ''),
(8,'WorkingFluidToStandardCoalCoefficient','workingfluidtostandardcoalcoefficient','GBT2589-2020',NULL,_binary '',_binary ''),
(9,'StandardCoalCO2EmissionCoefficient','standardcoalCO2emissioncoefficient',NULL,NULL,_binary '',_binary ''),
(10,'ComplexindexCategory','complexindexcategory',NULL,NULL,_binary '',_binary '');

-- 国外不适用，删除
-- (6,'ChinaTemperatureDistributionCoefficient','chinatemperaturedistributioncoefficient','GBT 40879-2021',NULL,_binary '',_binary ''),

INSERT INTO `energy_dataitem` VALUES (1,0,1,1,'tip','tip',0,0,'',NULL,NULL,NULL,NULL,NULL,0,0,0),
(2,0,1,2,'peak','peak',0,0,'',NULL,NULL,NULL,NULL,NULL,0,0,0),
(3,0,1,3,'flat','flat',0,0,'',NULL,NULL,NULL,NULL,NULL,0,0,0),
(4,0,1,4,'valley','valley',0,0,'',NULL,NULL,NULL,NULL,NULL,0,0,0),
(5,0,2,1,'1.2','1.2',0,0,'Level 1',NULL,NULL,NULL,NULL,NULL,0,0,0),
(6,0,2,2,'1.3','1.3',0,0,'Level 2',NULL,NULL,NULL,NULL,NULL,0,0,0),
(7,0,2,3,'1.5','1.5',0,0,'Level 3',NULL,NULL,NULL,NULL,NULL,0,0,0),
(8,0,3,1,'18','18',0,0,'Minimum',NULL,NULL,NULL,NULL,NULL,0,0,0),
(9,0,3,2,'27','27',0,0,'Maximum',NULL,NULL,NULL,NULL,NULL,0,0,0),
(10,0,4,1,'35','35',0,0,'a',NULL,NULL,NULL,NULL,NULL,0,0,0),
(11,0,4,2,'25','25',0,0,'b',NULL,NULL,NULL,NULL,NULL,0,0,0),
(12,0,4,3,'15','15',0,0,'c',NULL,NULL,NULL,NULL,NULL,0,0,0),
(13,0,4,4,'5','5',0,0,'d',NULL,NULL,NULL,NULL,NULL,0,0,0),
(14,0,4,5,'-5','-5',0,0,'e',NULL,NULL,NULL,NULL,NULL,0,0,0),
(15,0,5,1,'≥30','≥30',0,0,'Ta',NULL,NULL,NULL,NULL,NULL,0,0,0),
(16,0,5,2,'≥20,＜30','≥20,＜30',0,0,'Tb',NULL,NULL,NULL,NULL,NULL,0,0,0),
(17,0,5,3,'≥10,＜20','≥10,＜20',0,0,'Tc',NULL,NULL,NULL,NULL,NULL,0,0,0),
(18,0,5,4,'≥0,＜10','≥0,＜10',0,0,'Td',NULL,NULL,NULL,NULL,NULL,0,0,0),
(19,0,5,5,'＜0','＜0',0,0,'Te',NULL,NULL,NULL,NULL,NULL,0,0,0),
(20,0,6,1,'city1','example city',0,0,'','0.033','0.205','0.301','0.257','0.204',1,0,0),
(21,0,6,2,'city2','example city',0,0,'','0.008','0.331','0.373','0.282','0.006',1,0,0),
(22,0,6,3,'city3','example city',0,0,'','0.093','0.272','0.245','0.249','0.142',1,0,0),
(23,0,6,4,'city4','example city',0,0,'','0.022','0.191','0.227','0.187','0.374',1,0,0),
(24,0,6,5,'city5','example city',0,0,'','0.006','0.191','0.248','0.185','0.371',1,0,0),
(60,0,7,1,'Electricity (equivalent value)','',0,0,'','0.1229','kgce/(kw.h)',NULL,NULL,NULL,0,0,1),
(61,0,7,2,'Heat (equivalent value)','',0,0,'','0.03412','kgce/MJ',NULL,NULL,NULL,0,0,1),
(62,0,7,3,'Water resources','',0,0,'','0.0857','kgce/t',NULL,NULL,NULL,0,0,1),
(63,0,7,4,'Raw coal','',0,0,'','0.7143','Kgce/kg',NULL,NULL,NULL,0, 0,1),
(64,0,7,5,'Cleaned coal','',0,0,'','0.9','Kgce/kg',NULL,NULL,NULL,0,0,1),
(65,0,7,6,'Coal in washing','',0,0,'','0.2857','Kgce/kg',NULL,NULL,NULL,0,0,1),
(66,0,7,7,'Slime','',0,0,'','0.2857','0.2857kgce/kg～0.4286kgce/kg',NULL,NULL,NULL,0,0,1),
(67,0,7,8,'gangue (used as energy)','',0,0,'','0.2857','Kgce/kg',NULL,NULL,NULL,0,0,1),
(68,0,7,9,'Coke (dry full coke)','',0,0,'','0.9714','Kgce/kg',NULL,NULL,NULL,0,0,1),
(69,0,7,10,'Coal tar','',0,0,'','1.1429','Kgce/kg',NULL,NULL,NULL,0 ,0,1),
(70,0,7,11,'Crude oil','',0,0,'','1.4286','Kgce/kg',NULL,NULL,NULL,0, 0,1),
(71,0,7,12,'Fuel oil','',0,0,'','1.4286','Kgce/kg',NULL,NULL,NULL,0 ,0,1),
(72,0,7,13,'Gasoline','',0,0,'','1.4714','Kgce/kg',NULL,NULL,NULL,0, 0,1),
(73,0,7,14,'Kerosene','',0,0,'','1.4714','Kgce/kg',NULL,NULL,NULL,0, 0,1),
(74,0,7,15,'Diesel','',0,0,'','1.4571','Kgce/kg',NULL,NULL,NULL,0, 0,1),
(75,0,7,16,'Natural gas','',0,0,'','1.1','1.1000kgce/m3~1.3300kgce/m3',NULL,NULL,NULL,0,0,1),
(76,0,7,17,'LNG','',0,0,'','1.7572','Kgce/kg',NULL,NULL,NULL,0 ,0,1),
(77,0,7,18,'LPG','',0,0,'','1.7143','Kgce/kg',NULL,NULL,NULL,0,0,1),
(78,0,7,19,'Refinery dry gas','',0,0,'','1.5714','Kgce/kg',NULL,NULL,NULL,0,0,1),
(79,0,7,20,'Coke oven gas','',0,0,'','0.5714','0.5714kgce/m3～0.6143kgce/m3',NULL,NULL,NULL,0,0,1),
(80,0,7,21,'Blast furnace gas','',0,0,'','0.1286','kgce/m3',NULL,NULL,NULL,0 ,0,1),
(81,0,7,22,'Producer gas','',0,0,'','0.1786','kgce/m3',NULL,NULL,NULL,0,0,1),
(82,0,7,23,'Heavy oil catalytic cracking gas','',0,0,'','0.6571','kgce/m3',NULL,NULL,NULL,0,0,1),
(83,0,7,24,'Heavy oil pyrolysis gas','',0,0,'','1.2143','kgce/m3',NULL,NULL,NULL,0,0,1),
(84,0,7,25,'Coke gas production','',0,0,'','0.5571','kgce/m3',NULL,NULL,NULL,0,0,1),
(85,0,7,26,'Pressure gasification gas','',0,0,'','0.5143','kgce/m3',NULL,NULL,NULL,0,0,1),
(86,0,7,27,'Water gas','',0,0,'','0.3571','kgce/m3',NULL,NULL,NULL,0, 0,1),
(87,0,7,28,'Crude benzene','',0,0,'','1.4286','kgce/kg',NULL,NULL,NULL,0 ,0,1),
(88,0,7,29,'Methanol (used as fuel)','',0,0,'','0.6794','Kgce/kg',NULL,NULL,NULL,0,0,1),
(89,0,7,30,'Ethanol (used as fuel)','',0,0,'','0.9144','Kgce/kg',NULL,NULL,NULL,0,0,1),
(90,0,7,31,'Hydrogen','',0,0,'','0.3329','Kgce/m3',NULL,NULL,NULL,0, 0,1),
(91,0,7,32,'Biogas','',0,0,'','0.7143','0.7143kgce/m3～0.8286kgce/m3',NULL,NULL,NULL,0,0,1),
(92,0,8,1,'New Water','',0,0,'','0.2571','kgce/t',NULL,NULL,NULL,0 ,0,0),
(93,0,8,2,'Softened water','',0,0,'','0.4857','kgce/t',NULL,NULL,NULL,0 ,0,0),
(94,0,8,3,'Deoxygenated water','',0,0,'','0.9714','kgce/t',NULL,NULL,NULL,0,0,0),
(95,0,8,4,'Compressed air','',0,0,'','0.04','kgce/m3',NULL,NULL,NULL,0 ,0,0),
(96,0,8,5,'Oxygen','',0,0,'','0.4','kgce/m3',NULL,NULL,NULL,0, 0,0),
(97,0,8,6,'Nitrogen (as a by-product)','',0,0,'','0.4','kgce/m3',NULL,NULL,NULL ,0,0,0),
(98,0,8,7,'Nitrogen (when the main product)','',0,0,'','0.6714','kgce/m3',NULL,NULL,NULL ,0,0,0),
(99,0,8,8,'CO2','',0,0,'','0.2143','kgce/m3',NULL,NULL,NULL,0 ,0,0),
(100,0,8,9,'Acetylene','',0,0,'','8.3143','kgce/m3',NULL,NULL,NULL,0, 0,0),
(101,0,8,10,'Calcium carbide','',0,0,'','2.0786','kgce/kg',NULL,NULL,NULL,0, 0,0),
(102,0,9,1,'Standard coal CO2 emission coefficient','',0,0,'','0.67','tc/tce',NULL,NULL,NULL, 0,0,0),
(103,0,10,1,'Total','',0,0,'',NULL,NULL,NULL,NULL,NULL,0,0,0),
(104,0,10,2,'Efficiency','',0,0,'',NULL,NULL,NULL,NULL,NULL,0,0,0),
(105,0,10,3,'Option','',0,0,'',NULL,NULL,NULL,NULL,NULL,0,0,0),
(106,0,10,4,'Aircon Energy-Saving','',0,0,'',NULL,NULL,NULL,NULL,NULL,0,0,0),
(107,0,10,100,'Other','',0,0,'',NULL,NULL,NULL,NULL,NULL,0,0,0),
(108,0,7,100,'Photovoltaic','',0,0,'','0','',NULL,NULL,NULL,0,0,0),
(109,0,7,101,'Wind energy','',0,0,'','0','',NULL,NULL,NULL,0,0,0),
(110,0,11,1,'m²','',0,0,'',NULL,NULL,NULL,NULL,NULL,0,0,0),
(111,0,11,2,'t','',0,0,'',NULL,NULL,NULL,NULL,NULL,0,0,0),
(112,0,11,3,'Ten Thousand $','',0,0,'',NULL,NULL,NULL,NULL,NULL,0,0,0),
(113,0,11,4,'GJ','',0,0,'',NULL,NULL,NULL,NULL,NULL,0,0,0),
(114,0,12,1,'ComplexAbnormalAlarm','',0,0,'','0',NULL,NULL,NULL,NULL,0,0,0);


-- 指标business和definition映射表
insert into energy_businessdefinitionmap(BusinessTypeId,ComplexIndexDefinitionId,ComplexIndexDefinitionTypeId,ExtendField1) values
(2,6,1,null),(2,5,2,null),(2,7,3,null),(2,8,3,null),(2,9,3,null),(2,21,3,null),(2,22,3,null),(2,23,3,null),
(2,1,100,null),(2,2,100,null),(2,3,100,null),(2,13,100,null),(2,14,100,null),(2,15,100,null),(2,16,100,null),(2,20,100,null),
(3,24,1,null),(3,25,2,null),(3,26,3,null),(3,27,3,null),(3,28,3,null),(3,29,3,null),(3,30,3,null),(3,31,100,null),
(4,32,1,null),(4,33,2,null),(4,34,3,null),(4,35,3,null),(4,36,3,null),(4,37,3,null),(4,38,3,null),(4,39,100,null);

-- 能耗看板首页IDC
insert into graphicpage(Id, Type, GroupId, PageCategory, BaseEquipmentId, ObjectId,  Name, AppendName, TemplateId, Data, Style, Children, Description, RouterType, CrumbsRouterType, PageCanFullScreen, SceneId, IsDefault)
VALUES ('2010', 'page', '0', '8', '-1', '2', 'EnergyDashboard', '', NULL, '{\"api\":\"baseEquipmentId=undefined\",\"time\":\"1000\"}', '{\"width\": \"1680\",\"height\": \"900\",\"background\":\"#fff\"}', '[{\"compIndex\":62,\"name\":\"Dashboard\",\"type\":\"card\",\"data\":{\"text_val\":\"PUE\",\"card_val\":\"card\",\"sky_val\":\"card\",\"panelStyleType\":\"\"},\"style\":{\"height\":430,\"width\":550,\"left\":5,\"position\":\"absolute\",\"top\":0,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"userDrag\",\"zIndex\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":\"800\",\"height\":\"800\",\"background\":\"#fff\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"uuid\":\"ec810e8d-d7d6-e50f-4c57-23a4e70a89ab\"},{\"compIndex\":62,\"name\":\"Dashboard\",\"type\":\"card\",\"data\":{\"text_val\":\"24H EnergyConsume\",\"card_val\":\"card\",\"sky_val\":\"card\",\"panelStyleType\":\"\"},\"style\":{\"height\":430,\"width\":549,\"left\":562,\"position\":\"absolute\",\"top\":0,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"userDrag\",\"zIndex\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":\"800\",\"height\":\"800\",\"background\":\"#fff\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"uuid\":\"75749be0-31cd-11ce-919d-3cd186fef18c\"},{\"compIndex\":56,\"name\":\"ComplexIndex Compare \",\"type\":\"indexComparison\",\"data\":{\"indicator_table_val\":\"\",\"chartType\":\"ring\",\"complexIndexList\":[],\"room_data_source_val\":[],\"legendPosition\":\"bottom\",\"innerRadiusSize\":0,\"showLabel\":false,\"labelSize\":16,\"xAxisShow\":false,\"yAxisShow\":false,\"realTime\":false,\"realTimeNum\":2000,\"indicator-table_column\":[],\"indicator-table_row\":[]},\"style\":{\"height\":389,\"width\":530,\"position\":\"absolute\",\"left\":570,\"top\":15,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":\"800\",\"height\":\"800\",\"background\":\"#fff\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"uuid\":\"c4a411f8-a597-0edb-4210-91231fdb1c7f\"},{\"compIndex\":62,\"name\":\"Dashboard\",\"type\":\"card\",\"data\":{\"text_val\":\"CUE\",\"card_val\":\"card\",\"sky_val\":\"card\",\"panelStyleType\":\"\"},\"style\":{\"height\":430,\"width\":550,\"left\":1121,\"position\":\"absolute\",\"top\":0,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"userDrag\",\"zIndex\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":\"800\",\"height\":\"800\",\"background\":\"#fff\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"uuid\":\"4ebc164e-a11f-5edb-984b-083307631ad0\"},{\"compIndex\":61,\"name\":\"ActiveComplexIndex\",\"type\":\"realtimeIndicator\",\"data\":{\"title\":\"PUE\",\"realtime_indicator_val\":\"\",\"objectType\":0,\"areaObjectId\":0,\"complexIndexId\":0,\"cssStyleType\":\"10\",\"alignTypePadding\":20,\"isShowPanel\":false,\"alignType\":1,\"useDefaultBool\":true,\"defaultPercent\":0,\"defaultValue\":60,\"colorIndex\":0,\"selectedList\":[],\"minValue\":0,\"maxValue\":100,\"startColor\":\"#f8db76\",\"endColor\":\"#f8db76\",\"backgrdStartColor\":\"#9673ff\",\"backgrdEndColor\":\"#9673ff\",\"nameFontsize\":18,\"valueFontsize\":\"30\",\"nameColorIndex\":\"#007FFF\",\"valueColorIndex\":\"#00D9A3\",\"notShowBorder\":true,\"defaultUnit\":\"%\"},\"style\":{\"height\":300,\"width\":520,\"position\":\"absolute\",\"top\":130,\"left\":1131,\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"event\":{},\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":\"800\",\"height\":\"800\",\"background\":\"#fff\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"uuid\":\"4a18f84f-c2a5-784a-20e3-ee057cd9871d\"},{\"compIndex\":62,\"name\":\"board\",\"type\":\"card\",\"data\":{\"text_val\":\"TotalConsume\",\"card_val\":\"card\",\"sky_val\":\"card\",\"panelStyleType\":\"\"},\"style\":{\"height\":426,\"width\":845,\"left\":5,\"position\":\"absolute\",\"top\":437,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"userDrag\",\"zIndex\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":\"800\",\"height\":\"800\",\"background\":\"#fff\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"uuid\":\"a40cb40d-2863-d34f-0596-990b2bbdaa72\"},{\"compIndex\":56,\"name\":\"ComplexIndexRate\",\"type\":\"indexComparison\",\"data\":{\"indicator_table_val\":\"\",\"chartType\":\"bar\",\"complexIndexList\":[],\"room_data_source_val\":[],\"legendPosition\":\"bottom\",\"innerRadiusSize\":0,\"showLabel\":false,\"labelSize\":16,\"xAxisShow\":true,\"yAxisShow\":false,\"realTime\":false,\"realTimeNum\":2000,\"indicator-table_column\":[],\"indicator-table_row\":[]},\"style\":{\"height\":360,\"width\":808,\"position\":\"absolute\",\"left\":25,\"top\":470,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":\"800\",\"height\":\"800\",\"background\":\"#fff\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"uuid\":\"4ecfdf14-843f-0eac-12bc-4e868916a7a6\"},{\"compIndex\":62,\"name\":\"board\",\"type\":\"card\",\"data\":{\"text_val\":\"EnergyCategory\",\"card_val\":\"card\",\"sky_val\":\"card\",\"panelStyleType\":\"\"},\"style\":{\"height\":426,\"width\":812,\"left\":860,\"position\":\"absolute\",\"top\":437,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"userDrag\",\"zIndex\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":\"800\",\"height\":\"800\",\"background\":\"#fff\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"uuid\":\"1885b6c8-67f5-7fa6-0521-6af783349d27\"},{\"compIndex\":56,\"name\":\"ComplexIndexRate\",\"type\":\"indexComparison\",\"data\":{\"indicator_table_val\":\"\",\"chartType\":\"stack\",\"complexIndexList\":[],\"room_data_source_val\":[],\"legendPosition\":\"bottom\",\"innerRadiusSize\":0,\"showLabel\":false,\"labelSize\":16,\"xAxisShow\":true,\"yAxisShow\":false,\"realTime\":false,\"realTimeNum\":2000,\"indicator-table_column\":[],\"indicator-table_row\":[]},\"style\":{\"height\":372,\"width\":797,\"position\":\"absolute\",\"left\":859,\"top\":470,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":\"800\",\"height\":\"800\",\"background\":\"#fff\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false,\"uuid\":\"77f82e67-9a5a-af64-4f63-d84fe048ac9d\"},{\"compIndex\":61,\"name\":\"Realtime Indicator\",\"type\":\"realtimeIndicator\",\"data\":{\"title\":\"Realtime \",\"realtime_indicator_val\":\"\",\"objectType\":0,\"areaObjectId\":0,\"complexIndexId\":0,\"cssStyleType\":\"8\",\"alignTypePadding\":20,\"isShowPanel\":false,\"alignType\":1,\"useDefaultBool\":false,\"defaultPercent\":0,\"defaultValue\":0,\"colorIndex\":0,\"selectedList\":[],\"minValue\":1,\"maxValue\":2,\"startAngle\":180,\"startColor\":\"#f8db76\",\"endColor\":\"#f8db76\",\"backgrdStartColor\":\"#9673ff\",\"backgrdEndColor\":\"#9673ff\",\"nameFontsize\":\"14\",\"valueFontsize\":\"30\",\"nameColorIndex\":\"\",\"valueColorIndex\":\"#00FFFF\"},\"style\":{\"height\":100,\"width\":250,\"position\":\"absolute\",\"top\":154,\"left\":152,\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"zIndex\":10,\"userDrag\":\"element\"},\"event\":{},\"uuid\":\"aead15d5-8719-a07d-c366-69a764466a8e\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":\"800\",\"height\":\"800\",\"background\":\"#fff\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false}]', NULL, '', NULL, '0', NULL, NULL);
-- 能耗看板首页电信
insert into graphicpage(Id, Type, GroupId, PageCategory, BaseEquipmentId, ObjectId,  Name, AppendName, TemplateId, Data, Style, Children, Description, RouterType, CrumbsRouterType, PageCanFullScreen, SceneId, IsDefault)
values('2011','page',0,8,-1,2,'EnergyDashboard','',NULL,'{\"api\":\"baseEquipmentId=undefined\",\"time\":\"1000\"}','{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null}','[{\"compIndex\":62,\"name\":\"面板\",\"type\":\"card\",\"data\":{\"text_val\":\"用电数据\",\"card_val\":\"card\",\"sky_val\":\"card\",\"panelStyleType\":\"\"},\"style\":{\"height\":211,\"width\":546,\"left\":0,\"position\":\"absolute\",\"top\":0,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"userDrag\",\"zIndex\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"uuid\":\"c852574a-b062-4790-5c76-22e07b527adc\",\"action\":false},{\"compIndex\":62,\"name\":\"面板\",\"type\":\"card\",\"data\":{\"text_val\":\"PUE\",\"card_val\":\"card\",\"sky_val\":\"card\",\"panelStyleType\":\"\"},\"style\":{\"height\":212,\"width\":536,\"left\":1057,\"position\":\"absolute\",\"top\":0,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"userDrag\",\"zIndex\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"uuid\":\"8b00eda3-4889-30c7-9eec-5d4f1b1460a9\",\"action\":false},{\"compIndex\":62,\"name\":\"面板\",\"type\":\"card\",\"data\":{\"text_val\":\"用电类型数据\",\"card_val\":\"card\",\"sky_val\":\"card\",\"panelStyleType\":\"\"},\"style\":{\"height\":212,\"width\":505,\"left\":549,\"position\":\"absolute\",\"top\":0,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"userDrag\",\"zIndex\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false,\"uuid\":\"8c365f02-391b-1350-a15a-e2afad3b4306\"},{\"compIndex\":123,\"name\":\"多时间文本数字\",\"type\":\"textNumber\",\"data\":{\"text_number_val\":\"\",\"text_number_value\":\"3336760.8                   3879093.6                   3879093.6\",\"chartId\":3,\"styleId\":\"17\",\"styleImg\":\"textnumber.png\",\"expression\":\"\",\"apiId\":\"34\",\"parameterValue\":{\"userId\":\"personId\",\"timeType\":\"y\"},\"planFlag\":false,\"planList\":[],\"transform\":\"return (data.totalElectricityValue).toString() +\'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.yoyTotalElectricityValue).toString() +\'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.qoqTotalElectricityValue).toString();\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\"},\"style\":{\"height\":30,\"width\":400,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":140,\"left\":50,\"fontCol\":\"#BFDFFF\",\"fontsize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"zIndex\":10,\"hideByMultiPlan\":false},\"event\":{},\"uuid\":\"308815f0-407f-2abb-badc-81916c783a47\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":117,\"name\":\"饼图\",\"type\":\"pieChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":2,\"styleId\":\"8\",\"styleImg\":\"piechart.png\",\"style\":{\"seriesLayoutBy\":\"row\"},\"expression\":\"option = {\\n    grid: {\\n        bottom: 80\\n    },\\n    dataZoom: [],\\n    dataset: {\\n        source: [\\n        ]\\n    },\\n    tooltip: {\\n        show: true,\\n        trigger: \'item\',\\n        position: \'top\',\\n        textStyle: {\\n            color: \'#bfdfff\',\\n            fontSize: 12\\n        },\\n        backgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n        borderWidth: 1,\\n        borderColor: \'rgba(0,127,255,0.3)\',\\n        padding: null,\\n        axisPointer: {\\n            type: \\\"none\\\"\\n        },\\n    },\\n    legend: {//图例\\n        type: \\\"scroll\\\",\\n        pageIconColor: \'#aaa\',\\n        pageIconInactiveColor: \'#2f4554\',\\n        pageTextStyle: {\\n            color: \\\"#aaa\\\"\\n        },\\n        show: true,\\n        itemHeight: 5,\\n        bottom: 15,\\n        itemGap: 40,\\n      textStyle:{\\n       color:\'white\'\\n     }\\n    },\\n    series: [],\\n};\",\"apiId\":\"33\",\"parameterValue\":{\"session;userId\":\"personId\",\"timeType\":\"y\"},\"transform\":\"const arr = [data];\\nconst list = util.formatToTable(arr, [\'市电\',\'油机\',\'绿电\'], [\'cityElectValue\',\'oilElectValue\',\'greenElectValue\'])//二参是自定义表头\\nreturn list;\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\",\"selectedAPI\":[{\"name\":\"timeType\",\"array\":false,\"source\":\"input\",\"meaning\":\"timeType\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"\",\"delete\":false,\"parentName\":\"\",\"id\":\"1\"},{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"userId\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"2\"}]},\"style\":{\"background\":\"transparent\",\"height\":300,\"width\":400,\"left\":180,\"position\":\"absolute\",\"top\":280,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"zIndex\":10,\"hideByMultiPlan\":false},\"event\":{},\"uuid\":\"39df03d3-69df-89a0-9651-b263b4ce9473\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":2,\"name\":\"文字\",\"type\":\"text\",\"data\":{\"text_val\":\"总量 / 同比 / 环比 （ kW⋅h）\",\"writeMode\":\"horizontal-tb\",\"alignType\":\"center\"},\"style\":{\"height\":30,\"width\":400,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":90,\"left\":50,\"fontCol\":\"#BFDFFF\",\"fontSize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"zIndex\":10},\"uuid\":\"d4078fce-14a0-2281-f6f6-484580e92db7\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false,\"editeabled\":false},{\"compIndex\":123,\"name\":\"多时间文本数字\",\"type\":\"textNumber\",\"data\":{\"text_number_val\":\"\",\"text_number_value\":\"10649.52                   11363.04                   13722.24\",\"chartId\":3,\"styleId\":0,\"styleImg\":0,\"expression\":\"\",\"apiId\":\"34\",\"parameterValue\":{\"userId\":\"personId\",\"timeType\":\"d\"},\"planFlag\":false,\"planList\":[],\"transform\":\"return (data.totalElectricityValue).toString() +\'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.yoyTotalElectricityValue).toString() +\'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.qoqTotalElectricityValue).toString();\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\"},\"style\":{\"height\":30,\"width\":400,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":140,\"left\":50,\"fontCol\":\"#BFDFFF\",\"fontsize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"zIndex\":10,\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"2dc12688-a2c5-2a9b-d470-604b93aff9fe\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":123,\"name\":\"多时间文本数字\",\"type\":\"textNumber\",\"data\":{\"text_number_val\":\"\",\"text_number_value\":\"269791.2                   434743.2                   332330.4\",\"chartId\":3,\"styleId\":\"17\",\"styleImg\":\"textnumber.png\",\"expression\":\"\",\"apiId\":\"34\",\"parameterValue\":{\"userId\":\"personId\",\"timeType\":\"m\"},\"planFlag\":false,\"planList\":[],\"transform\":\"return (data.totalElectricityValue).toString() +\'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.yoyTotalElectricityValue).toString() +\'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.qoqTotalElectricityValue).toString();\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\"},\"style\":{\"height\":30,\"width\":400,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":140,\"left\":50,\"fontCol\":\"#BFDFFF\",\"fontsize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"zIndex\":10,\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"0d4efd0a-6b0e-631b-6178-7aee7a5b4199\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":2,\"name\":\"文字\",\"type\":\"text\",\"data\":{\"text_val\":\"市电总量 / 油机发电量 / 绿电发电量（ kW⋅h）\",\"writeMode\":\"horizontal-tb\"},\"style\":{\"height\":30,\"width\":400,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":90,\"left\":590,\"fontCol\":\"#BFDFFF\",\"fontSize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"zIndex\":10},\"uuid\":\"2ef2fab9-2651-e2bf-dab6-5692b9687a98\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":123,\"name\":\"多时间文本数字\",\"type\":\"textNumber\",\"data\":{\"text_number_val\":\"\",\"text_number_value\":\"1671278.4                   779644.8                   885837.6\",\"chartId\":3,\"styleId\":\"17\",\"styleImg\":\"textnumber.png\",\"expression\":\"\",\"apiId\":\"33\",\"parameterValue\":{\"userId\":\"personId\",\"timeType\":\"y\"},\"planFlag\":false,\"planList\":[],\"transform\":\"return (data.cityElectValue).toString() +\'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.oilElectValue).toString() +\'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.greenElectValue).toString();\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\"},\"style\":{\"height\":30,\"width\":400,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":140,\"left\":590,\"fontCol\":\"#BFDFFF\",\"fontsize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"zIndex\":10,\"hideByMultiPlan\":false},\"event\":{},\"uuid\":\"c1429933-cff0-0719-23b8-bce8a68534b1\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":123,\"name\":\"多时间文本数字\",\"type\":\"textNumber\",\"data\":{\"text_number_val\":\"\",\"text_number_value\":\"148168.8                   37857.6                   83764.8\",\"chartId\":3,\"styleId\":0,\"styleImg\":0,\"expression\":\"\",\"apiId\":\"33\",\"parameterValue\":{\"userId\":\"personId\",\"timeType\":\"m\"},\"planFlag\":false,\"planList\":[],\"transform\":\"return (data.cityElectValue).toString() +\'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.oilElectValue).toString() +\'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.greenElectValue).toString();\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\"},\"style\":{\"height\":30,\"width\":400,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":140,\"left\":590,\"fontCol\":\"#BFDFFF\",\"fontsize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"zIndex\":10,\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"2b653f73-7740-f3b5-df24-50d902f9eb26\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":123,\"name\":\"多时间文本数字\",\"type\":\"textNumber\",\"data\":{\"text_number_val\":\"\",\"text_number_value\":\"4222.56                   4034.4                   2392.56\",\"chartId\":3,\"styleId\":0,\"styleImg\":0,\"expression\":\"\",\"apiId\":\"33\",\"parameterValue\":{\"userId\":\"personId\",\"timeType\":\"d\"},\"planFlag\":false,\"planList\":[],\"transform\":\"return (data.cityElectValue).toString() +\'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.oilElectValue).toString() +\'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.greenElectValue).toString();\\n\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\"},\"style\":{\"height\":30,\"width\":400,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":140,\"left\":590,\"fontCol\":\"#BFDFFF\",\"fontsize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"zIndex\":10,\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"0b07f94d-9314-f3be-cae0-5dadc0a41e81\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":62,\"name\":\"面板\",\"type\":\"card\",\"data\":{\"text_val\":\"用电类型占比\",\"card_val\":\"card\",\"sky_val\":\"card\",\"panelStyleType\":\"\"},\"style\":{\"height\":382,\"width\":817,\"left\":0,\"position\":\"absolute\",\"top\":214,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"zIndex\":0},\"uuid\":\"4bf24fa1-**************-06118ba7ab7c\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"userDrag\",\"zIndex\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":123,\"name\":\"多时间文本数字\",\"type\":\"textNumber\",\"data\":{\"text_number_val\":\"\",\"text_number_value\":\"800.49                   71654.4                   2901.6\",\"chartId\":3,\"styleId\":\"17\",\"styleImg\":\"textnumber.png\",\"expression\":\"\",\"apiId\":\"32\",\"parameterValue\":{\"userId\":\"personId\",\"timeType\":\"y\"},\"planFlag\":false,\"planList\":[],\"transform\":\"return (data.avgValue).toString() +\'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.maxValue).toString() +\'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.minValue).toString();\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\"},\"style\":{\"height\":30,\"width\":400,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":141,\"left\":1090,\"fontCol\":\"#BFDFFF\",\"fontsize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":false},\"event\":{},\"uuid\":\"f4295cc3-528f-0b22-5286-ff1fdf2316a8\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":62,\"name\":\"面板\",\"type\":\"card\",\"data\":{\"text_val\":\"用电分项占比\",\"card_val\":\"card\",\"sky_val\":\"card\",\"panelStyleType\":\"\"},\"style\":{\"height\":383,\"width\":772,\"left\":819,\"position\":\"absolute\",\"top\":214,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"uuid\":\"7298254d-a069-6d2e-584b-60edff642570\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"userDrag\",\"zIndex\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":117,\"name\":\"饼图\",\"type\":\"pieChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":2,\"styleId\":\"8\",\"styleImg\":\"piechart.png\",\"style\":{\"seriesLayoutBy\":\"column\"},\"expression\":\"option = {\\n    grid: {\\n        bottom: 80\\n    },\\n    dataZoom: [],\\n    dataset: {\\n        source: [\\n        ]\\n    },\\n    tooltip: {\\n        show: true,\\n        trigger: \'item\',\\n        position: \'top\',\\n        textStyle: {\\n            color: \'#bfdfff\',\\n            fontSize: 12\\n        },\\n        backgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n        borderWidth: 1,\\n        borderColor: \'rgba(0,127,255,0.3)\',\\n        padding: null,\\n        axisPointer: {\\n            type: \\\"none\\\"\\n        },\\n    },\\n    legend: {//图例\\n        type: \\\"scroll\\\",\\n        pageIconColor: \'#aaa\',\\n        pageIconInactiveColor: \'#2f4554\',\\n        pageTextStyle: {\\n            color: \\\"#aaa\\\"\\n        },\\n        show: true,\\n        itemHeight: 5,\\n        bottom: 15,\\n        itemGap: 40,\\n      textStyle:{\\n       color:\'white\'\\n     }\\n    },\\n    series: [],\\n};\",\"apiId\":\"38\",\"parameterValue\":{\"session;userId\":\"personId\",\"timeType\":\"y\"},\"transform\":\"\\n\\n// 第三种情况,接口返回数据data格式是对象的数组,如下\\n// [{displayIndex:1, employeeName:\\\"a接口用户\\\", phone: 123456},\\n// {displayIndex:2, employeeName:\\\"b接口用户\\\", phone: 654321},\\n// {displayIndex:3, employeeName:\\\"c接口用户\\\", phone: 112233},]\\nconst list = util.formatToTable(data, [\'用电类型\',\'电量\'], [\'definitionName\',\'sumValue\'])//二参是自定义表头\\nreturn list;\\n\\n\\n//const arr = [data];\\n//const list = util.formatToTable(arr, [\'市电\',\'油机\',\'绿电\'], [\'cityElectValue\',\'oilElectValue\',\'greenElectValue\'])//二参是自定义表头\\n//return list;\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\",\"selectedAPI\":[{\"name\":\"timeType\",\"array\":false,\"source\":\"input\",\"meaning\":\"时间类型\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"\",\"delete\":false,\"parentName\":\"\",\"id\":\"2\"},{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"人员ID\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"3\"}]},\"style\":{\"background\":\"transparent\",\"height\":320,\"width\":400,\"left\":990,\"position\":\"absolute\",\"top\":265,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":false},\"event\":{},\"uuid\":\"b45d1d42-a097-34aa-8cca-b40b3664229a\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":123,\"name\":\"多时间文本数字\",\"type\":\"textNumber\",\"data\":{\"text_number_val\":\"\",\"text_number_value\":\"11495.25                   58118.4                   25732.8\",\"chartId\":3,\"styleId\":0,\"styleImg\":0,\"expression\":\"\",\"apiId\":\"32\",\"parameterValue\":{\"userId\":\"personId\",\"timeType\":\"m\"},\"planFlag\":false,\"planList\":[],\"transform\":\"return (data.avgValue).toString() +\'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.maxValue).toString() +\'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.minValue).toString();\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\"},\"style\":{\"height\":30,\"width\":400,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":140,\"left\":1090,\"fontCol\":\"#BFDFFF\",\"fontsize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"7a4a9f78-21ea-5067-c22d-2624d4ab11d6\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8},{\"compIndex\":2,\"name\":\"文字\",\"type\":\"text\",\"data\":{\"text_val\":\"平均值 / 最大值 / 最小值\",\"writeMode\":\"horizontal-tb\"},\"style\":{\"height\":30,\"width\":400,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":90,\"left\":1090,\"fontCol\":\"#BFDFFF\",\"fontSize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"uuid\":\"802cff97-61cc-e201-03a4-49298606bc03\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8},{\"compIndex\":123,\"name\":\"多时间文本数字\",\"type\":\"textNumber\",\"data\":{\"text_number_val\":\"\",\"text_number_value\":\"248.04                   2334.0                   386.88\",\"chartId\":3,\"styleId\":0,\"styleImg\":0,\"expression\":\"\",\"apiId\":\"32\",\"parameterValue\":{\"userId\":\"personId\",\"timeType\":\"d\"},\"planFlag\":false,\"planList\":[],\"transform\":\"return (data.avgValue).toString() +\'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.maxValue).toString() +\'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.minValue).toString();\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\"},\"style\":{\"height\":30,\"width\":400,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":140,\"left\":1090,\"fontCol\":\"#BFDFFF\",\"fontsize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"ae4bac45-d0c6-d06b-cbdf-c67e40d07441\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":62,\"name\":\"面板\",\"type\":\"card\",\"data\":{\"text_val\":\"PUE时间趋势\",\"card_val\":\"card\",\"sky_val\":\"card\",\"panelStyleType\":\"\"},\"style\":{\"height\":511,\"width\":766,\"left\":819,\"position\":\"absolute\",\"top\":600.5714285714284,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"uuid\":\"26ede2fc-737e-958f-bba5-f72e2a5b0cc2\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"userDrag\",\"zIndex\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8},{\"compIndex\":118,\"name\":\"折线图\",\"type\":\"lineChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":0,\"styleId\":\"12\",\"styleImg\":\"linechart.png\",\"style\":{\"seriesLayoutBy\":\"column\"},\"expression\":\"option = {\\n\\tgrid: {\\n\\t\\tbottom: 80\\n\\t},\\n\\txAxis: {\\n\\t\\ttype: \'category\',\\n\\t\\tdata: null,\\n\\t\\tshow: true,\\n\\t\\taxisTick: { show: false },\\n\\t\\tboundaryGap: true,\\n\\t\\tsilent: true,\\n\\t\\taxisLine: {//轴线\\n\\t\\t\\tshow: true,\\n\\t\\t\\tlineStyle: {\\n\\t\\t\\t\\tcolor: \'#BFDFFF\', //x轴轴线颜色\\n\\t\\t\\t\\topacity: 0.2,\\n\\t\\t\\t\\twidth: 1\\n\\t\\t\\t}\\n\\t\\t},\\n\\t\\taxisLabel: {//x轴文字\\n\\t\\t\\tshow: true,\\n\\t\\t\\toverflow: \'break\',\\n\\t\\t\\twidth: null,\\n\\t\\t\\tcolor: \'#BFDFFF\',\\n\\t\\t\\tfontFamily: \'\',\\n\\t\\t\\tfontSize: 13,\\n\\t\\t\\tellipsis: \'...\'\\n\\t\\t}\\n\\t},\\n\\tyAxis: {\\n\\t\\ttype: \'value\',\\n\\t\\tshow: true,\\n\\t\\tsplitNumber: 4,\\n\\t\\tmax: null,\\n\\t\\tmin: 0,\\n\\t\\tsilent: true,\\n\\t\\ttextStyle: { color: \'#BFDFFF\', fontFamily: \'\', fontSize: 14 },\\n\\t\\taxisLine: {//轴线\\n\\t\\t\\tshow: true,\\n\\t\\t\\tlineStyle: {\\n\\t\\t\\t\\tcolor: \'#BFDFFF\',\\n\\t\\t\\t\\topacity: 0.2,\\n\\t\\t\\t\\twidth: 1\\n\\t\\t\\t}\\n\\t\\t},\\n\\t\\taxisLabel: {//y轴文字\\n\\t\\t\\tshow: true,\\n\\t\\t\\tcolor: \'#BFDFFF\',\\n\\t\\t\\tfontFamily: \'\',\\n\\t\\t\\tfontSize: 13\\n\\t\\t},\\n\\t\\tsplitLine: {//分割线\\n\\t\\t\\tlineStyle: {\\n\\t\\t\\t\\twidth: 1,\\n\\t\\t\\t\\tcolor: \'#BFDFFF\',\\n\\t\\t\\t\\topacity: 0.1\\n\\t\\t\\t}\\n\\t\\t}\\n\\t},\\n\\tdataZoom: [],\\n\\tdataset: {\\n\\t\\tsource: [\\n\\t\\t]\\n\\t},\\n\\ttooltip: {\\n\\t\\tshow: true,\\n\\t\\ttrigger: \'item\',\\n\\t\\tposition: \'top\',\\n\\t\\ttextStyle: {\\n\\t\\t\\tcolor: \'#bfdfff\',\\n\\t\\t\\tfontSize: 12\\n\\t\\t},\\n\\t\\tbackgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n\\t\\tborderWidth: 1,\\n\\t\\tborderColor: \'rgba(0,127,255,0.3)\',\\n\\t\\tpadding: null,\\n\\t\\taxisPointer: {\\n\\t\\t\\ttype: \\\"none\\\"\\n\\t\\t},\\n\\t},\\n\\tlegend: {//图例\\n\\t\\ttype: \\\"scroll\\\",\\n\\t\\tpageIconColor: \'#aaa\',\\n\\t\\tpageIconInactiveColor: \'#2f4554\',\\n\\t\\tpageTextStyle: {\\n\\t\\t\\tcolor: \\\"#aaa\\\"\\n\\t\\t},\\n\\t\\tshow: true,\\n\\t\\titemHeight: 5,\\n\\t\\tbottom: 15,\\n\\t\\titemGap: 40,\\n      textStyle:{\\n       color:\'white\'\\n     }\\n\\t},\\n\\tseries: [],\\n};\",\"apiId\":\"41\",\"parameterValue\":{\"session;userId\":\"personId\",\"timeType\":\"y\"},\"transform\":\"\\n// 第三种情况,接口返回数据data格式是对象的数组,如下\\n// [{displayIndex:1, employeeName:\\\"a接口用户\\\", phone: 123456},\\n// {displayIndex:2, employeeName:\\\"b接口用户\\\", phone: 654321},\\n// {displayIndex:3, employeeName:\\\"c接口用户\\\", phone: 112233},]\\nconst list = util.formatToTable(data, [\'时间\',\'PUE值\'], [\'time\',\'avgValue\'])//二参是自定义表头\\nreturn util.arrArrFormatTime(list, \'$M\');//二参格式可选$[y|Q|M|W|d|h|m|s]\\n\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\",\"selectedAPI\":[{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"userId\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"1\"},{\"name\":\"timeType\",\"array\":false,\"source\":\"input\",\"meaning\":\"timeType\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"\",\"delete\":false,\"parentName\":\"\",\"id\":\"2\"}]},\"style\":{\"background\":\"transparent\",\"height\":370,\"width\":675,\"left\":865,\"position\":\"absolute\",\"top\":720,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"zIndex\":10,\"hideByMultiPlan\":false},\"event\":{},\"uuid\":\"b64611f3-23e6-e0d7-b49a-503efc3efb72\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":62,\"name\":\"面板\",\"type\":\"card\",\"data\":{\"text_val\":\"用能类型趋势\",\"card_val\":\"card\",\"sky_val\":\"card\",\"panelStyleType\":\"\"},\"style\":{\"height\":480,\"width\":1582,\"left\":0,\"position\":\"absolute\",\"top\":1116,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"uuid\":\"e8c1b5db-90aa-414e-f112-0b96c8cf8d41\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"userDrag\",\"zIndex\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8},{\"compIndex\":62,\"name\":\"面板\",\"type\":\"card\",\"data\":{\"text_val\":\"下级用电量排名\",\"card_val\":\"card\",\"sky_val\":\"card\",\"panelStyleType\":\"\"},\"style\":{\"height\":510,\"width\":815,\"left\":0,\"position\":\"absolute\",\"top\":600,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"uuid\":\"f1304a3a-8364-192f-8e98-3ea6c1f26131\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"userDrag\",\"zIndex\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":115,\"name\":\"柱状图\",\"type\":\"barChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":1,\"styleId\":\"3\",\"styleImg\":\"stackbarchart.png\",\"style\":{\"seriesLayoutBy\":\"column\"},\"expression\":\"option = {\\n    grid: {\\n        bottom: 80\\n    },\\n    xAxis: {\\n        type: \'category\',\\n        data: null,\\n        show: true,\\n        axisTick: { show: false },\\n        boundaryGap: true,\\n        silent: true,\\n        axisLine: {//轴线\\n            show: true,\\n            lineStyle: {\\n                color: \'#BFDFFF\', //x轴轴线颜色\\n                opacity: 0.2,\\n                width: 1\\n            }\\n        },\\n        axisLabel: {//x轴文字\\n            show: true,\\n            overflow: \'break\',\\n            width: null,\\n            color: \'#BFDFFF\',\\n            fontFamily: \'\',\\n            fontSize: 13,\\n            ellipsis: \'...\'\\n        }\\n    },\\n    yAxis: {\\n        type: \'value\',\\n        show: true,\\n        splitNumber: 4,\\n        max: null,\\n        min: 0,\\n        silent: true,\\n        textStyle: { color: \'#BFDFFF\', fontFamily: \'\', fontSize: 14 },\\n        axisLine: {//轴线\\n            show: true,\\n            lineStyle: {\\n                color: \'#BFDFFF\',\\n                opacity: 0.2,\\n                width: 1\\n            }\\n        },\\n        axisLabel: {//y轴文字\\n            show: true,\\n            color: \'#BFDFFF\',\\n            fontFamily: \'\',\\n            fontSize: 13\\n        },\\n        splitLine: {//分割线\\n            lineStyle: {\\n                width: 1,\\n                color: \'#BFDFFF\',\\n                opacity: 0.1\\n            }\\n        }\\n    },\\n    dataZoom: [],\\n    dataset: {\\n        source: [\\n        ]\\n    },\\n    tooltip: {\\n        show: true,\\n        trigger: \'axis\',\\n        position: \'\',\\n        textStyle: {\\n            color: \'#bfdfff\',\\n            fontSize: 12\\n        },\\n        backgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n        borderWidth: 1,\\n        borderColor: \'rgba(0,127,255,0.3)\',\\n        padding: null,\\n        axisPointer: {\\n            type: \\\"none\\\"\\n        },\\n    },\\n    legend: {//图例\\n        type: \\\"scroll\\\",\\n        pageIconColor: \'#aaa\',\\n        pageIconInactiveColor: \'#2f4554\',\\n        pageTextStyle: {\\n            color: \\\"#aaa\\\"\\n        },\\n        show: true,\\n        itemHeight: 5,\\n        bottom: 15,\\n        itemGap: 40,\\n      textStyle:{\\n       color:\'white\'\\n     }\\n    },\\n    series: [],\\n};\\n\",\"apiId\":\"44\",\"parameterValue\":{\"session;userId\":\"personId\",\"timeType\":\"y\"},\"transform\":\"const list = util.formatToTable(data, [\'层级\',\'市电\',\'油机发电\',\'绿电\'], [\'resourceStructureName\',\'cityElectValue\',\'oilElectValue\',\'greenElectValue\'])//二参是自定义表头\\nreturn list;\\n\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\",\"selectedAPI\":[{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"人员ID\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"1\"},{\"name\":\"timeType\",\"array\":false,\"source\":\"input\",\"meaning\":\"时间类型\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"\",\"delete\":false,\"parentName\":\"\",\"id\":\"2\"}]},\"style\":{\"background\":\"transparent\",\"height\":300,\"width\":770,\"left\":20,\"position\":\"absolute\",\"top\":740,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":false},\"event\":{},\"uuid\":\"2c7d8cd1-7319-44a2-d1b4-bc12bd92d1ad\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":115,\"name\":\"柱状图\",\"type\":\"barChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":1,\"styleId\":\"3\",\"styleImg\":\"stackbarchart.png\",\"style\":{\"seriesLayoutBy\":\"column\"},\"expression\":\"option = {\\n    grid: {\\n        bottom: 80\\n    },\\n    xAxis: {\\n        type: \'category\',\\n        data: null,\\n        show: true,\\n        axisTick: { show: false },\\n        boundaryGap: true,\\n        silent: true,\\n        axisLine: {//轴线\\n            show: true,\\n            lineStyle: {\\n                color: \'#BFDFFF\', //x轴轴线颜色\\n                opacity: 0.2,\\n                width: 1\\n            }\\n        },\\n        axisLabel: {//x轴文字\\n            show: true,\\n            overflow: \'break\',\\n            width: null,\\n            color: \'#BFDFFF\',\\n            fontFamily: \'\',\\n            fontSize: 13,\\n            ellipsis: \'...\'\\n        }\\n    },\\n    yAxis: {\\n        type: \'value\',\\n        show: true,\\n        splitNumber: 4,\\n        max: null,\\n        min: 0,\\n        silent: true,\\n        textStyle: { color: \'#BFDFFF\', fontFamily: \'\', fontSize: 14 },\\n        axisLine: {//轴线\\n            show: true,\\n            lineStyle: {\\n                color: \'#BFDFFF\',\\n                opacity: 0.2,\\n                width: 1\\n            }\\n        },\\n        axisLabel: {//y轴文字\\n            show: true,\\n            color: \'#BFDFFF\',\\n            fontFamily: \'\',\\n            fontSize: 13\\n        },\\n        splitLine: {//分割线\\n            lineStyle: {\\n                width: 1,\\n                color: \'#BFDFFF\',\\n                opacity: 0.1\\n            }\\n        }\\n    },\\n    dataZoom: [],\\n    dataset: {\\n        source: [\\n        ]\\n    },\\n    tooltip: {\\n        show: true,\\n        trigger: \'axis\',\\n        position: \'\',\\n        textStyle: {\\n            color: \'#bfdfff\',\\n            fontSize: 12\\n        },\\n        backgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n        borderWidth: 1,\\n        borderColor: \'rgba(0,127,255,0.3)\',\\n        padding: null,\\n        axisPointer: {\\n            type: \\\"none\\\"\\n        },\\n    },\\n    legend: {//图例\\n        type: \\\"scroll\\\",\\n        pageIconColor: \'#aaa\',\\n        pageIconInactiveColor: \'#2f4554\',\\n        pageTextStyle: {\\n            color: \\\"#aaa\\\"\\n        },\\n        show: true,\\n        itemHeight: 5,\\n        bottom: 15,\\n        itemGap: 40,\\n      textStyle:{\\n       color:\'white\'\\n     }\\n    },\\n    series: [],\\n};\\n\",\"apiId\":\"42\",\"parameterValue\":{\"session;userId\":\"personId\"},\"transform\":\"\\n//const list = util.formatToTable(data, [\'编号\',\'班组人员\',\'联系方式\'], [\'displayIndex\',\'employeeName\',\'phone\'])//二参是自定义表头\\n//如果要对时间进行格式化\\n//return util.arrArrFormatTime2(list, \'$M\');//二参格式可选$[y|Q|M|W|d|h|m|s]\\n\\n//堆叠图\\n//const list = util.formatToTable(data, [\'层级\',\'市电碳排放\',\'油机发电碳排放\',\'绿电碳抵消\'], [\'resourceStructureName\',\'cityElectCarbonValue\',\'oilElectCarbonValue\',\'greenElectCarbonValue\'])//二参是自定义表头\\n//return list;\\n\\n//趋势图\\n//const list = util.formatToTable(data, [\'时间\',\'PUE值\'], [\'time\',\'avgValue\'])//二参是自定义表头\\n//return util.arrArrFormatTime(list, \'$M\');//二参格式可选$[y|Q|M|W|d|h|m|s]\\n\\n\\nconst list = util.formatToTable(data, [\'时间\',\'市电\',\'油机发电\',\'绿电\'], [\'time\',\'cityElectValue\',\'oilElectValue\',\'greenElectValue\'])//二参是自定义表头\\nreturn util.arrArrFormatTime(list, \'$M\');//二参格式可选$[y|Q|M|W|d|h|m|s]\\n\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\",\"selectedAPI\":[{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"人员ID\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"1\"}]},\"style\":{\"background\":\"transparent\",\"height\":346,\"width\":1326,\"left\":95,\"position\":\"absolute\",\"top\":1216,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"event\":{},\"uuid\":\"bf69fa12-7bed-fcd2-9f89-97fc426e06ba\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":130,\"name\":\"Tab切换\",\"type\":\"multiPlans\",\"data\":{\"multi_plans\":\"\",\"defaultPlan\":0,\"activePlanIndex\":0,\"control\":{\"show\":true,\"left\":0,\"top\":0,\"btnWidth\":30,\"btnHeight\":30,\"btnGap\":11,\"normalImage\":\"./../../../assets/img/charts/option-normal.png\",\"activeImage\":\"./../../../assets/img/charts/option-active.png\",\"gapImage\":\"./../../../assets/img/charts/option-gap.png\",\"textColor\":\"#FFFFFF\",\"activeTextColor\":\"#FFFFFF\",\"textFont\":\"arial,sans-serif\",\"textFontSize\":12},\"plans\":[{\"planName\":\"本年\",\"uuid\":\"308815f0-407f-2abb-badc-81916c783a47\"},{\"planName\":\"本月\",\"uuid\":\"0d4efd0a-6b0e-631b-6178-7aee7a5b4199\"},{\"planName\":\"本日\",\"uuid\":\"2dc12688-a2c5-2a9b-d470-604b93aff9fe\"}]},\"style\":{\"background\":\"transparent\",\"height\":30,\"width\":120,\"position\":\"absolute\",\"top\":60,\"left\":420,\"border\":\"\",\"opacity\":1,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"zIndex\":10,\"userDrag\":\"element\"},\"event\":{},\"uuid\":\"2ea625dc-11a4-23c9-726f-0e4d3158c356\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"border\",\"fonts\",\"zIndex\",\"opacity\",\"userDrag\",\"background\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":130,\"name\":\"Tab切换\",\"type\":\"multiPlans\",\"data\":{\"multi_plans\":\"\",\"defaultPlan\":0,\"activePlanIndex\":0,\"control\":{\"show\":true,\"left\":0,\"top\":0,\"btnWidth\":30,\"btnHeight\":30,\"btnGap\":11,\"normalImage\":\"./../../../assets/img/charts/option-normal.png\",\"activeImage\":\"./../../../assets/img/charts/option-active.png\",\"gapImage\":\"./../../../assets/img/charts/option-gap.png\",\"textColor\":\"#FFFFFF\",\"activeTextColor\":\"#FFFFFF\",\"textFont\":\"arial,sans-serif\",\"textFontSize\":12},\"plans\":[{\"planName\":\"本年\",\"uuid\":\"c1429933-cff0-0719-23b8-bce8a68534b1\"},{\"planName\":\"本月\",\"uuid\":\"2b653f73-7740-f3b5-df24-50d902f9eb26\"},{\"planName\":\"本日\",\"uuid\":\"0b07f94d-9314-f3be-cae0-5dadc0a41e81\"}]},\"style\":{\"background\":\"transparent\",\"height\":30,\"width\":120,\"position\":\"absolute\",\"top\":60,\"left\":935,\"border\":\"\",\"opacity\":1,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"zIndex\":10,\"userDrag\":\"element\"},\"event\":{},\"uuid\":\"0cac6981-353e-a3c8-3475-0802d61422fd\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"border\",\"fonts\",\"zIndex\",\"opacity\",\"userDrag\",\"background\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":130,\"name\":\"Tab切换\",\"type\":\"multiPlans\",\"data\":{\"multi_plans\":\"\",\"defaultPlan\":0,\"activePlanIndex\":0,\"control\":{\"show\":true,\"left\":0,\"top\":0,\"btnWidth\":30,\"btnHeight\":30,\"btnGap\":11,\"normalImage\":\"./../../../assets/img/charts/option-normal.png\",\"activeImage\":\"./../../../assets/img/charts/option-active.png\",\"gapImage\":\"./../../../assets/img/charts/option-gap.png\",\"textColor\":\"#FFFFFF\",\"activeTextColor\":\"#FFFFFF\",\"textFont\":\"arial,sans-serif\",\"textFontSize\":12},\"plans\":[{\"planName\":\"本年\",\"uuid\":\"f4295cc3-528f-0b22-5286-ff1fdf2316a8\"},{\"planName\":\"本月\",\"uuid\":\"7a4a9f78-21ea-5067-c22d-2624d4ab11d6\"},{\"planName\":\"本日\",\"uuid\":\"ae4bac45-d0c6-d06b-cbdf-c67e40d07441\"}]},\"style\":{\"background\":\"transparent\",\"height\":30,\"width\":120,\"position\":\"absolute\",\"top\":60,\"left\":1470,\"border\":\"\",\"opacity\":1,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"zIndex\":10,\"userDrag\":\"element\"},\"event\":{},\"uuid\":\"b1096fb3-0888-d699-db96-9c82f396349c\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"border\",\"fonts\",\"zIndex\",\"opacity\",\"userDrag\",\"background\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":130,\"name\":\"Tab切换\",\"type\":\"multiPlans\",\"data\":{\"multi_plans\":\"\",\"defaultPlan\":0,\"activePlanIndex\":0,\"control\":{\"show\":true,\"left\":0,\"top\":0,\"btnWidth\":30,\"btnHeight\":30,\"btnGap\":11,\"normalImage\":\"./../../../assets/img/charts/option-normal.png\",\"activeImage\":\"./../../../assets/img/charts/option-active.png\",\"gapImage\":\"./../../../assets/img/charts/option-gap.png\",\"textColor\":\"#FFFFFF\",\"activeTextColor\":\"#FFFFFF\",\"textFont\":\"arial,sans-serif\",\"textFontSize\":12},\"plans\":[{\"planName\":\"本年\",\"uuid\":\"39df03d3-69df-89a0-9651-b263b4ce9473\"},{\"planName\":\"本月\",\"uuid\":\"836b8310-3160-75d4-4be3-094cccfc7c8f\"},{\"planName\":\"本日\",\"uuid\":\"40415937-3a3a-8ed7-4db6-50488aacd2fe\"}]},\"style\":{\"background\":\"transparent\",\"height\":30,\"width\":120,\"position\":\"absolute\",\"top\":290,\"left\":670,\"border\":\"\",\"opacity\":1,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"zIndex\":10,\"userDrag\":\"element\"},\"event\":{},\"uuid\":\"026610ee-9430-634d-0f70-7bbe56fe69a8\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"border\",\"fonts\",\"zIndex\",\"opacity\",\"userDrag\",\"background\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":117,\"name\":\"饼图\",\"type\":\"pieChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":2,\"styleId\":\"8\",\"styleImg\":\"piechart.png\",\"style\":{\"seriesLayoutBy\":\"row\"},\"expression\":\"option = {\\n    grid: {\\n        bottom: 80\\n    },\\n    dataZoom: [],\\n    dataset: {\\n        source: [\\n        ]\\n    },\\n    tooltip: {\\n        show: true,\\n        trigger: \'item\',\\n        position: \'top\',\\n        textStyle: {\\n            color: \'#bfdfff\',\\n            fontSize: 12\\n        },\\n        backgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n        borderWidth: 1,\\n        borderColor: \'rgba(0,127,255,0.3)\',\\n        padding: null,\\n        axisPointer: {\\n            type: \\\"none\\\"\\n        },\\n    },\\n    legend: {//图例\\n        type: \\\"scroll\\\",\\n        pageIconColor: \'#aaa\',\\n        pageIconInactiveColor: \'#2f4554\',\\n        pageTextStyle: {\\n            color: \\\"#aaa\\\"\\n        },\\n        show: true,\\n        itemHeight: 5,\\n        bottom: 15,\\n        itemGap: 40,\\n      textStyle:{\\n       color:\'white\'\\n     }\\n    },\\n    series: [],\\n};\",\"apiId\":\"33\",\"parameterValue\":{\"session;userId\":\"personId\",\"timeType\":\"m\"},\"transform\":\"const arr = [data];\\nconst list = util.formatToTable(arr, [\'市电\',\'油机\',\'绿电\'], [\'cityElectValue\',\'oilElectValue\',\'greenElectValue\'])//二参是自定义表头\\nreturn list;\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\",\"selectedAPI\":[{\"name\":\"timeType\",\"array\":false,\"source\":\"input\",\"meaning\":\"timeType\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"\",\"delete\":false,\"parentName\":\"\",\"id\":\"1\"},{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"userId\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"2\"}]},\"style\":{\"background\":\"transparent\",\"height\":300,\"width\":400,\"left\":180,\"position\":\"absolute\",\"top\":280,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"836b8310-3160-75d4-4be3-094cccfc7c8f\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8},{\"compIndex\":117,\"name\":\"饼图\",\"type\":\"pieChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":2,\"styleId\":\"8\",\"styleImg\":\"piechart.png\",\"style\":{\"seriesLayoutBy\":\"row\"},\"expression\":\"option = {\\n    grid: {\\n        bottom: 80\\n    },\\n    dataZoom: [],\\n    dataset: {\\n        source: [\\n        ]\\n    },\\n    tooltip: {\\n        show: true,\\n        trigger: \'item\',\\n        position: \'top\',\\n        textStyle: {\\n            color: \'#bfdfff\',\\n            fontSize: 12\\n        },\\n        backgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n        borderWidth: 1,\\n        borderColor: \'rgba(0,127,255,0.3)\',\\n        padding: null,\\n        axisPointer: {\\n            type: \\\"none\\\"\\n        },\\n    },\\n    legend: {//图例\\n        type: \\\"scroll\\\",\\n        pageIconColor: \'#aaa\',\\n        pageIconInactiveColor: \'#2f4554\',\\n        pageTextStyle: {\\n            color: \\\"#aaa\\\"\\n        },\\n        show: true,\\n        itemHeight: 5,\\n        bottom: 15,\\n        itemGap: 40,\\n      textStyle:{\\n       color:\'white\'\\n     }\\n    },\\n    series: [],\\n};\",\"apiId\":\"33\",\"parameterValue\":{\"session;userId\":\"personId\",\"timeType\":\"d\"},\"transform\":\"const arr = [data];\\nconst list = util.formatToTable(arr, [\'市电\',\'油机\',\'绿电\'], [\'cityElectValue\',\'oilElectValue\',\'greenElectValue\'])//二参是自定义表头\\nreturn list;\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\",\"selectedAPI\":[{\"name\":\"timeType\",\"array\":false,\"source\":\"input\",\"meaning\":\"timeType\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"\",\"delete\":false,\"parentName\":\"\",\"id\":\"3\"},{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"userId\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"4\"}]},\"style\":{\"background\":\"transparent\",\"height\":300,\"width\":400,\"left\":180,\"position\":\"absolute\",\"top\":280,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"40415937-3a3a-8ed7-4db6-50488aacd2fe\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":130,\"name\":\"Tab切换\",\"type\":\"multiPlans\",\"data\":{\"multi_plans\":\"\",\"defaultPlan\":0,\"activePlanIndex\":0,\"control\":{\"show\":true,\"left\":0,\"top\":0,\"btnWidth\":30,\"btnHeight\":30,\"btnGap\":11,\"normalImage\":\"./../../../assets/img/charts/option-normal.png\",\"activeImage\":\"./../../../assets/img/charts/option-active.png\",\"gapImage\":\"./../../../assets/img/charts/option-gap.png\",\"textColor\":\"#FFFFFF\",\"activeTextColor\":\"#FFFFFF\",\"textFont\":\"arial,sans-serif\",\"textFontSize\":12},\"plans\":[{\"planName\":\"本年\",\"uuid\":\"b45d1d42-a097-34aa-8cca-b40b3664229a\"},{\"planName\":\"本月\",\"uuid\":\"9bdb8c36-bc02-3a9d-ac5f-0e4311bfc053\"},{\"planName\":\"本日\",\"uuid\":\"41d3ffe9-37ac-0a7d-8db0-b1e77baaf20d\"}]},\"style\":{\"background\":\"transparent\",\"height\":30,\"width\":120,\"position\":\"absolute\",\"top\":290,\"left\":1450,\"border\":\"\",\"opacity\":1,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"zIndex\":10,\"userDrag\":\"element\"},\"event\":{},\"uuid\":\"758448e1-5aeb-8c0d-d3e3-4d8c6047b743\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"border\",\"fonts\",\"zIndex\",\"opacity\",\"userDrag\",\"background\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8},{\"compIndex\":117,\"name\":\"饼图\",\"type\":\"pieChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":2,\"styleId\":\"8\",\"styleImg\":\"piechart.png\",\"style\":{\"seriesLayoutBy\":\"column\"},\"expression\":\"option = {\\n    grid: {\\n        bottom: 80\\n    },\\n    dataZoom: [],\\n    dataset: {\\n        source: [\\n        ]\\n    },\\n    tooltip: {\\n        show: true,\\n        trigger: \'item\',\\n        position: \'top\',\\n        textStyle: {\\n            color: \'#bfdfff\',\\n            fontSize: 12\\n        },\\n        backgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n        borderWidth: 1,\\n        borderColor: \'rgba(0,127,255,0.3)\',\\n        padding: null,\\n        axisPointer: {\\n            type: \\\"none\\\"\\n        },\\n    },\\n    legend: {//图例\\n        type: \\\"scroll\\\",\\n        pageIconColor: \'#aaa\',\\n        pageIconInactiveColor: \'#2f4554\',\\n        pageTextStyle: {\\n            color: \\\"#aaa\\\"\\n        },\\n        show: true,\\n        itemHeight: 5,\\n        bottom: 15,\\n        itemGap: 40,\\n      textStyle:{\\n       color:\'white\'\\n     }\\n    },\\n    series: [],\\n};\",\"apiId\":\"38\",\"parameterValue\":{\"session;userId\":\"personId\",\"timeType\":\"m\"},\"transform\":\"// {displayIndex:3, employeeName:\\\"c接口用户\\\", phone: 112233},]\\nconst list = util.formatToTable(data, [\'用电类型\',\'电量\'], [\'definitionName\',\'sumValue\'])//二参是自定义表头\\nreturn list;\\n\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\",\"selectedAPI\":[{\"name\":\"timeType\",\"array\":false,\"source\":\"input\",\"meaning\":\"时间类型\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"\",\"delete\":false,\"parentName\":\"\",\"id\":\"1\"},{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"人员ID\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"2\"}]},\"style\":{\"background\":\"transparent\",\"height\":320,\"width\":400,\"left\":990,\"position\":\"absolute\",\"top\":265,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"zIndex\":10,\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"9bdb8c36-bc02-3a9d-ac5f-0e4311bfc053\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8},{\"compIndex\":117,\"name\":\"饼图\",\"type\":\"pieChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":2,\"styleId\":\"8\",\"styleImg\":\"piechart.png\",\"style\":{\"seriesLayoutBy\":\"column\"},\"expression\":\"option = {\\n    grid: {\\n        bottom: 80\\n    },\\n    dataZoom: [],\\n    dataset: {\\n        source: [\\n        ]\\n    },\\n    tooltip: {\\n        show: true,\\n        trigger: \'item\',\\n        position: \'top\',\\n        textStyle: {\\n            color: \'#bfdfff\',\\n            fontSize: 12\\n        },\\n        backgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n        borderWidth: 1,\\n        borderColor: \'rgba(0,127,255,0.3)\',\\n        padding: null,\\n        axisPointer: {\\n            type: \\\"none\\\"\\n        },\\n    },\\n    legend: {//图例\\n        type: \\\"scroll\\\",\\n        pageIconColor: \'#aaa\',\\n        pageIconInactiveColor: \'#2f4554\',\\n        pageTextStyle: {\\n            color: \\\"#aaa\\\"\\n        },\\n        show: true,\\n        itemHeight: 5,\\n        bottom: 15,\\n        itemGap: 40,\\n      textStyle:{\\n       color:\'white\'\\n     }\\n    },\\n    series: [],\\n};\",\"apiId\":\"38\",\"parameterValue\":{\"session;userId\":\"personId\",\"timeType\":\"d\"},\"transform\":\"// {displayIndex:3, employeeName:\\\"c接口用户\\\", phone: 112233},]\\nconst list = util.formatToTable(data, [\'用电类型\',\'电量\'], [\'definitionName\',\'sumValue\'])//二参是自定义表头\\nreturn list;\\n\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\",\"selectedAPI\":[{\"name\":\"timeType\",\"array\":false,\"source\":\"input\",\"meaning\":\"时间类型\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"\",\"delete\":false,\"parentName\":\"\",\"id\":\"3\"},{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"人员ID\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"4\"}]},\"style\":{\"background\":\"transparent\",\"height\":320,\"width\":400,\"left\":990,\"position\":\"absolute\",\"top\":265,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"41d3ffe9-37ac-0a7d-8db0-b1e77baaf20d\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":130,\"name\":\"Tab切换\",\"type\":\"multiPlans\",\"data\":{\"multi_plans\":\"\",\"defaultPlan\":0,\"activePlanIndex\":0,\"control\":{\"show\":true,\"left\":0,\"top\":0,\"btnWidth\":30,\"btnHeight\":30,\"btnGap\":11,\"normalImage\":\"./../../../assets/img/charts/option-normal.png\",\"activeImage\":\"./../../../assets/img/charts/option-active.png\",\"gapImage\":\"./../../../assets/img/charts/option-gap.png\",\"textColor\":\"#FFFFFF\",\"activeTextColor\":\"#FFFFFF\",\"textFont\":\"arial,sans-serif\",\"textFontSize\":12},\"plans\":[{\"planName\":\"本年\",\"uuid\":\"2c7d8cd1-7319-44a2-d1b4-bc12bd92d1ad\"},{\"planName\":\"本月\",\"uuid\":\"1d9bd006-411d-f99a-4056-6c1d0cb4ad48\"},{\"planName\":\"本日\",\"uuid\":\"3385f573-1f53-6a17-693b-96a1003c190f\"}]},\"style\":{\"background\":\"transparent\",\"height\":30,\"width\":120,\"position\":\"absolute\",\"top\":680,\"left\":670,\"border\":\"\",\"opacity\":1,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"zIndex\":10,\"userDrag\":\"element\"},\"event\":{},\"uuid\":\"586fa278-6ab0-a3e1-20ea-5d0d5d713fa3\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"border\",\"fonts\",\"zIndex\",\"opacity\",\"userDrag\",\"background\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":115,\"name\":\"柱状图\",\"type\":\"barChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":1,\"styleId\":\"3\",\"styleImg\":\"stackbarchart.png\",\"style\":{\"seriesLayoutBy\":\"column\"},\"expression\":\"option = {\\n    grid: {\\n        bottom: 80\\n    },\\n    xAxis: {\\n        type: \'category\',\\n        data: null,\\n        show: true,\\n        axisTick: { show: false },\\n        boundaryGap: true,\\n        silent: true,\\n        axisLine: {//轴线\\n            show: true,\\n            lineStyle: {\\n                color: \'#BFDFFF\', //x轴轴线颜色\\n                opacity: 0.2,\\n                width: 1\\n            }\\n        },\\n        axisLabel: {//x轴文字\\n            show: true,\\n            overflow: \'break\',\\n            width: null,\\n            color: \'#BFDFFF\',\\n            fontFamily: \'\',\\n            fontSize: 13,\\n            ellipsis: \'...\'\\n        }\\n    },\\n    yAxis: {\\n        type: \'value\',\\n        show: true,\\n        splitNumber: 4,\\n        max: null,\\n        min: 0,\\n        silent: true,\\n        textStyle: { color: \'#BFDFFF\', fontFamily: \'\', fontSize: 14 },\\n        axisLine: {//轴线\\n            show: true,\\n            lineStyle: {\\n                color: \'#BFDFFF\',\\n                opacity: 0.2,\\n                width: 1\\n            }\\n        },\\n        axisLabel: {//y轴文字\\n            show: true,\\n            color: \'#BFDFFF\',\\n            fontFamily: \'\',\\n            fontSize: 13\\n        },\\n        splitLine: {//分割线\\n            lineStyle: {\\n                width: 1,\\n                color: \'#BFDFFF\',\\n                opacity: 0.1\\n            }\\n        }\\n    },\\n    dataZoom: [],\\n    dataset: {\\n        source: [\\n        ]\\n    },\\n    tooltip: {\\n        show: true,\\n        trigger: \'axis\',\\n        position: \'\',\\n        textStyle: {\\n            color: \'#bfdfff\',\\n            fontSize: 12\\n        },\\n        backgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n        borderWidth: 1,\\n        borderColor: \'rgba(0,127,255,0.3)\',\\n        padding: null,\\n        axisPointer: {\\n            type: \\\"none\\\"\\n        },\\n    },\\n    legend: {//图例\\n        type: \\\"scroll\\\",\\n        pageIconColor: \'#aaa\',\\n        pageIconInactiveColor: \'#2f4554\',\\n        pageTextStyle: {\\n            color: \\\"#aaa\\\"\\n        },\\n        show: true,\\n        itemHeight: 5,\\n        bottom: 15,\\n        itemGap: 40,\\n      textStyle:{\\n       color:\'white\'\\n     }\\n    },\\n    series: [],\\n};\\n\",\"apiId\":\"39\",\"parameterValue\":{\"session;userId\":\"personId\",\"timeType\":\"m\"},\"transform\":\"const list = util.formatToTable(data, [\'层级\',\'市电\',\'油机发电\',\'绿电\'], [\'resourceStructureName\',\'cityElectValue\',\'oilElectValue\',\'greenElectValue\'])//二参是自定义表头\\nreturn list;\\n\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\",\"selectedAPI\":[{\"name\":\"timeType\",\"array\":false,\"source\":\"input\",\"meaning\":\"timeType\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"\",\"delete\":false,\"parentName\":\"\",\"id\":\"1\"},{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"timeType\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"2\"}]},\"style\":{\"background\":\"transparent\",\"height\":300,\"width\":770,\"left\":20,\"position\":\"absolute\",\"top\":740,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"1d9bd006-411d-f99a-4056-6c1d0cb4ad48\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8},{\"compIndex\":115,\"name\":\"柱状图\",\"type\":\"barChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":1,\"styleId\":\"3\",\"styleImg\":\"stackbarchart.png\",\"style\":{\"seriesLayoutBy\":\"column\"},\"expression\":\"option = {\\n    grid: {\\n        bottom: 80\\n    },\\n    xAxis: {\\n        type: \'category\',\\n        data: null,\\n        show: true,\\n        axisTick: { show: false },\\n        boundaryGap: true,\\n        silent: true,\\n        axisLine: {//轴线\\n            show: true,\\n            lineStyle: {\\n                color: \'#BFDFFF\', //x轴轴线颜色\\n                opacity: 0.2,\\n                width: 1\\n            }\\n        },\\n        axisLabel: {//x轴文字\\n            show: true,\\n            overflow: \'break\',\\n            width: null,\\n            color: \'#BFDFFF\',\\n            fontFamily: \'\',\\n            fontSize: 13,\\n            ellipsis: \'...\'\\n        }\\n    },\\n    yAxis: {\\n        type: \'value\',\\n        show: true,\\n        splitNumber: 4,\\n        max: null,\\n        min: 0,\\n        silent: true,\\n        textStyle: { color: \'#BFDFFF\', fontFamily: \'\', fontSize: 14 },\\n        axisLine: {//轴线\\n            show: true,\\n            lineStyle: {\\n                color: \'#BFDFFF\',\\n                opacity: 0.2,\\n                width: 1\\n            }\\n        },\\n        axisLabel: {//y轴文字\\n            show: true,\\n            color: \'#BFDFFF\',\\n            fontFamily: \'\',\\n            fontSize: 13\\n        },\\n        splitLine: {//分割线\\n            lineStyle: {\\n                width: 1,\\n                color: \'#BFDFFF\',\\n                opacity: 0.1\\n            }\\n        }\\n    },\\n    dataZoom: [],\\n    dataset: {\\n        source: [\\n        ]\\n    },\\n    tooltip: {\\n        show: true,\\n        trigger: \'axis\',\\n        position: \'\',\\n        textStyle: {\\n            color: \'#bfdfff\',\\n            fontSize: 12\\n        },\\n        backgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n        borderWidth: 1,\\n        borderColor: \'rgba(0,127,255,0.3)\',\\n        padding: null,\\n        axisPointer: {\\n            type: \\\"none\\\"\\n        },\\n    },\\n    legend: {//图例\\n        type: \\\"scroll\\\",\\n        pageIconColor: \'#aaa\',\\n        pageIconInactiveColor: \'#2f4554\',\\n        pageTextStyle: {\\n            color: \\\"#aaa\\\"\\n        },\\n        show: true,\\n        itemHeight: 5,\\n        bottom: 15,\\n        itemGap: 40,\\n      textStyle:{\\n       color:\'white\'\\n     }\\n    },\\n    series: [],\\n};\\n\",\"apiId\":\"39\",\"parameterValue\":{\"session;userId\":\"personId\",\"timeType\":\"d\"},\"transform\":\"const list = util.formatToTable(data, [\'层级\',\'市电\',\'油机发电\',\'绿电\'], [\'resourceStructureName\',\'cityElectValue\',\'oilElectValue\',\'greenElectValue\'])//二参是自定义表头\\nreturn list;\\n\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\",\"selectedAPI\":[{\"name\":\"timeType\",\"array\":false,\"source\":\"input\",\"meaning\":\"timeType\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"\",\"delete\":false,\"parentName\":\"\",\"id\":\"3\"},{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"timeType\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"4\"}]},\"style\":{\"background\":\"transparent\",\"height\":300,\"width\":770,\"left\":20,\"position\":\"absolute\",\"top\":740,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"3385f573-1f53-6a17-693b-96a1003c190f\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":130,\"name\":\"Tab切换\",\"type\":\"multiPlans\",\"data\":{\"multi_plans\":\"\",\"defaultPlan\":0,\"activePlanIndex\":0,\"control\":{\"show\":true,\"left\":0,\"top\":0,\"btnWidth\":30,\"btnHeight\":30,\"btnGap\":11,\"normalImage\":\"./../../../assets/img/charts/option-normal.png\",\"activeImage\":\"./../../../assets/img/charts/option-active.png\",\"gapImage\":\"./../../../assets/img/charts/option-gap.png\",\"textColor\":\"#FFFFFF\",\"activeTextColor\":\"#FFFFFF\",\"textFont\":\"arial,sans-serif\",\"textFontSize\":12},\"plans\":[{\"planName\":\"本年\",\"uuid\":\"b64611f3-23e6-e0d7-b49a-503efc3efb72\"},{\"planName\":\"本月\",\"uuid\":\"5e72996a-987e-6537-7275-00151d094e37\"},{\"planName\":\"本日\",\"uuid\":\"c12026b0-7c2f-ad28-3c44-e74e7c545fbf\"}]},\"style\":{\"background\":\"transparent\",\"height\":30,\"width\":120,\"position\":\"absolute\",\"top\":680,\"left\":1450,\"border\":\"\",\"opacity\":1,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"zIndex\":10,\"userDrag\":\"element\"},\"event\":{},\"uuid\":\"10c67929-d944-2937-d440-11bc36ed4b15\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"border\",\"fonts\",\"zIndex\",\"opacity\",\"userDrag\",\"background\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":118,\"name\":\"折线图\",\"type\":\"lineChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":0,\"styleId\":\"12\",\"styleImg\":\"linechart.png\",\"style\":{\"seriesLayoutBy\":\"column\"},\"expression\":\"option = {\\n\\tgrid: {\\n\\t\\tbottom: 80\\n\\t},\\n\\txAxis: {\\n\\t\\ttype: \'category\',\\n\\t\\tdata: null,\\n\\t\\tshow: true,\\n\\t\\taxisTick: { show: false },\\n\\t\\tboundaryGap: true,\\n\\t\\tsilent: true,\\n\\t\\taxisLine: {//轴线\\n\\t\\t\\tshow: true,\\n\\t\\t\\tlineStyle: {\\n\\t\\t\\t\\tcolor: \'#BFDFFF\', //x轴轴线颜色\\n\\t\\t\\t\\topacity: 0.2,\\n\\t\\t\\t\\twidth: 1\\n\\t\\t\\t}\\n\\t\\t},\\n\\t\\taxisLabel: {//x轴文字\\n\\t\\t\\tshow: true,\\n\\t\\t\\toverflow: \'break\',\\n\\t\\t\\twidth: null,\\n\\t\\t\\tcolor: \'#BFDFFF\',\\n\\t\\t\\tfontFamily: \'\',\\n\\t\\t\\tfontSize: 13,\\n\\t\\t\\tellipsis: \'...\'\\n\\t\\t}\\n\\t},\\n\\tyAxis: {\\n\\t\\ttype: \'value\',\\n\\t\\tshow: true,\\n\\t\\tsplitNumber: 4,\\n\\t\\tmax: null,\\n\\t\\tmin: 0,\\n\\t\\tsilent: true,\\n\\t\\ttextStyle: { color: \'#BFDFFF\', fontFamily: \'\', fontSize: 14 },\\n\\t\\taxisLine: {//轴线\\n\\t\\t\\tshow: true,\\n\\t\\t\\tlineStyle: {\\n\\t\\t\\t\\tcolor: \'#BFDFFF\',\\n\\t\\t\\t\\topacity: 0.2,\\n\\t\\t\\t\\twidth: 1\\n\\t\\t\\t}\\n\\t\\t},\\n\\t\\taxisLabel: {//y轴文字\\n\\t\\t\\tshow: true,\\n\\t\\t\\tcolor: \'#BFDFFF\',\\n\\t\\t\\tfontFamily: \'\',\\n\\t\\t\\tfontSize: 13\\n\\t\\t},\\n\\t\\tsplitLine: {//分割线\\n\\t\\t\\tlineStyle: {\\n\\t\\t\\t\\twidth: 1,\\n\\t\\t\\t\\tcolor: \'#BFDFFF\',\\n\\t\\t\\t\\topacity: 0.1\\n\\t\\t\\t}\\n\\t\\t}\\n\\t},\\n\\tdataZoom: [],\\n\\tdataset: {\\n\\t\\tsource: [\\n\\t\\t]\\n\\t},\\n\\ttooltip: {\\n\\t\\tshow: true,\\n\\t\\ttrigger: \'item\',\\n\\t\\tposition: \'top\',\\n\\t\\ttextStyle: {\\n\\t\\t\\tcolor: \'#bfdfff\',\\n\\t\\t\\tfontSize: 12\\n\\t\\t},\\n\\t\\tbackgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n\\t\\tborderWidth: 1,\\n\\t\\tborderColor: \'rgba(0,127,255,0.3)\',\\n\\t\\tpadding: null,\\n\\t\\taxisPointer: {\\n\\t\\t\\ttype: \\\"none\\\"\\n\\t\\t},\\n\\t},\\n\\tlegend: {//图例\\n\\t\\ttype: \\\"scroll\\\",\\n\\t\\tpageIconColor: \'#aaa\',\\n\\t\\tpageIconInactiveColor: \'#2f4554\',\\n\\t\\tpageTextStyle: {\\n\\t\\t\\tcolor: \\\"#aaa\\\"\\n\\t\\t},\\n\\t\\tshow: true,\\n\\t\\titemHeight: 5,\\n\\t\\tbottom: 15,\\n\\t\\titemGap: 40,\\n      textStyle:{\\n       color:\'white\'\\n     }\\n\\t},\\n\\tseries: [],\\n};\",\"apiId\":\"41\",\"parameterValue\":{\"session;userId\":\"personId\",\"timeType\":\"m\"},\"transform\":\"const list = util.formatToTable(data, [\'时间\',\'PUE值\'], [\'time\',\'avgValue\'])//二参是自定义表头\\nreturn util.arrArrFormatTime(list, \'$d\');//二参格式可选$[y|Q|M|W|d|h|m|s]\\n\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\",\"selectedAPI\":[{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"userId\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"1\"},{\"name\":\"timeType\",\"array\":false,\"source\":\"input\",\"meaning\":\"timeType\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"\",\"delete\":false,\"parentName\":\"\",\"id\":\"2\"}]},\"style\":{\"background\":\"transparent\",\"height\":370,\"width\":675,\"left\":865,\"position\":\"absolute\",\"top\":720,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"5e72996a-987e-6537-7275-00151d094e37\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":118,\"name\":\"折线图\",\"type\":\"lineChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":0,\"styleId\":\"12\",\"styleImg\":\"linechart.png\",\"style\":{\"seriesLayoutBy\":\"column\"},\"expression\":\"option = {\\n\\tgrid: {\\n\\t\\tbottom: 80\\n\\t},\\n\\txAxis: {\\n\\t\\ttype: \'category\',\\n\\t\\tdata: null,\\n\\t\\tshow: true,\\n\\t\\taxisTick: { show: false },\\n\\t\\tboundaryGap: true,\\n\\t\\tsilent: true,\\n\\t\\taxisLine: {//轴线\\n\\t\\t\\tshow: true,\\n\\t\\t\\tlineStyle: {\\n\\t\\t\\t\\tcolor: \'#BFDFFF\', //x轴轴线颜色\\n\\t\\t\\t\\topacity: 0.2,\\n\\t\\t\\t\\twidth: 1\\n\\t\\t\\t}\\n\\t\\t},\\n\\t\\taxisLabel: {//x轴文字\\n\\t\\t\\tshow: true,\\n\\t\\t\\toverflow: \'break\',\\n\\t\\t\\twidth: null,\\n\\t\\t\\tcolor: \'#BFDFFF\',\\n\\t\\t\\tfontFamily: \'\',\\n\\t\\t\\tfontSize: 13,\\n\\t\\t\\tellipsis: \'...\'\\n\\t\\t}\\n\\t},\\n\\tyAxis: {\\n\\t\\ttype: \'value\',\\n\\t\\tshow: true,\\n\\t\\tsplitNumber: 4,\\n\\t\\tmax: null,\\n\\t\\tmin: 0,\\n\\t\\tsilent: true,\\n\\t\\ttextStyle: { color: \'#BFDFFF\', fontFamily: \'\', fontSize: 14 },\\n\\t\\taxisLine: {//轴线\\n\\t\\t\\tshow: true,\\n\\t\\t\\tlineStyle: {\\n\\t\\t\\t\\tcolor: \'#BFDFFF\',\\n\\t\\t\\t\\topacity: 0.2,\\n\\t\\t\\t\\twidth: 1\\n\\t\\t\\t}\\n\\t\\t},\\n\\t\\taxisLabel: {//y轴文字\\n\\t\\t\\tshow: true,\\n\\t\\t\\tcolor: \'#BFDFFF\',\\n\\t\\t\\tfontFamily: \'\',\\n\\t\\t\\tfontSize: 13\\n\\t\\t},\\n\\t\\tsplitLine: {//分割线\\n\\t\\t\\tlineStyle: {\\n\\t\\t\\t\\twidth: 1,\\n\\t\\t\\t\\tcolor: \'#BFDFFF\',\\n\\t\\t\\t\\topacity: 0.1\\n\\t\\t\\t}\\n\\t\\t}\\n\\t},\\n\\tdataZoom: [],\\n\\tdataset: {\\n\\t\\tsource: [\\n\\t\\t]\\n\\t},\\n\\ttooltip: {\\n\\t\\tshow: true,\\n\\t\\ttrigger: \'item\',\\n\\t\\tposition: \'top\',\\n\\t\\ttextStyle: {\\n\\t\\t\\tcolor: \'#bfdfff\',\\n\\t\\t\\tfontSize: 12\\n\\t\\t},\\n\\t\\tbackgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n\\t\\tborderWidth: 1,\\n\\t\\tborderColor: \'rgba(0,127,255,0.3)\',\\n\\t\\tpadding: null,\\n\\t\\taxisPointer: {\\n\\t\\t\\ttype: \\\"none\\\"\\n\\t\\t},\\n\\t},\\n\\tlegend: {//图例\\n\\t\\ttype: \\\"scroll\\\",\\n\\t\\tpageIconColor: \'#aaa\',\\n\\t\\tpageIconInactiveColor: \'#2f4554\',\\n\\t\\tpageTextStyle: {\\n\\t\\t\\tcolor: \\\"#aaa\\\"\\n\\t\\t},\\n\\t\\tshow: true,\\n\\t\\titemHeight: 5,\\n\\t\\tbottom: 15,\\n\\t\\titemGap: 40,\\n      textStyle:{\\n       color:\'white\'\\n     }\\n\\t},\\n\\tseries: [],\\n};\",\"apiId\":\"41\",\"parameterValue\":{\"session;userId\":\"personId\",\"timeType\":\"d\"},\"transform\":\"const list = util.formatToTable(data, [\'时间\',\'PUE值\'], [\'time\',\'avgValue\'])//二参是自定义表头\\nreturn util.arrArrFormatTime(list, \'$h\');//二参格式可选$[y|Q|M|W|d|h|m|s]\\n\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\",\"selectedAPI\":[{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"userId\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"1\"},{\"name\":\"timeType\",\"array\":false,\"source\":\"input\",\"meaning\":\"timeType\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"\",\"delete\":false,\"parentName\":\"\",\"id\":\"2\"}]},\"style\":{\"background\":\"transparent\",\"height\":370,\"width\":675,\"left\":865,\"position\":\"absolute\",\"top\":720,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"c12026b0-7c2f-ad28-3c44-e74e7c545fbf\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false},{\"compIndex\":2,\"name\":\"文字\",\"type\":\"text\",\"data\":{\"text_val\":\"单位：kW⋅h\",\"writeMode\":\"horizontal-tb\"},\"style\":{\"height\":30,\"width\":100,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":285,\"left\":26,\"fontCol\":\"#BFDFFF\",\"fontSize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"uuid\":\"444ee452-6301-630c-63b8-ed10e6969160\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8},{\"compIndex\":2,\"name\":\"文字\",\"type\":\"text\",\"data\":{\"text_val\":\"单位：kW⋅h\",\"writeMode\":\"horizontal-tb\"},\"style\":{\"height\":30,\"width\":100,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":285,\"left\":870,\"fontCol\":\"#BFDFFF\",\"fontSize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"uuid\":\"16ac99a2-cf2e-e966-e9ed-e0d67887de9f\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8},{\"compIndex\":2,\"name\":\"文字\",\"type\":\"text\",\"data\":{\"text_val\":\"单位：kW⋅h\",\"writeMode\":\"horizontal-tb\"},\"style\":{\"height\":30,\"width\":100,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":682,\"left\":26,\"fontCol\":\"#BFDFFF\",\"fontSize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"uuid\":\"b9ef1b38-8410-9fab-db9e-252c918d4367\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8},{\"compIndex\":2,\"name\":\"文字\",\"type\":\"text\",\"data\":{\"text_val\":\"单位：kW⋅h\",\"writeMode\":\"horizontal-tb\"},\"style\":{\"height\":30,\"width\":100,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":1204,\"left\":26,\"fontCol\":\"#BFDFFF\",\"fontSize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"uuid\":\"d0960ca6-8d89-f614-add6-0a69601dc366\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1600,\"height\":2000,\"background\":\"#fff\",\"sizeState\":null,\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":8,\"action\":false}]',NULL,'',NULL,0,NULL,NULL);
-- 碳看板首页电信
insert into graphicpage(Id, Type, GroupId, PageCategory, BaseEquipmentId, ObjectId,  Name, AppendName, TemplateId, Data, Style, Children, Description, RouterType, CrumbsRouterType, PageCanFullScreen, SceneId, IsDefault)
values('2012','page',0,8,-1,2,'EnergyCarbonDashboard','',NULL,'{\"api\":\"baseEquipmentId=undefined\",\"time\":\"1000\"}','{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\"}','[{\"compIndex\":62,\"name\":\"面板\",\"type\":\"card\",\"data\":{\"text_val\":\"碳排放\",\"card_val\":\"card\",\"sky_val\":\"card\",\"panelStyleType\":\"\"},\"style\":{\"height\":317,\"width\":501,\"left\":0,\"position\":\"absolute\",\"top\":0,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"uuid\":\"c486bffd-82fd-9195-871a-a5f7d9f2111c\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"userDrag\",\"zIndex\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":62,\"name\":\"面板\",\"type\":\"card\",\"data\":{\"text_val\":\"碳抵消碳排放占比\",\"card_val\":\"card\",\"sky_val\":\"card\",\"panelStyleType\":\"\"},\"style\":{\"height\":320,\"width\":494,\"left\":993,\"position\":\"absolute\",\"top\":0,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"uuid\":\"a75e1135-eef7-3dd7-172f-929b9610ad33\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"userDrag\",\"zIndex\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":62,\"name\":\"面板\",\"type\":\"card\",\"data\":{\"text_val\":\"下级碳排放排名\",\"card_val\":\"card\",\"sky_val\":\"card\",\"panelStyleType\":\"\"},\"style\":{\"height\":341,\"width\":991,\"left\":502,\"position\":\"absolute\",\"top\":321,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"uuid\":\"2605daa2-6338-92d3-7d1c-3052303c1fcb\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"userDrag\",\"zIndex\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":2,\"name\":\"文字\",\"type\":\"text\",\"data\":{\"text_val\":\"碳排放总量 / 同比 / 环比（kgC）\",\"writeMode\":\"horizontal-tb\"},\"style\":{\"height\":30,\"width\":400,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":120,\"left\":15,\"fontCol\":\"#BFDFFF\",\"fontSize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"uuid\":\"2d43ff3e-5687-e78a-01e1-15d8f0e74235\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":62,\"name\":\"面板\",\"type\":\"card\",\"data\":{\"text_val\":\"碳排放溯源\",\"card_val\":\"card\",\"sky_val\":\"card\",\"panelStyleType\":\"\"},\"style\":{\"height\":341,\"width\":499,\"left\":0,\"position\":\"absolute\",\"top\":321,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"uuid\":\"36379975-a31f-1d72-936c-800891e23fab\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"userDrag\",\"zIndex\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":62,\"name\":\"面板\",\"type\":\"card\",\"data\":{\"text_val\":\"碳排放结构趋势\",\"card_val\":\"card\",\"sky_val\":\"card\",\"panelStyleType\":\"\"},\"style\":{\"height\":397,\"width\":1493,\"left\":0,\"position\":\"absolute\",\"top\":663,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"uuid\":\"7859ffa1-bfe7-1388-74e8-b2e2388a4414\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"userDrag\",\"zIndex\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":117,\"name\":\"饼图\",\"type\":\"pieChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":2,\"styleId\":\"8\",\"styleImg\":\"piechart.png\",\"style\":{\"seriesLayoutBy\":\"row\"},\"expression\":\"option = {\\n    grid: {\\n        bottom: 80\\n    },\\n    dataZoom: [],\\n    dataset: {\\n        source: [\\n        ]\\n    },\\n    tooltip: {\\n        show: true,\\n        trigger: \'item\',\\n        position: \'top\',\\n        textStyle: {\\n            color: \'#bfdfff\',\\n            fontSize: 12\\n        },\\n        backgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n        borderWidth: 1,\\n        borderColor: \'rgba(0,127,255,0.3)\',\\n        padding: null,\\n        axisPointer: {\\n            type: \\\"none\\\"\\n        },\\n    },\\n    legend: {//图例\\n        type: \\\"scroll\\\",\\n        pageIconColor: \'#aaa\',\\n        pageIconInactiveColor: \'#2f4554\',\\n        pageTextStyle: {\\n            color: \\\"#aaa\\\"\\n        },\\n        show: true,\\n        itemHeight: 5,\\n        bottom: 15,\\n        itemGap: 40,\\n       textStyle:{\\n       \\tcolor:\'white\'\\n      }\\n    },\\n    series: [],\\n};\",\"apiId\":\"43\",\"parameterValue\":{\"session;userId\":\"personId\",\"timeType\":\"y\"},\"transform\":\"const arr = [data];\\nconst list = util.formatToTable(arr, [\'市电碳排放\',\'油机碳排放\'], [\'cityElectCarbonValue\',\'oilElectCarbonValue\'])//二参是自定义表头\\nreturn list;\\n\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\",\"selectedAPI\":[{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"人员ID\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"5\"},{\"name\":\"timeType\",\"array\":false,\"source\":\"input\",\"meaning\":\"时间类型\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"\",\"delete\":false,\"parentName\":\"\",\"id\":\"6\"}]},\"style\":{\"background\":\"transparent\",\"height\":233,\"width\":322,\"left\":80,\"position\":\"absolute\",\"top\":415,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":false},\"event\":{},\"uuid\":\"dedbddb9-c903-be6a-54e1-47c1c12b2d02\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":123,\"name\":\"多时间文本数字\",\"type\":\"textNumber\",\"data\":{\"text_number_val\":\"\",\"text_number_value\":\"4122201.6            4823229.6            4823229.6\",\"chartId\":3,\"styleId\":\"17\",\"styleImg\":\"textnumber.png\",\"expression\":\"\",\"apiId\":\"35\",\"parameterValue\":{\"userId\":\"personId\",\"timeType\":\"y\"},\"planFlag\":false,\"planList\":[],\"transform\":\"return (data.totalCarbonValue).toString() + \'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'\\n  + (data.yoyTotalCarbonValue).toString() + \\\"\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\"\\n  + (data.qoqTotalCarbonValue).toString();\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\"},\"style\":{\"height\":30,\"width\":400,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":195,\"left\":50,\"fontCol\":\"#BFDFFF\",\"fontsize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":false},\"event\":{},\"uuid\":\"cdcbc82e-adba-f4d2-59f3-c4e115cc1c25\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":123,\"name\":\"多时间文本数字\",\"type\":\"textNumber\",\"data\":{\"text_number_val\":\"\",\"text_number_value\":\"334195.2            480650.4            382946.4\",\"chartId\":3,\"styleId\":0,\"styleImg\":0,\"expression\":\"\",\"apiId\":\"35\",\"parameterValue\":{\"userId\":\"personId\",\"timeType\":\"m\"},\"planFlag\":false,\"planList\":[],\"transform\":\"return (data.totalCarbonValue).toString() + \'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'\\n  + (data.yoyTotalCarbonValue).toString() + \\\"\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\"\\n  + (data.qoqTotalCarbonValue).toString();\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\"},\"style\":{\"height\":30,\"width\":400,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":195,\"left\":50,\"fontCol\":\"#BFDFFF\",\"fontsize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"17bbeb11-5244-ee56-4022-a2e0e32f1ca7\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":123,\"name\":\"多时间文本数字\",\"type\":\"textNumber\",\"data\":{\"text_number_val\":\"\",\"text_number_value\":\"13402.8            17940.24            12479.52\",\"chartId\":3,\"styleId\":0,\"styleImg\":0,\"expression\":\"\",\"apiId\":\"35\",\"parameterValue\":{\"userId\":\"personId\",\"timeType\":\"d\"},\"planFlag\":false,\"planList\":[],\"transform\":\"return (data.totalCarbonValue).toString() + \'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'\\n  + (data.yoyTotalCarbonValue).toString() + \\\"\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\"\\n  + (data.qoqTotalCarbonValue).toString();\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\"},\"style\":{\"height\":30,\"width\":400,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":195,\"left\":50,\"fontCol\":\"#BFDFFF\",\"fontsize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"3f6993aa-7360-8dcd-3be7-e7b11318041b\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":115,\"name\":\"柱状图\",\"type\":\"barChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":1,\"styleId\":\"3\",\"styleImg\":\"stackbarchart.png\",\"style\":{\"seriesLayoutBy\":\"column\"},\"expression\":\"option = {\\n    grid: {\\n        bottom: 80\\n    },\\n    xAxis: {\\n        type: \'category\',\\n        data: null,\\n        show: true,\\n        axisTick: { show: false },\\n        boundaryGap: true,\\n        silent: true,\\n        axisLine: {//轴线\\n            show: true,\\n            lineStyle: {\\n                color: \'#BFDFFF\', //x轴轴线颜色\\n                opacity: 0.2,\\n                width: 1\\n            }\\n        },\\n        axisLabel: {//x轴文字\\n            show: true,\\n            overflow: \'break\',\\n            width: null,\\n            color: \'#BFDFFF\',\\n            fontFamily: \'\',\\n            fontSize: 13,\\n            ellipsis: \'...\'\\n        }\\n    },\\n    yAxis: {\\n        type: \'value\',\\n        show: true,\\n        splitNumber: 4,\\n        max: null,\\n        min: 0,\\n        silent: true,\\n        textStyle: { color: \'#BFDFFF\', fontFamily: \'\', fontSize: 14 },\\n        axisLine: {//轴线\\n            show: true,\\n            lineStyle: {\\n                color: \'#BFDFFF\',\\n                opacity: 0.2,\\n                width: 1\\n            }\\n        },\\n        axisLabel: {//y轴文字\\n            show: true,\\n            color: \'#BFDFFF\',\\n            fontFamily: \'\',\\n            fontSize: 13\\n        },\\n        splitLine: {//分割线\\n            lineStyle: {\\n                width: 1,\\n                color: \'#BFDFFF\',\\n                opacity: 0.1\\n            }\\n        }\\n    },\\n    dataZoom: [],\\n    dataset: {\\n        source: [\\n        ]\\n    },\\n    tooltip: {\\n        show: true,\\n        trigger: \'axis\',\\n        position: \'\',\\n        textStyle: {\\n            color: \'#bfdfff\',\\n            fontSize: 12\\n        },\\n        backgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n        borderWidth: 1,\\n        borderColor: \'rgba(0,127,255,0.3)\',\\n        padding: null,\\n        axisPointer: {\\n            type: \\\"none\\\"\\n        },\\n    },\\n    legend: {//图例\\n        type: \\\"scroll\\\",\\n        pageIconColor: \'#aaa\',\\n        pageIconInactiveColor: \'#2f4554\',\\n        pageTextStyle: {\\n            color: \\\"#aaa\\\"\\n        },\\n        show: true,\\n        itemHeight: 5,\\n        bottom: 15,\\n        itemGap: 40,\\n      textStyle:{\\n       color:\'white\'\\n     }\\n    },\\n    series: [],\\n};\\n\",\"apiId\":\"40\",\"parameterValue\":{\"session;userId\":\"personId\"},\"transform\":\"\\n// 第三种情况,接口返回数据data格式是对象的数组,如下\\n// [{displayIndex:1, employeeName:\\\"a接口用户\\\", phone: 123456},\\n// {displayIndex:2, employeeName:\\\"b接口用户\\\", phone: 654321},\\n// {displayIndex:3, employeeName:\\\"c接口用户\\\", phone: 112233},]\\nconst list = util.formatToTable(data, [\'时间\',\'市电碳排放\',\'油机发电碳排放\',\'绿电碳抵消\'], [\'time\',\'cityElectCarbonValue\',\'oilElectCarbonValue\',\'greenElectCarbonValue\'])//二参是自定义表头\\nreturn util.arrArrFormatTime(list, \'$M\');//二参格式可选$[y|Q|M|W|d|h|m|s]\\n\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\",\"selectedAPI\":[{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"userId\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"2\"}]},\"style\":{\"background\":\"transparent\",\"height\":300,\"width\":1450,\"left\":18,\"position\":\"absolute\",\"top\":733,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"event\":{},\"uuid\":\"73471b93-3617-1442-076c-90639030642e\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":117,\"name\":\"饼图\",\"type\":\"pieChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":2,\"styleId\":\"8\",\"styleImg\":\"piechart.png\",\"style\":{\"seriesLayoutBy\":\"row\"},\"expression\":\"option = {\\n    grid: {\\n        bottom: 80\\n    },\\n    dataZoom: [],\\n    dataset: {\\n        source: [\\n        ]\\n    },\\n    tooltip: {\\n        show: true,\\n        trigger: \'item\',\\n        position: \'top\',\\n        textStyle: {\\n            color: \'#bfdfff\',\\n            fontSize: 12\\n        },\\n        backgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n        borderWidth: 1,\\n        borderColor: \'rgba(0,127,255,0.3)\',\\n        padding: null,\\n        axisPointer: {\\n            type: \\\"none\\\"\\n        },\\n    },\\n    legend: {//图例\\n        type: \\\"scroll\\\",\\n        pageIconColor: \'#aaa\',\\n        pageIconInactiveColor: \'#2f4554\',\\n        pageTextStyle: {\\n            color: \\\"#aaa\\\"\\n        },\\n        show: true,\\n        itemHeight: 5,\\n        bottom: 15,\\n        itemGap: 40,\\n       textStyle:{\\n       \\tcolor:\'white\'\\n      }\\n    },\\n    series: [],\\n};\",\"apiId\":\"36\",\"parameterValue\":{\"session;userId\":\"personId\",\"timeType\":\"y\"},\"transform\":\"// 第一种情况,接口返回数据data格式是二维数组,如下\\n// [[\\\"区域\\\",\\\"测试\\\"],[\\\"停电站点数\\\",0]]\\ndata.cityElectCarbonValue = Number((data.cityElectCarbonValue+data.oilElectCarbonValue).toFixed(2)); \\n\\nconst arr = [data];\\nconst list = util.formatToTable(arr, [\'碳排放\',\'碳抵消\'], [\'cityElectCarbonValue\',\'greenElectCarbonValue\'])//二参是自定义表头\\nreturn list;\\n\\n\\n//return data.oilElectValue;\\n\\n//return data.greenElectValue;\\n\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\",\"selectedAPI\":[{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"人员ID\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"3\"},{\"name\":\"timeType\",\"array\":false,\"source\":\"input\",\"meaning\":\"时间类型\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"\",\"delete\":false,\"parentName\":\"\",\"id\":\"4\"}]},\"style\":{\"background\":\"transparent\",\"height\":244,\"width\":385,\"left\":1044,\"position\":\"absolute\",\"top\":61,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":false},\"event\":{},\"uuid\":\"d3ee11b1-d916-c167-4612-4c6e0981df07\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":62,\"name\":\"面板\",\"type\":\"card\",\"data\":{\"text_val\":\"碳抵消\",\"card_val\":\"card\",\"sky_val\":\"card\",\"panelStyleType\":\"\"},\"style\":{\"height\":319,\"width\":488,\"left\":503,\"position\":\"absolute\",\"top\":0,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"uuid\":\"349709fc-07ad-13ed-4021-e75f9714909e\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"userDrag\",\"zIndex\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":2,\"name\":\"文字\",\"type\":\"text\",\"data\":{\"text_val\":\"碳抵消总量 / 同比 / 环比（kgC）\",\"writeMode\":\"horizontal-tb\"},\"style\":{\"height\":30,\"width\":400,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":120,\"left\":555,\"fontCol\":\"#BFDFFF\",\"fontSize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"uuid\":\"2ec07556-f7b2-af54-d094-d90112a2e731\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3},{\"compIndex\":123,\"name\":\"多时间文本数字\",\"type\":\"textNumber\",\"data\":{\"text_number_val\":\"\",\"text_number_value\":\"177167.52            200531.52            200531.52\",\"chartId\":3,\"styleId\":\"17\",\"styleImg\":\"textnumber.png\",\"expression\":\"\",\"apiId\":\"45\",\"parameterValue\":{\"userId\":\"personId\",\"timeType\":\"y\"},\"planFlag\":false,\"planList\":[],\"transform\":\"return (data.greenElectCarbonValue).toString() + \'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.yoyGreenElectCarbonValue).toString() + \'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.qoqGreenElectCarbonValue).toString();\\n\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\"},\"style\":{\"height\":30,\"width\":400,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":195,\"left\":555,\"fontCol\":\"#BFDFFF\",\"fontsize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":false},\"event\":{},\"uuid\":\"2583d04f-0b69-18a8-19cf-86b7fd566045\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":123,\"name\":\"多时间文本数字\",\"type\":\"textNumber\",\"data\":{\"text_number_val\":\"\",\"text_number_value\":\"16752.96            26435.52            19569.6\",\"chartId\":3,\"styleId\":\"17\",\"styleImg\":\"textnumber.png\",\"expression\":\"\",\"apiId\":\"45\",\"parameterValue\":{\"userId\":\"personId\",\"timeType\":\"m\"},\"planFlag\":false,\"planList\":[],\"transform\":\"// 第一种情况,接口返回数据data格式是二维数组,如下\\n// [[\\\"区域\\\",\\\"测试\\\"],[\\\"停电站点数\\\",0]]\\nreturn (data.greenElectCarbonValue).toString() + \'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.yoyGreenElectCarbonValue).toString() + \'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.qoqGreenElectCarbonValue).toString();\\n\\n\\n\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\"},\"style\":{\"height\":30,\"width\":400,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":195,\"left\":555,\"fontCol\":\"#BFDFFF\",\"fontsize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"aa2c38e0-5967-3455-af37-525beceb771e\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3},{\"compIndex\":123,\"name\":\"多时间文本数字\",\"type\":\"textNumber\",\"data\":{\"text_number_val\":\"\",\"text_number_value\":\"102.05            255.41            478.51\",\"chartId\":3,\"styleId\":\"17\",\"styleImg\":\"textnumber.png\",\"expression\":\"\",\"apiId\":\"45\",\"parameterValue\":{\"userId\":\"personId\",\"timeType\":\"d\"},\"planFlag\":false,\"planList\":[],\"transform\":\"return (data.greenElectCarbonValue).toString() + \'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.yoyGreenElectCarbonValue).toString() + \'\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\\\\xa0\'+ (data.qoqGreenElectCarbonValue).toString();\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\"},\"style\":{\"height\":30,\"width\":400,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":195,\"left\":555,\"fontCol\":\"#BFDFFF\",\"fontsize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"e65284dc-c878-f262-7d32-5864413217ca\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":115,\"name\":\"柱状图\",\"type\":\"barChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":1,\"styleId\":\"3\",\"styleImg\":\"stackbarchart.png\",\"style\":{\"seriesLayoutBy\":\"column\"},\"expression\":\"option = {\\n    grid: {\\n        bottom: 80\\n    },\\n    xAxis: {\\n        type: \'category\',\\n        data: null,\\n        show: true,\\n        axisTick: { show: false },\\n        boundaryGap: true,\\n        silent: true,\\n        axisLine: {//轴线\\n            show: true,\\n            lineStyle: {\\n                color: \'#BFDFFF\', //x轴轴线颜色\\n                opacity: 0.2,\\n                width: 1\\n            }\\n        },\\n        axisLabel: {//x轴文字\\n            show: true,\\n            overflow: \'break\',\\n            width: null,\\n            color: \'#BFDFFF\',\\n            fontFamily: \'\',\\n            fontSize: 13,\\n            ellipsis: \'...\'\\n        }\\n    },\\n    yAxis: {\\n        type: \'value\',\\n        show: true,\\n        splitNumber: 4,\\n        max: null,\\n        min: 0,\\n        silent: true,\\n        textStyle: { color: \'#BFDFFF\', fontFamily: \'\', fontSize: 14 },\\n        axisLine: {//轴线\\n            show: true,\\n            lineStyle: {\\n                color: \'#BFDFFF\',\\n                opacity: 0.2,\\n                width: 1\\n            }\\n        },\\n        axisLabel: {//y轴文字\\n            show: true,\\n            color: \'#BFDFFF\',\\n            fontFamily: \'\',\\n            fontSize: 13\\n        },\\n        splitLine: {//分割线\\n            lineStyle: {\\n                width: 1,\\n                color: \'#BFDFFF\',\\n                opacity: 0.1\\n            }\\n        }\\n    },\\n    dataZoom: [],\\n    dataset: {\\n        source: [\\n        ]\\n    },\\n    tooltip: {\\n        show: true,\\n        trigger: \'axis\',\\n        position: \'\',\\n        textStyle: {\\n            color: \'#bfdfff\',\\n            fontSize: 12\\n        },\\n        backgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n        borderWidth: 1,\\n        borderColor: \'rgba(0,127,255,0.3)\',\\n        padding: null,\\n        axisPointer: {\\n            type: \\\"none\\\"\\n        },\\n    },\\n    legend: {//图例\\n        type: \\\"scroll\\\",\\n        pageIconColor: \'#aaa\',\\n        pageIconInactiveColor: \'#2f4554\',\\n        pageTextStyle: {\\n            color: \\\"#aaa\\\"\\n        },\\n        show: true,\\n        itemHeight: 5,\\n        bottom: 15,\\n        itemGap: 40,\\n      textStyle:{\\n       color:\'white\'\\n     }\\n    },\\n    series: [],\\n};\\n\",\"apiId\":\"39\",\"parameterValue\":{\"session;userId\":\"personId\",\"timeType\":\"y\"},\"transform\":\"const list = util.formatToTable(data, [\'层级\',\'市电碳排放\',\'油机发电碳排放\'], [\'resourceStructureName\',\'cityElectCarbonValue\',\'oilElectCarbonValue\'])//二参是自定义表头\\nreturn list;\\n\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\",\"selectedAPI\":[{\"name\":\"timeType\",\"array\":false,\"source\":\"input\",\"meaning\":\"timeType\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"\",\"delete\":false,\"parentName\":\"\",\"id\":\"1\"},{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"timeType\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"2\"}]},\"style\":{\"background\":\"transparent\",\"height\":250,\"width\":930,\"left\":530,\"position\":\"absolute\",\"top\":400,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":false},\"event\":{},\"uuid\":\"588cc9bf-7cec-d6cb-ad2e-1773d85d6cc8\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":130,\"name\":\"Tab切换\",\"type\":\"multiPlans\",\"data\":{\"multi_plans\":\"\",\"defaultPlan\":0,\"activePlanIndex\":0,\"control\":{\"show\":true,\"left\":0,\"top\":0,\"btnWidth\":30,\"btnHeight\":30,\"btnGap\":11,\"normalImage\":\"./../../../assets/img/charts/option-normal.png\",\"activeImage\":\"./../../../assets/img/charts/option-active.png\",\"gapImage\":\"./../../../assets/img/charts/option-gap.png\",\"textColor\":\"#FFFFFF\",\"activeTextColor\":\"#FFFFFF\",\"textFont\":\"arial,sans-serif\",\"textFontSize\":12},\"plans\":[{\"planName\":\"本年\",\"uuid\":\"cdcbc82e-adba-f4d2-59f3-c4e115cc1c25\"},{\"planName\":\"本月\",\"uuid\":\"17bbeb11-5244-ee56-4022-a2e0e32f1ca7\"},{\"planName\":\"本日\",\"uuid\":\"3f6993aa-7360-8dcd-3be7-e7b11318041b\"}]},\"style\":{\"background\":\"transparent\",\"height\":40,\"width\":120,\"position\":\"absolute\",\"top\":90,\"left\":380,\"border\":\"\",\"opacity\":1,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"zIndex\":10,\"userDrag\":\"element\"},\"event\":{},\"uuid\":\"f7ec791f-cc90-e9e0-ab56-4044e105846c\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"border\",\"fonts\",\"zIndex\",\"opacity\",\"userDrag\",\"background\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":130,\"name\":\"Tab切换\",\"type\":\"multiPlans\",\"data\":{\"multi_plans\":\"\",\"defaultPlan\":0,\"activePlanIndex\":0,\"control\":{\"show\":true,\"left\":0,\"top\":0,\"btnWidth\":30,\"btnHeight\":30,\"btnGap\":11,\"normalImage\":\"./../../../assets/img/charts/option-normal.png\",\"activeImage\":\"./../../../assets/img/charts/option-active.png\",\"gapImage\":\"./../../../assets/img/charts/option-gap.png\",\"textColor\":\"#FFFFFF\",\"activeTextColor\":\"#FFFFFF\",\"textFont\":\"arial,sans-serif\",\"textFontSize\":12},\"plans\":[{\"planName\":\"本年\",\"uuid\":\"2583d04f-0b69-18a8-19cf-86b7fd566045\"},{\"planName\":\"本月\",\"uuid\":\"aa2c38e0-5967-3455-af37-525beceb771e\"},{\"planName\":\"本日\",\"uuid\":\"e65284dc-c878-f262-7d32-5864413217ca\"}]},\"style\":{\"background\":\"transparent\",\"height\":40,\"width\":120,\"position\":\"absolute\",\"top\":90,\"left\":870,\"border\":\"\",\"opacity\":1,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"zIndex\":10,\"userDrag\":\"element\"},\"event\":{},\"uuid\":\"45c38dc2-3a58-d110-a8ba-a4a030a2cd7e\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"border\",\"fonts\",\"zIndex\",\"opacity\",\"userDrag\",\"background\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":130,\"name\":\"Tab切换\",\"type\":\"multiPlans\",\"data\":{\"multi_plans\":\"\",\"defaultPlan\":0,\"activePlanIndex\":0,\"control\":{\"show\":true,\"left\":0,\"top\":0,\"btnWidth\":30,\"btnHeight\":30,\"btnGap\":11,\"normalImage\":\"./../../../assets/img/charts/option-normal.png\",\"activeImage\":\"./../../../assets/img/charts/option-active.png\",\"gapImage\":\"./../../../assets/img/charts/option-gap.png\",\"textColor\":\"#FFFFFF\",\"activeTextColor\":\"#FFFFFF\",\"textFont\":\"arial,sans-serif\",\"textFontSize\":12},\"plans\":[{\"planName\":\"本年\",\"uuid\":\"d3ee11b1-d916-c167-4612-4c6e0981df07\"},{\"planName\":\"本月\",\"uuid\":\"42b6c9f3-1fd4-e272-f3db-daf630e2f4fd\"},{\"planName\":\"本日\",\"uuid\":\"b741311b-eb44-966a-0561-245f92c0e7f9\"}]},\"style\":{\"background\":\"transparent\",\"height\":40,\"width\":120,\"position\":\"absolute\",\"top\":90,\"left\":1366,\"border\":\"\",\"opacity\":1,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"zIndex\":10,\"userDrag\":\"element\"},\"event\":{},\"uuid\":\"eab11e5c-2665-9e74-0e66-bef928ced727\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"border\",\"fonts\",\"zIndex\",\"opacity\",\"userDrag\",\"background\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":117,\"name\":\"饼图\",\"type\":\"pieChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":2,\"styleId\":\"8\",\"styleImg\":\"piechart.png\",\"style\":{\"seriesLayoutBy\":\"row\"},\"expression\":\"option = {\\n    grid: {\\n        bottom: 80\\n    },\\n    dataZoom: [],\\n    dataset: {\\n        source: [\\n        ]\\n    },\\n    tooltip: {\\n        show: true,\\n        trigger: \'item\',\\n        position: \'top\',\\n        textStyle: {\\n            color: \'#bfdfff\',\\n            fontSize: 12\\n        },\\n        backgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n        borderWidth: 1,\\n        borderColor: \'rgba(0,127,255,0.3)\',\\n        padding: null,\\n        axisPointer: {\\n            type: \\\"none\\\"\\n        },\\n    },\\n    legend: {//图例\\n        type: \\\"scroll\\\",\\n        pageIconColor: \'#aaa\',\\n        pageIconInactiveColor: \'#2f4554\',\\n        pageTextStyle: {\\n            color: \\\"#aaa\\\"\\n        },\\n        show: true,\\n        itemHeight: 5,\\n        bottom: 15,\\n        itemGap: 40,\\n       textStyle:{\\n       \\tcolor:\'white\'\\n      }\\n    },\\n    series: [],\\n};\",\"apiId\":\"43\",\"parameterValue\":{\"session;userId\":\"personId\",\"timeType\":\"m\"},\"transform\":\"data.cityElectCarbonValue = Number((data.cityElectCarbonValue+data.oilElectCarbonValue).toFixed(2)); \\n\\nconst arr = [data];\\nconst list = util.formatToTable(arr, [\'碳排放\',\'碳抵消\'], [\'cityElectCarbonValue\',\'greenElectCarbonValue\'])//二参是自定义表头\\nreturn list;\\n\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\",\"selectedAPI\":[{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"人员ID\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"1\"},{\"name\":\"timeType\",\"array\":false,\"source\":\"input\",\"meaning\":\"时间类型\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"\",\"delete\":false,\"parentName\":\"\",\"id\":\"2\"}]},\"style\":{\"background\":\"transparent\",\"height\":244,\"width\":385,\"left\":1044,\"position\":\"absolute\",\"top\":61,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"42b6c9f3-1fd4-e272-f3db-daf630e2f4fd\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":117,\"name\":\"饼图\",\"type\":\"pieChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":2,\"styleId\":\"8\",\"styleImg\":\"piechart.png\",\"style\":{\"seriesLayoutBy\":\"row\"},\"expression\":\"option = {\\n    grid: {\\n        bottom: 80\\n    },\\n    dataZoom: [],\\n    dataset: {\\n        source: [\\n        ]\\n    },\\n    tooltip: {\\n        show: true,\\n        trigger: \'item\',\\n        position: \'top\',\\n        textStyle: {\\n            color: \'#bfdfff\',\\n            fontSize: 12\\n        },\\n        backgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n        borderWidth: 1,\\n        borderColor: \'rgba(0,127,255,0.3)\',\\n        padding: null,\\n        axisPointer: {\\n            type: \\\"none\\\"\\n        },\\n    },\\n    legend: {//图例\\n        type: \\\"scroll\\\",\\n        pageIconColor: \'#aaa\',\\n        pageIconInactiveColor: \'#2f4554\',\\n        pageTextStyle: {\\n            color: \\\"#aaa\\\"\\n        },\\n        show: true,\\n        itemHeight: 5,\\n        bottom: 15,\\n        itemGap: 40,\\n       textStyle:{\\n       \\tcolor:\'white\'\\n      }\\n    },\\n    series: [],\\n};\",\"apiId\":\"43\",\"parameterValue\":{\"session;userId\":\"personId\",\"timeType\":\"d\"},\"transform\":\"data.cityElectCarbonValue = Number((data.cityElectCarbonValue+data.oilElectCarbonValue).toFixed(2)); \\n\\nconst arr = [data];\\nconst list = util.formatToTable(arr, [\'碳排放\',\'碳抵消\'], [\'cityElectCarbonValue\',\'greenElectCarbonValue\'])//二参是自定义表头\\nreturn list;\\n\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\",\"selectedAPI\":[{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"人员ID\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"3\"},{\"name\":\"timeType\",\"array\":false,\"source\":\"input\",\"meaning\":\"时间类型\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"\",\"delete\":false,\"parentName\":\"\",\"id\":\"4\"}]},\"style\":{\"background\":\"transparent\",\"height\":244,\"width\":385,\"left\":1044,\"position\":\"absolute\",\"top\":61,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"b741311b-eb44-966a-0561-245f92c0e7f9\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":130,\"name\":\"Tab切换\",\"type\":\"multiPlans\",\"data\":{\"multi_plans\":\"\",\"defaultPlan\":0,\"activePlanIndex\":0,\"control\":{\"show\":true,\"left\":0,\"top\":0,\"btnWidth\":30,\"btnHeight\":30,\"btnGap\":11,\"normalImage\":\"./../../../assets/img/charts/option-normal.png\",\"activeImage\":\"./../../../assets/img/charts/option-active.png\",\"gapImage\":\"./../../../assets/img/charts/option-gap.png\",\"textColor\":\"#FFFFFF\",\"activeTextColor\":\"#FFFFFF\",\"textFont\":\"arial,sans-serif\",\"textFontSize\":12},\"plans\":[{\"planName\":\"本年\",\"uuid\":\"dedbddb9-c903-be6a-54e1-47c1c12b2d02\"},{\"planName\":\"本月\",\"uuid\":\"a09e5c7f-bdb3-cd4c-0500-797ffa5e704b\"},{\"planName\":\"本日\",\"uuid\":\"b6e50edf-ec26-80fd-e5d5-2688fcb34826\"}]},\"style\":{\"background\":\"transparent\",\"height\":40,\"width\":120,\"position\":\"absolute\",\"top\":384,\"left\":380,\"border\":\"\",\"opacity\":1,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"zIndex\":10,\"userDrag\":\"element\"},\"event\":{},\"uuid\":\"501f0799-6f4f-2363-e33d-ab49e43632a1\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"border\",\"fonts\",\"zIndex\",\"opacity\",\"userDrag\",\"background\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":117,\"name\":\"饼图\",\"type\":\"pieChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":2,\"styleId\":\"8\",\"styleImg\":\"piechart.png\",\"style\":{\"seriesLayoutBy\":\"row\"},\"expression\":\"option = {\\n    grid: {\\n        bottom: 80\\n    },\\n    dataZoom: [],\\n    dataset: {\\n        source: [\\n        ]\\n    },\\n    tooltip: {\\n        show: true,\\n        trigger: \'item\',\\n        position: \'top\',\\n        textStyle: {\\n            color: \'#bfdfff\',\\n            fontSize: 12\\n        },\\n        backgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n        borderWidth: 1,\\n        borderColor: \'rgba(0,127,255,0.3)\',\\n        padding: null,\\n        axisPointer: {\\n            type: \\\"none\\\"\\n        },\\n    },\\n    legend: {//图例\\n        type: \\\"scroll\\\",\\n        pageIconColor: \'#aaa\',\\n        pageIconInactiveColor: \'#2f4554\',\\n        pageTextStyle: {\\n            color: \\\"#aaa\\\"\\n        },\\n        show: true,\\n        itemHeight: 5,\\n        bottom: 15,\\n        itemGap: 40,\\n       textStyle:{\\n       \\tcolor:\'white\'\\n      }\\n    },\\n    series: [],\\n};\",\"apiId\":\"43\",\"parameterValue\":{\"session;userId\":\"personId\",\"timeType\":\"m\"},\"transform\":\"const arr = [data];\\nconst list = util.formatToTable(arr, [\'市电碳排放\',\'油机碳排放\'], [\'cityElectCarbonValue\',\'oilElectCarbonValue\'])//二参是自定义表头\\nreturn list;\\n\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\",\"selectedAPI\":[{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"人员ID\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"1\"},{\"name\":\"timeType\",\"array\":false,\"source\":\"input\",\"meaning\":\"时间类型\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"\",\"delete\":false,\"parentName\":\"\",\"id\":\"2\"}]},\"style\":{\"background\":\"transparent\",\"height\":233,\"width\":322,\"left\":80,\"position\":\"absolute\",\"top\":415,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"a09e5c7f-bdb3-cd4c-0500-797ffa5e704b\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":117,\"name\":\"饼图\",\"type\":\"pieChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":2,\"styleId\":\"8\",\"styleImg\":\"piechart.png\",\"style\":{\"seriesLayoutBy\":\"row\"},\"expression\":\"option = {\\n    grid: {\\n        bottom: 80\\n    },\\n    dataZoom: [],\\n    dataset: {\\n        source: [\\n        ]\\n    },\\n    tooltip: {\\n        show: true,\\n        trigger: \'item\',\\n        position: \'top\',\\n        textStyle: {\\n            color: \'#bfdfff\',\\n            fontSize: 12\\n        },\\n        backgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n        borderWidth: 1,\\n        borderColor: \'rgba(0,127,255,0.3)\',\\n        padding: null,\\n        axisPointer: {\\n            type: \\\"none\\\"\\n        },\\n    },\\n    legend: {//图例\\n        type: \\\"scroll\\\",\\n        pageIconColor: \'#aaa\',\\n        pageIconInactiveColor: \'#2f4554\',\\n        pageTextStyle: {\\n            color: \\\"#aaa\\\"\\n        },\\n        show: true,\\n        itemHeight: 5,\\n        bottom: 15,\\n        itemGap: 40,\\n       textStyle:{\\n       \\tcolor:\'white\'\\n      }\\n    },\\n    series: [],\\n};\",\"apiId\":\"36\",\"parameterValue\":{\"session;userId\":\"personId\",\"timeType\":\"d\"},\"transform\":\"const arr = [data];\\nconst list = util.formatToTable(arr, [\'市电碳排放\',\'油机碳排放\'], [\'cityElectCarbonValue\',\'oilElectCarbonValue\'])//二参是自定义表头\\nreturn list;\\n\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\",\"selectedAPI\":[{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"人员ID\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"4\"},{\"name\":\"timeType\",\"array\":false,\"source\":\"input\",\"meaning\":\"时间类型\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"\",\"delete\":false,\"parentName\":\"\",\"id\":\"5\"}]},\"style\":{\"background\":\"transparent\",\"height\":233,\"width\":322,\"left\":80,\"position\":\"absolute\",\"top\":415,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"b6e50edf-ec26-80fd-e5d5-2688fcb34826\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":130,\"name\":\"Tab切换\",\"type\":\"multiPlans\",\"data\":{\"multi_plans\":\"\",\"defaultPlan\":0,\"activePlanIndex\":0,\"control\":{\"show\":true,\"left\":0,\"top\":0,\"btnWidth\":30,\"btnHeight\":30,\"btnGap\":11,\"normalImage\":\"./../../../assets/img/charts/option-normal.png\",\"activeImage\":\"./../../../assets/img/charts/option-active.png\",\"gapImage\":\"./../../../assets/img/charts/option-gap.png\",\"textColor\":\"#FFFFFF\",\"activeTextColor\":\"#FFFFFF\",\"textFont\":\"arial,sans-serif\",\"textFontSize\":12},\"plans\":[{\"planName\":\"本年\",\"uuid\":\"588cc9bf-7cec-d6cb-ad2e-1773d85d6cc8\"},{\"planName\":\"本月\",\"uuid\":\"3cfd6d96-9f98-5121-330e-67fe1ad1f3f6\"},{\"planName\":\"本日\",\"uuid\":\"d00eb758-3ee5-48ab-bb92-68de4a9ce327\"}]},\"style\":{\"background\":\"transparent\",\"height\":30,\"width\":120,\"position\":\"absolute\",\"top\":384,\"left\":1366,\"border\":\"\",\"opacity\":1,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"zIndex\":10,\"userDrag\":\"element\"},\"event\":{},\"uuid\":\"c4b8ea9c-fa13-a694-3fca-1cb3025b5e25\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"border\",\"fonts\",\"zIndex\",\"opacity\",\"userDrag\",\"background\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":115,\"name\":\"柱状图\",\"type\":\"barChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":1,\"styleId\":\"3\",\"styleImg\":\"stackbarchart.png\",\"style\":{\"seriesLayoutBy\":\"column\"},\"expression\":\"option = {\\n    grid: {\\n        bottom: 80\\n    },\\n    xAxis: {\\n        type: \'category\',\\n        data: null,\\n        show: true,\\n        axisTick: { show: false },\\n        boundaryGap: true,\\n        silent: true,\\n        axisLine: {//轴线\\n            show: true,\\n            lineStyle: {\\n                color: \'#BFDFFF\', //x轴轴线颜色\\n                opacity: 0.2,\\n                width: 1\\n            }\\n        },\\n        axisLabel: {//x轴文字\\n            show: true,\\n            overflow: \'break\',\\n            width: null,\\n            color: \'#BFDFFF\',\\n            fontFamily: \'\',\\n            fontSize: 13,\\n            ellipsis: \'...\'\\n        }\\n    },\\n    yAxis: {\\n        type: \'value\',\\n        show: true,\\n        splitNumber: 4,\\n        max: null,\\n        min: 0,\\n        silent: true,\\n        textStyle: { color: \'#BFDFFF\', fontFamily: \'\', fontSize: 14 },\\n        axisLine: {//轴线\\n            show: true,\\n            lineStyle: {\\n                color: \'#BFDFFF\',\\n                opacity: 0.2,\\n                width: 1\\n            }\\n        },\\n        axisLabel: {//y轴文字\\n            show: true,\\n            color: \'#BFDFFF\',\\n            fontFamily: \'\',\\n            fontSize: 13\\n        },\\n        splitLine: {//分割线\\n            lineStyle: {\\n                width: 1,\\n                color: \'#BFDFFF\',\\n                opacity: 0.1\\n            }\\n        }\\n    },\\n    dataZoom: [],\\n    dataset: {\\n        source: [\\n        ]\\n    },\\n    tooltip: {\\n        show: true,\\n        trigger: \'axis\',\\n        position: \'\',\\n        textStyle: {\\n            color: \'#bfdfff\',\\n            fontSize: 12\\n        },\\n        backgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n        borderWidth: 1,\\n        borderColor: \'rgba(0,127,255,0.3)\',\\n        padding: null,\\n        axisPointer: {\\n            type: \\\"none\\\"\\n        },\\n    },\\n    legend: {//图例\\n        type: \\\"scroll\\\",\\n        pageIconColor: \'#aaa\',\\n        pageIconInactiveColor: \'#2f4554\',\\n        pageTextStyle: {\\n            color: \\\"#aaa\\\"\\n        },\\n        show: true,\\n        itemHeight: 5,\\n        bottom: 15,\\n        itemGap: 40,\\n      textStyle:{\\n       color:\'white\'\\n     }\\n    },\\n    series: [],\\n};\\n\",\"apiId\":\"39\",\"parameterValue\":{\"session;userId\":\"personId\",\"timeType\":\"m\"},\"transform\":\"const list = util.formatToTable(data, [\'层级\',\'市电碳排放\',\'油机发电碳排放\'], [\'resourceStructureName\',\'cityElectCarbonValue\',\'oilElectCarbonValue\'])//二参是自定义表头\\nreturn list;\\n\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\",\"selectedAPI\":[{\"name\":\"timeType\",\"array\":false,\"source\":\"input\",\"meaning\":\"timeType\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"\",\"delete\":false,\"parentName\":\"\",\"id\":\"1\"},{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"timeType\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"2\"}]},\"style\":{\"background\":\"transparent\",\"height\":250,\"width\":930,\"left\":530,\"position\":\"absolute\",\"top\":400,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"3cfd6d96-9f98-5121-330e-67fe1ad1f3f6\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":115,\"name\":\"柱状图\",\"type\":\"barChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":1,\"styleId\":\"3\",\"styleImg\":\"stackbarchart.png\",\"style\":{\"seriesLayoutBy\":\"column\"},\"expression\":\"option = {\\n    grid: {\\n        bottom: 80\\n    },\\n    xAxis: {\\n        type: \'category\',\\n        data: null,\\n        show: true,\\n        axisTick: { show: false },\\n        boundaryGap: true,\\n        silent: true,\\n        axisLine: {//轴线\\n            show: true,\\n            lineStyle: {\\n                color: \'#BFDFFF\', //x轴轴线颜色\\n                opacity: 0.2,\\n                width: 1\\n            }\\n        },\\n        axisLabel: {//x轴文字\\n            show: true,\\n            overflow: \'break\',\\n            width: null,\\n            color: \'#BFDFFF\',\\n            fontFamily: \'\',\\n            fontSize: 13,\\n            ellipsis: \'...\'\\n        }\\n    },\\n    yAxis: {\\n        type: \'value\',\\n        show: true,\\n        splitNumber: 4,\\n        max: null,\\n        min: 0,\\n        silent: true,\\n        textStyle: { color: \'#BFDFFF\', fontFamily: \'\', fontSize: 14 },\\n        axisLine: {//轴线\\n            show: true,\\n            lineStyle: {\\n                color: \'#BFDFFF\',\\n                opacity: 0.2,\\n                width: 1\\n            }\\n        },\\n        axisLabel: {//y轴文字\\n            show: true,\\n            color: \'#BFDFFF\',\\n            fontFamily: \'\',\\n            fontSize: 13\\n        },\\n        splitLine: {//分割线\\n            lineStyle: {\\n                width: 1,\\n                color: \'#BFDFFF\',\\n                opacity: 0.1\\n            }\\n        }\\n    },\\n    dataZoom: [],\\n    dataset: {\\n        source: [\\n        ]\\n    },\\n    tooltip: {\\n        show: true,\\n        trigger: \'axis\',\\n        position: \'\',\\n        textStyle: {\\n            color: \'#bfdfff\',\\n            fontSize: 12\\n        },\\n        backgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n        borderWidth: 1,\\n        borderColor: \'rgba(0,127,255,0.3)\',\\n        padding: null,\\n        axisPointer: {\\n            type: \\\"none\\\"\\n        },\\n    },\\n    legend: {//图例\\n        type: \\\"scroll\\\",\\n        pageIconColor: \'#aaa\',\\n        pageIconInactiveColor: \'#2f4554\',\\n        pageTextStyle: {\\n            color: \\\"#aaa\\\"\\n        },\\n        show: true,\\n        itemHeight: 5,\\n        bottom: 15,\\n        itemGap: 40,\\n      textStyle:{\\n       color:\'white\'\\n     }\\n    },\\n    series: [],\\n};\\n\",\"apiId\":\"39\",\"parameterValue\":{\"session;userId\":\"personId\",\"timeType\":\"d\"},\"transform\":\"const list = util.formatToTable(data, [\'层级\',\'市电碳排放\',\'油机发电碳排放\'], [\'resourceStructureName\',\'cityElectCarbonValue\',\'oilElectCarbonValue\'])//二参是自定义表头\\nreturn list;\\n\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"energy\",\"selectedAPI\":[{\"name\":\"timeType\",\"array\":false,\"source\":\"input\",\"meaning\":\"timeType\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"\",\"delete\":false,\"parentName\":\"\",\"id\":\"1\"},{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"timeType\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"2\"}]},\"style\":{\"background\":\"transparent\",\"height\":250,\"width\":930,\"left\":530,\"position\":\"absolute\",\"top\":400,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"hideByMultiPlan\":true},\"event\":{},\"uuid\":\"d00eb758-3ee5-48ab-bb92-68de4a9ce327\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":2,\"name\":\"文字\",\"type\":\"text\",\"data\":{\"text_val\":\"单位：kgC\",\"writeMode\":\"horizontal-tb\"},\"style\":{\"height\":30,\"width\":100,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":70,\"left\":1009,\"fontCol\":\"#BFDFFF\",\"fontSize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"uuid\":\"fc5072af-72f8-ee92-eebf-36aefaa484f1\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3},{\"compIndex\":2,\"name\":\"文字\",\"type\":\"text\",\"data\":{\"text_val\":\"单位：kgC\",\"writeMode\":\"horizontal-tb\"},\"style\":{\"height\":30,\"width\":100,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":395,\"left\":26,\"fontCol\":\"#BFDFFF\",\"fontSize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"uuid\":\"977e1028-41c2-8841-ecc6-b10de248b57c\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3},{\"compIndex\":2,\"name\":\"文字\",\"type\":\"text\",\"data\":{\"text_val\":\"单位：kgC\",\"writeMode\":\"horizontal-tb\"},\"style\":{\"height\":30,\"width\":100,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":395,\"left\":546,\"fontCol\":\"#BFDFFF\",\"fontSize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"uuid\":\"d3a10833-9547-7487-c449-55540cc49d31\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3},{\"compIndex\":2,\"name\":\"文字\",\"type\":\"text\",\"data\":{\"text_val\":\"单位：kgC\",\"writeMode\":\"horizontal-tb\"},\"style\":{\"height\":30,\"width\":100,\"position\":\"absolute\",\"textAlign\":\"center\",\"textPosition\":\"50%\",\"textDirection\":\"0\",\"top\":739,\"left\":26,\"fontCol\":\"#BFDFFF\",\"fontSize\":\"14\",\"fontweight\":\"\",\"border\":\"\",\"borderColor\":\"\",\"borderWidth\":0,\"borderStyle\":\"\",\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"uuid\":\"62646ac9-367c-bafd-1a85-c430b8a32e17\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"fonts\",\"background\",\"border\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":115,\"name\":\"柱状图\",\"type\":\"barChart\",\"data\":{\"bar_chart_val\":true,\"chartId\":1,\"styleId\":\"1\",\"styleImg\":\"barchart.png\",\"style\":{\"seriesLayoutBy\":\"column\"},\"expression\":\"option = {\\n\\tgrid: {\\n\\t\\tbottom: 80\\n\\t},\\n\\txAxis: {\\n\\t\\ttype: \'category\',\\n\\t\\tdata: null,\\n\\t\\tshow: true,\\n\\t\\taxisTick: { show: false },\\n\\t\\tboundaryGap: true,\\n\\t\\tsilent: true,\\n\\t\\taxisLine: {//轴线\\n\\t\\t\\tshow: true,\\n\\t\\t\\tlineStyle: {\\n\\t\\t\\t\\tcolor: \'#BFDFFF\', //x轴轴线颜色\\n\\t\\t\\t\\topacity: 0.2,\\n\\t\\t\\t\\twidth: 1\\n\\t\\t\\t}\\n\\t\\t},\\n\\t\\taxisLabel: {//x轴文字\\n\\t\\t\\tshow: true,\\n\\t\\t\\toverflow: \'break\',\\n\\t\\t\\twidth: null,\\n\\t\\t\\tcolor: \'#BFDFFF\',\\n\\t\\t\\tfontFamily: \'\',\\n\\t\\t\\tfontSize: 13,\\n\\t\\t\\tellipsis: \'...\'\\n\\t\\t}\\n\\t},\\n\\tyAxis: {\\n\\t\\ttype: \'value\',\\n\\t\\tshow: true,\\n\\t\\tsplitNumber: 4,\\n\\t\\tmax: null,\\n\\t\\tmin: 0,\\n\\t\\tsilent: true,\\n\\t\\ttextStyle: { color: \'#BFDFFF\', fontFamily: \'\', fontSize: 14 },\\n\\t\\taxisLine: {//轴线\\n\\t\\t\\tshow: true,\\n\\t\\t\\tlineStyle: {\\n\\t\\t\\t\\tcolor: \'#BFDFFF\',\\n\\t\\t\\t\\topacity: 0.2,\\n\\t\\t\\t\\twidth: 1\\n\\t\\t\\t}\\n\\t\\t},\\n\\t\\taxisLabel: {//y轴文字\\n\\t\\t\\tshow: true,\\n\\t\\t\\tcolor: \'#BFDFFF\',\\n\\t\\t\\tfontFamily: \'\',\\n\\t\\t\\tfontSize: 13\\n\\t\\t},\\n\\t\\tsplitLine: {//分割线\\n\\t\\t\\tlineStyle: {\\n\\t\\t\\t\\twidth: 1,\\n\\t\\t\\t\\tcolor: \'#BFDFFF\',\\n\\t\\t\\t\\topacity: 0.1\\n\\t\\t\\t}\\n\\t\\t}\\n\\t},\\n\\tdataZoom: [],\\n\\tdataset: {\\n\\t\\tsource: [\\n\\t\\t]\\n\\t},\\n\\ttooltip: {\\n\\t\\tshow: true,\\n\\t\\ttrigger: \'item\',\\n\\t\\tposition: \'top\',\\n\\t\\ttextStyle: {\\n\\t\\t\\tcolor: \'#bfdfff\',\\n\\t\\t\\tfontSize: 12\\n\\t\\t},\\n\\t\\tbackgroundColor: \'transparent\' || \'rgba(0,127,255,0.1)\',\\n\\t\\tborderWidth: 1,\\n\\t\\tborderColor: \'rgba(0,127,255,0.3)\',\\n\\t\\tpadding: null,\\n\\t\\taxisPointer: {\\n\\t\\t\\ttype: \\\"none\\\"\\n\\t\\t},\\n\\t},\\n\\tlegend: {//图例\\n\\t\\ttype: \\\"scroll\\\",\\n\\t\\tpageIconColor: \'#aaa\',\\n\\t\\tpageIconInactiveColor: \'#2f4554\',\\n\\t\\tpageTextStyle: {\\n\\t\\t\\tcolor: \\\"#aaa\\\"\\n\\t\\t},\\n\\t\\tshow: true,\\n\\t\\titemHeight: 5,\\n\\t\\tbottom: 15,\\n\\t\\titemGap: 40,\\n\\t},\\n  series: [],\\n};\",\"apiId\":\"41\",\"parameterValue\":{\"session;userId\":\"personId\",\"timeType\":\"y\"},\"transform\":\"\\n// 第三种情况,接口返回数据data格式是对象的数组,如下\\n// [{displayIndex:1, employeeName:\\\"a接口用户\\\", phone: 123456},\\n// {displayIndex:2, employeeName:\\\"b接口用户\\\", phone: 654321},\\n// {displayIndex:3, employeeName:\\\"c接口用户\\\", phone: 112233},]\\nconst list = util.formatToTable(data, [\'时间\',\'PUE值\'], [\'time\',\'avgValue\'])//二参是自定义表头\\nreturn util.arrArrFormatTime(list, \'$M\');//二参格式可选$[y|Q|M|W|d|h|m|s]\\n\",\"themeId\":0,\"refresh\":30,\"apiCategoryName\":\"能管组态电信\",\"selectedAPI\":[{\"name\":\"userId\",\"array\":false,\"source\":\"session\",\"meaning\":\"userId\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"personId\",\"delete\":false,\"parentName\":\"\",\"id\":\"1\"},{\"name\":\"timeType\",\"array\":false,\"source\":\"input\",\"meaning\":\"timeType\",\"children\":[],\"required\":true,\"component\":\"\",\"customData\":\"\",\"defaultValue\":\"\",\"delete\":false,\"parentName\":\"\",\"id\":\"2\"}]},\"style\":{\"background\":\"transparent\",\"height\":300,\"width\":1332,\"left\":71,\"position\":\"absolute\",\"top\":1156,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\",\"zIndex\":10},\"event\":{},\"uuid\":\"b3bea8ec-b52d-cd7d-11b8-0be0f6686f54\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"background\",\"zIndex\",\"userDrag\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3,\"action\":false},{\"compIndex\":62,\"name\":\"面板\",\"type\":\"card\",\"data\":{\"text_val\":\"柱状图\",\"card_val\":\"card\",\"sky_val\":\"card\",\"panelStyleType\":\"\"},\"style\":{\"height\":440,\"width\":1487,\"left\":0,\"position\":\"absolute\",\"top\":1070,\"fixedWidthBool\":false,\"fixedHeightBool\":false,\"minWidth\":10,\"minHeight\":10,\"userDrag\":\"element\"},\"uuid\":\"851ab378-0a7a-655a-db4c-2d5010418b40\",\"supportStyleFullBool\":false,\"supportStyleList\":[\"size\",\"position\",\"userDrag\",\"zIndex\"],\"supportStyleSubList\":[\"width\",\"height\",\"top\",\"left\"],\"multipAction\":false,\"active\":false,\"currentPagesStyle\":{\"width\":1500,\"height\":2000,\"background\":\"#fff\",\"sizeState\":\"allPage\",\"range\":1},\"pageSize\":{\"_PAGE_SIZE_X\":235,\"_PAGE_SIZE_Y\":60},\"pageCategory\":3}]',NULL,'CHILDREN','ECC',0,1,NULL);



-- 能耗定制功能索引表
INSERT INTO `energy_customer_config` VALUES (1,'taibo',0,'2023-04-13 00:00:00','This is an automatic report generation function customized by Taibo. Because there is a scheduled job, it is necessary to add a mark here. If the field enable is set to 1, the job will be executed.');
INSERT INTO `energy_customer_config` VALUES (2,'hebeidianxin month pue clear',0,'2023-06-09 00:00:00','10000');
INSERT INTO `energy_customer_config` VALUES (3,'Mobile design institute site',0,'2023-11-01 00:00:00','');
INSERT INTO `energy_customer_config` VALUES (4,'wuhan generator signal',0,'2023-11-30 00:00:00','2');
INSERT INTO `energy_customer_config` VALUES (5,'baidu report',0,'2024-04-28 00:00:00','');
INSERT INTO `energy_customer_config` VALUES (6,'sichuan meishan generation',0,'2024-06-13 00:00:00','');