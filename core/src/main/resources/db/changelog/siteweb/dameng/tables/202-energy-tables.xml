<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="1-202" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <sql splitStatements="true" endDelimiter=";">
            CREATE TABLE "energy_businessdefinitionmap"
            (
            "MapId" INT AUTO_INCREMENT NOT NULL,
            "BusinessTypeId" INT NOT NULL,
            "ComplexIndexDefinitionId" INT NOT NULL,
            "ComplexIndexDefinitionTypeId" INT NOT NULL,
            "ExtendField1" VARCHAR(256),
            NOT CLUSTER PRIMARY KEY("MapId"));

            CREATE TABLE "energy_carbonemissionmanage"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "ObjectTypeId" VARCHAR(45) NOT NULL,
            "ObjectId" VARCHAR(45) NOT NULL,
            "Year" INT,
            "Month" INT,
            "PlanValue" REAL,
            "YearPlanTotalValue" REAL,
            "isArea" INT NOT NULL,
            "area" REAL,
            "units" VARCHAR(45) NOT NULL,
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "energy_carbonmanage"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "ObjectTypeId" VARCHAR(45) NOT NULL,
            "ObjectId" VARCHAR(45) NOT NULL,
            "Year" INT NOT NULL,
            "Month" INT NOT NULL,
            "PlanValue" REAL NOT NULL,
            "YearPlanTotalValue" REAL NOT NULL,
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "energy_complexindexclassificationmap"
            (
            "ResourceStructureId" INT NOT NULL,
            "ComplexIndexId" INT NOT NULL,
            "ClassificationId" INT NOT NULL,
            "ExtendField1" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("ComplexIndexId"));

            CREATE TABLE "energy_consumeconst"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "ObjectId" VARCHAR(45) NOT NULL,
            "ObjectTypeId" VARCHAR(45) NOT NULL,
            "Peoples" INT NOT NULL,
            "Area" REAL,
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "energy_consumedata"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "ObjectId" VARCHAR(45) NOT NULL,
            "ObjectTypeId" VARCHAR(45) NOT NULL,
            "EnergyTypeId" INT NOT NULL,
            "year" INT NOT NULL,
            "month" INT NOT NULL,
            "EnergySavingValue" REAL,
            "PlanValue" REAL,
            "OverstepValue" REAL,
            "PlanValuePreAlarm" REAL,
            "OverstepValuePreAlarm" REAL,
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "energy_customer_config"
            (
            "Id" INT NOT NULL,
            "CustomerName" VARCHAR(256),
            "Enable" TINYINT,
            "CreateTime" TIMESTAMP(0),
            "Notes" VARCHAR(1000),
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "energy_customer_tb_report"
            (
            "ReportId" INT NOT NULL,
            "ReportName" VARCHAR(256),
            "Path" VARCHAR(1000),
            "RecordPath" VARCHAR(1000),
            "Notes" VARCHAR(1000),
            NOT CLUSTER PRIMARY KEY("ReportId"));

            <!-- RowId是达梦数据库关键字，这里暂且注释掉该表 -->
<!--            CREATE TABLE "energy_customer_tb_reportmap"-->
<!--            (-->
<!--            "Id" INT AUTO_INCREMENT NOT NULL,-->
<!--            "ReportId" INT,-->
<!--            "SheetId" INT,-->
<!--            "RowId" INT,-->
<!--            "CellId" INT,-->
<!--            "Hour" INT,-->
<!--            "EquipmentId" VARCHAR(128),-->
<!--            "SignalId" VARCHAR(128),-->
<!--            NOT CLUSTER PRIMARY KEY("Id"));-->

            CREATE TABLE "energy_customer_tb_reportrecord"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "ReportId" INT,
            "CreateTime" TIMESTAMP(0),
            "ReportName" VARCHAR(256),
            "FilePath" VARCHAR(1000),
            "Notes" VARCHAR(1000),
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "energy_dataentry"
            (
            "EntryId" INT AUTO_INCREMENT NOT NULL,
            "EntryName" VARCHAR(128),
            "EntryAlias" VARCHAR(255),
            "Description" VARCHAR(255),
            "ExtendField1" VARCHAR(255),
            "CanEdit" BIT,
            "CanDelete" BIT,
            NOT CLUSTER PRIMARY KEY("EntryId"));

            CREATE TABLE "energy_dataitem"
            (
            "EntryItemId" INT AUTO_INCREMENT NOT NULL,
            "ParentEntryId" INT NOT NULL,
            "EntryId" INT NOT NULL,
            "ItemId" INT NOT NULL,
            "ItemValue" VARCHAR(128),
            "ItemAlias" VARCHAR(255),
            "IsSystem" TINYINT,
            "IsDefault" TINYINT,
            "Description" VARCHAR(255),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            "ExtendField3" VARCHAR(255),
            "ExtendField4" VARCHAR(255),
            "ExtendField5" VARCHAR(255),
            "CanEdit" TINYINT,
            "CanDelete" TINYINT,
            "canTimeliness" TINYINT,
            NOT CLUSTER PRIMARY KEY("EntryItemId"));

            CREATE TABLE "energy_dataitemtimeliness"
            (
            "ItemTimelinessId" INT AUTO_INCREMENT NOT NULL,
            "EntryItemId" INT NOT NULL,
            "ItemValue" VARCHAR(128),
            "ExtendField1" VARCHAR(255),
            "ExtendField2" VARCHAR(255),
            "ExtendField3" VARCHAR(255),
            "ExtendField4" VARCHAR(255),
            "ExtendField5" VARCHAR(255),
            "StartTime" TIMESTAMP(0),
            NOT CLUSTER PRIMARY KEY("ItemTimelinessId"),
            CONSTRAINT "Energy_DataItemtimeliness_unique" UNIQUE("EntryItemId", "StartTime"));

            CREATE TABLE "energy_dimensiontype"
            (
            "DimensionTypeId" INT AUTO_INCREMENT NOT NULL,
            "DimensionTypeName" VARCHAR(255) NOT NULL,
            "IsUsed" TINYINT DEFAULT 1,
            "OperatorUserId" INT NOT NULL,
            "UpdateTime" TIMESTAMP(0) NOT NULL,
            "Notes" VARCHAR(1000) NOT NULL,
            NOT CLUSTER PRIMARY KEY("DimensionTypeId"));

            CREATE TABLE "energy_elecfeeconfigoperatelog"
            (
            "LogId" INT AUTO_INCREMENT NOT NULL,
            "Operator" VARCHAR(128),
            "OperatorId" INT,
            "UpdateDate" TIMESTAMP(0) NOT NULL,
            "OperationContent" VARCHAR(4000) NOT NULL,
            "ChangeContent" CLOB,
            "ExtendField1" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("LogId"));

            CREATE TABLE "energy_elecfeefpg"
            (
            "FpgId" INT AUTO_INCREMENT NOT NULL,
            "SchemeId" INT NOT NULL,
            "EffectiveStart" TIMESTAMP(0) NOT NULL,
            "EffectiveEnd" TIMESTAMP(0) NOT NULL,
            "CreateDate" TIMESTAMP(0) NOT NULL,
            "UpdateDate" TIMESTAMP(0) NOT NULL,
            "CreaterId" INT NOT NULL,
            "CreaterName" VARCHAR(256),
            "UpdaterId" INT NOT NULL,
            "UpdaterName" VARCHAR(256),
            "FpgDescKey" INT,
            "FpgDesc" VARCHAR(256),
            "ExtendField1" VARCHAR(256),
            NOT CLUSTER PRIMARY KEY("FpgId"));

            CREATE TABLE "energy_elecfeefpgvalue"
            (
            "FpgValueId" INT AUTO_INCREMENT NOT NULL,
            "FpgId" INT NOT NULL,
            "StepPriceId" INT NOT NULL,
            "SchemeId" INT NOT NULL,
            "FpgValue" VARCHAR(64) NOT NULL,
            "CreateDate" TIMESTAMP(0) NOT NULL,
            "UpdateDate" TIMESTAMP(0) NOT NULL,
            "CreaterId" INT NOT NULL,
            "CreaterName" VARCHAR(256),
            "UpdaterId" INT NOT NULL,
            "UpdaterName" VARCHAR(256),
            "ExtendField1" VARCHAR(256),
            NOT CLUSTER PRIMARY KEY("FpgValueId"));

            CREATE TABLE "energy_elecfeescheme"
            (
            "SchemeId" INT AUTO_INCREMENT NOT NULL,
            "SchemeName" VARCHAR(512),
            "AppliedRange" VARCHAR(1024),
            "EnableDate" TIMESTAMP(0),
            "DeactivateDate" TIMESTAMP(0),
            "StartMonth" INT,
            "EndMonth" INT,
            "EnableStatus" INT NOT NULL,
            "CreateDate" TIMESTAMP(0) NOT NULL,
            "UpdateDate" TIMESTAMP(0) NOT NULL,
            "CreaterId" INT NOT NULL,
            "CreaterName" VARCHAR(256),
            "UpdaterId" INT NOT NULL,
            "UpdaterName" VARCHAR(256),
            "BusinessTypeId" INT,
            "BusinessTypeName" VARCHAR(128),
            "ExtendField1" VARCHAR(256),
            "ExtendField2" VARCHAR(256),
            NOT CLUSTER PRIMARY KEY("SchemeId"));

            CREATE TABLE "energy_elecfeeschemestructuremap"
            (
            "MapId" INT AUTO_INCREMENT NOT NULL,
            "SchemeId" INT NOT NULL,
            "ResourceStructureId" INT NOT NULL,
            "ResourceStructureName" VARCHAR(256),
            "CreateDate" TIMESTAMP(0) NOT NULL,
            "UpdateDate" TIMESTAMP(0) NOT NULL,
            "CreaterId" INT NOT NULL,
            "CreaterName" VARCHAR(256),
            "UpdaterId" INT NOT NULL,
            "UpdaterName" VARCHAR(256),
            "ExtendField1" VARCHAR(256),
            NOT CLUSTER PRIMARY KEY("MapId"));

            CREATE TABLE "energy_elecfeestepprice"
            (
            "StepPriceId" INT AUTO_INCREMENT NOT NULL,
            "SchemeId" INT NOT NULL,
            "StepName" VARCHAR(256) NOT NULL,
            "UpperLimit" INT NOT NULL,
            "CreateDate" TIMESTAMP(0) NOT NULL,
            "UpdateDate" TIMESTAMP(0) NOT NULL,
            "CreaterId" INT NOT NULL,
            "CreaterName" VARCHAR(256),
            "UpdaterId" INT NOT NULL,
            "UpdaterName" VARCHAR(256),
            "AsMaxStep" INT,
            "ExtendField1" VARCHAR(256),
            NOT CLUSTER PRIMARY KEY("StepPriceId"));

            CREATE TABLE "energy_errordatamodifyrecord" (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "ComplexIndexId" INT NOT NULL,
            "ErrorTime" TIMESTAMP(0) NOT NULL,
            "ModifyTime" TIMESTAMP(0) NOT NULL,
            "UserId" INT NOT NULL,
            "UserName" VARCHAR(255) NOT NULL,
            "OriginalValue" DOUBLE NOT NULL,
            "IndexValue" DOUBLE NOT NULL,
            "Note" VARCHAR(255),
            "ExtendField1" VARCHAR(255),
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "energy_management"
            (
            "EnergyId" INT AUTO_INCREMENT NOT NULL,
            "EnergyName" VARCHAR(128) NOT NULL,
            "Unit" VARCHAR(128),
            "Operator" VARCHAR(128),
            "Contact" VARCHAR(128),
            "StartTime" TIMESTAMP(0) NOT NULL,
            "EndTime" TIMESTAMP(0) NOT NULL,
            "Notes" VARCHAR(1000),
            NOT CLUSTER PRIMARY KEY("EnergyId"));

            CREATE TABLE "energy_managementmap"
            (
            "MapId" INT AUTO_INCREMENT NOT NULL,
            "EnergyId" INT NOT NULL,
            "ResourceStructureId" INT NOT NULL,
            "IsMarker" INT NOT NULL,
            "Property" VARCHAR(1000),
            NOT CLUSTER PRIMARY KEY("MapId"));

            CREATE OR REPLACE  INDEX "Energy_ManagementMap_IDX1" ON "energy_managementmap"("EnergyId" ASC,"ResourceStructureId" ASC);

            CREATE TABLE "energy_manualrecord" (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "ComplexIndexId" INT,
            "RecordTime" TIMESTAMP(0) NOT NULL,
            "RecordValue" DOUBLE NOT NULL,
            "CalcType" INT,
            "UserId" INT,
            "UserName" VARCHAR(255) NOT NULL,
            "InsertTime" TIMESTAMP(0) NOT NULL,
            "ExtendField1" VARCHAR(255),
            "Unit" VARCHAR(128),
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "energy_multilevelobject"
            (
            "ObjectId" INT NOT NULL,
            "ObjectName" VARCHAR(512),
            "ParentObjectId" INT,
            "LevelNum" INT,
            NOT CLUSTER PRIMARY KEY("ObjectId"));

            CREATE TABLE "energy_objectmap"
            (
            "Id" INT AUTO_INCREMENT NOT NULL,
            "DimensionTypeId" INT NOT NULL,
            "LevelId" INT NOT NULL,
            "ObjectId" INT NOT NULL,
            "ObjectIdType" INT NOT NULL,
            "ParentObjectId" INT NOT NULL,
            "ParentObjectIdType" INT NOT NULL,
            "LevelOfPath" VARCHAR(1024) NOT NULL,
            "Notes" VARCHAR(1000) NOT NULL,
            NOT CLUSTER PRIMARY KEY("Id"));

            CREATE TABLE "energy_ratingconfig"
            (
            "RatingConfigId" INT AUTO_INCREMENT NOT NULL,
            "OutDryTempParam" VARCHAR(255),
            "OutWetTempParam" VARCHAR(255),
            "InDryTempParam" VARCHAR(255),
            "InWetTempParam" VARCHAR(255),
            "RunningLoadParam" VARCHAR(255),
            "ITPowerParam" VARCHAR(255),
            "TotalPowerParam" VARCHAR(255),
            "ObjectId" INT NOT NULL,
            "Name" VARCHAR(255) NOT NULL,
            "IntervalSecond" INT NOT NULL,
            "Status" INT DEFAULT 0 NOT NULL,
            "StartTime" TIMESTAMP(0),
            "EndTime" TIMESTAMP(0),
            "WorkingCondition" VARCHAR(255),
            "UserId" INT NOT NULL,
            "CityId" INT NOT NULL,
            NOT CLUSTER PRIMARY KEY("RatingConfigId"));

            CREATE TABLE "energy_ratingdata"
            (
            "RatingDataId" INT AUTO_INCREMENT NOT NULL,
            "RatingConfigId" INT NOT NULL,
            "SampleTime" TIMESTAMP(0) NOT NULL,
            "IntervalSecond" INT NOT NULL,
            "WorkingCondition" VARCHAR(255),
            "OutDryTemp" DOUBLE,
            "OutWetTemp" DOUBLE,
            "InDryTemp" DOUBLE,
            "InWetTemp" DOUBLE,
            "RunningLoad" DOUBLE,
            "ITPower" DOUBLE,
            "TotalPower" DOUBLE,
            NOT CLUSTER PRIMARY KEY("RatingDataId"));

            CREATE OR REPLACE  INDEX "energy_ratingdata_idx1" ON "energy_ratingdata"("RatingConfigId" ASC,"SampleTime" ASC,"WorkingCondition" ASC);

            CREATE TABLE "energy_ratingdatahistory"
            (
            "RatingDataId" INT AUTO_INCREMENT NOT NULL,
            "RatingConfigId" INT NOT NULL,
            "SampleTime" TIMESTAMP(0) NOT NULL,
            "IntervalSecond" INT NOT NULL,
            "WorkingCondition" VARCHAR(255) NOT NULL,
            "OutDryTemp" DOUBLE NOT NULL,
            "OutWetTemp" DOUBLE NOT NULL,
            "InDryTemp" DOUBLE NOT NULL,
            "InWetTemp" DOUBLE NOT NULL,
            "RunningLoad" DOUBLE NOT NULL,
            "ITPower" DOUBLE NOT NULL,
            "TotalPower" DOUBLE NOT NULL,
            "Times" INT DEFAULT 1 NOT NULL,
            "DeleteDate" TIMESTAMP(0) NOT NULL,
            NOT CLUSTER PRIMARY KEY("RatingDataId"));

            CREATE TABLE "energy_structure"
            (
            "StructureId" INT AUTO_INCREMENT NOT NULL,
            "StructureName" VARCHAR(255) NOT NULL,
            "SourceCategory" INT NOT NULL,
            "Notes" VARCHAR(1000) NOT NULL,
            "AsRootInflowNode" INT,
            NOT CLUSTER PRIMARY KEY("StructureId"));
        </sql>
    </changeSet>
</databaseChangeLog>