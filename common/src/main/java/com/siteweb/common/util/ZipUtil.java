package com.siteweb.common.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.CharsetUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.Charset;

public class ZipUtil {

    private static final Logger logger = LoggerFactory.getLogger(ZipUtil.class);

    /**
     * 将文件压缩成zip
     *
     * @param sourceFile    源文件或目录，如：archive.tar
     * @param targetFile    目标文件，如：archive.tar.zip
     * @param delSourceFile 是否删除源文件
     */
    public static void compress(String sourceFile, String targetFile, boolean delSourceFile) {
        try {
            File srcFile = new File(sourceFile);
            File destFile = new File(targetFile);

            if (srcFile.isDirectory()) {
                // 如果是目录，使用zip方法压缩整个目录
                cn.hutool.core.util.ZipUtil.zip(destFile, Charset.defaultCharset(), true, srcFile);
            } else {
                // 如果是单个文件，使用zip方法将文件压缩
                cn.hutool.core.util.ZipUtil.zip(destFile, Charset.defaultCharset(), false, srcFile);
            }

            // 如果需要删除源文件
            if (delSourceFile && srcFile.exists()) {
                FileUtil.del(srcFile);
            }
        } catch (Exception e) {
            logger.error("压缩失败，原因: ", e);
        }
    }

    /**
     * 将zip文件解压到指定目录
     *
     * @param zipPath 源文件，如：archive.zip
     * @param descDir 解压目录
     */
    public static void uncompress(String zipPath, String descDir) {
        try {
            File zipFile = new File(zipPath);
            File destDir = new File(descDir);

            // 确保目标目录存在
            if (!destDir.exists()) {
                destDir.mkdirs();
            }

            // 使用Hutool解压
            cn.hutool.core.util.ZipUtil.unzip(zipFile, destDir, CharsetUtil.defaultCharset());
        } catch (Exception e) {
            logger.error("解压失败，原因：", e);
        }
    }

    /**
     * 将指定文件输出到web端
     *
     * @param file          文件
     * @param outPutFileName 输出文件名
     * @param response      HTTP响应对象
     */
    public static void toWebDownLoad(File file, String outPutFileName, HttpServletResponse response) {
        if (!file.exists()) {
            logger.error("文件{}不存在", file.getAbsolutePath());
        } else {
            InputStream inputStream = null;
            OutputStream outputStream = null;
            try {
                inputStream = FileUtil.getInputStream(file);
                // 清空response
                response.reset();

                // 设置响应头
                response.setContentType("application/octet-stream");
                response.setHeader("Content-Disposition",
                        "attachment;filename=" + new String(outPutFileName.getBytes("GB2312"), "ISO8859-1"));

                outputStream = response.getOutputStream();
                // 使用Hutool的IoUtil实现流复制
                IoUtil.copy(inputStream, outputStream);
                outputStream.flush();
            } catch (Exception e) {
                throw new RuntimeException("zip toWebDownLoad error from ZipUtils", e);
            } finally {
                IoUtil.close(inputStream);
                IoUtil.close(outputStream);
            }
        }
    }

    /**
     * 将指定文件输出到web端
     *
     * @param path          文件路径
     * @param outPutFileName 输出文件名
     * @param response      HTTP响应对象
     */
    public static void toWebDownLoad(String path, String outPutFileName, HttpServletResponse response) {
        File file = new File(path);
        toWebDownLoad(file, outPutFileName, response);
    }
}
