package com.siteweb.generator.listener;

import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.generator.manager.GenePowerStatisticsManager;
import com.siteweb.generator.manager.GeneratorManager;
import com.siteweb.generator.service.GeneBaseDataService;
import com.siteweb.monitoring.entity.AlarmChange;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class AlarmChangeListener implements ApplicationListener<BaseSpringEvent<AlarmChange>> {

    @Autowired
    private GeneBaseDataService geneBaseDataService;
    @Autowired
    private HAStatusService haStatusService;

    @Autowired
    private GenePowerStatisticsManager genePowerStatisticsManager;
    @Autowired
    private GeneratorManager generatorManager;

    @Override
    public void onApplicationEvent(BaseSpringEvent<AlarmChange> event) {
        if (!haStatusService.isMasterHost()) {
            log.info("AlarmChangeListener : HAStatus is BACKUP.");
            return;
        }
        AlarmChange alarmChange = event.getData();
        if (null == alarmChange || alarmChange.getBaseTypeId() == null) {
            return;
        }
        Boolean wuHanConfig = generatorManager.getCustomerConfigById(4);
        if (wuHanConfig != null && wuHanConfig) {
            geneBaseDataService.handleGenePowerOnAlarm(alarmChange);
            geneBaseDataService.handleGeneDynamicSignalCheck(alarmChange);
        }
        Boolean sichuanConfig = generatorManager.getCustomerConfigById(6);
        if (sichuanConfig != null && sichuanConfig ) {
            genePowerStatisticsManager.handleGenePowerOnAlarm(alarmChange);
            genePowerStatisticsManager.handleCityElectPowerOffAlarm(alarmChange);
        }
    }
}
