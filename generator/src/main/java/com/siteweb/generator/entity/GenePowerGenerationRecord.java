package com.siteweb.generator.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("Gene_PowerGenerationRecord")
public class GenePowerGenerationRecord {

    @TableId(value="SerialId", type = IdType.AUTO)
    private Integer serialId;
    private Integer equipmentId;
    private Integer equipmentBaseType;
    private Date startTime;
    private Date endTime;
    /** 正向有功电能 开始时的值 */
    private Double positiveElecEnergyStart;
    /** 正向有功电能 结束时的值 */
    private Double positiveElecEnergyEnd;
    /** 发电量 */
    private Double powerGeneration;
    /** 本次运行时长(秒) */
    private Long runDuration;
    /** 发电量对应的钱(元) */
    private Double powerGenerationCost;
    /** 耗油量 */
    private Double powerGenerationOil;
    /** 耗油量对应的钱(元) */
    private Double powerGenerationOilCost;
    /** 插入开始记录的实时时间 */
    private Date startInsertTime;
    /** 插入结束记录的实时时间 */
    private Date endInsertTime;
    /** 插入开始记录时的序号 */
    private Long startSerialNo;
    /** 插入结束记录时的序号 */
    private Long endSerialNo;
    private String extend1;
}
