package com.siteweb.generator.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName("gene_typepowerinfo")
public class GeneTypePowerInfo {
    @TableId(value="id", type = IdType.AUTO)
    private Integer id;
    /**
     * 油机类型ID
     */
    private Integer geneTypeId;
    /**
     * 油机类型名称
     */
    private String geneTypeName;
    /**
     * 额定功率
     */
    private Double ratedPower;
    /**
     * 标准油耗
     */
    private Double fuelConsumption;
    /**
     * 额定工况
     */
    private Double operateConditions;
}
