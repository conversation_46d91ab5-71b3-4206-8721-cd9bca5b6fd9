package com.siteweb.generator.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("Gene_RefuelRecord")
public class GeneRefuelRecord {

    @TableId(value="SerialId", type = IdType.AUTO)
    private Integer serialId;
    private Integer OilId;
    private String oilName;
    /** 加油时间 */
    private Date refuelTime;
    /** 加油量 单位：升(L) */
    private Double refuelQuantity;
    /** 本次加油单价  单位：元/升 */
    private Double unitPrice;
    /** 本次加油费用: refuelQuantity * unitPrice  单位：元 */
    private Double refuelFee;
    /** 操作人 */
    private String operator;

    private Integer updaterId;
    private Date updateTime;
    private String extend1;

}
