package com.siteweb.generator.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("Gene_GeneratorExt")
public class GeneGeneratorExt {

    @TableId(value="GeneId")
    private Integer geneId;
    /** 额定功率 KW */
    private Double ratedPower;
    /** 额定功耗 KG */
    private Double ratedPowerConsumption;
    /** 单位油耗 升/时(L/h) */
    private Double unitFuelConsumption;
    /** 默认电单价 元 */
    private Double defaultElecUnitPrice;
    private Integer updaterId;
    private Date updateTime;
    private String extend1;
}
