package com.siteweb.generator.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("gene_generatorhistorysignal")
public class GeneHistorySignal {
    @TableId(value="SerialId", type = IdType.AUTO)
    private Integer serialId;
    private Integer geneId;
    private Integer signalId;
    private String geneIdAndSignalId;
    private Double signalValue;
    private Date InsertTime;
    /**
     * 信号值0：有效；1：无效
     */
    private Integer signalValid;
    /**
     * 油机发电记录ID
     */
    private Integer powerSerialId;
    /**
     * 是否产生了告警
     * 0:没有
     * 1：产生了
     */
    private Boolean makeAlarm;
    private String extend1;
}
