package com.siteweb.generator.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("Gene_MaintenanceRecord")
public class GeneMaintenanceRecord {

    @TableId(value="SerialId", type = IdType.AUTO)
    private Integer serialId;
    /** 油机设备id */
    private Integer geneId;
    /** 油机设备名 */
    private String geneName;
    /** 保养时间 */
    private Date maintenanceTime;
    /** 位置 */
    private String position;
    /** 保养项目 */
    private String maintenanceItem;
    /** 保养人 */
    private String maintainer;
    /** 确认人 */
    private String confirmor;
    /** 备注 */
    private String remark;
    private Integer updaterId;
    private Date updateTime;
    private String extend1;

}
