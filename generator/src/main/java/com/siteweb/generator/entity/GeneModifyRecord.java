package com.siteweb.generator.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("gene_modifyrecord")
public class GeneModifyRecord {
    @TableId(value="id", type = IdType.AUTO)
    private Integer id;
    /**
     * 记录Id
     */
    private Integer recordId;
    /**
     * 修改时间
     */
    private Date modifyTime;
    /**
     * 操作人员ID
     */
    private Integer operatorId;
    /**
     * 操作人员名称
     */
    private String operatorName;
    /**
     * 修改内容
     */
    private String modifyContent;
    private String extendField;
}
