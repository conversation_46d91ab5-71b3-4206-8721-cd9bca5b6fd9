package com.siteweb.generator.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("Gene_GeneratorElecUnitPrice")
public class GeneGeneratorElecUnitPrice {

    @TableId(value="SerialId", type = IdType.AUTO)
    private Integer serialId;

    private Integer equipmentId;

    private Integer equipmentBaseType;
    /** 年份数字 */
    private Integer year;
    /** 月份数字 */
    private Integer month;
    /** yyyy-MM */
    private String yearMonth;
    /** 电单价 单位：元/度 */
    private Double elecUnitPrice;

    private Integer updaterId;

    private Date updateTime;

    private String extend1;
}
