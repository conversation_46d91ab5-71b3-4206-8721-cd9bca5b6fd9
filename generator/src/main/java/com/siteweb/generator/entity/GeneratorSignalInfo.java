package com.siteweb.generator.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@TableName("gene_generatorsignalcheckinfo")
public class GeneratorSignalInfo {
    @TableId(value="SerialId", type = IdType.AUTO)
    private Integer serialId;

    private Integer geneId;
    private Integer equipmentTemplateId;
    private Integer signalId;
    private String signalName;
    /**
     * 动态检测：0
     * 静态检测：1
     */
    private Integer dynamicOrStatic;
    private Double triggerValue;
    private String extend1;
    private String extend2;

    public String getCheckKey(){
        return   geneId + "." + signalId;
    }
}
