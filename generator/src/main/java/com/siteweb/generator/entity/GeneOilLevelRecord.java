package com.siteweb.generator.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("gene_oillevelrecord")
public class GeneOilLevelRecord {
    @TableId(value="SerialId", type = IdType.AUTO)
    private Integer serialId;
    private Integer oilId;
    /** 更新时间 */
    private Date updateTime;
    /** 满油液位 毫米 */
    private Double currentOilLevel;
    private Integer updateUserId;

    @TableField(exist = false)
    private String userName;

    private String extend1;

    public GeneOilLevelRecord(Integer oilId, double currentOilLevel, Date curDate,Integer updateUserId) {
        this.oilId = oilId;
        this.currentOilLevel = currentOilLevel;
        this.updateTime = curDate;
        this.updateUserId = updateUserId;
    }
}
