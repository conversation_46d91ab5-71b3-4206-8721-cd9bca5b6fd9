package com.siteweb.generator.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.siteweb.common.util.NumberUtil;
import lombok.Data;

import java.util.Date;

@Data
@TableName("gene_powergenerationstatistics")
public class GenePowerGenerationStatistics  implements Cloneable{
    @TableId(value="id", type = IdType.AUTO)
    private Integer id;
    /**
     * 分局ID
     */
    private Integer departmentId;
    /**
     * 局站ID
     */
    private Integer stationId;
    /**
     * 分区名称
     */
    @TableField(exist = false)
    private String groupName;
    /**
     * 局站类型
     */
    @TableField(exist = false)
    private Integer stationCategory;
    /**
     * 停电告警ID
     */
    private String powerCutAlarmSequenceId;
    /**
     * 市电停电时间
     */
    private Date powerCutTime;
    /**
     *  市电恢复时间
     */
    private Date powerRecoverTime;
    /**
     *  停电间隔
     */
    private Double powerCutInterval;
    /**
     *  停电间隔(数据库计算)
     */
    @TableField(exist = false)
    private Double powerCutIntervalCalc;
    /**
     *  油机发电告警Id
     */
    private String powerGenerationAlarmSequenceId;
    /**
     *  油机开始发电时间
     */
    private Date powerGenerationStartTime;
    /**
     *  油机停止发电时间
     */
    private Date powerGenerationEndTime;
    /**
     *  发电历时
     */
    private Double powerGenerationDuration;
    /**
     *  发电历时(数据库计算)
     */
    @TableField(exist = false)
    private Double powerGenerationDurationCalc;
    /**
     *  发电间隔
     */
    private Double powerGenerationInterval;
    /**
     *  市电恢复间隔
     */
    private Double powerRecoverInterval;
    /**
     *  油机电表电流A
     */
    private Double currentA;
    /**
     *  油机电表电流B
     */
    private Double currentB;
    /**
     *  油机电表电流C
     */
    private Double currentC;
    /**
     * 油机实际功率
     */
    private Double geneActualPower;
    /**
     *  发电量
     */
    private Double powerGenerationValue;
    /**
     *  油机编号
     */
    private String geneNo;
    /**
     *  油机类型
     */
    private Integer geneTypeId;
    /**
     *  额定功率
     */
    private Double geneRatedPower;
    /**
     *  总油耗
     */
    private Double approvedFuelConsumption;
    /**
     *  超时油耗
     */
    private Double overtimeFuelConsumption;
    /**
     *  核定油耗
     */
    private Double approvalFuelConsumption;
    /**
     *  核算时间
     */
    private Date approveTime;
    /**
     *  是否人工
     */
    private Boolean isArtificial;
    /**
     *  设备Id
     */
    private Integer equipmentId;
    /**
     *  数据是否有效
     */
    private Boolean isValid;
    /**
     *  插入时间
     */
    private Date insertTime;
    /**
     *  扩展字段
     */
    private String extendField;


    @Override
    public GenePowerGenerationStatistics clone() {
        try {
            GenePowerGenerationStatistics clone = (GenePowerGenerationStatistics) super.clone();
            return clone;
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }

    public Double getPowerCutInterval() {
        if (powerCutIntervalCalc != null)
            return NumberUtil.doubleAccuracy(powerCutIntervalCalc,2);
        return powerCutInterval;
    }
    public Double getPowerGenerationDuration(){
        if (powerGenerationDurationCalc != null)
            return NumberUtil.doubleAccuracy(powerGenerationDurationCalc,2);
        return powerGenerationDuration;
    }
}
