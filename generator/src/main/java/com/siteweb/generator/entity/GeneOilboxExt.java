package com.siteweb.generator.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@TableName("Gene_OilboxExt")
public class GeneOilboxExt {

    @TableId(value="OilId")
    private Integer oilId;
    /** 油箱容积 L */
    private Double ratedVolume;
    /** 满油液位 毫米 */
    private Double fullOilLevel;
    private Integer updaterId;
    private Date updateTime;
    private String extend1;
}
