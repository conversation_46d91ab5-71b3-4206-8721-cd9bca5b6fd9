package com.siteweb.generator.manager;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.energy.entity.EnergyCustomerConfig;
import com.siteweb.energy.mapper.EnergyCustomerConfigMapper;
import com.siteweb.generator.entity.GeneHistorySignal;
import com.siteweb.generator.entity.GeneratorSignalInfo;
import com.siteweb.generator.function.CommonUtils;
import com.siteweb.generator.mapper.GeneratorHistorySignalMapper;
import com.siteweb.generator.mapper.GeneratorSignalInfoMapper;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.ActiveSignalManager;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.RealTimeSignalManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.model.RealTimeSignalItem;
import com.siteweb.prealarm.entity.PreAlarm;
import com.siteweb.prealarm.entity.PreAlarmCategory;
import com.siteweb.prealarm.entity.PreAlarmSeverity;
import com.siteweb.prealarm.mapper.PreAlarmMapper;
import com.siteweb.prealarm.service.PreAlarmCategoryService;
import com.siteweb.prealarm.service.PreAlarmService;
import com.siteweb.prealarm.service.PreAlarmSeverityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class GeneratorSignalCheckManager {
    @Autowired
    private GeneratorSignalInfoMapper generatorSignalInfoMapper;
    @Autowired
    private GeneratorHistorySignalMapper generatorHistorySignalMapper;
    @Autowired
    private ActiveSignalManager activeSignalManager;
    @Autowired
    private RealTimeSignalManager realTimeSignalManager;
    @Autowired
    private PreAlarmSeverityService preAlarmSeverityService;
    @Autowired
    private EnergyCustomerConfigMapper energyCustomerConfigMapper;
    @Autowired
    private ResourceStructureManager resourceStructureManager;
    @Autowired
    private PreAlarmCategoryService preAlarmCategoryService;
    @Autowired
    private PreAlarmMapper preAlarmMapper;
    @Autowired
    private EquipmentManager equipmentManager;
    @Autowired
    private PreAlarmService preAlarmService;

    public void SignalCheck(Boolean dynamic, List<GeneratorSignalInfo> dynamicSignalInfoList, Integer powerSerialId) {
        try {
            List<GeneratorSignalInfo> signalInfoList = null;
            EnergyCustomerConfig energyCustomerConfig = energyCustomerConfigMapper.selectById(4);
            PreAlarmSeverity preAlarmSeverity = preAlarmSeverityService.findByPreAlarmSeverityId(Integer.parseInt(energyCustomerConfig.getNotes()));
            PreAlarmCategory preAlarmCategory = preAlarmCategoryService.findByCategoryId(7);
            //动态信号处理
            if (dynamic) {
                signalInfoList = dynamicSignalInfoList;
            } else {
                QueryWrapper<GeneratorSignalInfo> wrapper = new QueryWrapper<>();
                wrapper.eq("DynamicOrStatic", 0);
                signalInfoList = generatorSignalInfoMapper.selectList(wrapper);
            }

            List<GeneHistorySignal> historySignals = generatorHistorySignalMapper.getLastHistorySignalValue();
            //如果要检查的信号列表或者历史信号值为空，则不产生告警
            if (signalInfoList == null || signalInfoList.isEmpty() || historySignals == null || historySignals.isEmpty())
                return;
            //历史信号表中设备信号与历史信号值的映射
            Map<String, GeneHistorySignal> geneHisSignalValueMap = historySignals.stream()
                    .collect(Collectors.toMap(
                            GeneHistorySignal::getGeneIdAndSignalId,
                            signalItem -> signalItem));

            //根据设备ID和信号ID获取该设备所有实时信号值
            List<String> keys = new ArrayList<>();
            for (GeneratorSignalInfo oneSignal : signalInfoList) {
                keys.add("RealTimeSignal:" + oneSignal.getGeneId() + "." + oneSignal.getSignalId());
            }

            //获取到所有实时信号值
            List<RealTimeSignalItem> RealSignalItemList = realTimeSignalManager.getRealTimeSignalByKeys(keys);
            //信号key与信号的映射
            Map<String, RealTimeSignalItem> activeSignalKeyMap = RealSignalItemList.stream()
                    .collect(Collectors.toMap(
                            RealTimeSignalItem::getKey,
                            signalItem -> signalItem));
            for (GeneratorSignalInfo oneSignal : signalInfoList) {
                Boolean createPreAlarm = false;
                String checkKey = oneSignal.getCheckKey();
                //阀值
                Double triggerValue = oneSignal.getTriggerValue();
                //历史值
                GeneHistorySignal historySignal = geneHisSignalValueMap.get(checkKey);
                //实时信号
                RealTimeSignalItem currentSignal = activeSignalKeyMap.get(checkKey);

                if (checkKey == null || triggerValue == null || historySignal == null || historySignal.getSignalValue() == null || currentSignal == null || currentSignal.getCurrentValue() == null)
                    continue;
                //如果当天的信号值无效，且上次有效时间超过两天产生预警;否则直接保存为历史信号值
                if (currentSignal.getSignalValid().equals(1)) {
                    Date hisUpdateTime = historySignal.getInsertTime();
                    boolean overTwoDay = isDifferenceMoreThanNDays(hisUpdateTime, CommonUtils.getCurDate() ,2);
                    if (overTwoDay) {
                        //TODO 产生预警
                        String triggerValueStr = "距离上次有效信号值时间超过两天";
                        createPreAlarm = createNewPreAlarm(preAlarmSeverity, preAlarmCategory, oneSignal.getGeneId(), triggerValueStr, oneSignal.getSignalId(), oneSignal.getSignalName());
                    }
                } else {
                    //历史信号值
                    Double historyValue = historySignal.getSignalValue();
                    //实时信号值
                    Double currentValue = Double.valueOf(currentSignal.getCurrentValue());
                    Double diff;
                    if (historyValue.equals(0D))
                        diff = NumberUtil.doubleAccuracy((Math.abs(currentValue - historyValue)), 2);
                    else
                        diff = NumberUtil.doubleAccuracy((Math.abs(currentValue - historyValue) / historyValue), 2);
                    //大于等与阀值，产生预警
                    if (diff >= triggerValue) {
                        //TODO 产生预警
                        String triggerValueStr = "信号值变化百分比：" + diff + "超过阀值：" + triggerValue;
                        createPreAlarm = createNewPreAlarm(preAlarmSeverity, preAlarmCategory, oneSignal.getGeneId(), triggerValueStr, oneSignal.getSignalId(), oneSignal.getSignalName());
                    }
                }

                //如果没有创建新的预警，查询预警列表里endTime为null的预警将其填入现在时间
                if (!createPreAlarm) {
                    List<PreAlarm> existPreAlarm = preAlarmService.findAlarms(oneSignal.getGeneId(), 7).stream()
                            .filter(p -> p.getUniqueId()!= null && p.getUniqueId().equals(String.valueOf(oneSignal.getSignalId())) && p.getEndTime() == null && p.getConfirmTime() == null)
                            .toList();
                    if (!existPreAlarm.isEmpty()) {
                        PreAlarm preAlarm = existPreAlarm.get(0);
                        if (preAlarm == null) {
                            log.error("CloserPreAlarm error preAlarm is null");
                            continue;
                        }
                        preAlarm.setEndTime(CommonUtils.getCurDate());
                        preAlarmService.addToCache(preAlarm);
                        preAlarmMapper.updateById(preAlarm);
                    }
                } else {
                    //这条信号产生了告警的标志
                    oneSignal.setExtend2("makeAlarm");
                }
            }
            // 保存今天的信号值
            for (GeneratorSignalInfo oneSignal : signalInfoList) {
                String key = oneSignal.getCheckKey();
                String signalValue = null;
                Integer signalValid = 1;
                //实时信号值
                RealTimeSignalItem realTimeSignalItem = activeSignalKeyMap.get(key);
                if (realTimeSignalItem != null) {
                    signalValue = realTimeSignalItem.getCurrentValue();
                    signalValid = realTimeSignalItem.getSignalValid();
                }
                GeneHistorySignal temp = new GeneHistorySignal();
                temp.setGeneId(oneSignal.getGeneId());
                temp.setSignalId(oneSignal.getSignalId());
                temp.setGeneIdAndSignalId(key);
                temp.setInsertTime(CommonUtils.getCurDate());
                temp.setMakeAlarm(false);
                if (oneSignal.getExtend2() != null && oneSignal.getExtend2().equals("makeAlarm"))
                    temp.setMakeAlarm(true);
                if (signalValue != null) {
                    Double doubleValue = NumberUtil.doubleAccuracy(Double.valueOf(signalValue), 2);
                    temp.setSignalValue(doubleValue);
                } else {
                    temp.setSignalValue(null);
                }
                temp.setSignalValid(signalValid);
                temp.setPowerSerialId(powerSerialId);
                generatorHistorySignalMapper.insert(temp);
            }
        } catch (Exception e) {
            log.error("GeneratorSignalCheckManager StaticSignalCheck  error", e);
        }
    }

    public Boolean isDifferenceMoreThanNDays(Date hisUpdateTime, Date currentTime, Integer days) {
        try {
            // 计算时间差（以秒为单位）
            long timeDifference = Math.abs((hisUpdateTime.getTime() - currentTime.getTime()) / 1000);

            // 检查时间差是否大于n天（以秒为单位）
            long nDaysInMilliseconds = days * 24 * 60 * 60;
            return timeDifference > nDaysInMilliseconds;
        } catch (Exception e) {
            log.error("GeneratorSignalCheckManager isDifferenceMoreThanNDays error " , e);
            return false;
        }
    }

    public Boolean createNewPreAlarm(PreAlarmSeverity preAlarmSeverity, PreAlarmCategory preAlarmCategory, Integer equipmentId, String triggerValue, Integer signalId, String signalName) {
        try {
            List<PreAlarm> existPreAlarm = preAlarmService.findAlarms(equipmentId, 7).stream()
                    .filter(p -> p.getUniqueId()!= null && p.getUniqueId().equals(String.valueOf(signalId)) && p.getEndTime() == null && p.getConfirmTime() == null)
                    .toList();
            //存在该信号的告警
            if (!existPreAlarm.isEmpty()) {
                PreAlarm preAlarm = existPreAlarm.get(0);
                if (preAlarm == null) {
                    log.error("CreateNewPreAlarm error preAlarm is null");
                    return false;
                }
                preAlarm.setSampleTime(CommonUtils.getCurDate());
                preAlarm.setTriggerValue(triggerValue);
                preAlarmMapper.updateById(preAlarm);
                preAlarmService.addToCache(preAlarm);
                return true;
            }
            Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
            if (equipment == null) {
                log.error("CreateNewPreAlarm error equipment is null");
                return false;
            }
            ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(equipment.getResourceStructureId());
            if (resourceStructure == null) {
                log.error("CreateNewPreAlarm error equipment is null");
                return false;
            }
            String fullPathName = resourceStructureManager.getFullPath(equipment.getResourceStructureId());
            PreAlarm preAlarm = new PreAlarm();
            preAlarm.setColor(preAlarmSeverity.getColor());
            preAlarm.setPreAlarmSeverity(preAlarmSeverity.getPreAlarmSeverityId());
            preAlarm.setPreAlarmSeverityName(preAlarmSeverity.getPreAlarmSeverityName());
            preAlarm.setLevelOfPathName(fullPathName);
            preAlarm.setLevelOfPath(resourceStructure.getLevelOfPath());
            preAlarm.setObjectId(equipmentId);
            preAlarm.setObjectTypeId(7);
            preAlarm.setObjectName(equipment.getEquipmentName());
            preAlarm.setUniqueId(String.valueOf(signalId));
            preAlarm.setResourceStructureId(resourceStructure.getResourceStructureId());
            preAlarm.setPreAlarmCategory(preAlarmCategory.getCategoryId());
            preAlarm.setPreAlarmCategoryName(preAlarmCategory.getCategoryName());
            preAlarm.setMeanings(signalName + "信号检测异常预警");
            preAlarm.setTriggerValue(triggerValue);
            preAlarm.setSampleTime(CommonUtils.getCurDate());
            preAlarm.setStartTime(CommonUtils.getCurDate());
            preAlarm.setPreAlarmPointId(-1);
            preAlarmMapper.insert(preAlarm);
            preAlarmService.addToCache(preAlarm);
        } catch (Exception e) {
            log.error("GeneratorSignalCheckManager createNewPreAlarm error", e);
            return false;
        }
        return true;
    }
}
