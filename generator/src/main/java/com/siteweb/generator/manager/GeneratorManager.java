package com.siteweb.generator.manager;

import com.siteweb.energy.entity.EnergyCustomerConfig;
import com.siteweb.energy.mapper.EnergyCustomerConfigMapper;
import com.siteweb.generator.mapper.GeneratorConfigApiMapper;
import com.siteweb.monitoring.entity.Equipment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
public class GeneratorManager {

    private List<Equipment> allGenerator = new ArrayList<>();
    private Map<Integer, Boolean> customerConfigMap = new HashMap<>();
    @Autowired
    private GeneratorConfigApiMapper generatorConfigApiMapper;
    @Autowired
    private EnergyCustomerConfigMapper energyCustomerConfigMapper;

    public void Init(){
        allGenerator = generatorConfigApiMapper.getAllGenerator();

        List<EnergyCustomerConfig> energyCustomerConfigs = energyCustomerConfigMapper.selectList(null);
        customerConfigMap = energyCustomerConfigs.stream()
                .collect(Collectors.toMap(
                        EnergyCustomerConfig::getId,
                        EnergyCustomerConfig::getEnable ));
    }

    //返回所有油机
    public List<Equipment> GetAllGenerator(){
        return allGenerator;
    }

    public Boolean getCustomerConfigById(Integer configId){
        return customerConfigMap.get(configId);
    }
}
