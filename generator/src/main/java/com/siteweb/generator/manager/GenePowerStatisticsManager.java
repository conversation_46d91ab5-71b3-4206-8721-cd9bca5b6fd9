package com.siteweb.generator.manager;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.generator.dto.GeneHistoryDataResultDTO;
import com.siteweb.generator.dto.GeneStationDTO;
import com.siteweb.generator.entity.GeneMeiTeleStationGeneInfoMap;
import com.siteweb.generator.entity.GenePowerGenerationStatistics;
import com.siteweb.generator.entity.GeneTypePowerInfo;
import com.siteweb.generator.mapper.GenePowerGenerationStatisticsMapper;
import com.siteweb.generator.mapper.GeneTypePowerInfoMapper;
import com.siteweb.monitoring.entity.AlarmChange;
import com.siteweb.monitoring.mamager.ConfigSignalManager;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BoundParameterQuery;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBResultMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.siteweb.common.util.DateUtil.dateToString;

@Component
@Slf4j
public class GenePowerStatisticsManager {
    /**
     * 油机开机告警 基类id
     */
    public static final Long GENE_POWER_ON_ALARM_ID = 302326001L;
    /**
     * 油机电表电流A 基类id
     */
    public static final Long GENE_CURRENT_A_ID = 301062001L;
    /**
     * 油机电表电流B 基类id
     */
    public static final Long GENE_CURRENT_B_ID = 301063001L;
    /**
     * 油机电表电流C 基类id
     */
    public static final Long GENE_CURRENT_C_ID = 301064001L;
    /**
     * 输出有功功率 基类id
     */
    public static final Long GENE_ACTUAL_POWER_ID = 301065001L;
    /**
     * 油机正向有功电能 基类id
     */
    public static final Long GENE_POWER_GENERATION_ID = 1501039001L;
    /**
     * 市电停电告警 基类id
     */
    public static final Long CITY_ELECT_POWER_OFF_ALARM_ID = 201152001L;

    @Value("${spring.influx.database}")
    private String database;
    @Autowired
    private InfluxDB influxDB;
    @Autowired
    private GenePowerGenerationStatisticsMapper genePowerGenerationStatisticsMapper;

    @Autowired
    private ConfigSignalManager configSignalManager;
    @Autowired
    private GeneTypePowerInfoMapper geneTypePowerInfoMapper;

    /**
     * 处理油机发电告警
     */
    public void handleGenePowerOnAlarm(AlarmChange alarmChange) {
        try {
            //处理油机开机告警
            if (!alarmChange.getBaseTypeId().equals(GENE_POWER_ON_ALARM_ID)) {
                log.debug("GenePowerStatisticsManager.handleGenePowerOnAlarm() alarmChange baseTypeId is not match " + GENE_POWER_ON_ALARM_ID);
                return;
            }
            if (alarmChange.getOperationType() == null || alarmChange.getStationId() == null) {
                log.error("GenePowerStatisticsManager.handleGenePowerOnAlarm() operationType or stationId is null");
                return;
            }
            if (alarmChange.getEquipmentId() == null || alarmChange.getStartTime() == null) {
                log.error("GenePowerStatisticsManager.handleGenePowerOnAlarm() equipmentId or startTime is null");
                return;
            }
            if (alarmChange.getSequenceId() == null) {
                log.error("GenePowerStatisticsManager.handleGenePowerOnAlarm() sequenceId is null");
                return;
            }
            //新增告警
            if (alarmChange.getOperationType().equals(1)) {
                GenePowerGenerationStatistics existRecord = genePowerGenerationStatisticsMapper.getGeneExistStatisticsByPowerCutInfo(alarmChange.getStationId());
                if (existRecord == null) {
                    log.error("GenePowerStatisticsManager.handleGenePowerOnAlarm() alarmChange.getOperationType()=1, but records is null . equpmentId=" + alarmChange.getEquipmentId() + "; startTime=" + alarmChange.getStartTime());
                    return;
                }

                existRecord.setPowerGenerationStartTime(alarmChange.getStartTime());
                existRecord.setPowerGenerationAlarmSequenceId(alarmChange.getSequenceId());
                existRecord.setEquipmentId(alarmChange.getEquipmentId());

                Double powerGenerationInterval = getTimeDiff(existRecord.getPowerCutTime(), existRecord.getPowerGenerationStartTime());
                existRecord.setPowerGenerationInterval(powerGenerationInterval);

                genePowerGenerationStatisticsMapper.updateById(existRecord);
            }//结束告警
            else if (alarmChange.getOperationType().equals(2)) {
                if (alarmChange.getEndTime() == null) {
                    log.error("GenePowerStatisticsManager.handleGenePowerOnAlarm() alarmChange.getOperationType()=" + alarmChange.getOperationType() + ", but alarmChange.getEndTime() == null. equpmentId=" + alarmChange.getEquipmentId() + "; startTime=" + alarmChange.getStartTime());
                    return;
                }
                GenePowerGenerationStatistics existGeneRecord = genePowerGenerationStatisticsMapper.getGeneExistStatisticsByGeneSequenceId(alarmChange.getSequenceId(), alarmChange.getStationId());
                if (existGeneRecord == null) {
                    log.error("GenePowerStatisticsManager.handleGenePowerOnAlarm() alarmChange.getOperationType() = 2, but records is null. equpmentId=" + alarmChange.getEquipmentId() + "; startTime=" + alarmChange.getStartTime());
                    return;
                }
                //已经处理过了？
                if (existGeneRecord.getPowerGenerationEndTime() != null) {
                    log.error("GenePowerStatisticsManager.handleGenePowerOnAlarm() alarmChange.getOperationType() = 2, but generationEndTime is not null. equpmentId=" + alarmChange.getEquipmentId() + "; startTime=" + alarmChange.getStartTime());
                    return;
                }

                //填充各个时间
                existGeneRecord.setPowerGenerationEndTime(alarmChange.getEndTime());

                Double powerGenerationDuration = getTimeDiff(existGeneRecord.getPowerGenerationStartTime(), existGeneRecord.getPowerGenerationEndTime());
                existGeneRecord.setPowerGenerationDuration(powerGenerationDuration);


                //现场有可能出现油机断电时间早于市电恢复时间，所以在此处理
                if(existGeneRecord.getPowerRecoverTime() == null)
                    existGeneRecord.setPowerRecoverInterval(0D);
                else {
                    Double powerRecoverInterval = getTimeDiff(existGeneRecord.getPowerRecoverTime(), existGeneRecord.getPowerGenerationEndTime());
                    existGeneRecord.setPowerRecoverInterval(powerRecoverInterval);
                }

                //获取电流数据
                List<Long> baseId = new ArrayList<>();
                baseId.add(GENE_CURRENT_A_ID);
                baseId.add(GENE_CURRENT_B_ID);
                baseId.add(GENE_CURRENT_C_ID);
                baseId.add(GENE_ACTUAL_POWER_ID);

                List<GeneHistoryDataResultDTO> currentValues = getHistoryData(alarmChange.getEquipmentId(), baseId, alarmChange.getStartTime(), alarmChange.getEndTime(), "current");
                for (GeneHistoryDataResultDTO oneRes : currentValues) {
                    Double value = oneRes.getResult() == null ? 0D : NumberUtil.doubleAccuracy(oneRes.getResult(), 2);
                    if (oneRes.getBaseTypeId().equals(GENE_CURRENT_A_ID.toString()))
                        existGeneRecord.setCurrentA(value);
                    else if (oneRes.getBaseTypeId().equals(GENE_CURRENT_B_ID.toString()))
                        existGeneRecord.setCurrentB(value);
                    else if (oneRes.getBaseTypeId().equals(GENE_CURRENT_C_ID.toString()))
                        existGeneRecord.setCurrentC(value);
                    else if (oneRes.getBaseTypeId().equals(GENE_ACTUAL_POWER_ID.toString()))
                        existGeneRecord.setGeneActualPower(value);
                }

                //获取发电量数据
                baseId.clear();
                baseId.add(GENE_POWER_GENERATION_ID);
                List<GeneHistoryDataResultDTO> generationValues = getHistoryData(alarmChange.getEquipmentId(), baseId, alarmChange.getStartTime(), alarmChange.getEndTime(), "generation");
                if (generationValues.size() > 0) {
                    GeneHistoryDataResultDTO res = generationValues.get(0);
                    Double maxValue = res.getMaxValue() == null ? 0D : res.getMaxValue();
                    Double minValue = res.getMinValue() == null ? 0D : res.getMinValue();
                    Double geneValue = (maxValue - minValue) + 1;
                    existGeneRecord.setPowerGenerationValue(NumberUtil.doubleAccuracy(geneValue, 2));
                }
                //填写了油机信息则计算额定油耗
                if (existGeneRecord.getGeneTypeId() != null && existGeneRecord.getGeneRatedPower() != null && existGeneRecord.getPowerGenerationValue() != null) {
                    QueryWrapper<GeneTypePowerInfo> wrapper = new QueryWrapper<>();
                    wrapper.eq("geneTypeId", existGeneRecord.getGeneTypeId());
                    wrapper.eq("ratedPower", existGeneRecord.getGeneRatedPower());
                    GeneTypePowerInfo powerInfo = geneTypePowerInfoMapper.selectOne(wrapper);
                    if (powerInfo != null) {
                        Double fuelWaste = existGeneRecord.getPowerGenerationValue() * powerInfo.getFuelConsumption();
                        existGeneRecord.setApprovedFuelConsumption(NumberUtil.doubleAccuracy(fuelWaste, 2));
                        double timeoutDuration = existGeneRecord.getPowerRecoverInterval() - 2;
                        existGeneRecord.setOvertimeFuelConsumption(0D);
                        if(existGeneRecord.getGeneActualPower() != null && timeoutDuration > 0){
                            //超时油耗：实际功率(KW)×(市电恢复间隔-2)(H)×油机额定油耗（L/KWH）
                            Double overTimeFuel = timeoutDuration * powerInfo.getFuelConsumption();
                            existGeneRecord.setOvertimeFuelConsumption(NumberUtil.doubleAccuracy(overTimeFuel, 2));
                        }
                        //核定油耗(L):公式：总油耗-超时油耗
                        double approvalFuel = existGeneRecord.getApprovedFuelConsumption() - existGeneRecord.getOvertimeFuelConsumption();
                        existGeneRecord.setApprovalFuelConsumption(NumberUtil.doubleAccuracy(approvalFuel, 2));
                    }
                }
                genePowerGenerationStatisticsMapper.updateById(existGeneRecord);
            }
        } catch (Exception ex) {
            log.error("GenePowerStatisticsManager.handleGenePowerOnAlarm() error", ex);
        }
    }

    /**
     * 处理市电停电告警
     */
    public void handleCityElectPowerOffAlarm(AlarmChange alarmChange) {
        try {
            if (!alarmChange.getBaseTypeId().equals(CITY_ELECT_POWER_OFF_ALARM_ID)) {
                log.debug("GenePowerStatisticsManager.handleCityElectPowerOffAlarm() alarmChange baseTypeId is not match " + CITY_ELECT_POWER_OFF_ALARM_ID);
                return;
            }
            if (alarmChange.getOperationType() == null || alarmChange.getStartTime() == null) {
                log.error("GenePowerStatisticsManager.handleCityElectPowerOffAlarm() operationType or startTime is null");
                return;
            }
            if (alarmChange.getStationId() == null || alarmChange.getSequenceId() == null) {
                log.error("GenePowerStatisticsManager.handleCityElectPowerOffAlarm() stationId or sequenceId is null");
                return;
            }
            //新增告警
            if (alarmChange.getOperationType().equals(1)) {
                GenePowerGenerationStatistics powerGenerationStatistics = new GenePowerGenerationStatistics();
                powerGenerationStatistics.setPowerCutTime(alarmChange.getStartTime());
                powerGenerationStatistics.setStationId(alarmChange.getStationId());
                powerGenerationStatistics.setPowerCutAlarmSequenceId(alarmChange.getSequenceId());

                GeneStationDTO stationDTO = genePowerGenerationStatisticsMapper.getStationById(alarmChange.getStationId());
                if (stationDTO == null) {
                    log.error("GenePowerStatisticsManager.handleCityElectPowerOffAlarm() stationDTO is null ");
                    stationDTO = new GeneStationDTO();
                }
                try{
                    //获取当前站的油机信息
                    GeneMeiTeleStationGeneInfoMap geneInfo = genePowerGenerationStatisticsMapper.getGeneInfoByStation(powerGenerationStatistics.getStationId());
                    if (ObjectUtil.isNotEmpty(geneInfo)){
                        powerGenerationStatistics.setGeneNo(geneInfo.getGeneNo());
                        powerGenerationStatistics.setGeneTypeId(geneInfo.getGeneTypeId());
                        powerGenerationStatistics.setGeneRatedPower(geneInfo.getGeneRatedPower());
                    }
                }catch (Exception e){
                    log.error("Get GeneMeiTeleStationGeneInfoMap Error",e);
                }
                powerGenerationStatistics.setDepartmentId(stationDTO.getParentResourceStructureId());
                powerGenerationStatistics.setIsArtificial(false);
                powerGenerationStatistics.setIsValid(true);
                powerGenerationStatistics.setInsertTime(new Date());

                genePowerGenerationStatisticsMapper.insert(powerGenerationStatistics);

            }//结束、确认告警
            else if (alarmChange.getOperationType().equals(2)) {
                GenePowerGenerationStatistics existPowerRecord = genePowerGenerationStatisticsMapper.getGeneExistStatisticsByPowerSequenceId(alarmChange.getSequenceId(), alarmChange.getStationId());
                if (existPowerRecord.getPowerRecoverTime() != null) {
                    log.error("GenePowerStatisticsManager.handleCityElectPowerOffAlarm() alarmChange.getOperationType() = 2, but PowerRecoverTime is not null startTime=" + alarmChange.getEndTime());
                    return;
                }

                existPowerRecord.setPowerRecoverTime(alarmChange.getEndTime());
                Double powerCutInterval = getTimeDiff(existPowerRecord.getPowerCutTime(), existPowerRecord.getPowerRecoverTime());
                existPowerRecord.setPowerCutInterval(powerCutInterval);
                genePowerGenerationStatisticsMapper.updateById(existPowerRecord);
            }
        } catch (Exception ex) {
            log.error("GenePowerStatisticsManager.handleCityElectPowerOffAlarm() error", ex);
        }
    }

    //获取历史数据
    public List<GeneHistoryDataResultDTO> getHistoryData(Integer equipmentId, List<Long> baseTypeId, Date startTime, Date endTime, String queryType) {
        List<GeneHistoryDataResultDTO> result = new ArrayList<>();
        try {
            String selectDuration = "select mean(PointValue) as result from historydatas where time >=$startTime and time <= $endTime and DeviceId = '" + equipmentId + "' and  ( $someSignals ) group by BaseTypeId ";
            if (queryType.equals("generation")) {
                selectDuration = "select max(PointValue) as maxValue, min(PointValue) as minValue  from historydatas WHERE time >=$startTime and time <= $endTime and DeviceId = '" + equipmentId + "' and (BaseTypeId = '1501039001' ) ";
            }
            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
            StringBuilder bsWhereSignalId = new StringBuilder();
            for (Long oneBaseTypeId : baseTypeId) {
                if (bsWhereSignalId.length() == 0) {
                    bsWhereSignalId.append(" BaseTypeId = '").append(oneBaseTypeId).append("' ");
                } else {
                    bsWhereSignalId.append(" or BaseTypeId = '").append(oneBaseTypeId).append("' ");
                }
            }

            selectDuration = selectDuration.replace("$someSignals", bsWhereSignalId.toString());
            Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                    .forDatabase(database)
                    .bind("startTime", dateToString(startTime))
                    .bind("endTime", dateToString(endTime))
                    .create();

            query = influxDB.query(queryBuilder);
            if (query != null) {
                result = resultMapper.toPOJO(query, GeneHistoryDataResultDTO.class);
            }
            return result;
        } catch (Exception e) {
            log.error("GenePowerStatisticsManager-getHistoryData error ", e);
            return new ArrayList<>();
        }
    }

    public Double getTimeDiff(Date startTime, Date endTime) {
        if (startTime == null || endTime == null)
            return null;
        long diffInMilli = endTime.getTime() - startTime.getTime();

        // 将毫秒差值转换为小时，并保留两位小数
        return this.doubleAccuracy((diffInMilli / (1000.0 * 60 * 60)), 2);
    }
    public Double doubleAccuracy(Double value, Integer accuracy) {
        BigDecimal bigDecimalValue = new BigDecimal(value);
        BigDecimal result = bigDecimalValue.setScale(accuracy, RoundingMode.CEILING);
        return result.doubleValue();
    }
}
