package com.siteweb.generator.function;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Calendar;
import java.util.Date;

/** 工具方法类 */
public class CommonUtils {

    /** 获取当前日期对象，毫秒置为0 */
    public static Date getCurDate() {
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.set(Calendar.MILLISECOND, 0);
        return c.getTime();
    }

    public static String convertDouble(Double value) {
        if(value == null) {
            return "";
        } else {
            BigDecimal roundedValue = new BigDecimal(value).setScale(2, RoundingMode.HALF_UP);//实现四舍五入
            DecimalFormat decimalFormat = new DecimalFormat("#.##");//直接使用#.##是使用银行家舍入法（Bankers' rounding）
            return decimalFormat.format(roundedValue);
        }
    }
    /** 将秒数转为小时表示 */
    public static String convertSecondsToHours(long seconds) {
        double hours = (double) seconds / 3600.0;
        String formattedHours = String.format("%.2f", hours);
        return formattedHours + "h";
    }
    /** 计算下次的保养时间
     *  periodicMaintenanceTime - 周期保养时间，单位：天
     * */
    public static Date addDays(Date lastMaintenanceTime, Integer periodicMaintenanceTime) {
        Calendar c = Calendar.getInstance();
        c.setTime(lastMaintenanceTime);
        c.add(Calendar.DAY_OF_MONTH, periodicMaintenanceTime);
        return c.getTime();
    }
    /** 获取当前日期的月份 */
    public static Integer getCurrentMonth(Date time) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(time);
        return cal.get(Calendar.MONTH) + 1;
    }
}
