package com.siteweb.generator.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.generator.entity.GeneHistorySignal;
import com.siteweb.generator.entity.GeneHistorySignalResult;

import java.util.List;

public interface GeneratorHistorySignalMapper extends BaseMapper<GeneHistorySignal> {
    List<GeneHistorySignal> getLastHistorySignalValue();

    List<GeneHistorySignalResult> getHistorySignalValueByPowerSerialId(Integer serialId);
}
