package com.siteweb.generator.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.generator.dto.AccumulatedRuntimeData;
import com.siteweb.generator.dto.GeneMaintenanceOverview;
import com.siteweb.generator.entity.GeneGeneratorElecUnitPrice;
import com.siteweb.generator.entity.GeneGeneratorExt;
import com.siteweb.generator.entity.GeneMaintenanceRecord;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface GeneratorMapper extends BaseMapper<GeneGeneratorExt>{

    List<GeneGeneratorElecUnitPrice> getElecUnitPriceByGeneId(int geneId);

    GeneGeneratorElecUnitPrice getElecUnitPriceBy(Integer equipmentId, Integer year, Integer month, Integer serialId);

    void batchInsertUnitPrice(@Param("list") List<GeneGeneratorElecUnitPrice> list);

    void updateUnitPriceBy(Integer equipmentId, Integer year, Integer month, String yearMonth, Double elecUnitPrice,
                           Integer serialId, Date curDate, Integer userId);

    void delUnitPriceById(int serialId);

    List<GeneMaintenanceRecord> getMaintenanceRecordsById(int geneId);

    void delMaintenanceRecordById(int serialId);

    void insertMaintenanceRecord(GeneMaintenanceRecord record);

    void updateMaintenanceRecord(GeneMaintenanceRecord record);

    List<Integer> findAllEquipIdByRsId(Integer rsId, String rsLike, Integer year, Integer month, Integer generatorEquipBaseTypeId);

    List<GeneMaintenanceOverview> getMaintenanceOverviewByRsId(int rsId, String rsLike, Integer generatorEquipBaseTypeId);
    /** 获取传入层级id所下属的所有设备自上一次保养后的累计运行时长(秒)，以设备id为key返回数据 */
    @MapKey("equipmentId")
    Map<Integer, AccumulatedRuntimeData> getAccumulatedRuntimeByRsId(int rsId, String rsLike, Integer generatorEquipBaseTypeId);
}
