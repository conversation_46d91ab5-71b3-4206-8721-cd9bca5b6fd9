package com.siteweb.generator.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.admin.entity.Account;
import com.siteweb.generator.dto.GenePowerGenerationStatisticsDTO;
import com.siteweb.generator.dto.GeneStationDTO;
import com.siteweb.generator.entity.GeneMeiTeleStationGeneInfoMap;
import com.siteweb.generator.entity.GenePowerGenerationStatistics;

import java.util.Date;
import java.util.List;

public interface GenePowerGenerationStatisticsMapper extends BaseMapper<GenePowerGenerationStatistics> {
    GeneStationDTO getStationById(Integer stationId);

    List<GeneStationDTO> getAllStation();

    List<GenePowerGenerationStatisticsDTO> getGenePowerRecordByStationId(String stationId, Date startTime, Date endTime);

    GenePowerGenerationStatistics getGeneExistStatisticsByPowerCutInfo(Integer stationId);

    GenePowerGenerationStatistics getGeneExistStatisticsByGeneSequenceId(String sequenceId, Integer stationId);

    GenePowerGenerationStatistics getGeneExistStatisticsByPowerSequenceId(String sequenceId, Integer stationId);

    List<GenePowerGenerationStatisticsDTO> getAllRecord(Date startTime, Date endTime);

    GeneMeiTeleStationGeneInfoMap getGeneInfoByStation(Integer stationId);

    List<Account> getAllAccount();

    Integer upDateUserOperateMap(String userIds, Integer operateType);
}
