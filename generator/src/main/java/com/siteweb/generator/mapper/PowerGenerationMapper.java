package com.siteweb.generator.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.generator.entity.GeneGeneratorElecUnitPrice;
import com.siteweb.generator.entity.GenePowerGenerationRecord;

import java.util.Date;
import java.util.List;

public interface PowerGenerationMapper extends BaseMapper<GenePowerGenerationRecord>{

    /** 根据设备id、设备基类型id、开始时间查询发电量记录 */
    List<GenePowerGenerationRecord> getRecordsBy(Integer equipmentId, Integer equipmentBaseType, Date startTime);
    /** 获取该设备指定月份的电单价，不存在则取默认单价 */
    List<GeneGeneratorElecUnitPrice> getMonthUnitPriceBy(int equipmentId, int year, int month);
    /** 获取该设备的默认电单价 */
    Double getDefaultUnitPriceBy(Integer equipmentId);
    /** 更新告警结束时的各项值 */
    void updateByObject(GenePowerGenerationRecord record);
    List<GenePowerGenerationRecord> getRecordsByStartAndEndTime(Integer equipmentId, Integer equipmentBaseType, Date startTime,Date endTime);

    List<GenePowerGenerationRecord> getRecordsByRsIdAndStartAndEndTime(String rsIds,  Date startTime, Date endTime);
}
