package com.siteweb.generator.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.generator.dto.OilboxMapGene;
import com.siteweb.generator.entity.GeneOilboxExt;
import com.siteweb.generator.entity.GeneOilboxGeneratorMap;
import com.siteweb.generator.entity.GeneRefuelRecord;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface OilboxMapper extends BaseMapper<GeneOilboxExt>{

    List<Integer> getGeneIdsByOilId(int oilId);

    List<OilboxMapGene> getGeneEquipsByOilId(int oilId);

    void delMapsByOilId(Integer oilId);

    void batchInsertMaps(@Param("list") List<GeneOilboxGeneratorMap> list);

    List<GeneRefuelRecord> getRefuelRecordsByOilId(int oilId, Date startTime, Date endTime);

    void delRefuelRecordById(int serialId);

    void insertRefuelRecord(GeneRefuelRecord record);

    void updateRefuelRecord(GeneRefuelRecord refuelRecord);

    List<OilboxMapGene> findMapGeneratorsByRsId(Integer rsId, String rsLike, Integer generatorEquipBaseTypeId);
    /** 根据油机设备id，去加油记录中去获取最近一次加油记录中的单价 */
    Double findOilUnitPriceByGeneId(Integer equipmentId);
}
