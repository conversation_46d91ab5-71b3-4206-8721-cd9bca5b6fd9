package com.siteweb.generator.mapper;

import com.siteweb.generator.dto.GeneFuelPriceDTO;
import com.siteweb.generator.dto.GeneStartStopNumDTO;
import com.siteweb.generator.entity.GeneGeneratorElecUnitPrice;
import com.siteweb.generator.entity.GeneGeneratorExt;
import com.siteweb.generator.entity.GeneOilboxExt;
import com.siteweb.generator.entity.GenePowerGenerationRecord;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.prealarm.entity.PreAlarmPoint;

import java.util.Date;
import java.util.List;

public interface GeneratorConfigApiMapper {
    List<GeneGeneratorExt> getOilBoxUnderGeneratorInfo(Integer equipmentId);

    GeneOilboxExt getOilBoxInfo(Integer equipmentId);

    List<GeneOilboxExt> getAllOilBoxByResourceStructureId(String allObjectsId);

    List<GeneGeneratorExt> getAllGeneratorByResourceStructureId(String objectIds);

    List<GenePowerGenerationRecord> getGenePowerTime(String startTime, String endTime,String geneIds);

    List<GeneGeneratorElecUnitPrice> getGenePowerFee(int year,int month, String geneIds);

    List<GeneGeneratorExt> getGeneFuelConsumptionAndDefaultFee(String geneIds);

    List<GeneFuelPriceDTO> getGeneFuelPrice(String endTime);

    Integer getEquipmentBaseType(Integer objectId);

    List<GeneStartStopNumDTO> getGeneStartStopNum(String geneIds,Date startTime, Date endTime);

    List<PreAlarmPoint> GetGeneratorMaintenanceInfoByIds(String geneIds);
    List<Equipment> getAllGenerator();

    List<GeneOilboxExt> getAllOilUnderGeneratorByOilId(String oilIds);

    List<GeneOilboxExt> getGeneOilBoxById(Integer geneId);

    List<GenePowerGenerationRecord> getNoEndTimeGenePowerRecord( String geneIds);

}
