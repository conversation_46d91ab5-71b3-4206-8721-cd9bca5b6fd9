package com.siteweb.generator.scheduler;

import com.siteweb.energy.service.EnergyOverViewService;
import com.siteweb.generator.manager.GeneratorManager;
import com.siteweb.generator.manager.GeneratorSignalCheckManager;
import com.siteweb.utility.service.HAStatusService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Scheduler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@EnableScheduling
@Component
@Slf4j
public class GeneratorScheduler {
    private Scheduler scheduler;
    @Autowired
    private GeneratorManager gneratorManager;
    @Autowired
    private HAStatusService haStatusService;

    @Autowired
    private GeneratorSignalCheckManager generatorSignalCheckManager;

    @Autowired
    private EnergyOverViewService energyOverViewService;
    @PostConstruct
    private void init() {
        //初始化指标缓存
        gneratorManager.Init();
    }

    @Scheduled(fixedDelay = 10 * 60 * 1000)   //每10分钟执行
    //@Scheduled(cron = "0 20 * * * ?") //every 20 minute of hour  注意计算电费要在能耗小时转存之后进行
    protected void schedule() {
        try {
            gneratorManager.Init();
        } catch (Exception ex) {
            log.error("GeneratorScheduler error", ex);
        }
    }


    @Scheduled(cron = "0 0 0 * * *")  // 每天0点检测静态信号的存储阀值
    protected void geneSignalSchedule() {
        try {
            if(!haStatusService.isMasterHost()) {
                log.info("AlarmChangeListener : HAStatus is BACKUP.");
                return;
            }
            if(!energyOverViewService.getCustomerConfigResult(4)){
                log.info("----------Not Config 跳过油机信号预警检测--------------");
                return;
            }
            log.info("----------油机信号预警检测--------------");
            generatorSignalCheckManager.SignalCheck(false,null,null);
        } catch (Exception ex) {
            log.error("geneSignalSchedule error", ex);
        }
    }
}
