package com.siteweb.generator.controller;

import cn.hutool.core.date.DateTime;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.DateUtil;
import com.siteweb.generator.service.GeneratorConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@RestController
@RequestMapping("/api/generator/config")
@Api(value = "GeneratorConfigApiController",tags = "油机组态配置接口")
public class GeneratorConfigApiController {

    @Autowired
    private GeneratorConfigService generatorConfigService;

    @ApiOperation("全省地市油机可发电时长（园区）")
    @GetMapping(value = "/provincepowergenerationtime",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllCityPowerGenerationTime(
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        return ResponseHelper.successful(generatorConfigService.GetAllPowerGenerationTime(userId,null,"province"));
    }

    @ApiOperation("全地市油机可发电时长（大楼）")
    @GetMapping(value = "/citypowergenerationtime",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllBuildingPowerGenerationTime(
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId,
            @ApiParam(name = "resourceStructureId", value = "节点ID", required = true)Integer resourceStructureId

    ){
        return ResponseHelper.successful(generatorConfigService.GetAllPowerGenerationTime(userId,resourceStructureId,"city"));
    }
    @ApiOperation("全区县油机可发电时长")
    @GetMapping(value = "/countypowergenerationtime",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllCountyPowerGenerationTime(
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId,
            @ApiParam(name = "resourceStructureId", value = "节点ID", required = true)Integer resourceStructureId

    ){
        return ResponseHelper.successful(generatorConfigService.GetAllPowerGenerationTime(userId,resourceStructureId,"county"));
    }

    @ApiOperation("全基站油机可发电时长（房间）")
    @GetMapping(value = "/buildingpowergenerationtime",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getTotalEnergyUseOfRole(
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId,
            @ApiParam(name = "resourceStructureId", value = "节点ID", required = true)Integer resourceStructureId
    ){
        return ResponseHelper.successful(generatorConfigService.GetAllPowerGenerationTime(userId,resourceStructureId,"building"));
    }

    @ApiOperation("油箱可发电时长")
    @GetMapping(value = "/oilboxgenerationtime",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getOilBoxGenerationTime(
            @ApiParam(name = "equipmentId", value = "设备Id", required = true)Integer equipmentId
    ){
        return ResponseHelper.successful(generatorConfigService.GetOilBoxGenerationTime(equipmentId));
    }

    @ApiOperation("当前节点下所有油箱当前油量")
    @GetMapping(value = "/cityoilboxfuelquantity",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllCityOilBoxFuelQuantity(
            @ApiParam(name = "userId", value = "用户ID", required = true)Integer userId,
            @ApiParam(name = "resourceStructureId", value = "节点ID", required = true)Integer resourceStructureId

    ){
        return ResponseHelper.successful(generatorConfigService.GetOliBoxFuelQuantity(userId,resourceStructureId));
    }
    @ApiOperation("全省所有油箱当前油量")
    @GetMapping(value = "/alloilboxfuelquantity",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllOilBoxFuelQuantity(
            @ApiParam(name = "userId", value = "用户ID", required = true)Integer userId

    ){
        return ResponseHelper.successful(generatorConfigService.GetOliBoxFuelQuantity(userId,null));
    }

    @ApiOperation("全省油机发电费用和油价费用年趋势")
    @GetMapping(value = "/allgeneratorelectandfuelprice",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllGeneratorElectAndFuelPrice(
            @ApiParam(name = "userId", value = "用户ID", required = true)Integer userId
    ){
        return ResponseHelper.successful(generatorConfigService.GetGeneratorElectAndFuelPrice(userId,null));
    }
    @ApiOperation("某地市油机发电费用和油价费用年趋势")
    @GetMapping(value = "/onecitygeneratorelectandfuelprice",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getOneCityGeneratorElectAndFuelPrice(
            @ApiParam(name = "userId", value = "用户ID", required = true)Integer userId,
            @ApiParam(name = "resourceStructureId", value = "节点Id", required = true)Integer resourceStructureId
    ){
        return ResponseHelper.successful(generatorConfigService.GetGeneratorElectAndFuelPrice(userId,resourceStructureId));
    }
    @ApiOperation("某区县油机发电费用和油价费用年趋势")
    @GetMapping(value = "/onecountygeneratorelectandfuelprice",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getOneCountyGeneratorElectAndFuelPrice(
            @ApiParam(name = "userId", value = "用户ID", required = true)Integer userId,
            @ApiParam(name = "resourceStructureId", value = "节点Id", required = true)Integer resourceStructureId
    ){
        return ResponseHelper.successful(generatorConfigService.GetGeneratorElectAndFuelPrice(userId,resourceStructureId));
    }
    @ApiOperation("某大楼下各油机的启停次数")
    @GetMapping(value = "/generatorstartstopnum",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> generatorstartstopnum(
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户ID", required = true)Integer userId,
            @ApiParam(name = "resourceStructureId", value = "节点ID", required = true)Integer resourceStructureId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(generatorConfigService.GetGeneratorStartStopNum(resourceStructureId,userId,startTime,endTime));
    }
    @ApiOperation("判断设备是否为油机设备")
    @GetMapping(value = "/judgeequipmentisgenerator",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> judgeEquipmentIsGenerator(
            @ApiParam(name = "objectId", value = "设备Id", required = true)Integer objectId
    ){
        return ResponseHelper.successful(generatorConfigService.JudgeEquipmentIsGenerator(objectId));
    }

    // 如果接口没有传开始时间则按照时间类型，自动赋值本日本月本年
    public Date setStartTimeByTimeType(String timeType){
        Date startTime = new Date();
        switch (timeType){
            case "d" :
                startTime = DateUtil.getTodayStartTime().getTime();
                break;
            case "m":
                startTime = DateUtil.getFirstDayOfMonth(DateTime.now());
                break;
            case "y":
                startTime = DateUtil.dateAddMonth(DateUtil.getNextYearFirstDay(DateTime.now()),-12);
                break;
        }
//        startTime.setYear(122); //1900+122==2022
        return startTime;
    }
    public Date setEndTimeByTimeType(Date startTime,String timeType){
        Date endTime = new Date();
        switch (timeType){
            case "d" :
                endTime = DateUtil.getLastSecondsOfToday(startTime);
                break;
            case "m":
                endTime = DateUtil.getLastDayOfMonth(DateTime.now());
                break;
            case "y":
                endTime = DateUtil.dateAddSeconds(DateUtil.getNextYearFirstDay(DateTime.now()),-1);
                break;
        }
//        endTime.setYear(122);
        return endTime;
    }

}
