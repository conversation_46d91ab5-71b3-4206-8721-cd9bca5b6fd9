package com.siteweb.generator.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.generator.dto.OilboxMapGeneParam;
import com.siteweb.generator.entity.GeneOilboxExt;
import com.siteweb.generator.entity.GeneRefuelRecord;
import com.siteweb.generator.service.OilboxService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

@RestController
@RequestMapping("/api/gene")
@Api(value = "OilboxController",tags = "油箱相关接口")
public class OilboxController {

    private final Logger log = LoggerFactory.getLogger(OilboxController.class);

    @Autowired
    private OilboxService oilboxService;


    @ApiOperation(value = "根据油箱设备id获取油箱扩展信息")
    @GetMapping(value = "/oilbox/extInfo",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getExtInfoByEquipmentId(@RequestParam int oilId) {
        return ResponseHelper.successful(oilboxService.getExtInfoByEquipmentId(oilId));
    }

    @ApiOperation(value = "根据油箱设备id获取油箱当前油位历史记录")
    @GetMapping(value = "/oilbox/oillevelrecord",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getOilLevelByEquipmentId(@RequestParam int oilId) {
        return ResponseHelper.successful(oilboxService.getOilLevelByEquipmentId(oilId));
    }
    @ApiOperation(value = "新增或更新某个油箱的扩展信息")
    @PutMapping(value = "/oilbox/saveOrUpdate",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> saveOrUpdate(@RequestBody GeneOilboxExt oilbox) {

        try {
            ResponseEntity<ResponseResult> result = oilboxService.saveOrUpdate(oilbox);
            return result;
        } catch (Exception ex) {
            log.error("saveOrUpdate oilbox throw unknown Exception:", ex);
            return ResponseHelper.failed("-1",ex.getMessage(), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "删除一个油箱设备的扩展信息")
    @DeleteMapping(value = "/oilbox/delete",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteOne(@RequestParam int oilId) {
        return ResponseHelper.successful(oilboxService.delOilboxExt(oilId));
    }

    @ApiOperation(value = "根据油箱设备id获取其下属的所有油机id集合")
    @GetMapping(value = "/oilbox/geneIds",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getGeneIdsByOilId(@RequestParam int oilId) {
        return ResponseHelper.successful(oilboxService.getGeneIdsByOilId(oilId));
    }

    @ApiOperation(value = "根据油箱设备id获取其下属的所有油机设备集合")
    @GetMapping(value = "/oilbox/genes",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getGeneEquipsByOilId(@RequestParam int oilId) {
        return ResponseHelper.successful(oilboxService.getGeneEquipsByOilId(oilId));
    }

    @ApiOperation(value = "保存某个油箱下属油机映射关系")
    @PutMapping(value = "/oilbox/saveGeneMaps",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> saveGeneMaps(@RequestBody OilboxMapGeneParam mapInfo) {
        try {
            ResponseEntity<ResponseResult> result = oilboxService.saveGeneMaps(mapInfo);
            return result;
        } catch (Exception ex) {
            log.error("saveGeneMaps throw unknown Exception:", ex);
            return ResponseHelper.failed("-1",ex.getMessage(), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "根据油箱设备id获取其加油记录集合")
    @GetMapping(value = "/oilbox/refuelRecords",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getRefuelRecordsByOilId(@RequestParam int oilId, Date startTime, Date endTime) {
        return ResponseHelper.successful(oilboxService.getRefuelRecordsByOilId(oilId, startTime, endTime));
    }

    @ApiOperation(value = "为油箱新增一条加油记录")
    @PostMapping(value = "/oilbox/refuelRecord",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> addRefuelRecord(@RequestBody GeneRefuelRecord refuelRecord) {
        try {
            ResponseEntity<ResponseResult> result = oilboxService.addRefuelRecord(refuelRecord);
            return result;
        } catch (Exception ex) {
            log.error("addRefuelRecord throw unknown Exception:", ex);
            return ResponseHelper.failed("-1",ex.getMessage(), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "修改一条油箱加油记录")
    @PutMapping(value = "/oilbox/refuelRecord",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateRefuelRecord(@RequestBody GeneRefuelRecord refuelRecord) {
        try {
            ResponseEntity<ResponseResult> result = oilboxService.updateRefuelRecord(refuelRecord);
            return result;
        } catch (Exception ex) {
            log.error("updateRefuelRecord throw unknown Exception:", ex);
            return ResponseHelper.failed("-1",ex.getMessage(), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "删除一条油箱加油记录")
    @DeleteMapping(value = "/oilbox/refuelRecord",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateRefuelRecord(@RequestParam int serialId) {
        return ResponseHelper.successful(oilboxService.delRefuelRecord(serialId));
    }

    @ApiOperation(value = "根据油箱设备id获取其待关联油机集合")
    @GetMapping(value = "/oilbox/mapGenerators",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getMapGenerators(@RequestParam int oilId) {
        return ResponseHelper.successful(oilboxService.getMapGenerators(oilId));
    }

}
