package com.siteweb.generator.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.generator.dto.GeneElecUnitPrice;
import com.siteweb.generator.entity.GeneGeneratorExt;
import com.siteweb.generator.entity.GeneMaintenanceRecord;
import com.siteweb.generator.service.GeneratorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

@RestController
@RequestMapping("/api/gene")
@Api(value = "GeneratorController",tags = "油机相关接口")
public class GeneratorController {

    private final Logger log = LoggerFactory.getLogger(GeneratorController.class);

    @Autowired
    private GeneratorService generatorService;


    @ApiOperation("获取所有油机、油箱设备资源,有权限")
    @GetMapping(value = "/resourcegeneeq", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getGeneEqResourceByUserId(Integer type) {
        //return ResponseHelper.successful(resourceObjectManager.findAllResourceByUserId(id));
        return ResponseHelper.successful(generatorService.getGeneEqResourceWithPermissions(type));
    }
    @ApiOperation("获取所有油机、油箱设备资源,有权限")
    @GetMapping(value = "/resourcegeneeqnoequip", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getGeneEqResourceNoEquipByUserId(Integer type) {
        return ResponseHelper.successful(generatorService.getGeneEqResourceNoEquipByUserId(type));
    }
    @ApiOperation(value = "根据油机设备id获取油机扩展信息")
    @GetMapping(value = "/generator/extInfo",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getExtInfoByEquipmentId(@RequestParam int geneId) {
        return ResponseHelper.successful(generatorService.getExtInfoByEquipmentId(geneId));
    }

    @ApiOperation(value = "新增或更新某个油机的扩展信息")
    @PutMapping(value = "/generator/saveOrUpdate",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> saveOrUpdate(@RequestBody GeneGeneratorExt gene) {

        try {
            ResponseEntity<ResponseResult> result = generatorService.saveOrUpdate(gene);
            return result;
        } catch (Exception ex) {
            log.error("saveOrUpdate generator throw unknown Exception:", ex);
            return ResponseHelper.failed("-1",ex.getMessage(), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "删除一个油机设备的扩展信息")
    @DeleteMapping(value = "/generator/delete",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteOne(@RequestParam int geneId) {
        return ResponseHelper.successful(generatorService.deleteOneGeneratorExt(geneId));
    }

    @ApiOperation(value = "根据油机设备id获取其电单价列表")
    @GetMapping(value = "/generator/monthUnitPrices",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getElecUnitPriceByGeneId(@RequestParam int geneId) {
        return ResponseHelper.successful(generatorService.getElecUnitPriceByGeneId(geneId));
    }

    @ApiOperation(value = "新增油机月电单价")
    @PostMapping(value = "/generator/monthUnitPrice",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> addElecUnitPrice(@RequestBody GeneElecUnitPrice unitPrice) {
        try {
            ResponseEntity<ResponseResult> result = generatorService.addElecUnitPrice(unitPrice);
            return result;
        } catch (Exception ex) {
            log.error("addElecUnitPrice throw unknown Exception:", ex);
            return ResponseHelper.failed("-1",ex.getMessage(), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "修改油机月电单价")
    @PutMapping(value = "/generator/monthUnitPrice",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateElecUnitPrice(@RequestBody GeneElecUnitPrice unitPrice) {
        try {
            ResponseEntity<ResponseResult> result = generatorService.updateElecUnitPrice(unitPrice);
            return result;
        } catch (Exception ex) {
            log.error("updateElecUnitPrice throw unknown Exception:", ex);
            return ResponseHelper.failed("-1",ex.getMessage(), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "删除一条油机月电单价")
    @DeleteMapping(value = "/generator/monthUnitPrice",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteElecUnitPrice(@RequestParam int serialId) {
        return ResponseHelper.successful(generatorService.deleteElecUnitPrice(serialId));
    }

    @ApiOperation(value = "根据油机设备id获取其保养记录集合")
    @GetMapping(value = "/oilbox/maintenanceRecords",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getMaintenanceRecordsById(@RequestParam int geneId) {
        return ResponseHelper.successful(generatorService.getMaintenanceRecordsById(geneId));
    }

    @ApiOperation(value = "为油机新增一条保养记录")
    @PostMapping(value = "/oilbox/maintenanceRecord",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> addMaintenanceRecord(@RequestBody GeneMaintenanceRecord record) {
        try {
            ResponseEntity<ResponseResult> result = generatorService.addMaintenanceRecord(record);
            return result;
        } catch (Exception ex) {
            log.error("addMaintenanceRecord throw unknown Exception:", ex);
            return ResponseHelper.failed("-1",ex.getMessage(), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "修改一条油机保养记录")
    @PutMapping(value = "/oilbox/maintenanceRecord",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateMaintenanceRecord(@RequestBody GeneMaintenanceRecord record) {
        try {
            ResponseEntity<ResponseResult> result = generatorService.updateMaintenanceRecord(record);
            return result;
        } catch (Exception ex) {
            log.error("updateMaintenanceRecord throw unknown Exception:", ex);
            return ResponseHelper.failed("-1",ex.getMessage(), HttpStatus.OK);
        }
    }

    @ApiOperation(value = "删除一条油机保养记录")
    @DeleteMapping(value = "/oilbox/maintenanceRecord",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> delMaintenanceRecord(@RequestParam int serialId) {
        return ResponseHelper.successful(generatorService.delMaintenanceRecord(serialId));
    }

    @ApiOperation(value = "根据层级id获取其下属的所有油机设备的保养总览")
    @GetMapping(value = "/generator/maintenanceOverview",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getMaintenanceOverviewByRsId(@RequestParam int resourceStructureId) {
        return ResponseHelper.successful(generatorService.getMaintenanceOverviewByRsId(resourceStructureId));
    }

    @ApiOperation("获取所有油机设备资源,有权限")
    @GetMapping(value = "/resourcegeneequip", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getGeneEquipResourceByUserId() {
        return ResponseHelper.successful(generatorService.getGeneEqResourceWithPermissions(1));
    }
    @ApiOperation("获取该节点下所有油机的发电历史记录")
    @GetMapping(value = "/genehistorypowerrecord", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getGeneEquipHistoryPowerRecord(
            @RequestParam Integer objectId,
            @RequestParam Integer objectTypeId,
            @RequestParam Date startTime,
            @RequestParam Date endTime) {
        return ResponseHelper.successful(generatorService.getGeneEquipHistoryPowerRecord(objectId,objectTypeId,startTime,endTime));
    }

    @ApiOperation("获取该油机动态信号检测值历史记录")
    @GetMapping(value = "/genedynamicsignalhistory", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getGeneDynamicSignalHistory(
            @RequestParam Integer serialId){
        return ResponseHelper.successful(generatorService.getGeneDynamicSignalHistory(serialId));
    }

}
