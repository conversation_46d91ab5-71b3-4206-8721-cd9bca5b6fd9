package com.siteweb.generator.controller;


import cn.hutool.core.date.DateTime;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.DateUtil;
import com.siteweb.generator.service.GeneratorConfigExService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;


@RestController
@RequestMapping("/api/generator/generatorapi")
@Api(value = "GeneratorConfigExApiController",tags = "油机组态配置接口")
public class GeneratorConfigExApiController {

    @Autowired
    private GeneratorConfigExService generatorConfigExService;

    @ApiOperation("地市列表-油机累计运行时长、发电量")
    @GetMapping(value = "/listcityrunduration",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getListCityRunDuration(
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(generatorConfigExService.getListCityRunDuration(startTime,endTime,userId));
    }

    @ApiOperation("某地市下各大楼列表-油机累计运行时长、发电量")
    @GetMapping(value = "/listcountyrunduration",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getListCountyRunDuration(
            @ApiParam(name = "resourceStructureId", value = "地市Id", required = true)Integer resourceStructureId,
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(generatorConfigExService.getListCountyRunDuration(resourceStructureId,startTime,endTime,userId));
    }
    @ApiOperation("某区县下各基站列表-油机累计运行时长、发电量")
    @GetMapping(value = "/liststationrunduration",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getListStationRunDuration(
            @ApiParam(name = "resourceStructureId", value = "区县Id", required = true)Integer resourceStructureId,
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(generatorConfigExService.getListStationRunDuration(resourceStructureId,startTime,endTime,userId));
    }
    @ApiOperation("某基站下油机列表-累计运行时长、发电量")
    @GetMapping(value = "/listgenerunduration",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getListGeneRunDuration(
            @ApiParam(name = "resourceStructureId", value = "地市Id", required = true)Integer resourceStructureId,
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(generatorConfigExService.getListGeneRunDuration(resourceStructureId,startTime,endTime,userId));
    }


    @ApiOperation("某节点下油机状态数量")
    @GetMapping(value = "/genestatecount",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getGeneStateCount(
            @ApiParam(name = "resourceStructureId", value = "节点Id", required = true)Integer resourceStructureId,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        return ResponseHelper.successful(generatorConfigExService.getGeneStateCount(resourceStructureId,userId));
    }


    @ApiOperation("地市列表-油机状态数量")
    @GetMapping(value = "/listcitygenestatecount",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getListCityGeneStateCount(
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        return ResponseHelper.successful(generatorConfigExService.getListCityGeneStateCount(userId));
    }

    @ApiOperation("某地市下各区县列表-油机状态数量")
    @GetMapping(value = "/listcountygenestatecount",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getListCountyGeneStateCount(
            @ApiParam(name = "resourceStructureId", value = "地市Id", required = true)Integer resourceStructureId,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        return ResponseHelper.successful(generatorConfigExService.getListCountyGeneStateCount(resourceStructureId,userId));
    }
    @ApiOperation("某区县下各大楼列表-油机状态数量")
    @GetMapping(value = "/liststationgenestatecount",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getListStationGeneStateCount(
            @ApiParam(name = "resourceStructureId", value = "区县Id", required = true)Integer resourceStructureId,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        return ResponseHelper.successful(generatorConfigExService.getListStationGeneStateCount(resourceStructureId,userId));
    }


    @ApiOperation("某基站下油机列表-油机状态")
    @GetMapping(value = "/listgeneworkstatus",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getListGeneWorkStatus(
            @ApiParam(name = "resourceStructureId", value = "基站Id", required = true)Integer resourceStructureId,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        return ResponseHelper.successful(generatorConfigExService.getListGeneWorkStatus(resourceStructureId,userId));
    }

    @ApiOperation("某油机上次下次保养时间")
    @GetMapping(value = "/genemaintenacetime",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getGeneMaintenaceTime(
            @ApiParam(name = "equipmentId", value = "油机Id", required = true)Integer equipmentId
    ){
        return ResponseHelper.successful(generatorConfigExService.getGeneMaintenaceTime(equipmentId));
    }

    @ApiOperation("年趋势月列表-油机发电时长")
    @GetMapping(value = "/listmonthrunduration",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getListMonthRunDuration(
            @ApiParam(name = "resourceStructureId", value = "基站Id", required = true)Integer resourceStructureId,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        return ResponseHelper.successful(generatorConfigExService.getListMonthRunDuration(resourceStructureId,userId));
    }






    // 如果接口没有传开始时间则按照时间类型，自动赋值本日本月本年
    public Date setStartTimeByTimeType(String timeType){
        Date startTime = new Date();
        switch (timeType){
            case "d" :
                startTime = DateUtil.getTodayStartTime().getTime();
                break;
            case "m":
                startTime = DateUtil.getFirstDayOfMonth(DateTime.now());
                break;
            case "y":
                startTime = DateUtil.dateAddMonth(DateUtil.getNextYearFirstDay(DateTime.now()),-12);
                break;
        }
//        startTime.setYear(122); //1900+122==2022
        return startTime;
    }
    public Date setEndTimeByTimeType(Date startTime,String timeType){
        Date endTime = new Date();
        switch (timeType){
            case "d" :
                endTime = DateUtil.getLastSecondsOfToday(startTime);
                break;
            case "m":
                endTime = DateUtil.getLastDayOfMonth(DateTime.now());
                break;
            case "y":
                endTime = DateUtil.dateAddSeconds(DateUtil.getNextYearFirstDay(DateTime.now()),-1);
                break;
        }
//        endTime.setYear(122);
        return endTime;
    }


}
