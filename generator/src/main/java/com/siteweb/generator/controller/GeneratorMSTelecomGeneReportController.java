package com.siteweb.generator.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.generator.entity.GenePowerGenerationStatistics;
import com.siteweb.generator.manager.GenePowerStatisticsManager;
import com.siteweb.generator.service.MSTelecomGeneReportService;
import com.siteweb.monitoring.entity.AlarmChange;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

import static com.siteweb.generator.manager.GenePowerStatisticsManager.CITY_ELECT_POWER_OFF_ALARM_ID;
import static com.siteweb.generator.manager.GenePowerStatisticsManager.GENE_POWER_ON_ALARM_ID;

@RestController
@RequestMapping("/api/generator/mstelecom")
@Api(value = "GeneratorMSTelecomGeneReportController", tags = "眉山电信发电记录表相关接口")
public class GeneratorMSTelecomGeneReportController {

    @Autowired
    private MSTelecomGeneReportService msTelecomGeneReportService;
    @Autowired
    private GenePowerStatisticsManager genePowerStatisticsManager;

    /**
     * 获取分局列表
     */
    @ApiOperation("获取分局列表")
    @GetMapping(value = "/departmentlist", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getDepartmentList() {
        return ResponseHelper.successful(msTelecomGeneReportService.getDepartmentList());
    }

    /**
     * 根据分局ID获取局站列表
     */
    @ApiOperation("根据分局ID获取局站列表")
    @GetMapping(value = "/stationlist", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getStationListByIds(
            @ApiParam(name = "departmentIds", value = "分局ID", required = true) String departmentIds
    ) {
        return ResponseHelper.successful(msTelecomGeneReportService.getStationListByIds(departmentIds));
    }

    /**
     * 获取油机类型列表
     */
    @ApiOperation("获取油机类型列表")
    @GetMapping(value = "/genetypelist", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getGeneTypeList(
    ) {
        return ResponseHelper.successful(msTelecomGeneReportService.getGeneTypeList());
    }

    /**
     * 获取额定功率列表
     */
    @ApiOperation("获取额定功率列表")
    @GetMapping(value = "/ratedpowerlist", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getRatedPowerList(@RequestParam Integer geneTypeId
    ) {
        return ResponseHelper.successful(msTelecomGeneReportService.getRatedPowerList(geneTypeId));
    }

    /**
     * 根据局站ID获取油机发电统计记录
     */
    @ApiOperation("根据局站ID和时间获取油机发电统计记录")
    @GetMapping(value = "/genepowerrecordbystationid", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getGenePowerRecordByStationId(String departmentIds,@RequestParam String stationIds, @RequestParam Date queryTime) {
        return ResponseHelper.successful(msTelecomGeneReportService.getGenePowerRecordByStationId(departmentIds,stationIds, queryTime));
    }

    /**
     * 补录油机发电记录
     */
    @ApiOperation("补录油机发电记录")
    @PostMapping(value = "/addgenepowerrecord", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> addGenePowerRecord(@RequestBody GenePowerGenerationStatistics recordInfo) {
        int res = msTelecomGeneReportService.addGenePowerRecord(recordInfo);
        if (res != 1)
            return ResponseHelper.failed("insert fail please check");
        return ResponseHelper.successful(res);
    }

    /**
     * 修改油机发电记录
     */
    @ApiOperation("修改油机发电记录")
    @PutMapping(value = "/editgenepowerrecord", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> editGenePowerRecord(@RequestBody GenePowerGenerationStatistics recordInfo) {
        return ResponseHelper.successful(msTelecomGeneReportService.editGenePowerRecord(recordInfo));
    }

    /**
     * 获取修改记录列表
     */
    @ApiOperation("获取修改记录列表")
    @GetMapping(value = "/modifyrecordlist", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getModifyRecordList(@RequestParam Integer recordId) {
        return ResponseHelper.successful(msTelecomGeneReportService.getModifyRecordList(recordId));
    }

    /**
     * 核算
     */
    @ApiOperation("核定油耗")
    @PostMapping(value = "/approvedfuel", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> approvedFuelConsumption(@RequestBody GenePowerGenerationStatistics recordInfo) {
        return ResponseHelper.successful(msTelecomGeneReportService.approvedFuelConsumption(recordInfo));
    }


    /**
     * 获取区县列表
     */
    @ApiOperation("获取区县列表")
    @GetMapping(value = "/countylist", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCountyList() {
        return ResponseHelper.successful(msTelecomGeneReportService.getCountyList());
    }

    /**
     * 获取市电恢复间隔分析
     */
    @ApiOperation("获取市电恢复间隔分析")
    @GetMapping(value = "/powerrecover", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getPowerRecover(@RequestParam Date startTime,
                                                        @RequestParam Date endTime,
                                                        @RequestParam Integer objectId){
        return ResponseHelper.successful(msTelecomGeneReportService.getPowerRecoverInterval(startTime,endTime,objectId));
    }
    @ApiOperation("获取停电总时长分析")
    @GetMapping(value = "/powercut", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getTotalPowerCut(@RequestParam Date startTime,
                                                        @RequestParam Date endTime,
                                                        @RequestParam Integer objectId){
        return ResponseHelper.successful(msTelecomGeneReportService.getTotalPowerCutInterval(startTime,endTime,objectId));
    }
    @ApiOperation("获取平均停电时长分析")
    @GetMapping(value = "/avgpowercut", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getPowerCut(@RequestParam Date startTime,
                                                      @RequestParam Date endTime,
                                                      @RequestParam Integer objectId){
        return ResponseHelper.successful(msTelecomGeneReportService.getAvgPowerCutInterval(startTime,endTime,objectId));
    }
    @ApiOperation("获取发电间隔时长分析")
    @GetMapping(value = "/powergene", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getPowerGeneInterval(@RequestParam Date startTime,
                                                      @RequestParam Date endTime,
                                                      @RequestParam Integer objectId){
        return ResponseHelper.successful(msTelecomGeneReportService.getPowerGeneInterval(startTime,endTime,objectId));
    }
    @ApiOperation("获取发电平均历时分析")
    @GetMapping(value = "/powergenedurn", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getPowerGeneDuration(@RequestParam Date startTime,
                                                               @RequestParam Date endTime,
                                                               @RequestParam Integer objectId){
        return ResponseHelper.successful(msTelecomGeneReportService.getPowerGeneDuration(startTime,endTime,objectId));
    }
    @ApiOperation("获取账号映射列表")
    @GetMapping(value = "/getaccountlist", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAccountList(){
        return ResponseHelper.successful(msTelecomGeneReportService.getAccountList());
    }
    @ApiOperation("更改账号映射关系列表")
    @PutMapping(value = "/getroleopermap", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateAccountMap(
                                                            @RequestParam String userIds,
                                                            @RequestParam Integer operateType){
        return ResponseHelper.successful(msTelecomGeneReportService.updateAccountMap(userIds, operateType));
    }


    //测试告警
//    @ApiOperation("测试告警")
//    @PostMapping(value = "/testalarm", produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseEntity<ResponseResult> testalarm(@RequestBody AlarmChange alarmChange) {
//        if (alarmChange.getBaseTypeId().equals(GENE_POWER_ON_ALARM_ID))
//            genePowerStatisticsManager.handleGenePowerOnAlarm(alarmChange);
//        if (alarmChange.getBaseTypeId().equals(CITY_ELECT_POWER_OFF_ALARM_ID))
//            genePowerStatisticsManager.handleCityElectPowerOffAlarm(alarmChange);
//        return ResponseHelper.successful();
//    }

}
