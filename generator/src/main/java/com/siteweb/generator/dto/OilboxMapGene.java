package com.siteweb.generator.dto;

import com.siteweb.common.util.StringUtils;
import lombok.Data;

/** 油箱映射的油机对象类 */
@Data
public class OilboxMapGene {

    private Integer oilId;
    private String oilboxName;
    private Integer geneId;
    private String geneName;
    /** 油机设备所属局站 */
    private String stationName;
    /** 油机设备所属层级 */
    private String resourceStructureName;
    /** 油机设备基类型 */
    private Integer equipmentBaseType;

    public String getRsEquipmentName() {
        String rsName = StringUtils.isBlank(resourceStructureName) ? "Null" : resourceStructureName;
        String eName = StringUtils.isBlank(geneName) ? "Null" : geneName;
        return rsName + "-" + eName;
    }

}
