package com.siteweb.generator.dto;

import com.siteweb.monitoring.dto.ResourceObjectEntity;
import lombok.Data;

@Data
public class GeneResourceObjectEntity extends ResourceObjectEntity {

    public GeneResourceObjectEntity(Integer objectId, Integer objectTypeId, String resourceStructureName, Integer resourceStructureId,
           Integer parentResourceStructureId, Integer parentResourceStructureTypeId, Integer equipmentBaseType, String levelOfPath,Integer originId) {
        super(objectId, objectTypeId, resourceStructureName, resourceStructureId, parentResourceStructureId, parentResourceStructureTypeId,levelOfPath,originId);
        this.equipmentBaseType = equipmentBaseType;
    }

    private Integer equipmentBaseType;
}
