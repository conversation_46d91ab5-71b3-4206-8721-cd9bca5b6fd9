package com.siteweb.generator.dto;

import com.siteweb.generator.entity.GenePowerGenerationStatistics;
import lombok.Data;

@Data
public class GenePowerGenerationStatisticsDTO extends GenePowerGenerationStatistics {
    /**
     * 局站名称
     */
    private String stationName;
    /**
     * 分局名称
     */
    private String departmentName;
    /**
     * 油机类型名称
     */
    private String geneTypeName;
    /**
     * 局站类型名称
     */
    private String stationCategoryName;
    /**
     * 是否人工名称
     */
    private String isArtificialName;
    /**
     * 是否有效名称
     */
    private String isValidName;

    public String getIsArtificialName() {
        if (this.getIsArtificial())
            return "人工";
        else
            return "自动";
    }

    public String getIsValidName() {
        if (this.getIsValid())
            return "有效";
        else
            return "无效";
    }
}
