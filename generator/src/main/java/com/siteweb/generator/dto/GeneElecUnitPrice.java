package com.siteweb.generator.dto;

import lombok.Data;

@Data
public class GeneElecUnitPrice {

    private Integer serialId;
    private Integer equipmentId;
    /** yyyy-MM */
    private String yearMonth;

    private Double elecUnitPrice;
    /** 批量新增类型： 0-本油机
     *              1-本站所有油机(约定选中设备向上搜索两级层级，然后查找此祖父级层级下的所有油机设备)、
     *              2-平级站所有油机(约定选中设备向上搜索三级层级，然后查找此曾祖级层级下的所有油机设备)  */
    private Integer batchType;
}
