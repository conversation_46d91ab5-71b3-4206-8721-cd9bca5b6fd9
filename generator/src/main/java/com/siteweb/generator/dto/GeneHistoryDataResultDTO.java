package com.siteweb.generator.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.influxdb.annotation.Column;
import org.influxdb.annotation.Measurement;


@Data
@NoArgsConstructor
@Measurement(name = "historydatas")
public class GeneHistoryDataResultDTO {

    @Column(name = "time", tag = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private String time;

    @Column(name = "BaseTypeId", tag = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private String baseTypeId;

    @Column(name = "result", tag = true)
    private Double result;

    @Column(name = "maxValue", tag = true)
    private Double maxValue;

    @Column(name = "minValue", tag = true)
    private Double minValue;
}
