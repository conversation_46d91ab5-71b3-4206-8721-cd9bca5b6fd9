package com.siteweb.generator.dto;

import com.siteweb.generator.function.CommonUtils;
import lombok.Data;

import java.util.Date;

/** 油机保养总览记录对象 */
@Data
public class GeneMaintenanceOverview {

    private Integer equipmentId;
    private String equipmentName;
    private String stationName;
    private String houseName;
    /** 按周期保养（配置的保养周期，单位暂定：天） */
    private Integer periodicMaintenanceTime;
    /** 按时长保养（配置的时长，单位暂定：小时) */
    private Integer durationOfMaintenanceTime;
    /** 上次保养时间 */
    private Date lastMaintenanceTime;
    /** 自上次保养后的【累计运行时长】，单位：秒 */
    private Long runDurationSecond;
    /** 自上次保养后的【剩余运行时长】，单位：小时 */
    private String remainingRuntime;

    /** 备注：如果已到达保养时间，就显示为”需保养“，否则显示为空 */
    private String remark;
    /** 下次保养时间  */
    private Date nextMaintenanceTime;

    /** 计算得到【下次保养时间】、【剩余运行时间】 */
    public void calcData() {

        try {
            //计算剩余运行时间
            boolean isNeedMaintenance = false;
            if(this.durationOfMaintenanceTime != null && this.runDurationSecond != null) {
                if(this.runDurationSecond < 0 || this.durationOfMaintenanceTime < 0) {
                    remainingRuntime =  "——";
                } else {
                    long remainRuntime = this.durationOfMaintenanceTime * 3600L - this.runDurationSecond;
                    isNeedMaintenance = remainRuntime <= 0;
                    remainingRuntime = CommonUtils.convertSecondsToHours(isNeedMaintenance ? 0 : remainRuntime);
                }
            }
            //按周期计算，自上次保养时间之后，下次需要保养的时间
            if(this.periodicMaintenanceTime != null && this.lastMaintenanceTime != null) {
                this.nextMaintenanceTime = CommonUtils.addDays(this.lastMaintenanceTime, this.periodicMaintenanceTime);
            }
            //如果已到达保养时间，就显示为”需保养“，否则显示为空
            if(isNeedMaintenance || (this.nextMaintenanceTime != null && System.currentTimeMillis() > this.nextMaintenanceTime.getTime())) {
                this.remark = "需保养";
            }
        } catch (Exception e) {
            this.remark = "exception";
        }
    }
    /** 计算得到自上次保养后的【累计运行时长】，单位：小时 */
    public String getAccumulatedRuntime() {
        if(this.runDurationSecond == null || this.runDurationSecond < 0) {
            return "";
        } else {
            return CommonUtils.convertSecondsToHours(this.runDurationSecond);
        }
    }
}
