package com.siteweb.generator.service;

import java.text.ParseException;
import java.util.Date;

public interface GeneratorConfigExService {

    //地市列表油机累计运行时长
    Object getListCityRunDuration(Date startTime, Date endTime, Integer userId);

    //某地市下各大楼列表油机累计运行时长
    Object getListStationRunDuration(Integer resourceStructureId, Date startTime, Date endTime, Integer userId);

    //某基站下各油机累计运行时长
    Object getListGeneRunDuration(Integer resourceStructureId, Date startTime, Date endTime, Integer userId);

    Object getGeneStateCount(Integer resourceStructureId, Integer userId);

    Object getListCityGeneStateCount(Integer userId);
    Object getListCountyRunDuration(Integer resourceStructureId, Date startTime, Date endTime, Integer userId);

    Object getListStationGeneStateCount(Integer resourceStructureId, Integer userId);

    Object getListGeneWorkStatus(Integer resourceStructureId, Integer userId);

    Object getGeneMaintenaceTime(Integer equipmentId);

    Object getListMonthRunDuration(Integer resourceStructureId, Integer userId);

    Object getListCountyGeneStateCount(Integer resourceStructureId, Integer userId);
}
