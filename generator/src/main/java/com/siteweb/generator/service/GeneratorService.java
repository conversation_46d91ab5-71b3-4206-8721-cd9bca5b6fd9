package com.siteweb.generator.service;

import com.siteweb.common.response.ResponseResult;
import com.siteweb.generator.dto.GeneElecUnitPrice;
import com.siteweb.generator.dto.GeneMaintenanceOverview;
import com.siteweb.generator.dto.GeneResourceObjectEntity;
import com.siteweb.generator.entity.*;
import com.siteweb.monitoring.dto.ResourceStructureTreeDTO;
import org.springframework.http.ResponseEntity;

import java.util.Date;
import java.util.List;

public interface GeneratorService {

    GeneGeneratorExt getExtInfoByEquipmentId(int equipmentId);

    ResponseEntity<ResponseResult> saveOrUpdate(GeneGeneratorExt gene);

    boolean deleteOneGeneratorExt(int geneId);

    List<GeneGeneratorElecUnitPrice> getElecUnitPriceByGeneId(int geneId);

    /** 批量新增油机的月电单价，如果已存在，则跳过(本接口只做新增) */
    ResponseEntity<ResponseResult> addElecUnitPrice(GeneElecUnitPrice unitPrice);

    ResponseEntity<ResponseResult> updateElecUnitPrice(GeneElecUnitPrice unitPrice);

    boolean deleteElecUnitPrice(int serialId);

    List<GeneMaintenanceRecord> getMaintenanceRecordsById(int geneId);

    boolean delMaintenanceRecord(int serialId);

    ResponseEntity<ResponseResult> addMaintenanceRecord(GeneMaintenanceRecord record);

    ResponseEntity<ResponseResult> updateMaintenanceRecord(GeneMaintenanceRecord record);
    /** 1-只过滤油机设备； 2-只过滤油箱设备； else-油机和油箱 */
    List<GeneResourceObjectEntity> getGeneEqResourceWithPermissions(Integer type);

    List<GeneMaintenanceOverview> getMaintenanceOverviewByRsId(int resourceStructureId);

    ResourceStructureTreeDTO getGeneEqResourceNoEquipByUserId(Integer type);

    List<GenePowerGenerationRecord> getGeneEquipHistoryPowerRecord(Integer objectId, Integer objectTypeId, Date startTime, Date endTime);

    List<GeneHistorySignalResult> getGeneDynamicSignalHistory(Integer serialId);
}
