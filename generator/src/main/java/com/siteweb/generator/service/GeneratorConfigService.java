package com.siteweb.generator.service;

import com.siteweb.generator.dto.GenePowerFeeDTO;
import com.siteweb.generator.dto.GeneStartStopNumDTO;
import org.javatuples.Pair;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface GeneratorConfigService {
    Object GetAllPowerGenerationTime(Integer userId, Integer resourceStructureId, String queryType);

    Double GetOilBoxGenerationTime(Integer equipmentId);

    Double GetOliBoxFuelQuantity(Integer userId, Integer resourceStructureId);

    List<GenePowerFeeDTO> GetGeneratorElectAndFuelPrice(Integer userId, Integer resourceStructureId);

    Boolean JudgeEquipmentIsGenerator(Integer objectId);

    List<GeneStartStopNumDTO> GetGeneratorStartStopNum(Integer resourceStructureId, Integer userId, Date startTime, Date endTime);

    Map<Integer, Pair<Integer,Integer>> GetGeneratorMaintenanceInfo(List<Integer> geneIds);
}
