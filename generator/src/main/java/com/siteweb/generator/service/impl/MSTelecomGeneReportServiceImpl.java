package com.siteweb.generator.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.admin.entity.Account;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.generator.dto.GeneDataAnalysisDTO;
import com.siteweb.generator.dto.GenePowerGenerationStatisticsDTO;
import com.siteweb.generator.dto.GeneStationDTO;
import com.siteweb.generator.dto.ShowDataDTO;
import com.siteweb.generator.entity.GeneMeiTeleStationGeneInfoMap;
import com.siteweb.generator.entity.GeneModifyRecord;
import com.siteweb.generator.entity.GenePowerGenerationStatistics;
import com.siteweb.generator.entity.GeneTypePowerInfo;
import com.siteweb.generator.mapper.GeneModifyRecordMapper;
import com.siteweb.generator.mapper.GenePowerGenerationStatisticsMapper;
import com.siteweb.generator.mapper.GeneTypePowerInfoMapper;
import com.siteweb.generator.service.MSTelecomGeneReportService;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.entity.Station;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.mamager.StationManager;
import com.siteweb.monitoring.service.ResourceStructureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.ToDoubleFunction;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MSTelecomGeneReportServiceImpl implements MSTelecomGeneReportService {
    @Autowired
    private ResourceStructureService resourceStructureService;

    @Autowired
    private GeneTypePowerInfoMapper geneTypePowerInfoMapper;

    @Autowired
    private GenePowerGenerationStatisticsMapper genePowerGenerationStatisticsMapper;

    @Autowired
    private GeneModifyRecordMapper geneModifyRecordMapper;

    private final Integer timeDiffStandard = 24;
    @Autowired
    private ResourceStructureManager resourceStructureManager;
    @Autowired
    private StationManager stationManager;

    @Override
    public List<ResourceStructure> getDepartmentList() {
        return resourceStructureService.findResourceStructureByUserId(TokenUserUtil.getLoginUserId()).stream().filter(o -> o.getStructureTypeId().equals(103) || o.getStructureTypeId().equals(3)).toList();
    }

    /**
     * 根据分局ID获取局站列表
     *
     * @param departmentIds 分局ID，用“,”隔开，如果全选则包含“allStation”
     */
    @Override
    public List<GeneStationDTO> getStationListByIds(String departmentIds) {

        List<String> departmentList = Arrays.stream(departmentIds.split(",")).toList();
        List<GeneStationDTO> allStation = genePowerGenerationStatisticsMapper.getAllStation();

        List<ResourceStructure> stationList = resourceStructureService.findResourceStructureByUserId(TokenUserUtil.getLoginUserId()).stream().filter(o -> o.getStructureTypeId().equals(104)||o.getStructureTypeId().equals(4)).toList();

        if (departmentIds.contains("allStation") || departmentList.isEmpty()) {
            Set<Integer> originIds = stationList.stream()
                    .map(ResourceStructure::getOriginId)
                    .collect(Collectors.toSet());
            return allStation.stream()
                    .filter(station -> originIds.contains(station.getStationId()))
                    .collect(Collectors.toList());
        }

        List<ResourceStructure> filteredList = stationList.stream()
                .filter(resource -> departmentList.contains(String.valueOf(resource.getParentResourceStructureId()))).toList();

        Set<Integer> originIds = filteredList.stream()
                .map(ResourceStructure::getOriginId)
                .collect(Collectors.toSet());
        return allStation.stream()
                .filter(station -> originIds.contains(station.getStationId()))
                .collect(Collectors.toList());
    }

    @Override
    public List<GeneTypePowerInfo> getGeneTypeList() {
        return geneTypePowerInfoMapper.selectGeneTypeList();
    }

    @Override
    public List<Double> getRatedPowerList(Integer geneTypeId) {
        return geneTypePowerInfoMapper.selectRatedPowerList(geneTypeId);
    }

    /**
     * 补录发电信息
     *
     * @param recordInfo 发电统计信息
     */
    @Override
    public int addGenePowerRecord(GenePowerGenerationStatistics recordInfo) {

        recordInfo.setIsArtificial(true);
        recordInfo.setIsValid(true);
        Double powerGenerationDuration = getTimeDiff(recordInfo.getPowerGenerationStartTime(), recordInfo.getPowerGenerationEndTime());
        recordInfo.setPowerGenerationDuration(powerGenerationDuration);

        Double powerGenerationInterval = getTimeDiff(recordInfo.getPowerCutTime(), recordInfo.getPowerGenerationStartTime());
        recordInfo.setPowerGenerationInterval(powerGenerationInterval);

        Double powerRecoverInterval = getTimeDiff(recordInfo.getPowerRecoverTime(), recordInfo.getPowerGenerationEndTime());
        recordInfo.setPowerRecoverInterval(powerRecoverInterval);

        Double powerCutInterval = getTimeDiff(recordInfo.getPowerCutTime(), recordInfo.getPowerRecoverTime());
        recordInfo.setPowerCutInterval(powerCutInterval);
        try{
            //获取当前站的油机信息
            GeneMeiTeleStationGeneInfoMap geneInfo = genePowerGenerationStatisticsMapper.getGeneInfoByStation(recordInfo.getStationId());
            if (ObjectUtil.isNotEmpty(geneInfo)){
                recordInfo.setGeneNo(geneInfo.getGeneNo());
                recordInfo.setGeneTypeId(geneInfo.getGeneTypeId());
                recordInfo.setGeneRatedPower(geneInfo.getGeneRatedPower());
            }
        }catch (Exception e){
            log.error("Get GeneMeiTeleStationGeneInfoMap Error",e);
        }

        recordInfo.setInsertTime(new Date());
        return genePowerGenerationStatisticsMapper.insert(recordInfo);
    }

    /**
     * 修改油机发电记录
     *
     * @param recordInfo 发电记录信息
     */
    @Override
    public GenePowerGenerationStatistics editGenePowerRecord(GenePowerGenerationStatistics recordInfo) {

        GenePowerGenerationStatistics existRecord = genePowerGenerationStatisticsMapper.selectById(recordInfo.getId());
        GenePowerGenerationStatistics cloneRecord = existRecord.clone();

        GeneModifyRecord modifyRecord = new GeneModifyRecord();
        StringBuilder sb = new StringBuilder();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        boolean isChange = false;
        if (recordInfo.getPowerCutTime() != null && (cloneRecord.getPowerCutTime() == null || !recordInfo.getPowerCutTime().equals(cloneRecord.getPowerCutTime()))) {
            if (cloneRecord.getPowerCutTime() != null)
                sb.append("市电停电时间 修改前 ：").append(dateFormat.format(cloneRecord.getPowerCutTime())).append("，修改后 ：").append(dateFormat.format(recordInfo.getPowerCutTime())).append("；");
            else
                sb.append("市电停电时间 修改前 ：null").append("，修改后 ：").append(dateFormat.format(recordInfo.getPowerCutTime())).append("；");

            existRecord.setPowerCutTime(recordInfo.getPowerCutTime());
            isChange = true;
            if(cloneRecord.getApproveTime() != null){
                existRecord.setIsValid(getTimeDiff(existRecord.getPowerCutTime(), cloneRecord.getApproveTime()) <= timeDiffStandard);
            }
        }
        if (recordInfo.getPowerRecoverTime() != null && (cloneRecord.getPowerRecoverTime() == null || !recordInfo.getPowerRecoverTime().equals(cloneRecord.getPowerRecoverTime()))) {
            if (cloneRecord.getPowerRecoverTime() != null)
                sb.append("市电恢复时间 修改前 ：").append(dateFormat.format(cloneRecord.getPowerRecoverTime())).append("，修改后 ：").append(dateFormat.format(recordInfo.getPowerRecoverTime())).append("；");
            else
                sb.append("市电恢复时间 修改前 ：null").append("，修改后 ：").append(dateFormat.format(recordInfo.getPowerRecoverTime())).append("；");

            existRecord.setPowerRecoverTime(recordInfo.getPowerRecoverTime());
            isChange = true;
        }
        if (recordInfo.getPowerGenerationStartTime() != null && (cloneRecord.getPowerGenerationStartTime() == null || !recordInfo.getPowerGenerationStartTime().equals(cloneRecord.getPowerGenerationStartTime()))) {
            if (cloneRecord.getPowerGenerationStartTime() != null)
                sb.append("油机开始发电时间 修改前 ：").append(dateFormat.format(cloneRecord.getPowerGenerationStartTime())).append("，修改后 ：").append(dateFormat.format(recordInfo.getPowerGenerationStartTime())).append("；");
            else
                sb.append("油机开始发电时间 修改前 ：null").append("，修改后 ：").append(dateFormat.format(recordInfo.getPowerGenerationStartTime())).append("；");

            existRecord.setPowerGenerationStartTime(recordInfo.getPowerGenerationStartTime());
            isChange = true;
        }
        if (recordInfo.getPowerGenerationEndTime() != null && (cloneRecord.getPowerGenerationEndTime() == null || !recordInfo.getPowerGenerationEndTime().equals(cloneRecord.getPowerGenerationEndTime()))) {
            if (cloneRecord.getPowerGenerationEndTime() != null)
                sb.append("油机停止发电时间 修改前 ：").append(dateFormat.format(cloneRecord.getPowerGenerationEndTime())).append("，修改后 ：").append(dateFormat.format(recordInfo.getPowerGenerationEndTime())).append("；");
            else
                sb.append("油机停止发电时间 修改前 ：null").append("，修改后 ：").append(dateFormat.format(recordInfo.getPowerCutTime())).append("；");
            existRecord.setPowerGenerationEndTime(recordInfo.getPowerGenerationEndTime());
            isChange = true;
        }
        if (recordInfo.getCurrentA() != null && (cloneRecord.getCurrentA() == null || !recordInfo.getCurrentA().equals(cloneRecord.getCurrentA()))) {
            sb.append("油机电表电流A 修改前 ：").append(cloneRecord.getCurrentA()).append("，修改后 ：").append(recordInfo.getCurrentA()).append("；");
            existRecord.setCurrentA(recordInfo.getCurrentA());
            isChange = true;
        }
        if (recordInfo.getCurrentB() != null && (cloneRecord.getCurrentB() == null || !recordInfo.getCurrentB().equals(cloneRecord.getCurrentB()))) {
            sb.append("油机电表电流B 修改前 ：").append(cloneRecord.getCurrentB()).append("，修改后 ：").append(recordInfo.getCurrentB()).append("；");
            existRecord.setCurrentB(recordInfo.getCurrentB());
            isChange = true;
        }
        if (recordInfo.getCurrentC() != null && (cloneRecord.getCurrentC() == null || !recordInfo.getCurrentC().equals(cloneRecord.getCurrentC()))) {
            sb.append("油机电表电流C 修改前 ：").append(cloneRecord.getCurrentC()).append("，修改后 ：").append(recordInfo.getCurrentC()).append("；");
            existRecord.setCurrentC(recordInfo.getCurrentC());
            isChange = true;
        }
        if (recordInfo.getPowerGenerationValue() != null && (cloneRecord.getPowerGenerationValue() == null || !recordInfo.getPowerGenerationValue().equals(cloneRecord.getPowerGenerationValue()))) {
            sb.append("发电量 修改前 ：").append(cloneRecord.getPowerGenerationValue()).append("，修改后 ：").append(recordInfo.getPowerGenerationValue()).append("；");
            existRecord.setPowerGenerationValue(recordInfo.getPowerGenerationValue());
            isChange = true;
            if (cloneRecord.getGeneTypeId() != null && cloneRecord.getGeneRatedPower() != null) {
                //获取油机功率信息
                QueryWrapper<GeneTypePowerInfo> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("geneTypeId", cloneRecord.getGeneTypeId());
                queryWrapper.eq("ratedPower", cloneRecord.getGeneRatedPower());
                GeneTypePowerInfo typePowerInfo = geneTypePowerInfoMapper.selectOne(queryWrapper);

                Double fuelConsumption = 0d;
                if (typePowerInfo != null)
                    fuelConsumption = typePowerInfo.getFuelConsumption();
                existRecord.setApprovedFuelConsumption(NumberUtil.doubleAccuracy((fuelConsumption * recordInfo.getPowerGenerationValue()), 2));
            }
        }
        Double powerGenerationDuration = getTimeDiff(existRecord.getPowerGenerationStartTime(), existRecord.getPowerGenerationEndTime());
        existRecord.setPowerGenerationDuration(powerGenerationDuration);

        Double powerGenerationInterval = getTimeDiff(existRecord.getPowerCutTime(), existRecord.getPowerGenerationStartTime());
        existRecord.setPowerGenerationInterval(powerGenerationInterval);

        Double powerRecoverInterval = getTimeDiff(existRecord.getPowerRecoverTime(), existRecord.getPowerGenerationEndTime());
        existRecord.setPowerRecoverInterval(powerRecoverInterval);

        Double powerCutInterval = getTimeDiff(existRecord.getPowerCutTime(), existRecord.getPowerRecoverTime());
        existRecord.setPowerCutInterval(powerCutInterval);

        if (isChange) {
            modifyRecord.setModifyContent(sb.toString());
            modifyRecord.setModifyTime(new Date());
            modifyRecord.setOperatorId(TokenUserUtil.getLoginUserId());
            modifyRecord.setOperatorName(TokenUserUtil.getLoginUserName());
            modifyRecord.setRecordId(recordInfo.getId());
            geneModifyRecordMapper.insert(modifyRecord);
            existRecord.setIsArtificial(true);
            genePowerGenerationStatisticsMapper.updateById(existRecord);
        }
        return existRecord;
    }

    /**
     * 根据局站ID和时间获取油机发电统计记录
     *
     * @param departmentIds 分局ID，用“,”隔开，如果全选则包含“allStation”
     * @param stationIds    局站ID，用“,”隔开，如果全选则包含“allStation”
     * @param queryTime     查询时间
     */
    @Override
    public List<GenePowerGenerationStatisticsDTO> getGenePowerRecordByStationId(String departmentIds, String stationIds, Date queryTime) {

        // 计算时间范围
        Date[] dateRange = calculateDateRange(queryTime);
        Date startTime = dateRange[0];
        Date endTime = dateRange[1];

        if (stationIds == null || stationIds.isEmpty() || stationIds.contains("allStation")) {
            if (departmentIds.contains("allStation")) {
                List<GenePowerGenerationStatisticsDTO> res = genePowerGenerationStatisticsMapper.getAllRecord(startTime, endTime);
                return res;
            } else {
                String filterStationIds = getFilteredStationIds(departmentIds);
                return genePowerGenerationStatisticsMapper.getGenePowerRecordByStationId(filterStationIds, startTime, endTime);
            }
        }
        return genePowerGenerationStatisticsMapper.getGenePowerRecordByStationId(stationIds, startTime, endTime);
    }

    private String getFilteredStationIds(String departmentIds) {
        List<String> departmentList = Arrays.stream(departmentIds.split(",")).toList();
        List<ResourceStructure> stationList = resourceStructureService.findResourceStructureByUserId(TokenUserUtil.getLoginUserId()).stream()
                .filter(o -> o.getStructureTypeId().equals(104))
                .toList();

        return stationList.stream()
                .filter(station -> departmentList.contains(station.getParentResourceStructureId().toString()))
                .map(station -> station.getOriginId().toString())
                .collect(Collectors.joining(","));
    }

    /**
     * 获取数据的修改记录
     *
     * @param recordId 发电记录ID
     */
    @Override
    public List<GeneModifyRecord> getModifyRecordList(Integer recordId) {
        QueryWrapper<GeneModifyRecord> wrapper = new QueryWrapper<>();
        wrapper.eq("recordId", recordId);
        wrapper.orderByDesc("modifyTime");
        return geneModifyRecordMapper.selectList(wrapper);
    }

    /**
     * 核定油耗
     *
     * @param recordInfo 发电记录信息
     */
    @Override
    public GenePowerGenerationStatistics approvedFuelConsumption(GenePowerGenerationStatistics recordInfo) {
        if (recordInfo.getGeneTypeId() == null || recordInfo.getGeneRatedPower() == null || recordInfo.getGeneNo() == null)
            return null;
        GenePowerGenerationStatistics existRecord = genePowerGenerationStatisticsMapper.selectById(recordInfo.getId());
        existRecord.setGeneNo(recordInfo.getGeneNo());
        existRecord.setGeneTypeId(recordInfo.getGeneTypeId());
        existRecord.setGeneRatedPower(recordInfo.getGeneRatedPower());
        Double timeDiff = getTimeDiff(existRecord.getPowerCutTime(), new Date());
        if (timeDiff != null && timeDiff > timeDiffStandard) {
            existRecord.setIsValid(false);
        }
        existRecord.setApproveTime(new Date());
        //获取发电量
        Double generationValue = existRecord.getPowerGenerationValue();
        if (generationValue == null) {
            genePowerGenerationStatisticsMapper.updateById(existRecord);
            return existRecord;
        }

        //获取油机功率信息
        QueryWrapper<GeneTypePowerInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("geneTypeId", recordInfo.getGeneTypeId());
        queryWrapper.eq("ratedPower", recordInfo.getGeneRatedPower());
        GeneTypePowerInfo typePowerInfo = geneTypePowerInfoMapper.selectOne(queryWrapper);

        Double fuelConsumption = 0d;
        existRecord.setApprovalFuelConsumption(0D);
        if (typePowerInfo != null){
            fuelConsumption = typePowerInfo.getFuelConsumption();

            //总油耗
            existRecord.setApprovedFuelConsumption(NumberUtil.doubleAccuracy((fuelConsumption * generationValue), 2));
            double timeoutDuration = existRecord.getPowerRecoverInterval() - 2;
            existRecord.setOvertimeFuelConsumption(0D);
            if(existRecord.getGeneActualPower() != null && timeoutDuration > 0){
                //超时油耗：实际功率(KW)×(市电恢复间隔-2)(H)×油机额定油耗（L/KWH）
                Double overTimeFuel = timeoutDuration * typePowerInfo.getFuelConsumption();
                existRecord.setOvertimeFuelConsumption(NumberUtil.doubleAccuracy(overTimeFuel, 2));
            }
            //核定油耗(L):公式：总油耗-超时油耗
            double approvalFuel = existRecord.getApprovedFuelConsumption() - existRecord.getOvertimeFuelConsumption();
            existRecord.setApprovalFuelConsumption(NumberUtil.doubleAccuracy(approvalFuel, 2));
        }else {
            existRecord.setApprovedFuelConsumption(-1D);
            log.error("typePowerInfo is null can not calculate!");
        }

        genePowerGenerationStatisticsMapper.updateById(existRecord);
        return existRecord;
    }

    @Override
    public List<ResourceStructure> getCountyList() {
        return resourceStructureService.findResourceStructureByUserId(TokenUserUtil.getLoginUserId())
                .stream()
                .filter(o ->
                        o.getStructureTypeId().equals(103)
                                || o.getStructureTypeId().equals(3)
                                || o.getParentResourceStructureId().equals(0))
                .sorted(Comparator.comparing(ResourceStructure::getResourceStructureId))
                .toList();
    }

    @Override
    public Object getTotalPowerCutInterval(Date startTime, Date endTime, Integer objectId) {
        // 获取基础资源信息
        ResourceStructure objectRes = resourceStructureManager.getResourceStructureById(objectId);
        if (objectRes == null) {
            return null;
        }

        // 构建基础数据映射
        Map<String, List<GenePowerGenerationStatisticsDTO>> dataMap = buildDataMap(objectRes, startTime, endTime);

        // 生成图表数据
        Map<String, List<GeneDataAnalysisDTO>> result = new HashMap<>();
        result.put("barChart", Collections.singletonList(powerCutBarChart(dataMap)));
        result.put("trendChart", powerCutTrendChart(dataMap, startTime, endTime));

        return result;
    }

    @Override
    public Object getAvgPowerCutInterval(Date startTime, Date endTime, Integer objectId) {
        // 获取基础资源信息
        ResourceStructure objectRes = resourceStructureManager.getResourceStructureById(objectId);
        if (objectRes == null) {
            return null;
        }

        // 构建基础数据映射
        Map<String, List<GenePowerGenerationStatisticsDTO>> dataMap = buildDataMap(objectRes,  startTime, endTime);

        // 生成图表数据
        Map<String, List<GeneDataAnalysisDTO>> result = new HashMap<>();
        result.put("barChart", Collections.singletonList(avgPowerCutBarChart(dataMap)));
        result.put("trendChart", avgPowerCutTrendChart(dataMap, startTime, endTime));

        return result;
    }

    @Override
    public Object getPowerGeneInterval(Date startTime, Date endTime, Integer objectId) {
        // 获取基础资源信息
        ResourceStructure objectRes = resourceStructureManager.getResourceStructureById(objectId);
        if (objectRes == null) {
            return null;
        }
        // 构建基础数据映射
        Map<String, List<GenePowerGenerationStatisticsDTO>> dataMap = buildDataMap(objectRes, startTime, endTime);

        // 生成图表数据
        Map<String, List<GeneDataAnalysisDTO>> result = new HashMap<>();
        result.put("barChart", Collections.singletonList(powerGeneIntervalChart(dataMap)));
        result.put("trendChart", powerGeneIntervalTrendChart(dataMap, startTime, endTime));

        return result;
    }

    @Override
    public Object getPowerGeneDuration(Date startTime, Date endTime, Integer objectId) {
        // 获取基础资源信息
        ResourceStructure objectRes = resourceStructureManager.getResourceStructureById(objectId);
        if (objectRes == null) {
            return null;
        }
        // 构建基础数据映射
        Map<String, List<GenePowerGenerationStatisticsDTO>> dataMap = buildDataMap(objectRes, startTime, endTime);

        // 生成图表数据
        Map<String, List<GeneDataAnalysisDTO>> result = new HashMap<>();
        result.put("barChart", Collections.singletonList(powerGeneDurationChart(dataMap)));
        result.put("trendChart", powerGeneDurationTrendChart(dataMap, startTime, endTime));

        return result;
    }


    @Override
    public Object getPowerRecoverInterval(Date startTime, Date endTime, Integer objectId) {
        // 获取基础资源信息
        ResourceStructure objectRes = resourceStructureManager.getResourceStructureById(objectId);
        if (objectRes == null) {
            return null;
        }

        // 构建基础数据映射
        Map<String, List<GenePowerGenerationStatisticsDTO>> dataMap = buildDataMap(objectRes,  startTime, endTime);

        // 生成图表数据
        Map<String, List<GeneDataAnalysisDTO>> result = new HashMap<>();
        result.put("barChart", Collections.singletonList(powerRecoverBarChart(dataMap)));
        result.put("trendChart", powerRecoverTrendChart(dataMap, startTime, endTime));

        return result;
    }
    @Override
    public List<Account> getAccountList() {
        return genePowerGenerationStatisticsMapper.getAllAccount();
    }

    @Override
    public Object updateAccountMap(String userIds, Integer operateType) {
        return genePowerGenerationStatisticsMapper.upDateUserOperateMap(userIds,operateType);
    }

    /**
     * 构建数据映射关系
     */
    private Map<String, List<GenePowerGenerationStatisticsDTO>> buildDataMap(
            ResourceStructure objectRes,  Date startTime, Date endTime) {

        Map<String, List<GenePowerGenerationStatisticsDTO>> dataMap;
        String keyField;
        List<Integer> sonStationIds = new ArrayList<>();

        // 判断是全市还是区县级别
        if (objectRes.getParentResourceStructureId().equals(0)) {
            sonStationIds = resourceStructureManager.getResourceStructureLstByParentId(objectRes.getResourceStructureId())
                    .stream()
                    .filter(o -> o.getStructureTypeId() < 104)
                    .map(ResourceStructure::getResourceStructureId)
                    .toList();
            // 全市级别 - 按区县分组
            List<ResourceStructure> countyRes = resourceStructureManager.getResourceStructureByIds(sonStationIds);
            dataMap = countyRes.stream()
                    .filter(resourceStructure -> resourceStructure.getResourceStructureName() != null)
                    .collect(Collectors.toMap(
                            ResourceStructure::getResourceStructureName,
                            station -> new ArrayList<>(),
                            (existing, replacement) -> existing));
            keyField = "departmentName";
        } else {
            // 区县级别 - 按电站分组
            sonStationIds =  resourceStructureManager.getResourceStructureLstByParentId(objectRes.getResourceStructureId())
                    .stream()
                    .filter(o -> o.getStructureTypeId() == 104)
                    .map(ResourceStructure::getOriginId)
                    .toList();
            List<Station> stationInfo = stationManager.findByStationIds(sonStationIds);
            dataMap = stationInfo.stream()
                    .filter(station -> station.getProjectName() != null)
                    .collect(Collectors.toMap(
                            Station::getProjectName,
                            station -> new ArrayList<>(),
                            (existing, replacement) -> existing));
            keyField = "groupName";
        }

        // 获取数据并分组
        List<GenePowerGenerationStatisticsDTO> totalRecords =
                genePowerGenerationStatisticsMapper.getAllRecord(startTime, endTime);

        // 根据keyField将数据分配到对应的分组中
        for (GenePowerGenerationStatisticsDTO record : totalRecords) {
            String key = getKeyByField(record, keyField);
            if (dataMap.containsKey(key)) {
                dataMap.get(key).add(record);
            }
        }

        return dataMap;
    }

    /**
     * 根据字段名获取对应的值
     */
    private String getKeyByField(GenePowerGenerationStatisticsDTO record, String fieldName) {
        return switch (fieldName) {
            case "departmentName" -> record.getDepartmentName();
            case "groupName" -> record.getGroupName();
            default -> null;
        };
    }
    /**
     * 通用柱状图数据生成方法
     * @param dataMap 数据映射
     * @param valueExtractor 数值提取函数
     * @return 柱状图数据
     */
    private GeneDataAnalysisDTO generateBarChart(
            Map<String, List<GenePowerGenerationStatisticsDTO>> dataMap,
            ToDoubleFunction<GenePowerGenerationStatisticsDTO> valueExtractor) {

        List<ShowDataDTO> showDataList = dataMap.entrySet().parallelStream()
                .map(entry -> {
                    double avgValue = entry.getValue().stream()
                            .filter(Objects::nonNull)
                            .mapToDouble(dto -> {
                                try {
                                    double value = valueExtractor.applyAsDouble(dto);
                                    return Double.isNaN(value) ? 0.0 : value;  // 处理NaN值
                                } catch (Exception e) {
                                    return 0.0;  // 异常时返回0
                                }
                            })
                            .sum();

                    // 使用NumberUtil格式化数值，保留2位小数
                    double formattedValue = NumberUtil.doubleAccuracy(avgValue, 2);

                    return new ShowDataDTO(entry.getKey(), formattedValue);
                })
                .sorted(Comparator.comparing(ShowDataDTO::getValue))
                .collect(Collectors.toList());

        GeneDataAnalysisDTO barChart = new GeneDataAnalysisDTO();
        barChart.setShowDataList(showDataList);
        return barChart;
    }

    /**
     * 通用柱状图数据生成方法(平均值)
     * @param dataMap 数据映射
     * @param valueExtractor 数值提取函数
     * @return 柱状图数据
     */
    private GeneDataAnalysisDTO generateAvgDataBarChart(
            Map<String, List<GenePowerGenerationStatisticsDTO>> dataMap,
            ToDoubleFunction<GenePowerGenerationStatisticsDTO> valueExtractor) {

        List<ShowDataDTO> showDataList = dataMap.entrySet().parallelStream()
                .map(entry -> {
                    double avgValue = entry.getValue().stream()
                            .filter(Objects::nonNull)
                            .mapToDouble(dto -> {
                                try {
                                    double value = valueExtractor.applyAsDouble(dto);
                                    return Double.isNaN(value) ? 0.0 : value;  // 处理NaN值
                                } catch (Exception e) {
                                    return 0.0;  // 异常时返回0
                                }
                            })
                            .average()  // 改为求平均值
                            .orElse(0D);  // 如果没有数据则返回0

                    // 使用NumberUtil格式化数值，保留2位小数
                    double formattedValue = NumberUtil.doubleAccuracy(avgValue, 2);

                    return new ShowDataDTO(entry.getKey(), formattedValue);
                })
                .sorted(Comparator.comparing(ShowDataDTO::getValue))
                .collect(Collectors.toList());

        GeneDataAnalysisDTO barChart = new GeneDataAnalysisDTO();
        barChart.setShowDataList(showDataList);
        return barChart;
    }

    /**
     * 平均停电时长柱状图数据
     */
    private GeneDataAnalysisDTO avgPowerCutBarChart(Map<String, List<GenePowerGenerationStatisticsDTO>> dataMap) {
        return generateAvgDataBarChart(dataMap, GenePowerGenerationStatisticsDTO::getPowerCutInterval);
    }

    /**
     * 平均停电时长趋势图数据
     */
    private List<GeneDataAnalysisDTO> avgPowerCutTrendChart(
            Map<String, List<GenePowerGenerationStatisticsDTO>> dataMap, Date startTime, Date endTime) {
        return avgGenerateTrendChart(dataMap, startTime, endTime, GenePowerGenerationStatisticsDTO::getPowerCutInterval);
    }

    /**
     * 发电时间间隔柱状图数据
     */
    private GeneDataAnalysisDTO powerGeneIntervalChart(Map<String, List<GenePowerGenerationStatisticsDTO>> dataMap) {
        return generateBarChart(dataMap, GenePowerGenerationStatisticsDTO::getPowerGenerationInterval);
    }

    /**
     * 发电时间间隔柱趋势图数据
     */
    private List<GeneDataAnalysisDTO> powerGeneIntervalTrendChart(
            Map<String, List<GenePowerGenerationStatisticsDTO>> dataMap, Date startTime, Date endTime) {
        return generateTrendChart(dataMap, startTime, endTime, GenePowerGenerationStatisticsDTO::getPowerGenerationInterval);
    }
    /**
     * 发电历时柱状图数据
     */
    private GeneDataAnalysisDTO powerGeneDurationChart(Map<String, List<GenePowerGenerationStatisticsDTO>> dataMap) {
        return generateAvgDataBarChart(dataMap, GenePowerGenerationStatisticsDTO::getPowerGenerationDuration);
    }

    /**
     * 发电历时柱趋势图数据
     */
    private List<GeneDataAnalysisDTO> powerGeneDurationTrendChart(
            Map<String, List<GenePowerGenerationStatisticsDTO>> dataMap, Date startTime, Date endTime) {
        return avgGenerateTrendChart(dataMap, startTime, endTime, GenePowerGenerationStatisticsDTO::getPowerGenerationDuration);
    }


    /**
     * 停电总时长柱状图数据
     */
    private GeneDataAnalysisDTO powerCutBarChart(Map<String, List<GenePowerGenerationStatisticsDTO>> dataMap) {
        return generateBarChart(dataMap, GenePowerGenerationStatisticsDTO::getPowerCutInterval);
    }

    /**
     * 停电总时长趋势图数据
     */
    private List<GeneDataAnalysisDTO> powerCutTrendChart(
            Map<String, List<GenePowerGenerationStatisticsDTO>> dataMap, Date startTime, Date endTime) {
        return generateTrendChart(dataMap, startTime, endTime, GenePowerGenerationStatisticsDTO::getPowerCutInterval);
    }

    /**
     * 市电恢复间隔柱状图数据
     */
    private GeneDataAnalysisDTO powerRecoverBarChart(Map<String, List<GenePowerGenerationStatisticsDTO>> dataMap) {
        return generateBarChart(dataMap, GenePowerGenerationStatisticsDTO::getPowerRecoverInterval);
    }

    /**
     * 市电恢复间隔趋势图数据
     */
    private List<GeneDataAnalysisDTO> powerRecoverTrendChart(
            Map<String, List<GenePowerGenerationStatisticsDTO>> dataMap, Date startTime, Date endTime) {
        return generateTrendChart(dataMap, startTime, endTime, GenePowerGenerationStatisticsDTO::getPowerRecoverInterval);
    }






    /**
     * 通用趋势图数据生成方法（平均值版本）
     * @param dataMap 数据映射
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param valueExtractor 数值提取函数
     * @return 趋势图数据列表
     */
    private List<GeneDataAnalysisDTO> avgGenerateTrendChart(
            Map<String, List<GenePowerGenerationStatisticsDTO>> dataMap,
            Date startTime,
            Date endTime,
            ToDoubleFunction<GenePowerGenerationStatisticsDTO> valueExtractor) {

        // 生成完整的时间轴
        LocalDate startDate = toLocalDate(startTime);
        LocalDate endDate = toLocalDate(endTime);
        List<LocalDate> allDates = startDate.datesUntil(endDate.plusDays(1)).toList();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        return dataMap.entrySet().stream()
                .map(entry -> {
                    String groupName = entry.getKey();

                    // 按日期分组求平均值
                    Map<LocalDate, Double> dateToAvg = entry.getValue().stream()
                            .filter(Objects::nonNull)
                            .filter(dto -> {
                                try {
                                    // 测试valueExtractor是否会抛出异常
                                    valueExtractor.applyAsDouble(dto);
                                    return true;
                                } catch (Exception e) {
                                    return false;
                                }
                            })
                            .collect(Collectors.groupingBy(
                                    dto -> toLocalDate(dto.getPowerCutTime()),
                                    Collectors.averagingDouble(valueExtractor)  // 改为求平均值
                            ));

                    // 构建完整时间序列数据
                    List<ShowDataDTO> showData = allDates.stream()
                            .map(date -> {
                                try {
                                    Double rawValue = dateToAvg.get(date);
                                    Double formattedValue = null;

                                    // 如果值不为null，则使用NumberUtil格式化
                                    if (rawValue != null) {
                                        formattedValue = NumberUtil.doubleAccuracy(rawValue, 2);
                                    }

                                    return new ShowDataDTO(
                                            formatter.format(date),
                                            formattedValue  // 使用格式化后的值
                                    );
                                } catch (Exception e) {
                                    // 如果格式化失败，返回默认值
                                    return new ShowDataDTO(date.toString(), null);
                                }
                            })
                            .filter(Objects::nonNull) // 过滤空值
                            .collect(Collectors.toList());

                    // 检查showData中是否所有value都为null，如果是则不添加
                    boolean allValuesNull = showData.stream()
                            .allMatch(data -> data.getValue() == null);

                    if (allValuesNull) {
                        return null; // 所有值都为null，返回null，后续会被过滤掉
                    }

                    GeneDataAnalysisDTO dto = new GeneDataAnalysisDTO();
                    dto.setGroupName(groupName);
                    dto.setShowDataList(showData);
                    return dto;
                })
                .filter(Objects::nonNull)
                .toList();
    }

    /**
     * 通用趋势图数据生成方法
     * @param dataMap 数据映射
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param valueExtractor 数值提取函数
     * @return 趋势图数据列表
     */
    private List<GeneDataAnalysisDTO> generateTrendChart(
            Map<String, List<GenePowerGenerationStatisticsDTO>> dataMap,
            Date startTime,
            Date endTime,
            ToDoubleFunction<GenePowerGenerationStatisticsDTO> valueExtractor) {

        // 生成完整的时间轴
        LocalDate startDate = toLocalDate(startTime);
        LocalDate endDate = toLocalDate(endTime);
        List<LocalDate> allDates = startDate.datesUntil(endDate.plusDays(1)).toList();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        return dataMap.entrySet().stream()
                .map(entry -> {
                    String groupName = entry.getKey();

                    // 按日期分组求和
                    Map<LocalDate, Double> dateToSum = entry.getValue().stream()
                            .filter(Objects::nonNull)
                            .filter(dto -> {
                                try {
                                    // 测试valueExtractor是否会抛出异常
                                    valueExtractor.applyAsDouble(dto);
                                    return true;
                                } catch (Exception e) {
                                    return false;
                                }
                            })
                            .collect(Collectors.groupingBy(
                                    dto -> toLocalDate(dto.getPowerCutTime()),
                                    Collectors.summingDouble(valueExtractor)
                            ));

                    // 构建完整时间序列数据
                    List<ShowDataDTO> showData = allDates.stream()
                            .map(date -> {
                                try {
                                    Double rawValue = dateToSum.get(date);
                                    Double formattedValue = null;

                                    // 如果值不为null，则使用NumberUtil格式化
                                    if (rawValue != null) {
                                        formattedValue = NumberUtil.doubleAccuracy(rawValue, 2);
                                    }

                                    return new ShowDataDTO(
                                            formatter.format(date),
                                            formattedValue  // 使用格式化后的值
                                    );
                                } catch (Exception e) {
                                    // 如果格式化失败，返回默认值
                                    return new ShowDataDTO(date.toString(), null);
                                }
                            })
                            .filter(Objects::nonNull) // 过滤空值
                            .collect(Collectors.toList());

                    // 检查showData中是否所有value都为null，如果是则不添加
                    boolean allValuesNull = showData.stream()
                            .allMatch(data -> data.getValue() == null);

                    if (allValuesNull) {
                        return null; // 所有值都为null，返回null，后续会被过滤掉
                    }
                    GeneDataAnalysisDTO dto = new GeneDataAnalysisDTO();
                    dto.setGroupName(groupName);
                    dto.setShowDataList(showData);
                    return dto;
                })
                .filter(Objects::nonNull)
                .toList();
    }


    public static LocalDate toLocalDate(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }
    /**
     * 获取两个时间的时间差转换为小时
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    public Double getTimeDiff(Date startTime, Date endTime) {
        if (startTime == null || endTime == null)
            return null;
        long diffInMilli = endTime.getTime() - startTime.getTime();

        // 将毫秒差值转换为小时，并保留两位小数
        return NumberUtil.doubleAccuracy((diffInMilli / (1000.0 * 60 * 60)), 2);
    }
    /**
     * 根据queryTime计算startTime和endTime
     * startTime: queryTime上一个月的25号 00:00:00
     * endTime: queryTime当前月的24号 23:59:59
     */
    public static Date[] calculateDateRange(Date queryTime) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(queryTime);

        // 计算startTime：上一个月的25号 00:00:00
        Calendar startCal = Calendar.getInstance();
        startCal.setTime(queryTime);
        startCal.add(Calendar.MONTH, -1); // 上一个月
        startCal.set(Calendar.DAY_OF_MONTH, 25); // 25号
        startCal.set(Calendar.HOUR_OF_DAY, 0); // 0点
        startCal.set(Calendar.MINUTE, 0); // 0分
        startCal.set(Calendar.SECOND, 0); // 0秒
        startCal.set(Calendar.MILLISECOND, 0); // 0毫秒
        Date startTime = startCal.getTime();

        // 计算endTime：当前月的25号 23:59:59
        Calendar endCal = Calendar.getInstance();
        endCal.setTime(queryTime);
        endCal.set(Calendar.DAY_OF_MONTH, 24); // 25号
        endCal.set(Calendar.HOUR_OF_DAY, 23); // 23点
        endCal.set(Calendar.MINUTE, 59); // 59分
        endCal.set(Calendar.SECOND, 59); // 59秒
        endCal.set(Calendar.MILLISECOND, 999); // 999毫秒
        Date endTime = endCal.getTime();

        return new Date[]{startTime, endTime};
    }
}
