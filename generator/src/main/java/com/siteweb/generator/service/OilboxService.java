package com.siteweb.generator.service;

import com.siteweb.common.response.ResponseResult;
import com.siteweb.generator.dto.OilboxMapGene;
import com.siteweb.generator.dto.OilboxMapGeneParam;
import com.siteweb.generator.entity.GeneOilLevelRecord;
import com.siteweb.generator.entity.GeneOilboxExt;
import com.siteweb.generator.entity.GeneRefuelRecord;
import org.springframework.http.ResponseEntity;

import java.util.Date;
import java.util.List;

public interface OilboxService {

    GeneOilboxExt getExtInfoByEquipmentId(int oilId);

    ResponseEntity<ResponseResult> saveOrUpdate(GeneOilboxExt oilbox);

    boolean delOilboxExt(int oilId);

    List<Integer> getGeneIdsByOilId(int oilId);

    List<OilboxMapGene> getGeneEquipsByOilId(int oilId);

    ResponseEntity<ResponseResult> saveGeneMaps(OilboxMapGeneParam mapInfo);

    List<GeneRefuelRecord> getRefuelRecordsByOilId(int oilId, Date startTime, Date endTime);

    boolean delRefuelRecord(int serialId);

    ResponseEntity<ResponseResult> addRefuelRecord(GeneRefuelRecord refuelRecord);

    ResponseEntity<ResponseResult> updateRefuelRecord(GeneRefuelRecord refuelRecord);
    /** 通过油箱id，获取爷爷节点(向上两级)下所有的油机设备集合 */
    List<OilboxMapGene> getMapGenerators(int oilId);

    List<GeneOilLevelRecord> getOilLevelByEquipmentId(int oilId);
}
