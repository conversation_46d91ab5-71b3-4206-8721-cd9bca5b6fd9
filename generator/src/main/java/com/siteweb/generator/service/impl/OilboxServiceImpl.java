package com.siteweb.generator.service.impl;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.StringUtils;
import com.siteweb.generator.dto.OilboxMapGene;
import com.siteweb.generator.dto.OilboxMapGeneParam;
import com.siteweb.generator.entity.GeneOilLevelRecord;
import com.siteweb.generator.entity.GeneOilboxExt;
import com.siteweb.generator.entity.GeneOilboxGeneratorMap;
import com.siteweb.generator.entity.GeneRefuelRecord;
import com.siteweb.generator.function.CommonUtils;
import com.siteweb.generator.mapper.OilLevelRecordMapper;
import com.siteweb.generator.mapper.OilboxMapper;
import com.siteweb.generator.service.OilboxService;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.siteweb.generator.service.impl.GeneratorServiceImpl.GENE_EQUIP_BASE_TYPES;

@Service
@Transactional
@Slf4j
public class OilboxServiceImpl implements OilboxService {

    @Autowired
    private EquipmentManager equipmentManager;
    @Autowired
    private ResourceStructureManager resourceStructureManager;

    @Autowired
    private OilboxMapper oilboxMapper;
    @Autowired
    private OilLevelRecordMapper oilLevelRecordMapper;

    @Override
    public GeneOilboxExt getExtInfoByEquipmentId(int oilId) {
        return oilboxMapper.selectById(oilId);
    }

    @Override
    public ResponseEntity<ResponseResult> saveOrUpdate(GeneOilboxExt oilbox) {
        if(oilbox == null || oilbox.getOilId() == null || oilbox.getOilId() < 1) {
            return ResponseHelper.failed("-10", "Param is Null or oilId is invalid", HttpStatus.OK);
        }
        if(oilbox.getRatedVolume() == null || oilbox.getRatedVolume() < 0) {
            return ResponseHelper.failed("-10", "RatedVolume is Null or invalid", HttpStatus.OK);
        }
        if(oilbox.getFullOilLevel() == null || oilbox.getFullOilLevel() < 0) {
            return ResponseHelper.failed("-10", "FullOilLevel is Null or invalid", HttpStatus.OK);
        }
        Integer oilId = oilbox.getOilId();
        String curOilLevel = oilbox.getExtend1();
        GeneOilLevelRecord levelRecord = null;
        GeneOilboxExt oilboxDb = oilboxMapper.selectById(oilId);
        if(curOilLevel != null){
            double curOilLevelD = Double.parseDouble(curOilLevel);
            if(curOilLevelD < 0)
                return ResponseHelper.failed("-10", "CurrentOilLevel is Null or invalid", HttpStatus.OK);
            levelRecord = new GeneOilLevelRecord(oilId,curOilLevelD,CommonUtils.getCurDate(),TokenUserUtil.getLoginUserId());
        }

        oilbox.setUpdaterId(TokenUserUtil.getLoginUserId());
        oilbox.setUpdateTime(CommonUtils.getCurDate());
        if(oilboxDb == null) {//不存在，新增
            oilboxMapper.insert(oilbox);
            if(curOilLevel != null)
                oilLevelRecordMapper.insert(levelRecord);
        } else {//更新
            if(!oilbox.getRatedVolume().equals(oilboxDb.getRatedVolume())||
                    !oilbox.getFullOilLevel().equals(oilboxDb.getFullOilLevel())) {
                oilboxMapper.updateById(oilbox);
            }
            if (curOilLevel != null && !oilbox.getExtend1().equals(oilboxDb.getExtend1())){
                oilboxMapper.updateById(oilbox);
                oilLevelRecordMapper.insert(levelRecord);
            }
        }
        return ResponseHelper.successful("SUCCESS");
    }

    @Override
    public boolean delOilboxExt(int oilId) {
        try {
            oilboxMapper.deleteById(oilId);
            return true;
        } catch (Exception e) {
            log.error("deleteOilboxExt oilId=" + oilId + " throw Exception: ", e);
            return false;
        }
    }

    @Override
    public List<Integer> getGeneIdsByOilId(int oilId) {
        return oilboxMapper.getGeneIdsByOilId(oilId);
    }

    @Override
    public List<OilboxMapGene> getGeneEquipsByOilId(int oilId) {
        return oilboxMapper.getGeneEquipsByOilId(oilId);
    }

    @Override
    public ResponseEntity<ResponseResult> saveGeneMaps(OilboxMapGeneParam mapInfo) {
        if(mapInfo == null || mapInfo.getOilId() == null) {
            return ResponseHelper.failed("-10", "Param is Null", HttpStatus.OK);
        }
        if(mapInfo.getGeneIdList() == null) {
            mapInfo.setGeneIdList(new ArrayList<>());
        }
        Integer oilId = mapInfo.getOilId();
        List<Integer> geneIdsDB = oilboxMapper.getGeneIdsByOilId(oilId);
        geneIdsDB = geneIdsDB == null ? new ArrayList<>() : geneIdsDB;
        String idsStrDb = geneIdsDB.stream().sorted().map(String::valueOf).collect(Collectors.joining(","));
        String idsStr = mapInfo.getGeneIdList().stream().sorted().map(String::valueOf).collect(Collectors.joining(","));
        if(!idsStrDb.equals(idsStr)) {
            Date curDate = CommonUtils.getCurDate();
            Integer userId = TokenUserUtil.getLoginUserId();
            List<GeneOilboxGeneratorMap> list =  new ArrayList<>();
            for (Integer geneId : mapInfo.getGeneIdList()) {
                GeneOilboxGeneratorMap map = new GeneOilboxGeneratorMap();
                map.setOilId(oilId);
                map.setGeneId(geneId);
                map.setUpdaterId(userId);
                map.setUpdateTime(curDate);
                list.add(map);
            }
            oilboxMapper.delMapsByOilId(oilId);
            if(list.size() > 0) {
                oilboxMapper.batchInsertMaps(list);
            }
        }
        return ResponseHelper.successful("SUCCESS");
    }

    @Override
    public List<GeneRefuelRecord> getRefuelRecordsByOilId(int oilId, Date startTime, Date endTime) {
        return oilboxMapper.getRefuelRecordsByOilId(oilId, startTime, endTime);
    }

    @Override
    public ResponseEntity<ResponseResult> addRefuelRecord(GeneRefuelRecord refuelRecord) {
        if(refuelRecord == null) {
            return ResponseHelper.failed("-10", "Param is Null", HttpStatus.OK);
        }
        if(refuelRecord.getSerialId() != null) {
            return ResponseHelper.failed("-10", "Id is not null", HttpStatus.OK);
        }
        if(refuelRecord.getOilId() == null || refuelRecord.getRefuelTime() == null ||
            refuelRecord.getRefuelQuantity() == null || refuelRecord.getUnitPrice() == null
                || refuelRecord.getRefuelFee() == null) {
            return ResponseHelper.failed("-10", "Some required fields is Null", HttpStatus.OK);
        }
        if(refuelRecord.getRefuelQuantity() < 0 || refuelRecord.getUnitPrice() < 0 || refuelRecord.getRefuelFee() < 0) {
            return ResponseHelper.failed("-10", "Number cannot be less than 0", HttpStatus.OK);
        }
        if(StringUtils.isBlank(refuelRecord.getOilName())) {
            Equipment oilbox = equipmentManager.getEquipmentById(refuelRecord.getOilId());
            refuelRecord.setOilName(oilbox == null ? null : oilbox.getEquipmentName());
        }
        refuelRecord.setUpdaterId(TokenUserUtil.getLoginUserId());
        refuelRecord.setUpdateTime(CommonUtils.getCurDate());
        oilboxMapper.insertRefuelRecord(refuelRecord);
        return ResponseHelper.successful("SUCCESS");
    }

    @Override
    public ResponseEntity<ResponseResult> updateRefuelRecord(GeneRefuelRecord refuelRecord) {
        if(refuelRecord == null) {
            return ResponseHelper.failed("-10", "Param is Null", HttpStatus.OK);
        }
        if(refuelRecord.getSerialId() == null) {
            return ResponseHelper.failed("-10", "Id is null", HttpStatus.OK);
        }
        if(refuelRecord.getOilId() == null || refuelRecord.getRefuelTime() == null ||
                refuelRecord.getRefuelQuantity() == null || refuelRecord.getUnitPrice() == null
                || refuelRecord.getRefuelFee() == null) {
            return ResponseHelper.failed("-10", "Some required fields is Null", HttpStatus.OK);
        }
        if(refuelRecord.getRefuelQuantity() < 0 || refuelRecord.getUnitPrice() < 0 || refuelRecord.getRefuelFee() < 0) {
            return ResponseHelper.failed("-10", "Number cannot be less than 0", HttpStatus.OK);
        }
        if(StringUtils.isBlank(refuelRecord.getOilName())) {
            Equipment oilbox = equipmentManager.getEquipmentById(refuelRecord.getOilId());
            refuelRecord.setOilName(oilbox == null ? null : oilbox.getEquipmentName());
        }
        refuelRecord.setUpdaterId(TokenUserUtil.getLoginUserId());
        refuelRecord.setUpdateTime(CommonUtils.getCurDate());
        oilboxMapper.updateRefuelRecord(refuelRecord);
        return ResponseHelper.successful("SUCCESS");
    }

    @Override
    public boolean delRefuelRecord(int serialId) {
        try {
            oilboxMapper.delRefuelRecordById(serialId);
            return true;
        } catch (Exception e) {
            log.error("delRefuelRecord serialId=" + serialId + " throw Exception: ", e);
            return false;
        }
    }

    @Override
    public List<OilboxMapGene> getMapGenerators(int oilId) {

        Equipment oilboxEquip = equipmentManager.getEquipmentById(oilId);
        if(oilboxEquip == null) {
            log.warn("oilbox Equip not exists. oilId=" + oilId);
            return new ArrayList<>();
        } else {
            Integer resourceStructureId = oilboxEquip.getResourceStructureId();
            if(resourceStructureId == null) {
                log.warn("oilbox Equip resourceStructureId is null. oilId=" + oilId);
                return new ArrayList<>();
            } else {
                ResourceStructure targetRs = null;//需要查询的最上级的节点
                ResourceStructure rsLevel1 = resourceStructureManager.getResourceStructureById(resourceStructureId);
                if(rsLevel1 == null) {
                    log.warn("oilbox Equip ResourceStructure is null. oilId=" + oilId + "; resourceStructureId=" + resourceStructureId);
                    return new ArrayList<>();
                } else {
                    ResourceStructure rsLevel2 = resourceStructureManager.getResourceStructureById(rsLevel1.getParentResourceStructureId());
                    targetRs = rsLevel2 == null ? rsLevel1 : rsLevel2;
                }
                Integer rsId = targetRs.getResourceStructureId();
                String rootNodeFlag = targetRs.getParentResourceStructureId().equals(0) ? "" : "%.";
                List<OilboxMapGene> result = oilboxMapper.findMapGeneratorsByRsId(rsId, rootNodeFlag + rsId + ".%", GENE_EQUIP_BASE_TYPES[1].stream().findFirst().get());
                return result;
            }
        }
    }

    @Override
    public List<GeneOilLevelRecord> getOilLevelByEquipmentId(int oilId) {
        return oilLevelRecordMapper.getRecordByOilId(oilId);
    }
}
