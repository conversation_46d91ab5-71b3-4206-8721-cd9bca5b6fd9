package com.siteweb.generator.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.admin.service.SceneService;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.generator.dto.GeneFuelPriceDTO;
import com.siteweb.generator.dto.GenePowerFeeDTO;
import com.siteweb.generator.dto.GenePowerTimeDTO;
import com.siteweb.generator.dto.GeneStartStopNumDTO;
import com.siteweb.generator.entity.GeneGeneratorElecUnitPrice;
import com.siteweb.generator.entity.GeneGeneratorExt;
import com.siteweb.generator.entity.GeneOilboxExt;
import com.siteweb.generator.entity.GenePowerGenerationRecord;
import com.siteweb.generator.mapper.GeneratorConfigApiMapper;
import com.siteweb.generator.mapper.OilboxMapper;
import com.siteweb.generator.service.GeneratorConfigService;
import com.siteweb.monitoring.dto.EquipmentActiveSignal;
import com.siteweb.monitoring.dto.SimpleActiveSignal;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.ActiveSignalManager;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.monitoring.vo.ActiveSignalRequestByBaseTypeId;
import com.siteweb.prealarm.entity.PreAlarmPoint;
import lombok.extern.slf4j.Slf4j;
import org.javatuples.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GeneratorConfigServiceImpl implements GeneratorConfigService {
    @Autowired
    private SceneService sceneService;
    @Autowired
    private ResourceStructureService resourceStructureService;
    @Autowired
    private ActiveSignalManager activeSignalManager;
    @Autowired
    private GeneratorConfigApiMapper generatorConfigApiMapper;
    @Autowired
    private LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    private OilboxMapper oilboxMapper;
    private final Long FUELLEVELSIGNALID = 310227001L;//;301895011L
    /**
     * 获取省市大楼的油箱可发电时长
     * @param userId    用户ID
     * @param resourceStructureId 节点ID
     * @param queryType 查询类型
     */
    @Override
    public Object GetAllPowerGenerationTime(Integer userId, Integer resourceStructureId, String queryType) {

        //园区->大楼->房间->设备
        //省->地市->基站->设备

        List<GenePowerTimeDTO> result = new ArrayList<>();
        //得到用户权限下所有节点
        List<ResourceStructure> allRoleResourceStructure = resourceStructureService.findResourceStructureByUserId(userId);
        //然后根据省市地区查找对应的油箱
        List<ResourceStructure> parentList = new ArrayList<>();
        switch (queryType) {
            case "province":
                //权限下所有市的节点信息
                parentList = allRoleResourceStructure.stream()
                        .filter(rs -> rs.getStructureTypeId() == 102 || rs.getStructureTypeId() == 2).toList();
                break;
            case "city":
                //权限下所有区县的节点信息
                parentList = allRoleResourceStructure.stream()
                        .filter(rs -> rs.getLevelOfPath().contains(String.valueOf(resourceStructureId)) && (rs.getStructureTypeId() == 103 || rs.getStructureTypeId() == 3)).toList();
                break;
            case "county":
                //权限下所有基站的节点信息
                parentList = allRoleResourceStructure.stream()
                        .filter(rs -> rs.getLevelOfPath().contains(String.valueOf(resourceStructureId)) && (rs.getStructureTypeId() == 104 || rs.getStructureTypeId() == 4)).toList();
                break;
            case "building":
                //权限下所有机房的节点信息
                parentList = allRoleResourceStructure.stream()
                        .filter(rs -> rs.getLevelOfPath().contains(String.valueOf(resourceStructureId)) && (rs.getStructureTypeId() == 105 || rs.getStructureTypeId() == 5)).toList();
                break;
        }
        try {
            if (!queryType.equals("building")){
                for (ResourceStructure oneObject : parentList) {
                    double totalPowerTime = 0D;
                    //节点下的所有油箱;
                    List<ResourceStructure> childObject = allRoleResourceStructure.stream()
                            .filter(rs -> rs.getLevelOfPath().contains(String.valueOf(oneObject.getResourceStructureId())) && (rs.getStructureTypeId() == 105 || rs.getStructureTypeId() == 5)).toList();
                    String childObjects = childObject.stream()
                            .map(ResourceStructure::getResourceStructureId)
                            .map(String::valueOf)
                            .collect(Collectors.joining(","));
                    if (childObjects.isEmpty()) {
                        continue;
                    }
                    //获取所有油箱设备
                    List<GeneOilboxExt> allOil = generatorConfigApiMapper.getAllOilBoxByResourceStructureId(childObjects);
                    String oilIds = allOil.stream()
                            .map(GeneOilboxExt::getOilId)
                            .map(String::valueOf)
                            .collect(Collectors.joining(","));
                    if(oilIds.isEmpty())
                        continue;
                    //获取油箱下所有油机个数
                    List<GeneOilboxExt> oilGeneList = generatorConfigApiMapper.getAllOilUnderGeneratorByOilId(oilIds);
                    Map<Integer,String> oilGeneNumMap = oilGeneList.stream()
                            .collect(Collectors.toMap(
                                    GeneOilboxExt::getOilId,
                                    GeneOilboxExt::getExtend1));
                    //如果是全地市的可发电时长，可简化为所有油箱全功耗发电时长*油箱下属油机设备个数
                    for (GeneOilboxExt oneOil : allOil) {
                        double oilPowerTime = GetOilBoxGenerationTime(oneOil.getOilId());
                        int geneNum = Integer.parseInt(oilGeneNumMap.get(oneOil.getOilId()) == null ? "0" : oilGeneNumMap.get(oneOil.getOilId()));
                        totalPowerTime += (oilPowerTime * geneNum);
                    }
                    GenePowerTimeDTO temp = new GenePowerTimeDTO();
                    temp.setTabName(oneObject.getResourceStructureName());
                    temp.setValue(NumberUtil.doubleAccuracy(totalPowerTime,2));
                    result.add(temp);
                }
            }
            else {
                String childObjects = parentList.stream()
                        .map(ResourceStructure::getResourceStructureId)
                        .map(String::valueOf)
                        .collect(Collectors.joining(","));
                if(childObjects.isEmpty())
                    return result;
                //获取所有油机设备
                List<GeneGeneratorExt> allGenerator = generatorConfigApiMapper.getAllGeneratorByResourceStructureId(childObjects);
                for (GeneGeneratorExt oneGene : allGenerator ) {
                    double totalPowerTime = 0D;
                    //获取该油机的父油箱
                    List<GeneOilboxExt> geneOilBox = generatorConfigApiMapper.getGeneOilBoxById(oneGene.getGeneId());
                    for (GeneOilboxExt oneBox: geneOilBox ) {
                        //获取单个油箱的可发电时长
                        double onePowerTime = GetOilBoxGenerationTime(oneBox.getOilId());
                        totalPowerTime += onePowerTime;
                    }
                    GenePowerTimeDTO temp = new GenePowerTimeDTO();
                    temp.setTabName(oneGene.getExtend1());
                    temp.setValue(NumberUtil.doubleAccuracy(totalPowerTime,2));
                    result.add(temp);
                }
            }
        }catch (Exception ex){
            log.error("GeneratorConfigServiceImpl-GetAllPowerGenerationTime error" + ex.getMessage());
        }
        return result;
    }
    /**
     * 油机保养配置信息
     */
    @Override
    public Map<Integer, Pair<Integer,Integer> > GetGeneratorMaintenanceInfo(List<Integer> geneIds){
        Map<Integer, Pair<Integer,Integer>> res = new HashMap<>();
        String geneIdStr = geneIds.stream()
                .map(Object::toString)  // 将每个元素转换为字符串
                .collect(Collectors.joining(", "));
        if(geneIdStr.isEmpty())
            return res;
        List<PreAlarmPoint> alarmPointList = generatorConfigApiMapper.GetGeneratorMaintenanceInfoByIds(geneIdStr);
        if(alarmPointList.isEmpty())
            return res;
        for (PreAlarmPoint onePoint: alarmPointList ) {
            if(onePoint.getExpression() == null)
                continue;
            String expression = onePoint.getExpression();
            String[] expressionParams = expression.split("\\|");
            res.put(onePoint.getObjectId(),new Pair<>(Integer.parseInt(expressionParams[0]),Integer.parseInt(expressionParams[1])));
        }
        return res;
    }
    /**
     * 获取油箱可发电时长
     * @param equipmentId 油箱设备Id
     */
    @Override
    public Double GetOilBoxGenerationTime(Integer equipmentId) {
        double generationTime = 0D;
        double liveOilLevel = 0D;
        try{
            //通过油箱Id找到该油箱下属的油机设备，油箱油位信号310227001
            //获取实时油位信息
            List<SimpleActiveSignal> signalItemList = activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId(equipmentId, List.of(FUELLEVELSIGNALID));
            if(signalItemList.isEmpty()){
                //TODO 如果实时信号的油位信息查不到，去油箱信息表里查询extend1字段
                QueryWrapper<GeneOilboxExt> wrapper = new QueryWrapper<>();
                wrapper.eq("OilId",equipmentId);
                GeneOilboxExt oilInfo = oilboxMapper.selectOne(wrapper);
                if(oilInfo == null || oilInfo.getExtend1() == null)
                    return generationTime;
                liveOilLevel = Double.parseDouble(oilInfo.getExtend1());
            }else {
                liveOilLevel = Double.parseDouble(signalItemList.get(0).getOriginalValue() == null ? "0" :signalItemList.get(0).getOriginalValue());
            }

            //找到油箱油位和容量的关系
            GeneOilboxExt oilBoxExt = generatorConfigApiMapper.getOilBoxInfo(equipmentId);
            if(oilBoxExt == null)
                return generationTime;
            //油箱容积
            double ratedVolume = oilBoxExt.getRatedVolume() == null ? 0.0 : oilBoxExt.getRatedVolume();
            //满油液位
            double fullOilLevel = oilBoxExt.getFullOilLevel() == null ? 0.0 : oilBoxExt.getFullOilLevel();
            if(fullOilLevel == 0)
                return generationTime;
            //剩余油量
            double lastOilAmount = ratedVolume * (liveOilLevel / fullOilLevel);
            //找到该油箱所供给的油机相关信息
            List<GeneGeneratorExt> generatorsInfo =  generatorConfigApiMapper.getOilBoxUnderGeneratorInfo(equipmentId);
            if(!generatorsInfo.isEmpty()){
                //得到所有下属油机的单位耗油量
                double totalRatedFuelConsumption = generatorsInfo.stream()
                        .mapToDouble(generator -> generator.getUnitFuelConsumption() == null ? 0.0 : generator.getUnitFuelConsumption())
                        .sum();
                //用剩余油量除以油机的单位耗油量可以得出该发电机可发电时长
                generationTime = lastOilAmount / totalRatedFuelConsumption;
            }
        }catch (Exception e){
            log.error("GeneratorConfigServiceImpl.getOilBoxGenerationTime error:" + e.getMessage());
            return 0D;
        }

        return NumberUtil.doubleAccuracy(generationTime,2);
    }

    /**
     * 获取当前节点下所有油箱当前油量
     * @param userId 用户ID
     * @param resourceStructureId 节点ID
     */
    @Override
    public Double GetOliBoxFuelQuantity(Integer userId, Integer resourceStructureId) {
        //通过传入的用户ID得到该用户下属的层级节点
        List<ResourceStructure> allResourceStructure = resourceStructureService.findResourceStructureByUserId(userId);
        //根据resourceStructureId筛选出此站点下的所有机房
        List<ResourceStructure> filteredList = new ArrayList<>();
        filteredList = allResourceStructure;
        if(resourceStructureId != null){
            filteredList = allResourceStructure.stream()
                    .filter(rs -> rs.getLevelOfPath().contains( String.valueOf(resourceStructureId) ) &&
                            (rs.getStructureTypeId() == 105 || rs.getStructureTypeId() == 5)).toList();

        }
        String allObjectsId = filteredList.stream()
                .map(ResourceStructure::getResourceStructureId)
                .map(String::valueOf)
                .collect(Collectors.joining(","));

        return this.GetObjectsOilBoxesFuelLevel(allObjectsId);

    }

    /**
     * 获取全年权限节点下所有油机的发电费用以及油价费用
     * @param userId 用户ID
     * @param resourceStructureId 节点ID
     */
    @Override
    public List<GenePowerFeeDTO> GetGeneratorElectAndFuelPrice(Integer userId, Integer resourceStructureId) {
        List<GenePowerFeeDTO> result = new ArrayList<>();
        try{
            List<GeneGeneratorExt> generators = GetAllGeneratorsByUserIdOrObjectId(userId,resourceStructureId);
            String geneIds = generators.stream()
                    .map(GeneGeneratorExt::getGeneId)
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));

            LocalDate today = LocalDate.now();
            LocalDate firstDayOfYear = today.with(TemporalAdjusters.firstDayOfYear());
            LocalDate lastDayOfMonth = LocalDate.of(firstDayOfYear.getYear(), firstDayOfYear.getMonth(), 1)
                    .with(TemporalAdjusters.lastDayOfMonth());

            // 创建一个 Calendar 对象
            Calendar calendar = Calendar.getInstance();
            // 获取当前年份
            int currentYear = calendar.get(Calendar.YEAR);
            //按月查询
            for (int i = 1; i <= 12; i++) {
                double totalFuelFee = 0D;
                double totalElecFee = 0D;
                // 将 Calendar 对象设置为当前年份和当前月份的一号零时零分零秒
                calendar.set(currentYear, i - 1, 1, 0, 0, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                // 获取开始时间的 Date 对象
                Date startOfMonth = calendar.getTime();
                calendar.set(currentYear, i - 1, calendar.getActualMaximum(Calendar.DAY_OF_MONTH), 23, 59, 59);
                calendar.set(Calendar.MILLISECOND, 999);
                Date endOfMonth = calendar.getTime();
                if(geneIds.isEmpty()){
                    GenePowerFeeDTO temp = new GenePowerFeeDTO();
                    temp.setTime(String.valueOf(firstDayOfYear));
                    temp.setFuelFee(NumberUtil.doubleAccuracy(totalFuelFee,2));
                    temp.setPowerFee(NumberUtil.doubleAccuracy(totalElecFee,2));
                    result.add(temp);
                    continue;
                }

                //怎么计算油机的发电时长？根据发电时长得到本月总的油量消耗，再根据发电时长获取本月的发电量，然后可以得到油机的这两个费用
                List<GenePowerGenerationRecord> genePowerTime = generatorConfigApiMapper.getGenePowerTime(firstDayOfYear + " 00:00:00",lastDayOfMonth + " 23:59:59",geneIds);
                Map<Integer, GenePowerGenerationRecord> geneIdToPowerTimeMap = genePowerTime.stream()
                        .collect(Collectors.toMap(
                                GenePowerGenerationRecord::getEquipmentId,
                                GenePowerGenerationRecord -> GenePowerGenerationRecord,
                                (existingValue, newValue) -> existingValue ));
                //TODO 如果该设备又存在没有EndTime的记录，需要获取到其开始时间，然后用现在时间算出其累计运行时间
                List<GenePowerGenerationRecord> noEndTimeGenePowerRecord = generatorConfigApiMapper.getNoEndTimeGenePowerRecord(geneIds);
                Map<Integer, GenePowerGenerationRecord> geneIdToPowerNoEndTimeMap = noEndTimeGenePowerRecord.stream()
                        .collect(Collectors.toMap(
                                GenePowerGenerationRecord::getEquipmentId,
                                GenePowerGenerationRecord -> GenePowerGenerationRecord,
                                (existingValue, newValue) -> existingValue ));
                //得到所有油机的发电时长，然后根据设备Id去获取油机的发电单价，以及消耗的油量
                List<GeneGeneratorElecUnitPrice> genePowerFee = generatorConfigApiMapper.getGenePowerFee(firstDayOfYear.getYear(),firstDayOfYear.getMonth().getValue(),geneIds);
                //设备ID和发电费用的map
                Map<Integer, GeneGeneratorElecUnitPrice> geneIdToFeeMap = genePowerFee.stream()
                        .collect(Collectors.toMap(
                                GeneGeneratorElecUnitPrice::getEquipmentId,
                                GeneGeneratorElecUnitPrice->GeneGeneratorElecUnitPrice,
                                (existingValue, newValue) -> existingValue ));
                //得到所有发电机机的单位油耗以及默认发电单价
                List<GeneGeneratorExt> geneFuelConsumptionAndDefaultFee = generatorConfigApiMapper.getGeneFuelConsumptionAndDefaultFee(geneIds);
                Map<Integer, GeneGeneratorExt> geneIdToFuelFeeAndConsumptionMap = geneFuelConsumptionAndDefaultFee.stream()
                        .collect(Collectors.toMap(
                                GeneGeneratorExt::getGeneId,
                                GeneGeneratorExt -> GeneGeneratorExt,
                                (existingValue, newValue) -> existingValue ));
                List<GeneFuelPriceDTO> geneFuelPrice = generatorConfigApiMapper.getGeneFuelPrice(lastDayOfMonth + " 23:59:59");
                Map<Integer,GeneFuelPriceDTO> geneIdToFuelFeeMap = geneFuelPrice.stream()
                        .collect(Collectors.toMap(
                                GeneFuelPriceDTO::getEquipmentId,
                                GeneFuelPriceDTO -> GeneFuelPriceDTO,
                                (existingValue, newValue) -> existingValue ));
                //循环所有油机开始计算月消耗量
                for (GeneGeneratorExt oneGene : generators ) {
                    Integer geneId = oneGene.getGeneId();
                    if(geneIdToFuelFeeAndConsumptionMap.get(geneId) == null)
                        geneIdToFuelFeeAndConsumptionMap.put(geneId,new GeneGeneratorExt());
                    if(geneId == null)
                        continue;
                    //发电量
                    double powerGeneration = geneIdToPowerTimeMap.get(geneId) == null ? 0D : (geneIdToPowerTimeMap.get(geneId).getPowerGeneration() == null ? 0D : geneIdToPowerTimeMap.get(geneId).getPowerGeneration());
                    //运行时间（秒）
                    long runTime = geneIdToPowerTimeMap.get(geneId) == null ? 0L :(geneIdToPowerTimeMap.get(geneId).getRunDuration() == null ? 0L : geneIdToPowerTimeMap.get(geneId).getRunDuration());
                    GenePowerGenerationRecord noEndTimeRecord = geneIdToPowerNoEndTimeMap.get(geneId) ;
                    long addTime = 0L;
                    //如果存在endTime为null的记录，在计算时用现在时间减去开始时间计算开机到现在的时间差
                    if(noEndTimeRecord != null){
                        //如果开机记录的时间比查询时间开始时间后
                        if(noEndTimeRecord.getStartTime().compareTo(startOfMonth) >= 0 ){
                            //查询结束时间在开机记录时间之后
                            if(endOfMonth.compareTo(noEndTimeRecord.getStartTime()) >= 0){
                                //如果当前时间在月末之后
                                if(new Date().compareTo(endOfMonth) >= 0){
                                    addTime = ((endOfMonth.getTime() - noEndTimeRecord.getStartTime().getTime()) / 1000);
                                }else {
                                    addTime = ((new Date().getTime() - noEndTimeRecord.getStartTime().getTime()) / 1000);
                                }
                            }
                        }
                        else {
                            //如果当前时间在月末之后
                            if(new Date().compareTo(endOfMonth) >= 0){
                                addTime = ((endOfMonth.getTime() - startOfMonth.getTime()) / 1000);
                            }
                            else {
                                addTime = ((new Date().getTime()  - startOfMonth.getTime()) / 1000);
                            }
                        }
                    }
                    runTime = runTime + addTime;
                    //默认电价
                    double defaultPrice = geneIdToFuelFeeAndConsumptionMap.get(geneId).getDefaultElecUnitPrice() == null ? 0D :geneIdToFuelFeeAndConsumptionMap.get(geneId).getDefaultElecUnitPrice();
                    //发电单价（元/度）
                    double elecUnitPrice = geneIdToFeeMap.get(geneId) == null ? (defaultPrice) :(geneIdToFeeMap.get(geneId).getElecUnitPrice() == null ? (defaultPrice) : geneIdToFeeMap.get(geneId).getElecUnitPrice());
                    //发电油耗(L/h)
                    double ratedFuelConsumption = geneIdToFuelFeeAndConsumptionMap.get(geneId) == null ? 0D :(geneIdToFuelFeeAndConsumptionMap.get(geneId).getUnitFuelConsumption() == null ? 0D : geneIdToFuelFeeAndConsumptionMap.get(geneId).getUnitFuelConsumption());
                    //父油箱油价
                    double fuelFee = geneIdToFuelFeeMap.get(geneId) == null ? 0D : (geneIdToFuelFeeMap.get(geneId).getFuelPrice() == null ? 0D : geneIdToFuelFeeMap.get(geneId).getFuelPrice());
                    totalFuelFee += ((runTime / 3600.0) * ratedFuelConsumption * fuelFee);
                    totalElecFee += (powerGeneration * elecUnitPrice);
                }

                GenePowerFeeDTO temp = new GenePowerFeeDTO();
                temp.setTime(String.valueOf(firstDayOfYear));
                temp.setFuelFee(NumberUtil.doubleAccuracy(totalFuelFee,2));
                temp.setPowerFee(NumberUtil.doubleAccuracy(totalElecFee,2));
                result.add(temp);
                //切换时间
                firstDayOfYear = firstDayOfYear.plusMonths(1);
                lastDayOfMonth = LocalDate.of(firstDayOfYear.getYear(), firstDayOfYear.getMonth(), 1).with(TemporalAdjusters.lastDayOfMonth());;
            }
        }catch (Exception ex){
            log.error("GeneratorConfigServiceImpl.getGeneratorElectAndFuelPrice error:" + ex.getMessage());
            result.add(new GenePowerFeeDTO("-1",0D,0D));
            return result;
        }

        return result;
    }

    @Override
    public Boolean JudgeEquipmentIsGenerator(Integer objectId) {
        Integer baseTypeId = generatorConfigApiMapper.getEquipmentBaseType(objectId);
        if(baseTypeId == null)
            return false;
        return  baseTypeId == 301;
    }

    /**
     * 获取某大楼下各油机的启停次数
     * @param resourceStructureId 节点ID
     */
    @Override
    public List<GeneStartStopNumDTO> GetGeneratorStartStopNum(Integer resourceStructureId, Integer userId, Date startTime, Date endTime) {
        List<GeneGeneratorExt> generators = generatorConfigApiMapper.getAllGeneratorByResourceStructureId(resourceStructureId.toString());
        String geneIds = generators.stream()
                .map(GeneGeneratorExt::getGeneId)
                .map(String::valueOf)
                .collect(Collectors.joining(","));
        if(geneIds.isEmpty())
            return new ArrayList<>();
        return generatorConfigApiMapper.getGeneStartStopNum(geneIds,startTime,endTime);
    }

    //获取节点下所有油箱的总油量
    public Double GetObjectsOilBoxesFuelLevel(String objects){
        double totalFuel = 0D;
        try{
            //得到权限节点下的所有油箱设备
            List<GeneOilboxExt> oilBoxes= generatorConfigApiMapper.getAllOilBoxByResourceStructureId(objects);
            //根据油箱的油位信号获取实时的油位值
            Map<Integer,Double> equipSignalMap = new HashMap<>();
            List<ActiveSignalRequestByBaseTypeId> redisKey = new ArrayList<>();
            int count = 0;
            for (GeneOilboxExt oneOilBox : oilBoxes) {
                ActiveSignalRequestByBaseTypeId tempId = new ActiveSignalRequestByBaseTypeId();
                tempId.setBaseTypeId(List.of(FUELLEVELSIGNALID));
                tempId.setEquipmentId(oneOilBox.getOilId());
                redisKey.add(tempId);
                count++;
                if (count % 1000 == 0 || count == oilBoxes.size()) {
                    List<EquipmentActiveSignal> realTimeSignalValueList = activeSignalManager.getEquipmentActiveSignalByBaseTypeId(redisKey);
                    Map<Integer, Double> tempMap = realTimeSignalValueList.stream().collect(Collectors.toMap(EquipmentActiveSignal::getEquipmentId, realTimeSignalItem -> realTimeSignalItem.getActiveSignals().isEmpty() ?  0D : Double.parseDouble(realTimeSignalItem.getActiveSignals().get(0).getOriginalValue() == null ? "0" : realTimeSignalItem.getActiveSignals().get(0).getOriginalValue())));
                    equipSignalMap.putAll(tempMap);
                    redisKey.clear();
                }
            }
            //计算所有油箱的总油量
            for (GeneOilboxExt oneOilBox : oilBoxes) {
                //单个油箱的实时油量
                double oneOilBoxFuelLevel = equipSignalMap.get(oneOilBox.getOilId()) == null ? 0D : equipSignalMap.get(oneOilBox.getOilId());
                //单个油箱的满油液位
                double oneOilBoxFullOilLevel = oneOilBox.getFullOilLevel() == null ? 0D : oneOilBox.getFullOilLevel();
                //单个油箱的容积
                double oneOilBoxRatedVolume = oneOilBox.getRatedVolume() == null ? 0D : oneOilBox.getRatedVolume();

                totalFuel += (oneOilBoxRatedVolume * (oneOilBoxFuelLevel / oneOilBoxFullOilLevel));
            }
        }catch (Exception e){
            log.error("GeneratorConfigServiceImpl.getObjectsOilBoxesFuelLevel error:" + e.getMessage());
            return -99D;
        }
        return NumberUtil.doubleAccuracy(totalFuel,2);
    }

    /**
     * 获取节点或权限下所有油箱
     */
    public List<GeneGeneratorExt> GetAllGeneratorsByUserIdOrObjectId (Integer userId, Integer resourceStructureId){
        //通过传入的用户ID得到该用户下属的层级节点
        List<ResourceStructure> allResourceStructure = resourceStructureService.findResourceStructureByUserId(userId);
        //如果传入的有节点Id，则表明是求某节点下油机费用
        if(resourceStructureId != null){
            allResourceStructure = allResourceStructure.stream().filter(res -> res.getLevelOfPath().contains(resourceStructureId.toString()) && (res.getStructureTypeId() == 105 || res.getStructureTypeId() == 5)).toList();
        }
        String objectIds = allResourceStructure.stream()
                .map(ResourceStructure::getResourceStructureId)
                .map(String::valueOf)
                .collect(Collectors.joining(","));
        if(objectIds.isEmpty())
            return new ArrayList<>();
        //获取该节点以及ID权限下属的所有油机
        return generatorConfigApiMapper.getAllGeneratorByResourceStructureId(objectIds);
    }
}
