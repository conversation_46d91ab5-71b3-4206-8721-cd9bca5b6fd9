package com.siteweb.generator.service.impl;

import cn.hutool.core.date.DateTime;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.generator.dto.GeneStateCountDTO;
import com.siteweb.generator.dto.GeneWorkStatusDTO;
import com.siteweb.generator.dto.RunDurationOfObjectDTO;
import com.siteweb.generator.entity.GenePowerGenerationRecord;
import com.siteweb.generator.manager.GeneratorManager;
import com.siteweb.generator.mapper.GeneratorConfigApiMapper;
import com.siteweb.generator.service.GeneratorConfigExService;
import com.siteweb.generator.service.GeneratorConfigService;
import com.siteweb.monitoring.dto.SimpleActiveSignal;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.ActiveSignalManager;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.prealarm.entity.PreAlarmPoint;
import com.siteweb.prealarm.mapper.PreAlarmPointMapper;
import com.siteweb.prealarm.service.PreAlarmPointService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class GeneratorConfigExServiceImpl implements GeneratorConfigExService {

    @Autowired
    private ResourceStructureService resourceStructureService;
    @Autowired
    private GeneratorConfigService generatorConfigService;
    @Autowired
    private GeneratorManager generatorManager;
    @Autowired
    private GeneratorConfigApiMapper generatorConfigApiMapper;
    @Autowired
    private ActiveSignalManager activeSignalManager;
    @Autowired
    private PreAlarmPointMapper preAlarmPointMapper;
    @Autowired
    private PreAlarmPointService preAlarmPointService;

    //油机工作状态和有功功率信号基类
    private String geneWorkStateAndPower = "301155001,301065001";

    //油机发电时长 地市列表
    @Override
    public Object getListCityRunDuration(Date startTime, Date endTime, Integer userId) {
        List<RunDurationOfObjectDTO> result = new ArrayList<>();

        //得到用户权限下所有节点
        List<ResourceStructure> allRoleResourceStructure = resourceStructureService.findResourceStructureByUserId(userId);
        if (allRoleResourceStructure.size() == 0)
            return result;
        //获取所有油机
        List<Equipment> allGenerator = generatorManager.GetAllGenerator();

        //获取地市102和大楼3
        List<ResourceStructure> lstCity = allRoleResourceStructure.stream().filter(i->i.getStructureTypeId().equals(2) || i.getStructureTypeId().equals(102)).collect(Collectors.toList());

        for (ResourceStructure city : lstCity){
            RunDurationOfObjectDTO oneRunDurationOfObjectDTO  = new RunDurationOfObjectDTO();
            oneRunDurationOfObjectDTO.setObjectName(city.getResourceStructureName());
            oneRunDurationOfObjectDTO.setRunDuration(0d);
            oneRunDurationOfObjectDTO.setPowerGeneration(0d);

            //获取此地市下的所有机房的id
            List<Integer> lstHouseIds = allRoleResourceStructure.stream().filter(i->i.getLevelOfPath().startsWith(city.getLevelOfPath()) &&
                                (i.getStructureTypeId().equals(5) || i.getStructureTypeId().equals(105))
                        ).map(ResourceStructure::getResourceStructureId).collect(Collectors.toList());
            if (lstHouseIds.size() == 0) continue;
            //这些机房下的油机
            List<Equipment> thisHouseGenerator = allGenerator.stream().filter(i->lstHouseIds.contains(i.getResourceStructureId())).collect(Collectors.toList());
            if (thisHouseGenerator.size() == 0) continue;


            String equipmentIds = thisHouseGenerator.stream()
                    .map(Equipment::getEquipmentId)
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));

            //这些油机的发电时长记录
            List<GenePowerGenerationRecord>  lstRecord = generatorConfigApiMapper.getGenePowerTime(DateUtil.dateToString(startTime), DateUtil.dateToString(endTime),equipmentIds);
            if (lstRecord.size() == 0) continue;
            //TODO 如果该设备又存在没有EndTime的记录，需要获取到其开始时间，然后用现在时间算出其累计运行时间
            List<GenePowerGenerationRecord> noEndTimeGenePowerRecord = generatorConfigApiMapper.getNoEndTimeGenePowerRecord(equipmentIds);
            long addTime = 0L;
            for (GenePowerGenerationRecord oneNoEndTimeRecord: noEndTimeGenePowerRecord ) {
                addTime = addTime + calculateAddTime(oneNoEndTimeRecord,startTime,endTime);
            }

            long runDuration = lstRecord.stream().filter(record -> record.getRunDuration() != null)
                    .mapToLong(GenePowerGenerationRecord::getRunDuration)
                    .sum();
            runDuration = runDuration + addTime;
            Double powerGeneration = lstRecord.stream().filter(record -> record.getPowerGeneration() != null)
                    .mapToDouble(GenePowerGenerationRecord::getPowerGeneration)
                    .sum();
            oneRunDurationOfObjectDTO.setRunDuration(NumberUtil.doubleAccuracy((runDuration / 3600.0),2));
            oneRunDurationOfObjectDTO.setPowerGeneration(NumberUtil.doubleAccuracy(powerGeneration,2));

            result.add(oneRunDurationOfObjectDTO);
        }

        return result;
    }
    //油机发电时长 区县列表
    @Override
    public Object getListCountyRunDuration(Integer resourceStructureId, Date startTime, Date endTime, Integer userId) {
        List<RunDurationOfObjectDTO> result = new ArrayList<>();

        //得到用户权限下所有节点
        List<ResourceStructure> allRoleResourceStructure = resourceStructureService.findResourceStructureByUserId(userId);
        if (allRoleResourceStructure.size() == 0)
            return result;

        //获取区县103和楼层3
        List<ResourceStructure> lstStation = allRoleResourceStructure.stream().filter(i -> i.getLevelOfPath().contains(resourceStructureId.toString()) &&
                (i.getStructureTypeId().equals(103) || i.getStructureTypeId().equals(3))).toList();

        //获取所有油机
        List<Equipment> allGenerator = generatorManager.GetAllGenerator();

        for (ResourceStructure station : lstStation){
            RunDurationOfObjectDTO oneRunDurationOfObjectDTO  = new RunDurationOfObjectDTO();
            oneRunDurationOfObjectDTO.setObjectName(station.getResourceStructureName());
            oneRunDurationOfObjectDTO.setRunDuration(0d);
            oneRunDurationOfObjectDTO.setPowerGeneration(0d);

            //获取此站点下的所有机房的id
            List<Integer> lstHouseIds = allRoleResourceStructure.stream().filter(i -> i.getLevelOfPath().startsWith(station.getLevelOfPath()) &&
                    (i.getStructureTypeId().equals(5) || i.getStructureTypeId().equals(105))
            ).map(ResourceStructure::getResourceStructureId).toList();
            if (lstHouseIds.size() == 0) continue;
            //这些机房下的油机
            List<Equipment> thisHouseGenerator = allGenerator.stream().filter(i -> lstHouseIds.contains(i.getResourceStructureId())).toList();
            if (thisHouseGenerator.size() == 0) continue;

            String equipmentIds = thisHouseGenerator.stream()
                    .map(Equipment::getEquipmentId)
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));

            //这些油机的发电时长记录
            List<GenePowerGenerationRecord>  lstRecord = generatorConfigApiMapper.getGenePowerTime(DateUtil.dateToString(startTime), DateUtil.dateToString(endTime),equipmentIds);
            if (lstRecord.size() == 0) continue;
            //TODO 如果该设备又存在没有EndTime的记录，需要获取到其开始时间，然后用现在时间算出其累计运行时间
            List<GenePowerGenerationRecord> noEndTimeGenePowerRecord = generatorConfigApiMapper.getNoEndTimeGenePowerRecord(equipmentIds);
            long addTime = 0L;
            for (GenePowerGenerationRecord oneNoEndTimeRecord: noEndTimeGenePowerRecord ) {
                addTime = addTime + calculateAddTime(oneNoEndTimeRecord,startTime,endTime);
            }

            long runDuration = lstRecord.stream().filter(record -> record.getRunDuration() != null)
                    .mapToLong(GenePowerGenerationRecord::getRunDuration)
                    .sum();
            runDuration = runDuration + addTime;

            Double powerGeneration = lstRecord.stream().filter(record -> record.getPowerGeneration() != null)
                    .mapToDouble(GenePowerGenerationRecord::getPowerGeneration)
                    .sum();
            oneRunDurationOfObjectDTO.setRunDuration(NumberUtil.doubleAccuracy((runDuration / 3600.0),2));
            oneRunDurationOfObjectDTO.setPowerGeneration(NumberUtil.doubleAccuracy(powerGeneration,2));

            result.add(oneRunDurationOfObjectDTO);
        }

        return result;
    }

    //油机发电时长 基站列表
    @Override
    public Object getListStationRunDuration(Integer resourceStructureId, Date startTime, Date endTime, Integer userId) {
        List<RunDurationOfObjectDTO> result = new ArrayList<>();

        //得到用户权限下所有节点
        List<ResourceStructure> allRoleResourceStructure = resourceStructureService.findResourceStructureByUserId(userId);
        if (allRoleResourceStructure.size() == 0)
            return result;

        //获取基站104和楼层4
        List<ResourceStructure> lstStation = allRoleResourceStructure.stream().filter(i -> i.getLevelOfPath().contains(resourceStructureId.toString()) &&
                (i.getStructureTypeId().equals(104) || i.getStructureTypeId().equals(4))).toList();

        //获取所有油机
        List<Equipment> allGenerator = generatorManager.GetAllGenerator();

        for (ResourceStructure station : lstStation){
            RunDurationOfObjectDTO oneRunDurationOfObjectDTO  = new RunDurationOfObjectDTO();
            oneRunDurationOfObjectDTO.setObjectName(station.getResourceStructureName());
            oneRunDurationOfObjectDTO.setRunDuration(0d);
            oneRunDurationOfObjectDTO.setPowerGeneration(0d);

            //获取此站点下的所有机房的id
            List<Integer> lstHouseIds = allRoleResourceStructure.stream().filter(i -> i.getLevelOfPath().startsWith(station.getLevelOfPath()) &&
                    (i.getStructureTypeId().equals(5) || i.getStructureTypeId().equals(105))
            ).map(ResourceStructure::getResourceStructureId).toList();
            if (lstHouseIds.size() == 0) continue;
            //这些机房下的油机
            List<Equipment> thisHouseGenerator = allGenerator.stream().filter(i -> lstHouseIds.contains(i.getResourceStructureId())).toList();
            if (thisHouseGenerator.size() == 0) continue;

            String equipmentIds = thisHouseGenerator.stream()
                    .map(Equipment::getEquipmentId)
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));

            //这些油机的发电时长记录
            List<GenePowerGenerationRecord>  lstRecord = generatorConfigApiMapper.getGenePowerTime(DateUtil.dateToString(startTime), DateUtil.dateToString(endTime),equipmentIds);
            if (lstRecord.size() == 0) continue;

            //TODO 如果该设备又存在没有EndTime的记录，需要获取到其开始时间，然后用现在时间算出其累计运行时间
            List<GenePowerGenerationRecord> noEndTimeGenePowerRecord = generatorConfigApiMapper.getNoEndTimeGenePowerRecord(equipmentIds);
            long addTime = 0L;
            for (GenePowerGenerationRecord oneNoEndTimeRecord: noEndTimeGenePowerRecord ) {
                addTime = addTime + calculateAddTime(oneNoEndTimeRecord,startTime,endTime);
            }

            long runDuration = lstRecord.stream().filter(record -> record.getRunDuration() != null)
                    .mapToLong(GenePowerGenerationRecord::getRunDuration)
                    .sum();
            runDuration = runDuration + addTime;

            Double powerGeneration = lstRecord.stream().filter(record -> record.getPowerGeneration() != null)
                    .mapToDouble(GenePowerGenerationRecord::getPowerGeneration)
                    .sum();
            oneRunDurationOfObjectDTO.setRunDuration(NumberUtil.doubleAccuracy((runDuration / 3600.0),2));
            oneRunDurationOfObjectDTO.setPowerGeneration(NumberUtil.doubleAccuracy(powerGeneration,2));

            result.add(oneRunDurationOfObjectDTO);
        }

        return result;
    }
    //油机发电时长 设备列表
    @Override
    public Object getListGeneRunDuration(Integer resourceStructureId, Date startTime, Date endTime, Integer userId) {
        List<RunDurationOfObjectDTO> result = new ArrayList<>();

        //得到用户权限下所有节点
        List<ResourceStructure> allRoleResourceStructure = resourceStructureService.findResourceStructureByUserId(userId);
        if (allRoleResourceStructure.size() == 0)
            return result;

        //获取局房105和机房5
        List<ResourceStructure> lstHouse = allRoleResourceStructure.stream().filter(i -> i.getLevelOfPath().contains(resourceStructureId.toString()) &&
                (i.getStructureTypeId().equals(105) || i.getStructureTypeId().equals(5))).toList();
        //机房的id
        List<Integer> lstHouseIds = lstHouse.stream().map(ResourceStructure::getResourceStructureId).toList();

        //获取油机
        List<Equipment> allGenerator = generatorManager.GetAllGenerator().stream().filter(i -> lstHouseIds.contains(i.getResourceStructureId())).toList();
        if (allGenerator.size() == 0) return result;

        for(Equipment gene : allGenerator){
            RunDurationOfObjectDTO oneRunDurationOfObjectDTO  = new RunDurationOfObjectDTO();
            oneRunDurationOfObjectDTO.setObjectName(gene.getEquipmentName());
            oneRunDurationOfObjectDTO.setRunDuration(0d);
            oneRunDurationOfObjectDTO.setPowerGeneration(0d);

            //这油机的发电时长记录
            List<GenePowerGenerationRecord>  lstRecord = generatorConfigApiMapper.getGenePowerTime(DateUtil.dateToString(startTime), DateUtil.dateToString(endTime),gene.getEquipmentId().toString());
            if (lstRecord.size() == 0) continue;
            //TODO 如果该设备又存在没有EndTime的记录，需要获取到其开始时间，然后用现在时间算出其累计运行时间
            List<GenePowerGenerationRecord> noEndTimeGenePowerRecord = generatorConfigApiMapper.getNoEndTimeGenePowerRecord(gene.getEquipmentId().toString());
            long addTime = 0L;
            for (GenePowerGenerationRecord oneNoEndTimeRecord: noEndTimeGenePowerRecord ) {
                addTime = addTime + calculateAddTime(oneNoEndTimeRecord,startTime,endTime);
            }

            long runDuration = lstRecord.stream().filter(record -> record.getRunDuration() != null)
                    .mapToLong(GenePowerGenerationRecord::getRunDuration)
                    .sum();
            runDuration = runDuration + addTime;

            Double powerGeneration = lstRecord.stream().filter(record -> record.getPowerGeneration() != null)
                    .mapToDouble(GenePowerGenerationRecord::getPowerGeneration)
                    .sum();
            oneRunDurationOfObjectDTO.setRunDuration(NumberUtil.doubleAccuracy((runDuration / 3600.0),2));
            oneRunDurationOfObjectDTO.setPowerGeneration(NumberUtil.doubleAccuracy(powerGeneration,2));

            result.add(oneRunDurationOfObjectDTO);
        }

        return result;
    }

    @Override
    public Object getGeneStateCount(Integer resourceStructureId, Integer userId) {
        GeneStateCountDTO result = new GeneStateCountDTO();
        //得到用户权限下所有节点
        List<ResourceStructure> allRoleResourceStructure = resourceStructureService.findResourceStructureByUserId(userId);
        if (allRoleResourceStructure.size() == 0)
            return result;

        result.setObjectName(resourceStructureService.findById(resourceStructureId).getResourceStructureName());

        //获取局房105和机房5
        List<ResourceStructure> lstHouse = allRoleResourceStructure.stream().filter(i -> i.getLevelOfPath().contains(resourceStructureId + ".") &&
                (i.getStructureTypeId().equals(105) || i.getStructureTypeId().equals(5))).toList();
        //机房的id
        List<Integer> lstHouseIds = lstHouse.stream().map(ResourceStructure::getResourceStructureId).toList();
        //获取油机
        List<Equipment> allGenerator = generatorManager.GetAllGenerator().stream().filter(i -> lstHouseIds.contains(i.getResourceStructureId())).toList();
        if (allGenerator.size() == 0) return result;

        Integer running = 0;
        Integer stopped = 0;
        Integer emptyPower = 0;
        Integer havePower = 0;

        List<Long> baseTypeIdList = Arrays.stream(geneWorkStateAndPower.split(",")).map(Long::parseLong).toList();

        for(Equipment gene : allGenerator){
            List<SimpleActiveSignal> activeSignals = activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId(gene.getEquipmentId(),baseTypeIdList);
            if (activeSignals.size() == 0){
                stopped++; continue;
            }

            List<SimpleActiveSignal> activeSignalStates = activeSignals.stream().filter(i-> Objects.equals(i.getBaseTypeId(), baseTypeIdList.get(0))).collect(Collectors.toList());
            if (activeSignalStates.size() == 0){
                stopped++; continue;
            }
            SimpleActiveSignal activeSignalState = activeSignalStates.get(0);

            if (activeSignalState.getOriginalValue() == null || activeSignalState.getOriginalValue().equals("0")){
                stopped++;
            }
            else{
                running++;
                List<SimpleActiveSignal> activeSignalPowers = activeSignals.stream().filter(i-> Objects.equals(i.getBaseTypeId(), baseTypeIdList.get(1))).collect(Collectors.toList());
                if (activeSignalPowers.size() == 0) {
                    emptyPower++;
                }
                else{
                    SimpleActiveSignal activeSignalPower = activeSignalPowers.get(0);
                    if (activeSignalPower.getOriginalValue() == null || activeSignalPower.getOriginalValue().equals("") || NumberUtil.formatNumeric(activeSignalPower.getOriginalValue()) < 0.1){
                        emptyPower++;
                    }
                    else{
                        havePower++;
                    }
                }
            }
        }
        result.setEmptyPower(emptyPower);
        result.setRunning(running);
        result.setStopped(stopped);
        result.setHavePower(havePower);
        return result;
    }

    @Override
    public Object getListCityGeneStateCount(Integer userId) {
        List<GeneStateCountDTO> result = new ArrayList<>();

        //得到用户权限下所有节点
        List<ResourceStructure> allRoleResourceStructure = resourceStructureService.findResourceStructureByUserId(userId);
        if (allRoleResourceStructure.size() == 0)
            return result;

        //获取地市102和大楼3
        List<ResourceStructure> lstCity = allRoleResourceStructure.stream().filter(i->i.getStructureTypeId().equals(3) || i.getStructureTypeId().equals(102)).collect(Collectors.toList());

        for (ResourceStructure city : lstCity){
            GeneStateCountDTO dto = (GeneStateCountDTO) getGeneStateCount(city.getResourceStructureId(), userId);
            result.add(dto);
        }

        return result;
    }
    @Override
    public Object getListCountyGeneStateCount(Integer resourceStructureId, Integer userId) {
        List<GeneStateCountDTO> result = new ArrayList<>();

        //得到用户权限下所有节点
        List<ResourceStructure> allRoleResourceStructure = resourceStructureService.findResourceStructureByUserId(userId);
        if (allRoleResourceStructure.size() == 0)
            return result;
        //获取区县103和楼层3
        List<ResourceStructure> lstStation = allRoleResourceStructure.stream().filter(i -> i.getLevelOfPath().contains(resourceStructureId.toString()) &&
                (i.getStructureTypeId().equals(103) || i.getStructureTypeId().equals(3))).toList();

        for (ResourceStructure station : lstStation){
            GeneStateCountDTO dto = (GeneStateCountDTO) getGeneStateCount(station.getResourceStructureId(), userId);
            result.add(dto);
        }

        return result;
    }
    @Override
    public Object getListStationGeneStateCount(Integer resourceStructureId, Integer userId) {
        List<GeneStateCountDTO> result = new ArrayList<>();

        //得到用户权限下所有节点
        List<ResourceStructure> allRoleResourceStructure = resourceStructureService.findResourceStructureByUserId(userId);
        if (allRoleResourceStructure.size() == 0)
            return result;
        //获取站点104和楼层4
        List<ResourceStructure> lstStation = allRoleResourceStructure.stream().filter(i -> i.getLevelOfPath().contains(resourceStructureId.toString()) &&
                (i.getStructureTypeId().equals(104) || i.getStructureTypeId().equals(4))).toList();

        for (ResourceStructure station : lstStation){
            GeneStateCountDTO dto = (GeneStateCountDTO) getGeneStateCount(station.getResourceStructureId(), userId);
            result.add(dto);
        }

        return result;
    }

    @Override
    public Object getListGeneWorkStatus(Integer resourceStructureId, Integer userId) {
        List<GeneWorkStatusDTO> result = new ArrayList<>();

        //得到用户权限下所有节点
        List<ResourceStructure> allRoleResourceStructure = resourceStructureService.findResourceStructureByUserId(userId);
        if (allRoleResourceStructure.size() == 0)
            return result;

        //获取局房105和机房5
        List<ResourceStructure> lstHouse = allRoleResourceStructure.stream().filter(i -> i.getLevelOfPath().contains(resourceStructureId.toString()) &&
                (i.getStructureTypeId().equals(105) || i.getStructureTypeId().equals(5))).toList();
        //机房的id
        List<Integer> lstHouseIds = lstHouse.stream().map(ResourceStructure::getResourceStructureId).toList();

        //获取油机
        List<Equipment> allGenerator = generatorManager.GetAllGenerator().stream().filter(i -> lstHouseIds.contains(i.getResourceStructureId())).toList();
        if (allGenerator.size() == 0) return result;


        List<Long> baseTypeIdList = Arrays.asList(geneWorkStateAndPower.split(",")).stream().map(Long::parseLong).toList();
        List<SimpleActiveSignal> activeSignals = new ArrayList<>();
        for(Equipment gene : allGenerator){
            GeneWorkStatusDTO dto  = new GeneWorkStatusDTO();
            dto.setGeneName(gene.getEquipmentName());

            activeSignals = activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId(gene.getEquipmentId(),baseTypeIdList);
            if(activeSignals.size() == 0)
                dto.setWorkStatus(0);
            else{


                List<SimpleActiveSignal> activeSignalStates = activeSignals.stream().filter(i-> Objects.equals(i.getBaseTypeId(), baseTypeIdList.get(0))).collect(Collectors.toList());
                if (activeSignalStates.size() == 0){
                    dto.setWorkStatus(0);
                }
                else{
                    Integer workStatus = activeSignalStates.get(0).getOriginalValue() == null?0: Integer.parseInt(activeSignalStates.get(0).getOriginalValue());
                    dto.setWorkStatus(workStatus);
                }
            }

            result.add(dto);
        }

        return result;
    }

    @Override
    public Object getGeneMaintenaceTime(Integer equipmentId)  {
        List<String> result = new ArrayList<>();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        //保养记录表最后保养时间
        String theLastMaintenanceTime = preAlarmPointMapper.getGeneratorLastMaintenanceTime(equipmentId);
        //预警配置规则
        List<PreAlarmPoint>  lstRules =  preAlarmPointService.findPreAlarmPoints(equipmentId,7,6);

        //如果上次保养时间在保养表里查不到，则用默认的开始时间
        if(theLastMaintenanceTime == null) {
            if (lstRules == null || lstRules.size() == 0){
                result.add("");
                theLastMaintenanceTime = "";
            } else {
                String evalExpression = lstRules.get(0).getExpression();
                String[] expressionParams = evalExpression.split("\\|");

                String lastTime = expressionParams[2] == null ? "" : expressionParams[2];
                result.add(sdf.format(lastTime));
                theLastMaintenanceTime = lastTime;
            }
        } else {
            result.add(theLastMaintenanceTime);
        }

        //计算下次时间
        if (lstRules == null || lstRules.size() == 0 || theLastMaintenanceTime == ""){
            result.add("");
            return result;
        } else {
            String evalExpression = lstRules.get(0).getExpression();
            String[] expressionParams = evalExpression.split("\\|");

            //间隔时间
            int daysToAdd  = Integer.parseInt(expressionParams[0] == null ? "0" : expressionParams[0]);
            try{
                Date date = sdf.parse(theLastMaintenanceTime);
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(date);
                calendar.add(Calendar.DAY_OF_MONTH, daysToAdd);

                Date newDate = calendar.getTime();
                String newDateStr = sdf.format(newDate);
                result.add(newDateStr);
            }
            catch(Exception ex ){
                log.error("油机组态接口getGeneMaintenaceTime error "+ex.getMessage());
                result.add("");
            }
        }
        return result;
    }

    @Override
    public List<RunDurationOfObjectDTO> getListMonthRunDuration(Integer resourceStructureId, Integer userId) {
        List<RunDurationOfObjectDTO> result = new ArrayList<>();
        //今年初
        Date startTime = DateUtil.dateAddMonth(DateUtil.getNextYearFirstDay(DateTime.now()),-12);

        for ( int i = 0; i < 12; i++ ){
            RunDurationOfObjectDTO thisMonthResult = new RunDurationOfObjectDTO();
            thisMonthResult.setTime(startTime);

            Date endTime = DateUtil.dateAddMonth(startTime,1);
            endTime = DateUtil.dateAddSeconds(endTime,-1);

            List<RunDurationOfObjectDTO> listCity = (List<RunDurationOfObjectDTO>)getListCityRunDuration(startTime, endTime, userId);
            Double sumRunDuration = listCity.stream().mapToDouble(RunDurationOfObjectDTO::getRunDuration).sum();
            Double sumPowerGeneration = listCity.stream().mapToDouble(RunDurationOfObjectDTO::getPowerGeneration).sum();

            thisMonthResult.setRunDuration(sumRunDuration);
            thisMonthResult.setPowerGeneration(sumPowerGeneration);

            result.add(thisMonthResult);

            startTime = DateUtil.dateAddMonth(startTime,1);
        }

        return result;
    }
    public Long calculateAddTime(GenePowerGenerationRecord oneNoEndTimeRecord,Date startTime,Date endTime){
        long addTime = 0L;
        //如果开机记录的时间比查询时间开始时间后而且查询结束时间在开机记录时间之后
        if(oneNoEndTimeRecord.getStartTime().compareTo(startTime) >= 0 ){
            //查询结束时间在开机记录时间之后
            if(endTime.compareTo(oneNoEndTimeRecord.getStartTime()) >= 0) {
                //如果当前时间在月末之后
                if (new Date().compareTo(endTime) >= 0) {
                    addTime = ((endTime.getTime() - oneNoEndTimeRecord.getStartTime().getTime()) / 1000);
                } else {
                    addTime = ((new Date().getTime() - oneNoEndTimeRecord.getStartTime().getTime()) / 1000);
                }
            }
        }
        else {
            //如果当前时间在月末之后
            if(new Date().compareTo(endTime) >= 0){
                addTime += ((endTime.getTime() - startTime.getTime()) / 1000);
            }
            else {
                addTime += ((new Date().getTime()  - startTime.getTime()) / 1000);
            }
        }
        return addTime;
    }


}
