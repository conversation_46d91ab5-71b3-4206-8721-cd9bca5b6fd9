package com.siteweb.generator.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.common.util.DateUtil;
import com.siteweb.generator.entity.GeneGeneratorElecUnitPrice;
import com.siteweb.generator.entity.GeneGeneratorExt;
import com.siteweb.generator.entity.GenePowerGenerationRecord;
import com.siteweb.generator.entity.GeneratorSignalInfo;
import com.siteweb.generator.function.CommonUtils;
import com.siteweb.generator.manager.GeneratorSignalCheckManager;
import com.siteweb.generator.mapper.GeneratorMapper;
import com.siteweb.generator.mapper.GeneratorSignalInfoMapper;
import com.siteweb.generator.mapper.OilboxMapper;
import com.siteweb.generator.mapper.PowerGenerationMapper;
import com.siteweb.generator.service.GeneBaseDataService;
import com.siteweb.monitoring.dto.SimpleActiveSignal;
import com.siteweb.monitoring.entity.AlarmChange;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.mamager.ActiveSignalManager;
import com.siteweb.monitoring.mamager.EquipmentManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import static com.siteweb.generator.service.impl.GeneratorServiceImpl.GENE_EQUIP_BASE_TYPES;

/** 油机基础数据处理服务类 */
@Service
@Slf4j
public class GeneBaseDataServiceImpl implements GeneBaseDataService {

    /** 油机开机告警 基类id */
    public static final Long GENE_POWERON_ALARM_BID = 301155001L;
    /** 油机正向有功电能信号 基类id */
    public static final Long GENE_ELECENERGY_BID = 201039001L;

    @Autowired
    private EquipmentManager equipmentManager;

    @Autowired
    private ActiveSignalManager activeSignalManager;

    @Autowired
    private PowerGenerationMapper powerGenerationMapper;
    @Autowired
    private GeneratorMapper generatorMapper;
    @Autowired
    private OilboxMapper  oilboxMapper;
    @Autowired
    private GeneratorSignalCheckManager generatorSignalCheckManager;

    @Autowired
    private GeneratorSignalInfoMapper generatorSignalInfoMapper;
    @Override
    public void handleGenePowerOnAlarm(AlarmChange alarmChange) {

        try {
            //只处理油机开机告警
            if(!alarmChange.getBaseTypeId().equals(GENE_POWERON_ALARM_BID)) {
                log.debug("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() alarmChange baseTypeId is not match " + GENE_POWERON_ALARM_BID);
                return;
            }
            if(alarmChange.getOperationType() == null || alarmChange.getEquipmentId() == null) {
                log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() operationType or equipmentId is null");
                return;
            }
            Integer equipmentId = alarmChange.getEquipmentId();
            Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
            if(equipment == null) {
                log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() equipment not exists. equipmentId=" + equipmentId);
                return;
            }
            Integer equipmentBaseType = equipment.getEquipmentBaseType();
            if(equipmentBaseType == null) {
                log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() equipment.getEquipmentBaseType() == null. equipmentId=" + equipmentId);
                return;
            }
            if(!GENE_EQUIP_BASE_TYPES[1].contains(equipmentBaseType)) {
                log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() equipment.getEquipmentBaseType()=" + equipmentBaseType);
                return;
            }
            if(alarmChange.getStartTime() == null) {
                log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() alarmChange.getStartTime() == null");
                return;
            }
            List<GenePowerGenerationRecord> records = powerGenerationMapper.getRecordsBy(equipmentId, equipmentBaseType, alarmChange.getStartTime());//前面已经限定，只处理一种告警 {GENE_POWERON_ALARM_BID}
            if(alarmChange.getOperationType().equals(1)) {//新增一个告警
                if(records.size() > 0) {
                    log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() alarmChange.getOperationType()=1, but records.size() > 0. equpmentId=" + equipmentId + "; startTime=" + alarmChange.getStartTime());
                    return;
                }
                GenePowerGenerationRecord record = new GenePowerGenerationRecord();
                record.setEquipmentId(equipmentId);
                record.setEquipmentBaseType(equipmentBaseType);
                record.setStartTime(alarmChange.getStartTime());
                double originalValue = 0d;
                List<SimpleActiveSignal> signalItemList = null;
                try {
                    signalItemList = activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId(equipmentId, List.of(GENE_ELECENERGY_BID));
                } catch (Exception e) {
                    log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId() throw exception; equpmentId=" + equipmentId + ", baseTypeId=" + GENE_ELECENERGY_BID, e);
                }
                SimpleActiveSignal temp = new SimpleActiveSignal();
                temp.setOriginalValue("99.1");
                if(signalItemList == null || signalItemList.size() < 1) {
                    log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId() return null or size < 1. equpmentId=" + equipmentId + ", baseTypeId=" + GENE_ELECENERGY_BID);
                } else {
                    if(signalItemList.size() > 1) {
                        log.warn("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId() return size > 1. equpmentId=" + equipmentId + ", baseTypeId=" + GENE_ELECENERGY_BID);
                    }
                    String originalValueStr = signalItemList.get(0).getOriginalValue();
                    try {
                        originalValue = Double.parseDouble(originalValueStr);
                    } catch (NumberFormatException e) {
                        log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() Double.parseDouble(originalValueStr)" +
                                " throw NumberFormatException. originalValueStr=" + originalValueStr + ", equipmentId=" + equipmentId, e);
                    }
                }
                record.setPositiveElecEnergyStart(originalValue);
                record.setStartInsertTime(CommonUtils.getCurDate());
                if(alarmChange.getSerialNo() != null) {
                    record.setStartSerialNo(alarmChange.getSerialNo());
                }
                powerGenerationMapper.insert(record);
            } else if(alarmChange.getOperationType().equals(2) || alarmChange.getOperationType().equals(3)) {//结束告警或确认告警
                if(records.size() < 1) {
                    log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() alarmChange.getOperationType()=" + alarmChange.getOperationType() + ", but records.size() < 1. equpmentId=" + equipmentId + "; startTime=" + alarmChange.getStartTime());
                    return;
                }
                if(records.size() > 1) {
                    log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() alarmChange.getOperationType()=" + alarmChange.getOperationType() + ", but records.size() > 1. equpmentId=" + equipmentId + "; startTime=" + alarmChange.getStartTime());
                    return;
                }
                GenePowerGenerationRecord record = records.get(0);
                if(record.getEndTime() != null) {//已计算过，直接跳过
                    //log.info("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() alarmChange.getOperationType()=" + alarmChange.getOperationType() + ", but record.getEndTime() != null, return . equpmentId=" + equipmentId + "; startTime=" + alarmChange.getStartTime());
                    return;
                }
                if(alarmChange.getEndTime() == null) {
                    log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() alarmChange.getOperationType()=" + alarmChange.getOperationType() + ", but alarmChange.getEndTime() == null. equpmentId=" + equipmentId + "; startTime=" + alarmChange.getStartTime());
                    return;
                }
                record.setEndTime(alarmChange.getEndTime());
                double originalValue = 0d;
                List<SimpleActiveSignal> signalItemList = null;
                try {
                    signalItemList = activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId(equipmentId, List.of(GENE_ELECENERGY_BID));
                } catch (Exception e) {
                    log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId() throw exception. equpmentId=" + equipmentId + ", baseTypeId=" + GENE_ELECENERGY_BID, e);
                }
                if(signalItemList == null || signalItemList.size() < 1) {
                    log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId() return null or size < 1. equpmentId=" + equipmentId + ", baseTypeId=" + GENE_ELECENERGY_BID);
                } else {
                    if(signalItemList.size() > 1) {
                        log.warn("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() activeSignalManager.getActiveSignalsByEquipmentIdAndBaseTypeId() return size > 1. equpmentId=" + equipmentId + ", baseTypeId=" + GENE_ELECENERGY_BID);
                    }
                    String originalValueStr = signalItemList.get(0).getOriginalValue();
                    try {
                        originalValue = Double.parseDouble(originalValueStr);
                    } catch (NumberFormatException e) {
                        log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() Double.parseDouble(originalValueStr)" +
                                " throw NumberFormatException. originalValueStr=" + originalValueStr + ", equipmentId=" + equipmentId, e);
                    }
                }
                record.setPositiveElecEnergyEnd(originalValue);
                String convertDoubleStr = CommonUtils.convertDouble(record.getPositiveElecEnergyEnd() - record.getPositiveElecEnergyStart());
                record.setPowerGeneration(Double.parseDouble(convertDoubleStr));
                record.setRunDuration((record.getEndTime().getTime() - record.getStartTime().getTime())/1000);
                //计算发电量对应的钱
                GeneGeneratorExt geneGeneratorExt = generatorMapper.selectById(equipmentId);
                if(geneGeneratorExt == null) {
                    log.warn("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() generatorMapper.selectById(equipmentId) return null. equipmentId=" + equipmentId);
                }
                double pgc = calcPowerGenerationCost(record.getEquipmentId(), record.getStartTime(), record.getEndTime(), record.getPowerGeneration(), geneGeneratorExt);
                record.setPowerGenerationCost(pgc);
                //计算耗油量
                double powerGenerationOil = 0d;
                if(geneGeneratorExt != null) {
                    String str = CommonUtils.convertDouble(geneGeneratorExt.getUnitFuelConsumption() * (record.getRunDuration() / 3600.0));
                    powerGenerationOil = Double.parseDouble(str);
                }
                record.setPowerGenerationOil(powerGenerationOil);
                //计算耗油量对应的钱
                double powerGenerationOilCost = 0d;
                if(record.getPowerGenerationOil() > 0) {//有油耗量才计算油量对应的钱
                    //从加油记录中获取油单价
                    double unitPrice = 0d;
                    Double up = oilboxMapper.findOilUnitPriceByGeneId(equipmentId);
                    if(up == null) {
                        log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() oilboxMapper.findOilUnitPriceByGeneId() return null. equipmentId=" + equipmentId);
                    } else {
                        unitPrice = up;
                    }
                    String str = CommonUtils.convertDouble(record.getPowerGenerationOil() * unitPrice);
                    powerGenerationOilCost = Double.parseDouble(str);
                }
                record.setPowerGenerationOilCost(powerGenerationOilCost);
                record.setEndInsertTime(CommonUtils.getCurDate());
                if(alarmChange.getSerialNo() != null) {
                    record.setEndSerialNo(alarmChange.getSerialNo());
                }
                powerGenerationMapper.updateByObject(record);
            } else {
                log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() unkown operationType: " + alarmChange.getOperationType() + ", equpmentId=" + equipmentId + "; startTime=" + alarmChange.getStartTime());
            }
        } catch (Exception e) {
            log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() throw unknown exception: ", e);
        }
    }

    /** 计算发电量对应的钱:
     *  如果设置了月单价，则用月单价；没设置则用默认单价；都没设置，按0计算 */
    private double calcPowerGenerationCost(Integer equipmentId, Date startTime, Date endTime, Double powerGeneration
            , GeneGeneratorExt geneGeneratorExt) {
        //通过endTime获取它的年的int值和月的int值
        int year = DateUtil.getCurrentYear(endTime);
        int month = CommonUtils.getCurrentMonth(endTime);
        //获取电费单价
        double unitPrice = 0d;
        List<GeneGeneratorElecUnitPrice> unitPrices = powerGenerationMapper.getMonthUnitPriceBy(equipmentId, year, month);
        if(unitPrices == null || unitPrices.isEmpty()) {//如果未配置，则取默认单价
            Double up = geneGeneratorExt == null ? null : geneGeneratorExt.getDefaultElecUnitPrice();
            if(up == null) {
                log.error("GeneBaseDataServiceImpl.calcPowerGenerationCost() geneGeneratorExt or geneGeneratorExt.getDefaultElecUnitPrice() is null. equipmentId=" + equipmentId + ", year=" + year + ", month=" + month);
            } else {
                unitPrice = up;
            }
        } else {
            if(unitPrices.size() > 1) {
                log.warn("GeneBaseDataServiceImpl.calcPowerGenerationCost() powerGenerationMapper.getMonthUnitPriceBy() return size > 1. equipmentId=" + equipmentId + ", year=" + year + ", month=" + month);
            }
            unitPrice = unitPrices.get(0).getElecUnitPrice();
        }
        String pgcStr = CommonUtils.convertDouble(powerGeneration * unitPrice);
        return Double.parseDouble(pgcStr);
    }

    /** 处理油机开机时要检测的信号是否正常 */
    @Override
    public void handleGeneDynamicSignalCheck(AlarmChange alarmChange) {
        try {
            //只处理油机开机告警
            if(!alarmChange.getBaseTypeId().equals(GENE_POWERON_ALARM_BID)) {
                log.debug("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() alarmChange baseTypeId is not match " + GENE_POWERON_ALARM_BID);
                return;
            }
            if(alarmChange.getOperationType() == null || alarmChange.getEquipmentId() == null) {
                log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() operationType or equipmentId is null");
                return;
            }
            Integer equipmentId = alarmChange.getEquipmentId();
            Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
            if(equipment == null) {
                log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() equipment not exists. equipmentId=" + equipmentId);
                return;
            }
            Integer equipmentBaseType = equipment.getEquipmentBaseType();
            if(equipmentBaseType == null) {
                log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() equipment.getEquipmentBaseType() == null. equipmentId=" + equipmentId);
                return;
            }
            if(!GENE_EQUIP_BASE_TYPES[1].contains(equipmentBaseType)) {
                log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() equipment.getEquipmentBaseType()=" + equipmentBaseType);
                return;
            }
            Integer equipmentTemplateId = equipment.getEquipmentTemplateId();
            if(equipmentTemplateId == null) {
                log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() equipment.getEquipmentBaseType() == null. equipmentTemplateId=" + equipmentId);
                return;
            }
            if(alarmChange.getStartTime() == null) {
                log.error("GeneBaseDataServiceImpl.handleGenePowerOnAlarm() alarmChange.getStartTime() == null");
                return;
            }
            List<GenePowerGenerationRecord> records = powerGenerationMapper.getRecordsBy(equipmentId, equipmentBaseType, alarmChange.getStartTime());
            if(records.size() < 1) {
                log.error("GeneBaseDataServiceImpl.handleGeneDynamicSignalCheck() alarmChange.getOperationType()=" + alarmChange.getOperationType() + ", but records.size() < 1. equpmentId=" + equipmentId + "; startTime=" + alarmChange.getStartTime());
                return;
            }
            if(records.size() > 1) {
                log.error("GeneBaseDataServiceImpl.handleGeneDynamicSignalCheck() alarmChange.getOperationType()=" + alarmChange.getOperationType() + ", but records.size() > 1. equpmentId=" + equipmentId + "; startTime=" + alarmChange.getStartTime());
                return;
            }
            //新增一个油机开机告警
            if(alarmChange.getOperationType().equals(1)){
                QueryWrapper<GeneratorSignalInfo> wrapper = new QueryWrapper<>();
                wrapper.eq("DynamicOrStatic", 1);
                wrapper.eq("GeneId", equipment.getEquipmentId());
                List<GeneratorSignalInfo> dynamicSignalInfoList = generatorSignalInfoMapper.selectList(wrapper);
                if(dynamicSignalInfoList.isEmpty())
                    return;
                generatorSignalCheckManager.SignalCheck(true,dynamicSignalInfoList,records.get(0).getSerialId());
            }
        } catch (Exception e) {
            log.error("GeneBaseDataServiceImpl.handleGeneDynamicSignalCheck() error" ,e);
        }
    }

}
