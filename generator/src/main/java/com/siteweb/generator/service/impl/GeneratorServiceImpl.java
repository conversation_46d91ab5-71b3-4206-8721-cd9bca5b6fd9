package com.siteweb.generator.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.admin.entity.RegionMap;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.RegionMapService;
import com.siteweb.admin.service.RegionService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.StringUtils;
import com.siteweb.generator.dto.*;
import com.siteweb.generator.entity.*;
import com.siteweb.generator.function.CommonUtils;
import com.siteweb.generator.mapper.GeneratorConfigApiMapper;
import com.siteweb.generator.mapper.GeneratorHistorySignalMapper;
import com.siteweb.generator.mapper.GeneratorMapper;
import com.siteweb.generator.mapper.PowerGenerationMapper;
import com.siteweb.generator.service.GeneratorConfigService;
import com.siteweb.generator.service.GeneratorService;
import com.siteweb.monitoring.dto.EquipmentDTO;
import com.siteweb.monitoring.dto.ResourceStructureTreeDTO;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.service.ResourceStructureService;
import lombok.extern.slf4j.Slf4j;
import org.javatuples.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
@Slf4j
public class GeneratorServiceImpl implements GeneratorService {

    /**  301: 燃油发电机; 310: 油箱 */
    public static final Set<Integer>[] GENE_EQUIP_BASE_TYPES = new Set[] { Set.of(301, 310) , Set.of(301), Set.of(310) };
    @Autowired
    private RegionService regionService;
    @Autowired
    private RegionMapService regionMapService;
    @Autowired
    private GeneratorConfigService generatorConfigService;

    @Autowired
    private EquipmentManager equipmentManager;
    @Autowired
    private ResourceStructureManager resourceStructureManager;
    @Autowired
    private ResourceStructureService resourceStructureService;

    @Autowired
    private GeneratorMapper generatorMapper;
    @Autowired
    private PowerGenerationMapper powerGenerationMapper;
    @Autowired
    private GeneratorHistorySignalMapper generatorHistorySignalMapper;
    @Autowired
    private GeneratorConfigApiMapper generatorConfigApiMapper;

    @Override
    public GeneGeneratorExt getExtInfoByEquipmentId(int equipmentId) {
        return generatorMapper.selectById(equipmentId);
    }

    @Override
    public ResponseEntity<ResponseResult> saveOrUpdate(GeneGeneratorExt gene) {

        if(gene == null || gene.getGeneId() == null || gene.getGeneId() < 1) {
            return ResponseHelper.failed("-10", "Param is Null or geneId is invalid", HttpStatus.OK);
        }
        if(gene.getRatedPower() == null || gene.getRatedPower() < 0) {
            return ResponseHelper.failed("-10", "RatedPower is Null or invalid", HttpStatus.OK);
        }
        if(gene.getRatedPowerConsumption() == null || gene.getRatedPowerConsumption() < 0) {
            return ResponseHelper.failed("-10", "RatedPowerConsumption is Null or invalid", HttpStatus.OK);
        }
        if(gene.getDefaultElecUnitPrice() == null || gene.getDefaultElecUnitPrice() < 0) {
            return ResponseHelper.failed("-10", "DefaultElecUnitPrice is Null or invalid", HttpStatus.OK);
        }
        Integer geneId = gene.getGeneId();
        GeneGeneratorExt geneDB = generatorMapper.selectById(geneId);
        if(geneDB == null) {//不存在，新增
            calcAndSetValue(gene);
            generatorMapper.insert(gene);
        } else {//更新
            if(!gene.getRatedPower().equals(geneDB.getRatedPower()) ||
                    !gene.getRatedPowerConsumption().equals(geneDB.getRatedPowerConsumption()) ||
                    !gene.getDefaultElecUnitPrice().equals(geneDB.getDefaultElecUnitPrice())) {
                calcAndSetValue(gene);
                generatorMapper.updateById(gene);
            }
        }
        return ResponseHelper.successful("SUCCESS");
    }

    @Override
    public boolean deleteOneGeneratorExt(int geneId) {

        try {
            generatorMapper.deleteById(geneId);
            return true;
        } catch (Exception e) {
            log.error("deleteOneGeneratorExt geneId=" + geneId + " throw Exception: ", e);
            return false;
        }
    }

    @Override
    public List<GeneGeneratorElecUnitPrice> getElecUnitPriceByGeneId(int geneId) {
        return generatorMapper.getElecUnitPriceByGeneId(geneId);
    }

    @Override
    public ResponseEntity<ResponseResult> addElecUnitPrice(GeneElecUnitPrice unitPrice) {
        if(unitPrice == null) {
            return ResponseHelper.failed("-10", "Param is Null", HttpStatus.OK);
        }
        if(unitPrice.getBatchType() == null || unitPrice.getYearMonth() == null || unitPrice.getEquipmentId() == null
            || unitPrice.getElecUnitPrice() == null) {
            return ResponseHelper.failed("-10", "Some field value is Null", HttpStatus.OK);
        }
        if(!List.of(0,1,2).contains(unitPrice.getBatchType())) {
            return ResponseHelper.failed("-10", "Invalid batchType", HttpStatus.OK);
        }
        if(unitPrice.getElecUnitPrice() <= 0) {
            return ResponseHelper.failed("-10", "Invalid elecUnitPrice", HttpStatus.OK);
        }
        String yearMonth = unitPrice.getYearMonth();
        String[] arrays = yearMonth.split("-");
        Integer year = Integer.valueOf(arrays[0]);
        Integer month = Integer.valueOf(arrays[1]);
        if(month < 1 || month > 12) {
            return ResponseHelper.failed("-10", "Invalid month(" + month + ")", HttpStatus.OK);
        }
        if(year < 1) {
            return ResponseHelper.failed("-10", "Invalid year(" + year + ")", HttpStatus.OK);
        }
        Integer userId = TokenUserUtil.getLoginUserId();
        Date curDate = CommonUtils.getCurDate();
        String batchInfo = unitPrice.getBatchType() == 0 ? null : ("BatchType" + unitPrice.getBatchType());
        List<Integer> equipIdList = findAllEquipIdByBatchType(unitPrice.getBatchType(), unitPrice.getEquipmentId(), year, month);
        if(equipIdList.size() > 0) {
            List<GeneGeneratorElecUnitPrice> list = new ArrayList<>();
            for (Integer equipmentId : equipIdList) {
                GeneGeneratorElecUnitPrice newItem = new GeneGeneratorElecUnitPrice();
                newItem.setEquipmentId(equipmentId);
                newItem.setYear(year);
                newItem.setMonth(month);
                newItem.setYearMonth(yearMonth);
                newItem.setElecUnitPrice(unitPrice.getElecUnitPrice());
                newItem.setUpdaterId(userId);
                newItem.setUpdateTime(curDate);
                newItem.setExtend1(batchInfo);
                list.add(newItem);
            }
            generatorMapper.batchInsertUnitPrice(list);
        }
        return ResponseHelper.successful("SUCCESS");
    }

    @Override
    public ResponseEntity<ResponseResult> updateElecUnitPrice(GeneElecUnitPrice unitPrice) {
        if(unitPrice == null) {
            return ResponseHelper.failed("-10", "Param is Null", HttpStatus.OK);
        }
        if(unitPrice.getSerialId() == null) {
            return ResponseHelper.failed("-10", "SerialId is Null", HttpStatus.OK);
        }
        if(StringUtils.isBlank(unitPrice.getYearMonth()) || unitPrice.getEquipmentId() == null
                || unitPrice.getElecUnitPrice() == null) {
            return ResponseHelper.failed("-10", "Some field value is Null", HttpStatus.OK);
        }
        if(unitPrice.getElecUnitPrice() <= 0) {
            return ResponseHelper.failed("-10", "Invalid elecUnitPrice", HttpStatus.OK);
        }
        String yearMonth = unitPrice.getYearMonth();
        String[] arrays = yearMonth.split("-");
        Integer year = Integer.valueOf(arrays[0]);
        Integer month = Integer.valueOf(arrays[1]);
        if(month < 1 || month > 12) {
            return ResponseHelper.failed("-10", "Invalid month(" + month + ")", HttpStatus.OK);
        }
        if(year < 1) {
            return ResponseHelper.failed("-10", "Invalid year(" + year + ")", HttpStatus.OK);
        }
        GeneGeneratorElecUnitPrice upDb = generatorMapper.getElecUnitPriceBy(unitPrice.getEquipmentId(), year, month, unitPrice.getSerialId());
        if(upDb != null) {
            return ResponseHelper.failed("-10", "Has Exists.", HttpStatus.OK);
        }
        generatorMapper.updateUnitPriceBy(unitPrice.getEquipmentId(), year, month, yearMonth, unitPrice.getElecUnitPrice(),
                unitPrice.getSerialId(), CommonUtils.getCurDate(), TokenUserUtil.getLoginUserId());
        return ResponseHelper.successful("SUCCESS");
    }

    @Override
    public boolean deleteElecUnitPrice(int serialId) {

        try {
            generatorMapper.delUnitPriceById(serialId);
            return true;
        } catch (Exception e) {
            log.error("deleteElecUnitPrice serialId=" + serialId + " throw Exception: ", e);
            return false;
        }
    }

    @Override
    public List<GeneMaintenanceRecord> getMaintenanceRecordsById(int geneId) {
        return generatorMapper.getMaintenanceRecordsById(geneId);
    }

    @Override
    public boolean delMaintenanceRecord(int serialId) {

        try {
            generatorMapper.delMaintenanceRecordById(serialId);
            return true;
        } catch (Exception e) {
            log.error("delMaintenanceRecord serialId=" + serialId + " throw Exception: ", e);
            return false;
        }
    }

    @Override
    public ResponseEntity<ResponseResult> addMaintenanceRecord(GeneMaintenanceRecord record) {
        if(record == null) {
            return ResponseHelper.failed("-10", "Param is Null", HttpStatus.OK);
        }
        if(record.getSerialId() != null) {
            return ResponseHelper.failed("-10", "Id is not null", HttpStatus.OK);
        }
        if(record.getGeneId() == null || StringUtils.isBlank(record.getMaintenanceItem()) ||
                StringUtils.isBlank(record.getMaintainer()) || StringUtils.isBlank(record.getConfirmor()) ||
                record.getMaintenanceTime() == null) {
            return ResponseHelper.failed("-10", "Some required fields is Null", HttpStatus.OK);
        }
        if(StringUtils.isBlank(record.getGeneName())) {
            Equipment gene = equipmentManager.getEquipmentById(record.getGeneId());
            record.setGeneName(gene == null ? null : gene.getEquipmentName());
        }
        record.setUpdaterId(TokenUserUtil.getLoginUserId());
        record.setUpdateTime(CommonUtils.getCurDate());
        generatorMapper.insertMaintenanceRecord(record);
        return ResponseHelper.successful("SUCCESS");
    }

    @Override
    public ResponseEntity<ResponseResult> updateMaintenanceRecord(GeneMaintenanceRecord record) {
        if(record == null) {
            return ResponseHelper.failed("-10", "Param is Null", HttpStatus.OK);
        }
        if(record.getSerialId() == null) {
            return ResponseHelper.failed("-10", "Id is null", HttpStatus.OK);
        }
        if(record.getGeneId() == null || StringUtils.isBlank(record.getMaintenanceItem()) ||
                StringUtils.isBlank(record.getMaintainer()) || StringUtils.isBlank(record.getConfirmor()) ||
                record.getMaintenanceTime() == null) {
            return ResponseHelper.failed("-10", "Some required fields is Null", HttpStatus.OK);
        }
        if(StringUtils.isBlank(record.getGeneName())) {
            Equipment gene = equipmentManager.getEquipmentById(record.getGeneId());
            record.setGeneName(gene == null ? null : gene.getEquipmentName());
        }
        record.setUpdaterId(TokenUserUtil.getLoginUserId());
        record.setUpdateTime(CommonUtils.getCurDate());
        generatorMapper.updateMaintenanceRecord(record);
        return ResponseHelper.successful("SUCCESS");
    }

    @Override
    public List<GeneResourceObjectEntity> getGeneEqResourceWithPermissions(Integer type) {

        type = (type == null || (type != 1 && type != 2)) ? 0 : type;

        Integer loginUserId = TokenUserUtil.getLoginUserId();
        //取到所有的层级资源
        List<GeneResourceObjectEntity> allResource = findAllResourceStructureByUserId(loginUserId);
        //从设备资源中过滤过油机、油箱设备
        List<GeneResourceObjectEntity> allGeneEquip = findGeneEquipmentsByUserId(loginUserId, GENE_EQUIP_BASE_TYPES[type]);
        // 提取 allGeneEquip 中所有机房的 resourceStructureId
        Set<Integer> resourceStructureIds = allGeneEquip.stream()
                .map(GeneResourceObjectEntity::getResourceStructureId)
                .collect(Collectors.toSet());
        // 筛选出所有机房
        List<GeneResourceObjectEntity> filteredRoomId = allResource.stream()
                .filter(entity -> resourceStructureIds.stream()
                        .anyMatch(id -> entity.getLevelOfPath().contains(String.valueOf(id)))).toList();
        //根据机房的levelOfPath属性找到所有机房的父节点及根节点
        Set<String> resId = filteredRoomId.stream()
                .flatMap(resource -> Arrays.stream(resource.getLevelOfPath().split("\\.")))
                .collect(Collectors.toSet());
        //筛选出所有节点
        List<GeneResourceObjectEntity> filteredRes = allResource.stream()
                .filter(entity -> resId.stream()
                        .anyMatch(id -> entity.getResourceStructureId().equals(Integer.parseInt(id)))).toList();
        //合并集合
        List<GeneResourceObjectEntity> allItem = new ArrayList<>();
        allItem.addAll(filteredRes);
        allItem.addAll(allGeneEquip);
        return allItem;
    }

    @Override
    public List<GeneMaintenanceOverview> getMaintenanceOverviewByRsId(int rsId) {

        ResourceStructure targetRs = resourceStructureManager.getResourceStructureById(rsId);
        if(targetRs == null) return new ArrayList<>();
        String rootNodeFlag = targetRs.getParentResourceStructureId().equals(0) ? "" : "%.";
        List<GeneMaintenanceOverview> records = generatorMapper.getMaintenanceOverviewByRsId(rsId, rootNodeFlag + rsId + ".%", GENE_EQUIP_BASE_TYPES[1].stream().findFirst().get());
        if(records == null || records.size() < 1) {
            return new ArrayList<>();
        } else {
            List<Integer> eqIds = records.stream().map(GeneMaintenanceOverview::getEquipmentId).toList();//本次要查询的所有设备id集合
            String geneIdStr = eqIds.stream()
                    .map(Object::toString)  // 将每个元素转换为字符串
                    .collect(Collectors.joining(", "));
            List<GenePowerGenerationRecord> noEndTimeResult = generatorConfigApiMapper.getNoEndTimeGenePowerRecord(geneIdStr);
            Map<Integer, GenePowerGenerationRecord> geneIdToPowerNoEndTimeMap = noEndTimeResult.stream()
                    .collect(Collectors.toMap(
                            GenePowerGenerationRecord::getEquipmentId,
                            GenePowerGenerationRecord -> GenePowerGenerationRecord,
                            (existingValue, newValue) -> existingValue ));
            Map<Integer, AccumulatedRuntimeData> mapRuntimes = generatorMapper.getAccumulatedRuntimeByRsId(rsId, rootNodeFlag + rsId + ".%", GENE_EQUIP_BASE_TYPES[1].stream().findFirst().get());
            Map<Integer, Pair<Integer, Integer>> configInfos = generatorConfigService.GetGeneratorMaintenanceInfo(eqIds);
            boolean isRuntimesEmpty = mapRuntimes.size() < 1;
            boolean isConfigsEmpty = configInfos.size() < 1;
            for (GeneMaintenanceOverview record : records) {
                //回填周期保养、时长保养配置数据
                if(!isConfigsEmpty) {
                    Pair<Integer, Integer> conf = configInfos.get(record.getEquipmentId());
                    if(conf != null) {
                        record.setPeriodicMaintenanceTime(conf.getValue0());
                        record.setDurationOfMaintenanceTime(conf.getValue1());
                    }
                }
                long addTime = 0L;
                long totalRunTime = 0L;
                //回填累计运行时长数据

                //如果该设备存在endTime为null的记录，则累计运行时间加上从当前时间到startTime的
                GenePowerGenerationRecord oneNoEndTimeRecord = geneIdToPowerNoEndTimeMap.get(record.getEquipmentId());
                if (oneNoEndTimeRecord != null && oneNoEndTimeRecord.getStartTime() != null) {
                    Date geneStartTime = oneNoEndTimeRecord.getStartTime();
                    Date now = new Date();
                    //如果开机记录的时间在保养时间之前
                    if (geneStartTime.compareTo(now) < 0) {
                        addTime = ((now.getTime() - geneStartTime.getTime()) / 1000);
                    }
                }

                if(!isRuntimesEmpty) {
                    AccumulatedRuntimeData runtime = mapRuntimes.get(record.getEquipmentId());
                    if(runtime != null)
                        totalRunTime = runtime.getAccumulatedRuntime();
                    totalRunTime = totalRunTime + addTime;
                    record.setRunDurationSecond(totalRunTime == 0L ? null : totalRunTime);
                }else {
                    record.setRunDurationSecond(addTime == 0L ? null : addTime);
                }
                record.calcData();
            }
            return records;
        }
    }

    @Override
    public ResourceStructureTreeDTO getGeneEqResourceNoEquipByUserId(Integer type) {

        try{
            type = (type == null || (type != 1 && type != 2)) ? 0 : type;

            Integer loginUserId = TokenUserUtil.getLoginUserId();
            //取到所有的层级资源
            List<GeneResourceObjectEntity> allResource = findAllResourceStructureByUserId(loginUserId);
            //从设备资源中过滤过油机、油箱设备
            List<GeneResourceObjectEntity> allGeneEquip = findGeneEquipmentsByUserId(loginUserId, GENE_EQUIP_BASE_TYPES[type]);
            // 提取 allGeneEquip 中所有机房的 resourceStructureId
            Set<Integer> RoomResourceStructureIds = allGeneEquip.stream()
                    .map(GeneResourceObjectEntity::getResourceStructureId)
                    .collect(Collectors.toSet());
            // 筛选出所有机房
            List<GeneResourceObjectEntity> filteredRoomId = allResource.stream()
                    .filter(entity -> RoomResourceStructureIds.stream()
                            .anyMatch(id -> entity.getLevelOfPath().contains(String.valueOf(id)))).toList();
            //根据机房的levelOfPath属性找到所有机房的父节点及根节点
            Set<String> resId = filteredRoomId.stream()
                    .flatMap(resource -> Arrays.stream(resource.getLevelOfPath().split("\\.")))
                    .collect(Collectors.toSet());

            if (CollUtil.isEmpty(resId)) {
                return null;
            }

            List<ResourceStructureTreeDTO> listRecord = new ArrayList<>(resId.size());
            Map<Integer, ResourceStructureTreeDTO> param = resourceStructureManager.getTreeDtoAll().stream().collect(Collectors.toMap(ResourceStructureTreeDTO::getId, ResourceStructureTreeDTO::new));

            if (CollUtil.isNotEmpty(param)) {
                // 循环
                Set<ResourceStructureTreeDTO> resourceStructureList = new HashSet<>(resId.size());

                for (String id : resId) {
                    ResourceStructureTreeDTO resourceStructureTreeDTO = param.get(Integer.valueOf(id));
                    if (resourceStructureTreeDTO != null) {
                        resourceStructureTreeDTO.setAccessFlag(true);
                        resourceStructureList.add(resourceStructureTreeDTO);
                    }
                }
                listRecord.addAll(resourceStructureList);
            }
            return resourceStructureManager.resourceStructureTreeDTOBuildTree(listRecord.stream()
                    .sorted(Comparator.comparing(ResourceStructureTreeDTO::getSortValue).thenComparing(ResourceStructureTreeDTO::getName))
                    .toList());
        }catch (Exception e){
            log.error("getGeneEqResourceNoEquipByUserId throw unknown exception: " , e);
        }
        return null;
    }


    /**根据节点id获取该节点下油机的历史发电记录*/
    @Override
    public List<GenePowerGenerationRecord> getGeneEquipHistoryPowerRecord(Integer objectId, Integer objectTypeId, Date startTime,Date endTime) {

        try {
            List<GenePowerGenerationRecord> powerRecords;
            //传入的节点是油机
            if (objectTypeId.equals(7)) {
                powerRecords = powerGenerationMapper.getRecordsByStartAndEndTime(objectId, 301, startTime, endTime);
            } else {
                ResourceStructure targetRs = resourceStructureManager.getResourceStructureById(objectId);
                if (targetRs == null || targetRs.getResourceStructureId() == null || targetRs.getStructureTypeId() == null)
                    return null;
                Integer loginUserId = TokenUserUtil.getLoginUserId();
                String roomIds = String.valueOf(targetRs.getResourceStructureId());
                if(targetRs.getStructureTypeId() != 5){
                    //取到所有的层级资源
                    List<GeneResourceObjectEntity> allResource = findAllResourceStructureByUserId(loginUserId);
                    //筛选出所有目标节点下的机房
                    List<GeneResourceObjectEntity> fileRooms = allResource.stream().filter(item ->
                            item.getLevelOfPath().contains(String.valueOf(targetRs.getStructureTypeId())) &&
                                    (item.getObjectTypeId().equals(105) || item.getObjectTypeId().equals(5))).toList();
                    //得到所有机房ID
                    roomIds = fileRooms.stream()
                            .map(GeneResourceObjectEntity::getObjectId)
                            .map(String::valueOf) // 将每个 id 转换为字符串
                            .collect(Collectors.joining(","));
                }
                powerRecords = powerGenerationMapper.getRecordsByRsIdAndStartAndEndTime(roomIds, startTime, endTime);
            }
            for (GenePowerGenerationRecord oneRecord : powerRecords) {
                Equipment equipment = equipmentManager.getEquipmentById(oneRecord.getEquipmentId());
                if (equipment == null)
                    continue;
                oneRecord.setExtend1(equipment.getEquipmentName());
            }
            return powerRecords;
        } catch (Exception e) {
            log.error("getGeneEquipHistoryPowerRecord error ", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<GeneHistorySignalResult> getGeneDynamicSignalHistory(Integer serialId) {
        return generatorHistorySignalMapper.getHistorySignalValueByPowerSerialId(serialId);
    }

    /** 根据批量保存选项返回本次所有的需要创建的设备id集合（此年此月此设备已存在记录的，此设备跳过，不包含在返回集合中） */
    private List<Integer> findAllEquipIdByBatchType(Integer batchType, Integer equipmentId, Integer year, Integer month) {

        List<Integer> allIds = new ArrayList<>();
        try {
            Equipment curEquip = equipmentManager.getEquipmentById(equipmentId);
            if(curEquip == null) {
                log.error("Equipment not exists. equipmentId=" + equipmentId);
                return allIds;
            }
            if(batchType == 0) {
                GeneGeneratorElecUnitPrice up = generatorMapper.getElecUnitPriceBy(equipmentId, year, month, null);
                if(up == null) {
                    allIds.add(equipmentId);
                }
            } else if(batchType == 1 || batchType ==2) {
                ResourceStructure targetRs = null;//需要查询的最上级的节点
                Integer resourceStructureId = curEquip.getResourceStructureId();
                ResourceStructure rsLevel1 = resourceStructureManager.getResourceStructureById(resourceStructureId);
                if(batchType == 1) {//本站所有油机
                    if(rsLevel1 != null) {
                        ResourceStructure rsLevel2 = resourceStructureManager.getResourceStructureById(rsLevel1.getParentResourceStructureId());
                        targetRs = rsLevel2 == null ? rsLevel1 : rsLevel2;
                    }
                } else {//平级站所有油机 batchType == 2
                    if(rsLevel1 != null) {
                        ResourceStructure rsLevel2 = resourceStructureManager.getResourceStructureById(rsLevel1.getParentResourceStructureId());
                        if(rsLevel2 == null) {
                            targetRs = rsLevel1;
                        } else {
                            ResourceStructure rsLevel3 = resourceStructureManager.getResourceStructureById(rsLevel2.getParentResourceStructureId());
                            targetRs = rsLevel3 == null ? rsLevel2 : rsLevel3;
                        }
                    }
                }
                if(targetRs == null) {
                    log.error("Equipment find super resourcestructure is null. equipmentId=" + equipmentId);
                } else {
                    Integer rsId = targetRs.getResourceStructureId();
                    String rootNodeFlag = targetRs.getParentResourceStructureId().equals(0) ? "" : "%.";
                    allIds = generatorMapper.findAllEquipIdByRsId(rsId, rootNodeFlag + rsId + ".%", year, month, GENE_EQUIP_BASE_TYPES[1].stream().findFirst().get());
                }
            }
        } catch (Exception e) {
            log.error("findAllEquipIdByBatchType throw unknown exception: " , e);
            allIds = new ArrayList<>();
        }
        return allIds;
    }

    /** 计算并赋值单位油耗；并赋值其他字段 */
    private void calcAndSetValue(GeneGeneratorExt gene) {
        Integer userId = TokenUserUtil.getLoginUserId();
        gene.setUpdaterId(userId);
        gene.setUpdateTime(CommonUtils.getCurDate());
        //计算单位油耗
        Double ratedPowerConsumption = gene.getRatedPowerConsumption();
        Double ratedPower = gene.getRatedPower();
        BigDecimal rpcBigDecimal = BigDecimal.valueOf(ratedPowerConsumption);
        BigDecimal rpBigDecimal = BigDecimal.valueOf(ratedPower);
        BigDecimal product = rpcBigDecimal.multiply(rpBigDecimal);//乘
        Double result = product.divide(BigDecimal.valueOf(0.84), 2, RoundingMode.HALF_UP).doubleValue();//除0.84 （0.84是定值）
        gene.setUnitFuelConsumption(Double.valueOf(CommonUtils.convertDouble(result)));
    }

    /** copy from ResourceStructureServiceImpl, 仅将返回值由 ResourceObjectEntity 变为了 GeneResourceObjectEntity */
    private List<GeneResourceObjectEntity> findAllResourceStructureByUserId(Integer userId) {
        List<ResourceStructure> resourceStructureList = resourceStructureService.findResourceStructureByUserId(userId);
        return resourceStructureList.stream()
                .map(resourceStructure -> {
                    Integer parentType = this.getResourceStructureType(resourceStructure.getParentResourceStructureId());
                    return new GeneResourceObjectEntity(
                            resourceStructure.getResourceStructureId(),
                            resourceStructure.getStructureTypeId(),
                            resourceStructure.getResourceStructureName(),
                            resourceStructure.getResourceStructureId(),
                            resourceStructure.getParentResourceStructureId(),
                            parentType,
                            0,
                            resourceStructure.getLevelOfPath(),
                            resourceStructure.getOriginId()); //层级资源没有 equipmentBaseType
                }).toList();
    }
    /** copy from ResourceStructureServiceImpl */
    private Integer getResourceStructureType(Integer resourceStructureId) {
        ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(resourceStructureId);
        //无类型时使用center(无真实父节点情况下)
        if (ObjectUtil.isNull(resourceStructure)) {
            return SourceType.CENTER.value();
        }
        return resourceStructure.getStructureTypeId();
    }
    /** copy from EquipmentServiceImpl */
    private List<GeneResourceObjectEntity> findGeneEquipmentsByUserId(Integer userId, Set<Integer>  geneEquipBaseType) {
        //获取拥有权限的油机类设备
        List<GeneEquipmentDTO> equipmentDTOs = this.findGeneEquipmentDTOsByUserId(userId, geneEquipBaseType);
        //构建返回对象
        List<GeneResourceObjectEntity> resourceObjectEntityList = new ArrayList<>(equipmentDTOs.size());
        for (GeneEquipmentDTO equipmentDTO : equipmentDTOs) {
            ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(equipmentDTO.getRId());
            if (ObjectUtil.isNull(resourceStructure)) {
                continue;
            }
            GeneResourceObjectEntity resourceObjectEntity = new GeneResourceObjectEntity(equipmentDTO.getEqId(),
                    SourceType.EQUIPMENT.value(), equipmentDTO.getEqName(),
                    equipmentDTO.getRId(),
                    resourceStructure.getResourceStructureId(),
                    resourceStructure.getStructureTypeId(),
                    equipmentDTO.getEquipmentBaseType(),
                    "",null);
            resourceObjectEntityList.add(resourceObjectEntity);
        }
        return resourceObjectEntityList;
    }
    /** copy from EquipmentServiceImpl：
     *  1. 将 EquipmentDTO 替换为 GeneEquipmentDTO
     *  2. 除了过滤权限，同时过滤只得到油机和油箱设备
     *  */
    private List<GeneEquipmentDTO> findGeneEquipmentDTOsByUserId(Integer userId, Set<Integer> geneEquipBaseType) {
        //拥有所有区域的权限 获取所有油机类设备
        if (Boolean.TRUE.equals(regionService.isAllRegion(userId))) {
            return equipmentManager.findEquipsByBaseTypeIds(geneEquipBaseType)
                    .stream()
                    .map(this::convertGeneEquipmentDTO)
                    .toList();
        }
        List<RegionMap> regionMapList = regionMapService.findByUserId(userId);
        List<GeneEquipmentDTO> equipmentList = new ArrayList<>();
        for (RegionMap regionMap : regionMapList) {
            //层级下所有设备权限
            if (regionMap.getEquipmentId().equals(-1)) {
                List<GeneEquipmentDTO> equipmentsByResourceStructureId = equipmentManager.findEquipmentsByResourceStructureId(regionMap.getResourceStructureId())
                        .stream()
                        .filter(eq -> geneEquipBaseType.contains(eq.getEquipmentBaseType()))
                        .map(this::convertGeneEquipmentDTO)
                        .toList();
                equipmentList.addAll(equipmentsByResourceStructureId);
                continue;
            }
            Equipment equipment = equipmentManager.getEquipmentById(regionMap.getEquipmentId());
            if(geneEquipBaseType.contains(equipment.getEquipmentBaseType())) {
                equipmentList.add(this.convertGeneEquipmentDTO(equipment));
            }
        }
        return equipmentList.stream()
                .sorted(Comparator.comparing(EquipmentDTO::getDisplayIndex).thenComparing(EquipmentDTO::getEqName))
                .toList();
    }

    private GeneEquipmentDTO convertGeneEquipmentDTO(Equipment equipment) {
        GeneEquipmentDTO dto = new GeneEquipmentDTO();
        dto.setSId(equipment.getStationId());
        dto.setEqId(equipment.getEquipmentId());
        dto.setEqName(equipment.getEquipmentName());
        dto.setRId(equipment.getResourceStructureId());
        dto.setEqCategory(equipment.getEquipmentBaseType());
        dto.setDisplayIndex(equipment.getDisplayIndex());
        dto.setEquipmentBaseType(equipment.getEquipmentBaseType());
        return dto;
    }
}
