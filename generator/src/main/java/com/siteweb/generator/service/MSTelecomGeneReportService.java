package com.siteweb.generator.service;

import com.siteweb.admin.entity.Account;
import com.siteweb.generator.dto.GenePowerGenerationStatisticsDTO;
import com.siteweb.generator.dto.GeneStationDTO;
import com.siteweb.generator.entity.GeneModifyRecord;
import com.siteweb.generator.entity.GenePowerGenerationStatistics;
import com.siteweb.generator.entity.GeneTypePowerInfo;
import com.siteweb.monitoring.entity.ResourceStructure;

import java.util.Date;
import java.util.List;

public interface MSTelecomGeneReportService {
    List<ResourceStructure> getDepartmentList();

    List<GeneStationDTO> getStationListByIds(String departmentIds);

    List<GeneTypePowerInfo> getGeneTypeList();

    List<Double> getRatedPowerList(Integer geneTypeId);

    int addGenePowerRecord(GenePowerGenerationStatistics recordInfo);

    GenePowerGenerationStatistics editGenePowerRecord(GenePowerGenerationStatistics recordInfo);

    List<GenePowerGenerationStatisticsDTO> getGenePowerRecordByStationId(String departmentIds,String stationIds, Date queryTime );

    List<GeneModifyRecord> getModifyRecordList(Integer recordId);

    GenePowerGenerationStatistics approvedFuelConsumption(GenePowerGenerationStatistics recordInfo);

    Object getPowerRecoverInterval(Date startTime, Date endTime, Integer objectId);

    List<ResourceStructure> getCountyList();

    Object getTotalPowerCutInterval(Date startTime, Date endTime, Integer objectId);
    Object getAvgPowerCutInterval(Date startTime, Date endTime, Integer objectId);

    Object getPowerGeneInterval(Date startTime, Date endTime, Integer objectId);

    Object getPowerGeneDuration(Date startTime, Date endTime, Integer objectId);

    List<Account> getAccountList();

    Object updateAccountMap(String userIds, Integer operateType);
}
