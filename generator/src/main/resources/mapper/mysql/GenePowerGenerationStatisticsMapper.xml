<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.generator.mapper.GenePowerGenerationStatisticsMapper">

    <select id="getStationById" resultType="com.siteweb.generator.dto.GeneStationDTO">
        select td.ItemValue stationCategoryName ,rr.ParentResourceStructureId,ts.* from tbl_station ts
        left join tbl_dataitem td on ts.StationCategory = td.ItemId and td.EntryId = 71
        inner join resourcestructure rr on rr.OriginId = ts.StationId and rr.structureTypeId = 104
        where stationId = #{stationId}
    </select>
    <select id="getAllStation" resultType="com.siteweb.generator.dto.GeneStationDTO">
        select td.ItemValue stationCategoryName ,rr.ParentResourceStructureId , ts.*  from tbl_station ts
        left join tbl_dataitem td on ts.StationCategory = td.ItemId and td.EntryId = 71
        inner join resourcestructure rr on rr.OriginId = ts.StationId and rr.structureTypeId = 104;
    </select>
    <select id="getGenePowerRecordByStationId"
            resultType="com.siteweb.generator.dto.GenePowerGenerationStatisticsDTO">
        <![CDATA[
        select b.ResourceStructureName departmentName,d.ItemValue stationCategoryName,c.StationName ,c.stationCategory ,c.projectName groupName,e.genetypeName,a.* ,
        CASE
            WHEN a.powerCutTime IS NOT NULL AND a.powerRecoverTime IS NULL THEN
                ROUND(TIMESTAMPDIFF(SECOND, a.powerCutTime, NOW()) / 3600, 2)
            ELSE
                a.powerCutInterval
        END AS powerCutInterval ,
        CASE
            WHEN a.powerGenerationStartTime IS NOT NULL AND a.powerGenerationEndTime IS NULL THEN
                ROUND(TIMESTAMPDIFF(SECOND, a.powerGenerationStartTime, NOW()) / 3600, 2)
            ELSE
                a.powerGenerationInterval
        END AS powerGenerationIntervalCalc
        from gene_powergenerationstatistics a
        left join resourcestructure b on a.departmentId = b.ResourceStructureId
        left join tbl_station c on a.stationId = c.stationId
        left join tbl_dataitem d  on c.StationCategory = d.ItemId and d.EntryId = 71
        left join gene_typepowerinfo e on a.geneTypeId = e.geneTypeId and a.geneRatedPower = e.ratedPower
        where a.stationId in (${stationId})
        and ((a.powerCutTime >= #{startTime} and a.powerCutTime <= #{endTime}) or a.powerCutTime is null)
        order by a.powerCutTime desc;
        ]]>
    </select>
    <select id="getAllRecord" resultType="com.siteweb.generator.dto.GenePowerGenerationStatisticsDTO">
        <![CDATA[
        select b.ResourceStructureName departmentName,d.ItemValue stationCategoryName,c.StationName ,c.stationCategory ,c.projectName groupName,e.genetypeName,a.* ,
        CASE
            WHEN a.powerCutTime IS NOT NULL AND a.powerRecoverTime IS NULL THEN
                ROUND(TIMESTAMPDIFF(SECOND, a.powerCutTime, NOW()) / 3600, 2)
            ELSE
                a.powerCutInterval
        END AS powerCutIntervalCalc ,
        CASE
            WHEN a.powerGenerationStartTime IS NOT NULL AND a.powerGenerationEndTime IS NULL THEN
                ROUND(TIMESTAMPDIFF(SECOND, a.powerGenerationStartTime, NOW()) / 3600, 2)
            ELSE
                a.powerGenerationDuration
        END AS powerGenerationDurationCalc
        from gene_powergenerationstatistics a
        left join resourcestructure b on a.departmentId = b.ResourceStructureId
        left join tbl_station c on a.stationId = c.stationId
        left join tbl_dataitem d  on c.StationCategory = d.ItemId and d.EntryId = 71
        left join gene_typepowerinfo e on a.geneTypeId = e.geneTypeId and a.geneRatedPower = e.ratedPower
        where  ((a.powerCutTime >= #{startTime} and a.powerCutTime <= #{endTime}) or a.powerCutTime is null)
        order by a.powerCutTime desc;
        ]]>
    </select>

    <select id="getGeneExistStatisticsByPowerCutInfo" resultType="com.siteweb.generator.entity.GenePowerGenerationStatistics">
        select * from gene_powergenerationstatistics where stationId = #{stationId}
        and powerCutTime is not null and PowerCutAlarmSequenceId is not null
        and powerGenerationStartTime is null and powerGenerationEndTime is null order by powerCutTime desc limit 1;
    </select>
    <select id="getGeneExistStatisticsByGeneSequenceId" resultType="com.siteweb.generator.entity.GenePowerGenerationStatistics">
        select * from gene_powergenerationstatistics where stationId = #{stationId}
        and powerGenerationAlarmSequenceId = #{sequenceId} ;
    </select>
    <select id="getGeneExistStatisticsByPowerSequenceId"
            resultType="com.siteweb.generator.entity.GenePowerGenerationStatistics">
        select * from gene_powergenerationstatistics where stationId = #{stationId}
        and powerCutAlarmSequenceId = #{sequenceId} ;
    </select>
    <select id="getGeneInfoByStation" resultType="com.siteweb.generator.entity.GeneMeiTeleStationGeneInfoMap">
        select * from gene_meitelestationgeneinfomap where stationId = #{stationId}
    </select>
    <select id="getAllAccount" resultType="com.siteweb.admin.entity.Account">
            select * from tbl_account;
    </select>
    <update id="upDateUserOperateMap">
        update tbl_account set description = #{operateType} where userId in (${userIds})
    </update>

</mapper>