<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.generator.mapper.OilLevelRecordMapper">

    <select id="getRecordByOilId" resultType="com.siteweb.generator.entity.GeneOilLevelRecord">
        SELECT go.*,ta.UserName FROM gene_oillevelrecord go
        LEFT JOIN tbl_account ta ON go.UpdateUserId = ta.UserId
        WHERE go.OilId = #{oilId} ORDER BY go.UpdateTime DESC;
    </select>
</mapper>