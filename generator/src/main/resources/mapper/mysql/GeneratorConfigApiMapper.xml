<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.generator.mapper.GeneratorConfigApiMapper">
    <select id="getOilBoxUnderGeneratorInfo" resultType="com.siteweb.generator.entity.GeneGeneratorExt">
        SELECT g.* FROM gene_generatorext g
        INNER JOIN gene_oilboxgeneratormap m ON g.geneId = m.geneId
        WHERE m.OilId = #{equipmentId};
    </select>
    <select id="getOilBoxInfo" resultType="com.siteweb.generator.entity.GeneOilboxExt">
        select * from gene_oilboxext where oilId = #{equipmentId};
    </select>
    <select id="getAllOilBoxByResourceStructureId" resultType="com.siteweb.generator.entity.GeneOilboxExt">
        select go.* from gene_oilboxext go
        inner join tbl_equipment te on go.OilId = te.EquipmentId
        inner join tbl_equipmenttemplate tet on tet.EquipmentTemplateId = te.EquipmentTemplateId
        where tet.EquipmentBaseType = 310 and te.ResourceStructureId in (${allObjectsId});
    </select>
    <select id="getAllGeneratorByResourceStructureId"
            resultType="com.siteweb.generator.entity.GeneGeneratorExt">
        select gg.GeneId,gg.RatedPower,gg.RatedPowerConsumption,gg.UnitFuelConsumption,gg.DefaultElecUnitPrice,gg.ratedFuelConsumption ,te.EquipmentName Extend1 from gene_generatorext gg
        inner join tbl_equipment te on gg.GeneId = te.EquipmentId
        inner join tbl_equipmenttemplate tet on tet.EquipmentTemplateId = te.EquipmentTemplateId
        where tet.EquipmentBaseType = 301 and te.ResourceStructureId in (${allObjectsId});
    </select>
    <select id="getGenePowerTime" resultType="com.siteweb.generator.entity.GenePowerGenerationRecord">
        <![CDATA[
        select EquipmentId,sum(PowerGeneration) PowerGeneration,sum(RunDuration) RunDuration from gene_powergenerationrecord
        where StartTime >= #{startTime} and EndTime <= #{endTime} and EquipmentId in( ${geneIds} ) group by equipmentId;
        ]]>
    </select>
    <select id="getGenePowerFee" resultType="com.siteweb.generator.entity.GeneGeneratorElecUnitPrice">
        select * from gene_generatorelecunitprice where Year = #{year} and Month = #{month} and EquipmentId In (${geneIds});
    </select>
    <select id="getGeneFuelConsumptionAndDefaultFee"
            resultType="com.siteweb.generator.entity.GeneGeneratorExt">
        select * from gene_generatorext where GeneId in (${geneIds});
    </select>
    <select id="getGeneFuelPrice" resultType="com.siteweb.generator.dto.GeneFuelPriceDTO">
        <![CDATA[
        SELECT
            res.GeneId AS equipmentId, res.RefuelTime AS lastFuelingDate, res.UnitPrice AS fuelPrice
        FROM (
            SELECT gge.GeneId , OilInfo.RefuelTime, OilInfo.UnitPrice,ROW_NUMBER() OVER(PARTITION BY gge.GeneId ORDER BY OilInfo.RefuelTime DESC) AS rowNo
                FROM gene_generatorext gge
                LEFT JOIN  ( SELECT  gom.GeneId, grr.RefuelTime, latest.UnitPrice
                                FROM  gene_oilboxgeneratormap gom
                                JOIN  ( select c.* from gene_refuelrecord c
                                JOIN  (SELECT  OilId, MAX(RefuelTime) AS latest_fueling_date
                                            FROM  gene_refuelrecord  WHERE RefuelTime<= #{endTime} GROUP BY OilId) latestRe
                                            ON c.OilId = latestRe.OilId  ) latest
                                ON  gom.OilId = latest.OilId
                                JOIN  gene_refuelrecord grr
                                ON  gom.OilId = grr.OilId AND latest.RefuelTime = grr.RefuelTime ) OilInfo
                ON gge.GeneId = OilInfo.GeneId) res
        where res.rowNo = 1;

        ]]>
    </select>
    <select id="getEquipmentBaseType" resultType="java.lang.Integer">
        SELECT tet.EquipmentBaseType FROM tbl_equipmenttemplate tet
        INNER JOIN tbl_equipment te ON tet.EquipmentTemplateid = te.EquipmentTemplateid
        WHERE te.EquipmentId = #{objectId};
    </select>
    <select id="getGeneStartStopNum" resultType="com.siteweb.generator.dto.GeneStartStopNumDTO">
        <![CDATA[
        SELECT numRes.nums startStopNum, numRes.equipmentId geneId,te.equipmentName geneName FROM tbl_equipment te
        INNER JOIN (SELECT count(EquipmentId) nums, equipmentId FROM gene_powergenerationrecord WHERE startTime >= #{startTime} AND endTime <= #{endTime} GROUP BY EquipmentId) numRes
        ON te.EquipmentId = numRes.EquipmentId
        WHERE te.EquipmentId IN (${geneIds});
        ]]>
    </select>
    <select id="GetGeneratorMaintenanceInfoByIds" resultType="com.siteweb.prealarm.entity.PreAlarmPoint">
        SELECT * FROM prealarmpoint WHERE ObjectId IN (${geneIds}) AND prealarmcategory = 6;
    </select>

    <select id="getAllGenerator" resultType="com.siteweb.monitoring.entity.Equipment">
        select a.* from tbl_equipment a
        inner join tbl_equipmentTemplate b on a.equipmenttemplateId = b.equipmenttemplateId
        where b.EquipmentBaseType = 301
    </select>
    <select id="getAllOilUnderGeneratorByOilId" resultType="com.siteweb.generator.entity.GeneOilboxExt">
        select count(oilId) Extend1, oilId from gene_oilboxgeneratormap where oilId in (${oilIds}) group by oilId;
    </select>
    <select id="getGeneOilBoxById" resultType="com.siteweb.generator.entity.GeneOilboxExt">
        select * from gene_oilboxext goe
        inner join (select * from gene_oilboxgeneratormap where GeneId = #{geneId}) oilmap
        on oilmap.OilId = goe.OilId;
    </select>
    <select id="getNoEndTimeGenePowerRecord"
            resultType="com.siteweb.generator.entity.GenePowerGenerationRecord">
        SELECT * FROM gene_powergenerationrecord
        WHERE EquipmentId IN( ${geneIds} )
        AND EquipmentBaseType = 301
        AND EndTime IS NULL;
    </select>
</mapper>