<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.generator.mapper.GeneTypePowerInfoMapper">
    <select id="selectGeneTypeList" resultType="com.siteweb.generator.entity.GeneTypePowerInfo">
        select geneTypeId,geneTypeName from gene_typepowerinfo group by geneTypeId,geneTypeName;
    </select>
    <select id="selectRatedPowerList" resultType="java.lang.Double">
        select ratedPower from gene_typepowerinfo where geneTypeId = #{geneTypeId} ;
    </select>
</mapper>