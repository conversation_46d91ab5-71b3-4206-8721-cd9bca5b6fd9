<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.generator.mapper.PowerGenerationMapper">
    <update id="updateByObject">
        UPDATE Gene_PowerGenerationRecord SET EndTime = #{endTime}, PositiveElecEnergyEnd = #{positiveElecEnergyEnd},
                PowerGeneration = #{powerGeneration}, RunDuration = #{runDuration}, PowerGenerationCost = #{powerGenerationCost},
                PowerGenerationOil = #{powerGenerationOil},PowerGenerationOilCost = #{powerGenerationOilCost},
                EndInsertTime = #{endInsertTime}, EndSerialNo = #{endSerialNo}
        WHERE SerialId = #{serialId} AND EquipmentId = #{equipmentId}
    </update>

    <select id="getRecordsBy" resultType="com.siteweb.generator.entity.GenePowerGenerationRecord">
        SELECT * FROM Gene_PowerGenerationRecord
        WHERE EquipmentId = #{equipmentId} AND EquipmentBaseType = #{equipmentBaseType} AND StartTime = #{startTime}
        ORDER BY StartTime DESC
    </select>
    <select id="getMonthUnitPriceBy" resultType="com.siteweb.generator.entity.GeneGeneratorElecUnitPrice">
        SELECT * FROM Gene_GeneratorElecUnitPrice WHERE EquipmentId = #{equipmentId} AND Year = #{year} AND Month = #{month}
    </select>
    <select id="getDefaultUnitPriceBy" resultType="java.lang.Double">
        SELECT DefaultElecUnitPrice FROM Gene_GeneratorExt WHERE GeneId = #{equipmentId}
    </select>
    <select id="getRecordsByStartAndEndTime"
            resultType="com.siteweb.generator.entity.GenePowerGenerationRecord">
        <![CDATA[
        SELECT * FROM Gene_PowerGenerationRecord
        WHERE EquipmentId = #{equipmentId}
        AND EquipmentBaseType = #{equipmentBaseType}
        AND StartTime >= #{startTime}
        AND startTime < #{endTime}
        AND (EndTime <= #{endTime} OR EndTime IS NULL)
        ORDER BY StartTime DESC
        ]]>
    </select>
    <select id="getRecordsByRsIdAndStartAndEndTime"
            resultType="com.siteweb.generator.entity.GenePowerGenerationRecord">
        <![CDATA[
        SELECT * FROM gene_powergenerationrecord WHERE EquipmentId IN
        (
            SELECT equipmentId FROM tbl_equipment e
            INNER JOIN TBL_EquipmentTemplate et
            ON et.EquipmentBaseType = 301
            AND e.EquipmentTemplateId = et.EquipmentTemplateId
            WHERE resourceStructureId IN (${rsIds})
        )
        AND startTime >= #{startTime}
        AND startTime < #{endTime}
        AND (EndTime <= #{endTime} OR EndTime IS NULL)
        ORDER BY StartTime DESC;
        ]]>
    </select>
</mapper>