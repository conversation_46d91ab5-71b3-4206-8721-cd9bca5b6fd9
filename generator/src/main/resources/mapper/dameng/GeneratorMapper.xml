<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.generator.mapper.GeneratorMapper">

    <resultMap id="accumulatedRuntimeMap" type="com.siteweb.generator.dto.AccumulatedRuntimeData">
        <result property="equipmentId" column="EquipmentId"/>
        <result property="accumulatedRuntime" column="AccumulatedRuntime"/>
    </resultMap>

    <update id="updateUnitPriceBy">
        UPDATE Gene_GeneratorElecUnitPrice SET YearMonth = #{yearMonth}, ElecUnitPrice = #{elecUnitPrice},
                Year = #{year}, Month = #{month}, UpdaterId = #{userId}, UpdateTime = #{curDate}, Extend1 = ''
        WHERE SerialId = #{serialId}
    </update>
    <update id="updateMaintenanceRecord">
        UPDATE Gene_MaintenanceRecord
          SET GeneName = #{geneName},MaintenanceTime = #{maintenanceTime},Position = #{position},
              MaintenanceItem = #{maintenanceItem},Maintainer = #{maintainer},Confirmor = #{confirmor},
              Remark = #{remark},UpdaterId = #{updaterId},UpdateTime = #{updateTime},Extend1 = #{extend1}
        WHERE SerialId = #{serialId} AND GeneId = #{geneId}
    </update>
    <delete id="delUnitPriceById">
        DELETE FROM Gene_GeneratorElecUnitPrice WHERE SerialId = #{serialId}
    </delete>
    <delete id="delMaintenanceRecordById">
        DELETE FROM Gene_MaintenanceRecord WHERE SerialId = #{serialId}
    </delete>

    <select id="getElecUnitPriceByGeneId" resultType="com.siteweb.generator.entity.GeneGeneratorElecUnitPrice">
        SELECT * FROM Gene_GeneratorElecUnitPrice WHERE EquipmentId = #{geneId} ORDER BY Year DESC, Month DESC
    </select>
    <select id="getElecUnitPriceBy" resultType="com.siteweb.generator.entity.GeneGeneratorElecUnitPrice">
        SELECT * FROM Gene_GeneratorElecUnitPrice WHERE EquipmentId = #{equipmentId} AND Year = #{year} AND Month = #{month}
        <if test="serialId != null"> AND SerialId != #{serialId} </if>
    </select>
    <select id="getMaintenanceRecordsById" resultType="com.siteweb.generator.entity.GeneMaintenanceRecord">
        SELECT * FROM Gene_MaintenanceRecord WHERE GeneId = #{geneId} ORDER BY MaintenanceTime DESC
    </select>
    <select id="findAllEquipIdByRsId" resultType="java.lang.Integer">
      SELECT
        equip.EquipmentId
      FROM
        (
            SELECT e.EquipmentId, e.ResourceStructureId FROM
            TBL_Equipment e INNER JOIN TBL_EquipmentTemplate et ON et.EquipmentBaseType = #{generatorEquipBaseTypeId} AND e.EquipmentTemplateId = et.EquipmentTemplateId
                            LEFT JOIN Gene_GeneratorElecUnitPrice gep ON e.EquipmentId = gep.EquipmentId AND gep.Year = #{year} AND gep.Month = #{month}
            WHERE gep.SerialId IS NULL
        ) equip
        INNER JOIN
        (
            SELECT ResourceStructureId FROM ResourceStructure WHERE ResourceStructureId = #{rsId} OR LevelOfPath LIKE #{rsLike}
        ) rs ON equip.ResourceStructureId = rs.ResourceStructureId
    </select>
    <select id="getMaintenanceOverviewByRsId" resultType="com.siteweb.generator.dto.GeneMaintenanceOverview">
        SELECT
            equip.EquipmentId, equip.EquipmentName, equip.StationName, equip.HouseName, equip.MaintenanceTime AS LastMaintenanceTime
        FROM
            (
                SELECT e.EquipmentId, e.EquipmentName, ts.StationName, th.HouseName, mr.MaintenanceTime,
                    ROW_NUMBER() OVER(PARTITION BY e.EquipmentId ORDER BY mr.MaintenanceTime DESC) AS rn
                FROM TBL_Equipment e
                INNER JOIN TBL_EquipmentTemplate et ON et.EquipmentBaseType = #{generatorEquipBaseTypeId} AND e.EquipmentTemplateId = et.EquipmentTemplateId
                INNER JOIN
                ( SELECT ResourceStructureId FROM ResourceStructure WHERE ResourceStructureId = #{rsId} OR LevelOfPath LIKE #{rsLike} ) rs
                    ON e.ResourceStructureId = rs.ResourceStructureId
                LEFT JOIN Gene_MaintenanceRecord mr ON e.EquipmentId = mr.GeneId
                LEFT JOIN TBL_Station ts ON e.StationId = ts.StationId
                LEFT JOIN TBL_House th ON e.StationId = th.StationId AND e.HouseId = th.HouseId
            ) equip
        WHERE equip.rn = 1
        ORDER BY equip.EquipmentId
    </select>
    <select id="getAccumulatedRuntimeByRsId" resultMap="accumulatedRuntimeMap">
        SELECT equip.EquipmentId, SUM(NVL(pgr.RunDuration, 0)) AS AccumulatedRuntime
        FROM
        (
            SELECT em.EquipmentId, em.EquipmentName, NVL(em.MaintenanceTime, '1900-01-01 00:00:00') AS LastMaintenanceTime
            FROM
            (
                SELECT e.EquipmentId,e.EquipmentName, mr.MaintenanceTime,
                        ROW_NUMBER() OVER(PARTITION BY e.EquipmentId ORDER BY mr.MaintenanceTime DESC) AS rn
                FROM
                <!-- ( SELECT EquipmentId, EquipmentName FROM TBL_Equipment WHERE EquipmentId IN (${eqIdsStr}) ) e -->
                TBL_Equipment e
                INNER JOIN TBL_EquipmentTemplate et ON et.EquipmentBaseType = #{generatorEquipBaseTypeId} AND e.EquipmentTemplateId = et.EquipmentTemplateId
                INNER JOIN
                ( SELECT ResourceStructureId FROM ResourceStructure WHERE ResourceStructureId = #{rsId} OR LevelOfPath LIKE #{rsLike} ) rs
                            ON e.ResourceStructureId = rs.ResourceStructureId
                LEFT JOIN Gene_MaintenanceRecord mr ON e.EquipmentId = mr.GeneId
            ) em
            WHERE em.rn = 1
        ) equip INNER JOIN Gene_PowerGenerationRecord pgr ON equip.EquipmentId = pgr.EquipmentId AND pgr.StartTime >= equip.LastMaintenanceTime
        GROUP BY equip.EquipmentId
    </select>

    <insert id="batchInsertUnitPrice">
        INSERT INTO Gene_GeneratorElecUnitPrice(EquipmentId, EquipmentBaseType, Year, Month, YearMonth, ElecUnitPrice, UpdaterId, UpdateTime, Extend1)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.equipmentId},#{item.equipmentBaseType},#{item.year},#{item.month},#{item.yearMonth},#{item.elecUnitPrice},#{item.updaterId},#{item.updateTime},#{item.extend1})
        </foreach>
    </insert>
    <insert id="insertMaintenanceRecord">
        INSERT INTO Gene_MaintenanceRecord(GeneId,GeneName,MaintenanceTime,Position,MaintenanceItem,Maintainer,Confirmor,
                                                Remark,UpdaterId,UpdateTime,Extend1)
        VALUES(#{geneId},#{geneName},#{maintenanceTime},#{position},#{maintenanceItem},#{maintainer},#{confirmor},
                        #{remark},#{updaterId},#{updateTime},#{extend1})
    </insert>


</mapper>