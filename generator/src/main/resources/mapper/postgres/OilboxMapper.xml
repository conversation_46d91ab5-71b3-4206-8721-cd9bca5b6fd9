<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.generator.mapper.OilboxMapper">

    <update id="updateRefuelRecord">
        UPDATE Gene_RefuelRecord
            SET OilName = #{oilName}, RefuelTime = #{refuelTime}, RefuelQuantity = #{refuelQuantity},
                UnitPrice = #{unitPrice}, RefuelFee = #{refuelFee}, Operator = #{operator},
                UpdaterId = #{updaterId}, UpdateTime = #{updateTime}, Extend1 = #{extend1}
        WHERE SerialId = #{serialId} AND OilId = #{oilId}
    </update>

    <delete id="delMapsByOilId">
        DELETE FROM Gene_OilboxGeneratorMap WHERE OilId = #{oilId}
    </delete>
    <delete id="delRefuelRecordById">
        DELETE FROM Gene_RefuelRecord WHERE SerialId = #{serialId}
    </delete>

    <select id="getGeneIdsByOilId" resultType="java.lang.Integer">
        SELECT GeneId FROM Gene_OilboxGeneratorMap WHERE OilId = #{oilId}
    </select>
    <select id="getGeneEquipsByOilId" resultType="com.siteweb.generator.dto.OilboxMapGene">
      SELECT map.OilId, map.GeneId, geneEq.EquipmentName as GeneName,
                rs.ResourceStructureName, et.EquipmentBaseType
        FROM
        ( SELECT OilId, GeneId FROM Gene_OilboxGeneratorMap WHERE OilId = #{oilId} ) map
            LEFT JOIN TBL_Equipment geneEq ON map.GeneId = geneEq.EquipmentId
            LEFT JOIN TBL_EquipmentTemplate et ON geneEq.EquipmentTemplateId = et.EquipmentTemplateId
            LEFT JOIN ResourceStructure rs ON geneEq.ResourceStructureId = rs.ResourceStructureId
    </select>
    <select id="getRefuelRecordsByOilId" resultType="com.siteweb.generator.entity.GeneRefuelRecord">
        SELECT * FROM Gene_RefuelRecord WHERE OilId = #{oilId}
        <if test="startTime != null"> AND RefuelTime >= #{startTime} </if>
        <if test="endTime != null"> AND RefuelTime &lt;= #{endTime} </if>
        ORDER BY RefuelTime DESC
    </select>
    <select id="findMapGeneratorsByRsId" resultType="com.siteweb.generator.dto.OilboxMapGene">
        SELECT e.EquipmentId AS GeneId, e.EquipmentName AS GeneName, e.ResourceStructureId, rs.ResourceStructureName
        FROM TBL_Equipment e
             INNER JOIN TBL_EquipmentTemplate et ON et.EquipmentBaseType = #{generatorEquipBaseTypeId} AND e.EquipmentTemplateId = et.EquipmentTemplateId
             INNER JOIN
             (
               SELECT ResourceStructureId, ResourceStructureName FROM ResourceStructure WHERE ResourceStructureId = #{rsId} OR LevelOfPath LIKE #{rsLike}
             ) rs ON e.ResourceStructureId = rs.ResourceStructureId
        ORDER BY e.EquipmentId
    </select>
    <select id="findOilUnitPriceByGeneId" resultType="java.lang.Double">
        SELECT UnitPrice
        FROM
        Gene_RefuelRecord rr INNER JOIN
        (SELECT OilId FROM Gene_OilboxGeneratorMap WHERE GeneId = #{equipmentId}) map ON rr.OilId=map.OilId
        ORDER BY rr.RefuelTime DESC LIMIT 1
    </select>

    <insert id="batchInsertMaps">
        INSERT INTO Gene_OilboxGeneratorMap(OilId, GeneId, UpdaterId, UpdateTime, Extend1)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.oilId},#{item.geneId},#{item.updaterId},#{item.updateTime},#{item.extend1})
        </foreach>
    </insert>
    <insert id="insertRefuelRecord">
        INSERT INTO Gene_RefuelRecord(OilId, OilName, RefuelTime, RefuelQuantity, RefuelFee, UnitPrice, UpdaterId, UpdateTime, Operator, Extend1)
        VALUES(#{oilId},#{oilName},#{refuelTime},#{refuelQuantity},#{refuelFee},#{unitPrice},#{updaterId},#{updateTime}, #{operator}, #{extend1})
    </insert>
</mapper>