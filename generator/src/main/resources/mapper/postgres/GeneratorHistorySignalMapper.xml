<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.generator.mapper.GeneratorHistorySignalMapper">

    <select id="getLastHistorySignalValue" resultType="com.siteweb.generator.entity.GeneHistorySignal">
        SELECT *
        FROM (SELECT *,ROW_NUMBER() OVER(PARTITION BY geneIdAndSignalId ORDER BY InsertTime DESC) AS rowNo
            FROM  gene_generatorhistorysignal
            WHERE signalValue IS NOT NULL AND signalValid = 0) rn
        WHERE rn.rowNo =1;
    </select>
    <select id="getHistorySignalValueByPowerSerialId"
            resultType="com.siteweb.generator.entity.GeneHistorySignalResult">
        SELECT gghs.*,ggci.SignalName AS signalName,te.equipmentName AS equipmentName FROM gene_generatorhistorysignal  gghs
        INNER JOIN gene_generatorsignalcheckinfo ggci ON gghs.GeneId = ggci.GeneId AND gghs.SignalId = ggci.SignalId AND ggci.DynamicOrStatic = 1
        LEFT JOIN tbl_equipment te ON gghs.GeneId = te.EquipmentId
        WHERE gghs.PowerSerialId = #{serialId};
    </select>
</mapper>