package com.siteweb.complexindex.dto;

import com.siteweb.complexindex.entity.ComplexIndexDefinition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 指标业务类型请求体
 * @author: lzy
 * @creat: 2022/4/25 18:04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("指标业务类型请求体")
public class ComplexIndexBusinessTypeDTO {

    /**
     * id
     */
    @ApiModelProperty("id")
    private Integer businessTypeId;

    /**
     * 业务分类名称
     */
    @ApiModelProperty("业务分类名称")
    private String businessTypeName;

    /**
     * 父id
     */
    @ApiModelProperty("父id")
    private Integer parentId;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String description;

    /**
     * 场景id
     */
    @ApiModelProperty("场景id")
    private Integer sceneId;

    /**
     * 资源类型id
     */
    @ApiModelProperty("资源类型id")
    private Integer objectTypeId;

    /**
     * 指标定义集合
     */
    @ApiModelProperty("指标定义集合")
    private List<ComplexIndexDefinition> complexIndexDefinitions;
}
