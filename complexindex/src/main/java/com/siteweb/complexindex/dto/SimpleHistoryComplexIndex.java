package com.siteweb.complexindex.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.influxdb.annotation.Column;
import org.influxdb.annotation.Measurement;

@Data
@NoArgsConstructor
@Measurement(name = "HistoryComplexIndex")
public class SimpleHistoryComplexIndex {
    @Column(name = "time")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String  time;

    @Column(name = "IndexValue", tag = true)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    public String indexValue;

    public String sampleTime;

    public String getTime(){
        String tempTime = this.time;
        if(tempTime!=null && !tempTime.equals("")){
            tempTime = tempTime.substring(0, 10) + " " + tempTime.substring(11, 19);
        }
        return tempTime;
    }
}
