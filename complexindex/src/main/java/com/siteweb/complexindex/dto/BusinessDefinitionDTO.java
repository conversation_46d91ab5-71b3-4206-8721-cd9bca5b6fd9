package com.siteweb.complexindex.dto;

import com.siteweb.complexindex.entity.ComplexIndexDefinition;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 指标业务类型请求体
 * @author: cy
 * @creat: 2022/7/14 13:04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("指标类型请求体")
public class BusinessDefinitionDTO {
    private Integer businessTypeId;
    private Integer complexIndexDefinitionId;
    private String complexIndexDefinitionName;
    private Integer complexIndexDefinitionTypeId;
}
