package com.siteweb.energy.controller;


import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.energy.dto.EnergyRatingDataParams;
import com.siteweb.energy.entity.EnergyRatingConfig;
import com.siteweb.energy.service.EnergyRatingConfigService;
import com.siteweb.energy.service.EnergyRatingDataService;
import io.swagger.annotations.Api;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api/energy")
@Api(value = "EnergyRatingConfigController", tags = "等级评定配置相关接口")
public class EnergyRatingConfigController {

    @Autowired
    private EnergyRatingConfigService energyRatingConfigService;

    @ApiOperation(value = "获取某个等级评定配置详情")
    @GetMapping(value = "/ratingConfig", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEnergyRatingConfig(@ApiParam(name = "configId", value = "configId", required = true) Integer configId) {
        return ResponseHelper.successful(energyRatingConfigService.getEnergyRatingExByConfigId(configId));
    }

    @ApiOperation(value = "获取等级评定配置")
    @GetMapping(value = "/ratingConfigList", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEnergyRatingConfigList(@ApiParam(name = "objectId", value = "objectId") Integer objectId) {
        return ResponseHelper.successful(energyRatingConfigService.getEnergyRatingConfigList(objectId));
    }

    @ApiOperation(value = "新增等级评定配置")
    @PostMapping(value = "/ratingConfig", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> addEnergyRatingConfig(@RequestBody EnergyRatingConfig config) {
        return ResponseHelper.successful(energyRatingConfigService.addEnergyRatingConfig(config));
    }

    @ApiOperation(value = "修改等级评定配置")
    @PutMapping(value = "/ratingConfig", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> modifyEnergyRatingConfig(@RequestBody EnergyRatingConfig config) {
        return ResponseHelper.successful(energyRatingConfigService.modifyEnergyRatingConfig(config));
    }

    @ApiOperation(value = "删除等级评定配置")
    @DeleteMapping (value = "/ratingConfig/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteEnergyRatingConfig(@PathVariable Integer id) {
        return ResponseHelper.successful(energyRatingConfigService.deleteEnergyRatingConfig(id));
    }

    @ApiOperation(value = "计算能耗等级评定")
    @GetMapping(value = "/ratingResult", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEnergyRatingResult(@ApiParam(name = "configId", value = "configId", required = true) Integer configId) {
        return ResponseHelper.successful(energyRatingConfigService.getEnergyRatingResult(configId));
    }

    @ApiOperation(value = "计算能耗等级评定导出")
    @GetMapping(value = "/ratingResultTable", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEnergyRatingResultTable(@ApiParam(name = "configId", value = "configId", required = true) Integer configId) {
        return ResponseHelper.successful(energyRatingConfigService.getEnergyRatingResultTable(configId));
    }
}
