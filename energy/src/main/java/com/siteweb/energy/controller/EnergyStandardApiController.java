package com.siteweb.energy.controller;

import cn.hutool.core.date.DateTime;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.DateUtil;
import com.siteweb.energy.dto.*;
import com.siteweb.energy.service.EnergyStandardApiService;
import com.siteweb.energy.service.EnergyTotalAnalysisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api/energy/energyapi")
@Api(value = "EnergyStandardApiController",tags = "能耗数据标准接口")
public class EnergyStandardApiController {

    @Autowired
    EnergyStandardApiService energyStandardApiService;
    @Autowired
    private EnergyTotalAnalysisService energyTotalAnalysisService;

    @ApiOperation("外部层级结构及其指标配置接口")
    @GetMapping(value = "/energymodel",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getResourceStructureComplexIndexIds(){
        return ResponseHelper.successful(energyStandardApiService.getResourceStructureComplexIndexIds());
    }

    @ApiOperation("外部获取能源类型列表")
    @GetMapping(value = "/energyType",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEnergyTypeList(){
        return ResponseHelper.successful(energyStandardApiService.getEnergyTypeList());
    }

    @ApiOperation("用能总量接口")
    @GetMapping(value = "/usage/total",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getTotalEnergyUse(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                            @RequestParam Integer objectId,
                                                            @RequestParam Integer objectTypeId,
                                                            @RequestParam Integer businessTypeId,
                                                            @RequestParam String timeType){
        return ResponseHelper.successful(energyStandardApiService.getTotalEnergyUse(objectId, objectTypeId, businessTypeId, timeType, startTime, endTime));
    }

    @ApiOperation("用能总量趋势接口")
    @GetMapping(value = "/usage/total/trends",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getTotalEnergyUseTrends(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                            @RequestParam Integer objectId,
                                                            @RequestParam Integer objectTypeId,
                                                            @RequestParam Integer businessTypeId,
                                                            @RequestParam String timeType){
        return ResponseHelper.successful(energyStandardApiService.getTotalEnergyUseTrends(objectId,objectTypeId,businessTypeId,timeType,startTime,endTime));
    }

    @ApiOperation("外部用能总量接口")
    @GetMapping(value = "/out/usage/total",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getTotalEnergyUseOut(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                            @RequestParam Integer nodeId,
                                                            @RequestParam Integer nodeTypeId,
                                                            @RequestParam Integer energyTypeId,
                                                            @RequestParam String timeType){
        EnergySdkTotalResult totalEnergyUse = energyStandardApiService.getTotalEnergyUse(nodeId, nodeTypeId, energyTypeId, timeType, startTime, endTime);
        return ResponseHelper.successful(new EnergySdkOutTotalResult(totalEnergyUse));
    }

    @ApiOperation("外部用能总量趋势接口")
    @GetMapping(value = "/out/usage/total/trends",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getTotalEnergyUseTrendsOut(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                                  @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                                  @RequestParam Integer nodeId,
                                                                  @RequestParam Integer nodeTypeId,
                                                                  @RequestParam Integer energyTypeId,
                                                                  @RequestParam String timeType){
        EnergySdkTotalTrendsResult totalEnergyUseTrends = energyStandardApiService.getTotalEnergyUseTrends(nodeId, nodeTypeId, energyTypeId, timeType, startTime, endTime);
        return ResponseHelper.successful(new EnergySdkOutTotalTrendsResult(totalEnergyUseTrends));
    }

    @ApiOperation("用能效率接口")
    @GetMapping(value = "/usage/efficiency",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEfficiencyEnergyUse(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                                 @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                                 @RequestParam Integer objectId,
                                                                 @RequestParam Integer objectTypeId,
                                                                 @RequestParam Integer businessTypeId,
                                                                 @RequestParam String timeType){
        return ResponseHelper.successful(energyStandardApiService.getEfficiencyEnergyUse(objectId,objectTypeId,businessTypeId,timeType,startTime,endTime));
    }


    @ApiOperation("外部用能效率接口")
    @GetMapping(value = "/out/usage/efficiency",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEfficiencyEnergyUseOut(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                                    @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                                    @RequestParam Integer nodeId,
                                                                    @RequestParam Integer nodeTypeId,
                                                                    @RequestParam Integer energyTypeId,
                                                                    @RequestParam String timeType){
        EnergySdkBasicResult efficiencyEnergyUse = energyStandardApiService.getEfficiencyEnergyUse(nodeId, nodeTypeId, energyTypeId, timeType, startTime, endTime);
        return ResponseHelper.successful(new EnergySdkOutBasicResult(efficiencyEnergyUse));
    }

    @ApiOperation("用能效率趋势接口")
    @GetMapping(value = "/usage/efficiency/trends",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEfficiencyEnergyUseTrends(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                                 @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                                 @RequestParam Integer objectId,
                                                                 @RequestParam Integer objectTypeId,
                                                                 @RequestParam Integer businessTypeId,
                                                                 @RequestParam String timeType){
        return ResponseHelper.successful(energyStandardApiService.getEfficiencyEnergyUseTrends(objectId,objectTypeId,businessTypeId,timeType,startTime,endTime));
    }

    @ApiOperation("用能分项接口")
    @GetMapping(value = "/usage/options",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getOptionsEnergyUse(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                                       @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                                       @RequestParam Integer objectId,
                                                                       @RequestParam Integer objectTypeId,
                                                                       @RequestParam Integer businessTypeId,
                                                                       @RequestParam String timeType){
        return ResponseHelper.successful(energyStandardApiService.getOptionsEnergyUse(objectId,objectTypeId,businessTypeId,timeType,startTime,endTime));
    }

    @ApiOperation("用能分项趋势接口")
    @GetMapping(value = "/usage/options/trends",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getOptionsEnergyUseTrends(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                              @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                              @RequestParam Integer objectId,
                                                              @RequestParam Integer objectTypeId,
                                                              @RequestParam Integer businessTypeId,
                                                              @RequestParam String timeType){
        return ResponseHelper.successful(energyStandardApiService.getOptionsEnergyUseTrends(objectId,objectTypeId,businessTypeId,timeType,startTime,endTime));
    }

    @ApiOperation("外部用能分项接口")
    @GetMapping(value = "/out/usage/options",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getOptionsEnergyUseOut(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                              @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                              @RequestParam Integer nodeId,
                                                              @RequestParam Integer nodeTypeId,
                                                              @RequestParam Integer energyTypeId,
                                                              @RequestParam String timeType){
        EnergySdkBasicResult optionsEnergyUse = energyStandardApiService.getOptionsEnergyUse(nodeId, nodeTypeId, energyTypeId, timeType, startTime, endTime);
        return ResponseHelper.successful(new EnergySdkOutBasicResult(optionsEnergyUse));
    }

    @ApiOperation("外部用能分项趋势接口")
    @GetMapping(value = "/out/usage/options/trends",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getOptionsEnergyUseTrendsOut(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                                    @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                                    @RequestParam Integer nodeId,
                                                                    @RequestParam Integer nodeTypeId,
                                                                    @RequestParam Integer energyTypeId,
                                                                    @RequestParam String timeType){
        EnergySdkBasicTrendsResult optionsEnergyUseTrends = energyStandardApiService.getOptionsEnergyUseTrends(nodeId, nodeTypeId, energyTypeId, timeType, startTime, endTime);
        return ResponseHelper.successful( new EnergySdkOutBasicTrendsResult(optionsEnergyUseTrends));
    }

    @ApiOperation("根据complexIndexDefinitionTypeId获取用能数据")
    @GetMapping(value = "/usage/definitiontype",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEnergyUseByComplexIndexDefinitionTypeId(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                                    @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                                    @RequestParam Integer objectId,
                                                                    @RequestParam Integer objectTypeId,
                                                                    @RequestParam Integer businessTypeId,
                                                                    @RequestParam String timeType,
                                                                    @RequestParam Integer complexIndexDefinitionTypeId){
        return ResponseHelper.successful(energyStandardApiService.getEnergyUseByComplexIndexDefinitionTypeId(objectId,objectTypeId,businessTypeId,timeType,startTime,endTime,complexIndexDefinitionTypeId));
    }

    @ApiOperation("根据complexIndexDefinitionTypeId获取用能趋势数据")
    @GetMapping(value = "/usage/trends/definitiontype",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEnergyUseTrendByComplexIndexDefinitionTypeId(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                                                           @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                                                           @RequestParam Integer objectId,
                                                                                           @RequestParam Integer objectTypeId,
                                                                                           @RequestParam Integer businessTypeId,
                                                                                           @RequestParam String timeType,
                                                                                           @RequestParam Integer complexIndexDefinitionTypeId){
        return ResponseHelper.successful(energyStandardApiService.getEnergyUseTrendByComplexIndexDefinitionTypeId(objectId,objectTypeId,businessTypeId,timeType,startTime,endTime,complexIndexDefinitionTypeId));
    }


    @ApiOperation("子节点用能分项接口")
    @GetMapping(value = "/usage/options/child",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getOptionsEnergyUseChild(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                                                          @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                                                          @RequestParam Integer objectId,
                                                                                          @RequestParam Integer objectTypeId,
                                                                                          @RequestParam Integer businessTypeId,
                                                                                          @RequestParam String timeType){
        return ResponseHelper.successful(energyStandardApiService.getOptionsEnergyUseChild(objectId,objectTypeId,businessTypeId,timeType,startTime,endTime));
    }

    @ApiOperation("子节点用能分项趋势接口")
    @GetMapping(value = "/usage/options/trends/child",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getOptionsEnergyUseTrendsChild(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                                   @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                                   @RequestParam Integer objectId,
                                                                   @RequestParam Integer objectTypeId,
                                                                   @RequestParam Integer businessTypeId,
                                                                   @RequestParam String timeType){
        return ResponseHelper.successful(energyStandardApiService.getOptionsEnergyUseTrendsChild(objectId,objectTypeId,businessTypeId,timeType,startTime,endTime));
    }

    @ApiOperation("指标时间段总量接口")
    @GetMapping(value = "/usage/complexindexs",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getComplexIndexsValue(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                                @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                                @RequestParam String timeType,
                                                                @RequestParam Integer[] complexIndexIds){
        return ResponseHelper.successful(energyStandardApiService.getComplexIndexsValue(complexIndexIds,timeType,startTime,endTime));
    }

    @ApiOperation("指标时间段总量趋势接口")
    @GetMapping(value = "/usage/complexindexs/trends",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getComplexIndexsValueTrends(@RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                                @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                                @RequestParam String timeType,
                                                                @RequestParam Integer[] complexIndexIds){
        return ResponseHelper.successful(energyStandardApiService.getComplexIndexsValueTrends(complexIndexIds,timeType,startTime,endTime));
    }

    @ApiOperation("用能总量同环比接口")
    @GetMapping(value = "/energyTotalYoY",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEnergyTotalYoY(
            @RequestParam int businessTypeId,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam Integer objectId,
            @RequestParam Integer objectTypeId,
            @RequestParam String timeType){
        return ResponseHelper.successful(energyStandardApiService.calculationEnergyYOY(objectId,objectTypeId,businessTypeId,timeType,startTime,endTime));
    }

    @ApiOperation("绿能总量")
    @GetMapping(value = "/greenEnergyTotal",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getGreenEnergyTotal(
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam Integer objectId,
            @RequestParam Integer objectTypeId,
            @RequestParam String timeType){
        return ResponseHelper.successful(energyStandardApiService.getGreenEnergyTotal(objectId,objectTypeId,timeType,startTime,endTime));
    }
    @ApiOperation("绿能趋势")
    @GetMapping(value = "/greenEnergyTrend",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getGreenEnergyTrend(
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam Integer objectId,
            @RequestParam Integer objectTypeId,
            @RequestParam String timeType){
        return ResponseHelper.successful(energyStandardApiService.getGreenEnergyTrend(objectId,objectTypeId,timeType,startTime,endTime));
    }
    @ApiOperation("绿能占比")
    @GetMapping(value = "/greenEnergyProportion",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getGreenEnergyProportion(
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam Integer objectId,
            @RequestParam Integer objectTypeId,
            @RequestParam String timeType){
        return ResponseHelper.successful(energyStandardApiService.getGreenEnergyProportion(objectId,objectTypeId,timeType,startTime,endTime));
    }


    @ApiOperation("碳排放总量")
    @GetMapping(value = "/totalCarbonEmissions",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getGreenEnergyTotal(
            @RequestParam int businessTypeId,//businesstypeId为-1返回所有非绿能的碳排放总量
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam Integer objectId,
            @RequestParam Integer objectTypeId,
            @RequestParam String timeType){
        return ResponseHelper.successful(energyStandardApiService.getTotalCarbonEmissions(businessTypeId,objectId,objectTypeId,timeType,startTime,endTime));
    }

    @ApiOperation("碳排放趋势")
    @GetMapping(value = "/totalCarbonTrend",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCarbonTrend(
            @RequestParam int businessTypeId,//businessTypeId为-1返回所有非绿能的碳排放总量
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam Integer objectId,
            @RequestParam Integer objectTypeId,
            @RequestParam String timeType){
        return ResponseHelper.successful(energyStandardApiService.getCarbonTrend(businessTypeId,objectId,objectTypeId,timeType,startTime,endTime));
    }
    @ApiOperation("碳排放进度")
    @GetMapping(value = "/carbonEmissionsProgress",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCarbonEmissionsProgress(
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date startTime,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date endTime,
            @RequestParam Integer objectId,
            @RequestParam Integer objectTypeId,
            @RequestParam String timeType){
        return ResponseHelper.successful(energyStandardApiService.getCarbonEmissionsProgress(objectId,objectTypeId,timeType,startTime,endTime));
    }

    @ApiOperation("总碳强度比例")
    @GetMapping(value = "/carbonIntensityRatio",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCarbonIntensityRatio(
            @RequestParam Integer objectId,
            @RequestParam Integer objectTypeId,
            @RequestParam String timeType,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date startTime
            ){
        return ResponseHelper.successful(energyStandardApiService.getCarbonIntensityRatio(objectId,objectTypeId,timeType,startTime));
    }
    @ApiOperation("各用能强度比例")
    @GetMapping(value = "/everyCarbonIntensityRatio",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEveryCarbonIntensityRatio(
            @RequestParam Integer objectId,
            @RequestParam Integer objectTypeId,
            @RequestParam String timeType,
            @RequestParam @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date startTime
    ){
        return ResponseHelper.successful(energyStandardApiService.getEveryCarbonIntensityRatio(objectId,objectTypeId,timeType,startTime));
    }


    @ApiOperation("各用能强度比例")
    @GetMapping(value = "/everyCarbonIntensityRatio1",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getEveryCarbonIntensityRatio1(
             Integer objectId,
             Integer objectTypeId,
             String timeType,
             @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date startTime
    ){
        return ResponseHelper.successful(energyStandardApiService.getEveryCarbonIntensityRatio(objectId,objectTypeId,timeType,startTime));
    }

    @ApiOperation(value = "用能&碳排放统计（组态）")
    @GetMapping(value = "/useEnergyAndCarbonTotal", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findUseEnergyAndCarbonTotal(
        @ApiParam(name = "startTime", value = "开始时间", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
        @ApiParam(name = "endTime", value = "结束时间", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
        @ApiParam(name = "objectId", value = "节点Id", required = false) Integer objectId,
        @ApiParam(name = "businessTypeId", value = "能源类型id", required = false) Integer businessTypeId,
        @ApiParam(name = "timeType", value = "能源类型id", required = false) String timeType,
        @ApiParam(name = "userId", value = "用户id", required = false) Integer userId
    ) {
        return ResponseHelper.successful(energyTotalAnalysisService.findUseEnergyAndCarbonTotal(startTime, endTime, objectId, businessTypeId, userId));
    }

    @ApiOperation(value = "用能趋势分析（组态）")
    @GetMapping(value = "/useElectricityTrendAnalysis", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findUseElectricityTrendAnalysis(
        @ApiParam(name = "startTime", value = "开始时间", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
        @ApiParam(name = "endTime", value = "结束时间", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
        @ApiParam(name = "objectId", value = "节点Id", required = false) Integer objectId,
        @ApiParam(name = "businessTypeId", value = "能源类型id", required = false) Integer businessTypeId,
        @ApiParam(name = "timeType", value = "能源类型id", required = false) String timeType,
        @ApiParam(name = "userId", value = "用户id", required = false) Integer userId
    ) {
        return ResponseHelper.successful(energyTotalAnalysisService.findUseElectricityTrendAnalysis(startTime, endTime, objectId, businessTypeId,timeType,userId));
    }




    //××××××××××××××××××××××××××××××××××××××××××××××××××××权限接口×××××××××××××××××××××××××××××××××××××××××××××××××//
    @ApiOperation("用能碳总量接口(权限)")
    @GetMapping(value = "/usage/totalOfRole",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getTotalEnergyUseOfRole(
            @ApiParam(name = "businessTypeId", value = "能源类型", required = true)Integer businessTypeId,
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(energyStandardApiService.getTotalEnergyUseOfRole(businessTypeId, timeType, startTime, endTime, userId));
    }

    @ApiOperation("子节点用能碳总量排序接口(权限)")
    @GetMapping(value = "/usage/childTotalOfRole",produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getChildTotalEnergyUseOfRole(
            @ApiParam(name = "childObjectTypeId", value = "要排序的子节点的层级类型", required = true)Integer childObjectTypeId,
            @ApiParam(name = "businessTypeId", value = "能源类型", required = true)Integer businessTypeId,
            @ApiParam(name = "timeType", value = "时间类型", required = true) String timeType,
            @ApiParam(name = "userId", value = "用户Id", required = true)Integer userId
    ){
        Date startTime = setStartTimeByTimeType(timeType);
        Date endTime = setEndTimeByTimeType(startTime,timeType);
        return ResponseHelper.successful(energyStandardApiService.getChildTotalEnergyUseOfRole(childObjectTypeId,businessTypeId, timeType, startTime, endTime, userId));
    }





    // 如果接口没有传开始时间则按照时间类型，自动赋值本日本月本年
    public Date setStartTimeByTimeType(String timeType){
        Date startTime = new Date();
        switch (timeType){
            case "d" :
                startTime = DateUtil.getTodayStartTime().getTime();
                break;
            case "m":
                startTime = DateUtil.getFirstDayOfMonth(DateTime.now());
                break;
            case "y":
                startTime = DateUtil.dateAddMonth(DateUtil.getNextYearFirstDay(DateTime.now()),-12);
                break;
        }
        return startTime;
    }
    public Date setEndTimeByTimeType(Date startTime,String timeType){
        Date endTime = new Date();
        switch (timeType){
            case "d" :
                endTime = DateUtil.getLastSecondsOfToday(startTime);
                break;
            case "m":
                endTime = DateUtil.getLastDayOfMonth(DateTime.now());
                break;
            case "y":
                endTime = DateUtil.dateAddSeconds(DateUtil.getNextYearFirstDay(DateTime.now()),-1);
                break;
        }

        return endTime;
    }

    //*************************************************组态看板接口××××××××××××××××××××××××××××××××××××××××××××××××××××××××××××//
    @ApiOperation(value = "查询历史能耗指标数据")
    @GetMapping(value = "/historycomplexindexs", params = {"complexIndexIds","tableType","startTime", "endTime"},
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findHistoryComplexIndexsByIdAndDuration(
            @ApiParam(name = "complexIndexIds", value = "指标ID", required = true) @RequestParam List<Integer> complexIndexIds,
            @ApiParam(name = "tableType", value = "年月日趋势参数ymd", required = false) @RequestParam String tableType,
            @ApiParam(name = "startTime", value = "开始时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @ApiParam(name = "endTime", value = "结束时间", required = true) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime){
        return ResponseHelper.successful(energyStandardApiService.findHistoryComplexIndexsByIdAndDuration(complexIndexIds,tableType,startTime, endTime));
    }


}