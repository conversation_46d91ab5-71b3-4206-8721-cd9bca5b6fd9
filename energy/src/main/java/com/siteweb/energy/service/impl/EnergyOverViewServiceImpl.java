package com.siteweb.energy.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.math.Calculator;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.datatype.jsr310.DecimalUtils;
import com.siteweb.common.influxdb.InfluxDBManager;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.complexindex.entity.*;
import com.siteweb.complexindex.manager.HistoryComplexIndexManager;
import com.siteweb.complexindex.manager.LiveComplexIndexManager;
import com.siteweb.complexindex.service.BusinessDefinitionMapService;
import com.siteweb.complexindex.service.ComplexIndexBusinessTypeService;
import com.siteweb.complexindex.service.ComplexIndexService;
import com.siteweb.computerrack.entity.ComputerRack;
import com.siteweb.computerrack.entity.ITDevice;
import com.siteweb.computerrack.service.ComputerRackService;
import com.siteweb.computerrack.service.ITDeviceService;
import com.siteweb.energy.dto.*;
import com.siteweb.energy.entity.*;
import com.siteweb.energy.enumeration.SourceCategory;
import com.siteweb.energy.manager.EnergyComplexIndexManager;
import com.siteweb.energy.mapper.EnergyCustomerConfigMapper;
import com.siteweb.energy.mapper.EnergyObjectMapMapper;
import com.siteweb.energy.model.EnergyResourceStructure;
import com.siteweb.energy.service.*;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.prealarm.entity.PreAlarm;
import com.siteweb.prealarm.entity.PreAlarmHistory;
import com.siteweb.prealarm.entity.PreAlarmSeverity;
import com.siteweb.prealarm.service.PreAlarmHistoryService;
import com.siteweb.prealarm.service.PreAlarmService;
import com.siteweb.prealarm.service.PreAlarmSeverityService;
import com.siteweb.utility.manager.ResourceStructureTypeManager;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BoundParameterQuery;
import org.influxdb.dto.Point;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBResultMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.siteweb.common.util.DateUtil.*;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Service
@Transactional
public class EnergyOverViewServiceImpl implements EnergyOverViewService {

    private final Logger log = LoggerFactory.getLogger(EnergyOverViewService.class);

    @Autowired
    private InfluxDBManager influxDBManager;
    @Autowired
    private ResourceStructureManager resourceStructureManager;
    @Autowired
    private ResourceStructureService resourceStructureService;
    @Autowired
    private ComplexIndexService complexIndexService;
    @Autowired
    private LiveComplexIndexManager liveComplexIndexManager;
    @Autowired
    private PreAlarmService preAlarmService;
    @Autowired
    private PreAlarmHistoryService preAlarmHistoryService;   //历史预警
    @Autowired
    private PreAlarmSeverityService preAlarmSeverityService;
    @Autowired
    private HistoryComplexIndexManager historyComplexIndexManager;
    @Autowired
    private HistoryComplexIndexFeeService historyComplexIndexFeeService;
    @Autowired
    private EnergyStructureService energyStructureService;
    @Autowired
    private EnergyObjectMapService energyObjectMapService;
    @Autowired
    private EnergyElecFeeConfigService energyElecFeeConfigService;
    @Autowired
    private EquipmentManager equipmentManager;
    @Autowired
    private ComputerRackService computerRackService;
    @Autowired
    private ITDeviceService iTDeviceService;
    @Autowired
    private BusinessDefinitionMapService businessDefinitionMapService;
    @Autowired
    private EnergyComplexIndexManager energyComplexIndexManager;
    @Autowired
    private ComplexIndexBusinessTypeService complexIndexBusinessTypeService;
    @Autowired
    private EnergyObjectMapMapper objectMapMapper;
    @Autowired
    private EnergyCustomerConfigMapper energyCustomerConfigMapper;
    @Autowired
    EnergyConfigTelComService energyConfigTelComService;
    @Autowired
    ResourceStructureTypeManager resourceStructureTypeManager;
    @Autowired
    private InfluxDB influxDB;

    @Value("${spring.influx.database}")
    private String database;

    @Value("${spring.influx.database3}")
    private String databaseEnergy;

    private List<PreAlarmSeverity> lstPreAlarmSeverity;
    private List<PreAlarm> lstPreAlarm;

    // 获取所有维度节点，并默认维度预警信息
    @Override
    public List<ResourceStructureMin> GetEnergyOverViewResourceStructureAll(int dimensionTypeId, int personId) {
        List<ResourceStructureMin> result = new ArrayList<>();
        ResourceStructureMin min;
        List<ResourceStructure> allResourceStructure = resourceStructureService.findResourceStructureByUserId(personId);

        //默认层级维度
        if (dimensionTypeId == -1) {
            //查询节点的预警；
            lstPreAlarmSeverity = preAlarmSeverityService.findAllPreAlarmSeverity();
            lstPreAlarm = preAlarmService.findByPreAlarmCategory(2);   //2是能耗
            for (ResourceStructure oneNode : allResourceStructure) {
                min = new ResourceStructureMin();
                min.setResourceStructureId(oneNode.getResourceStructureId());
                min.setResourceStructureName(oneNode.getResourceStructureName());
                min.setParentResourceStructureId(oneNode.getParentResourceStructureId());
                min.setStructureTypeId(oneNode.getStructureTypeId());
                SetPreAlarmSeverityIdAndColor(min);
                result.add(min);
            }
        } else {   //自定义层级维度
            List<EnergyObjectMap> lstMap = energyObjectMapService.findByDimensionTypeId(dimensionTypeId);
            List<EnergyStructure> lstStructure = energyStructureService.findAll();
            for (EnergyObjectMap map : lstMap) {
                min = new ResourceStructureMin();
                min.setResourceStructureId(map.getObjectId());
                String name = "";

                switch (map.getObjectIdType()) {
                    case 1://原层级对象
                        ResourceStructure rs = resourceStructureManager.getResourceStructureById(map.getObjectId());
                        if (rs != null) {
                            name = rs.getResourceStructureName();
                            min.setStructureTypeId(rs.getStructureTypeId());
                        }
                        break;
                    case 2: //新建层级对象
                        EnergyStructure es = lstStructure.stream().filter(item -> item.getStructureId().equals(map.getObjectId())).findFirst().orElse(null);
                        if (es != null) {
                            name = es.getStructureName();
                            min.setStructureTypeId(200);
                        }
                        break;
                    case 3:
                        Equipment equipment = equipmentManager.getEquipmentById(map.getObjectId());
                        if (equipment != null) {
                            name = equipment.getEquipmentName();
                            min.setStructureTypeId(7);
                        }
                        break;
                    case 4:
                        ComputerRack computerRack = computerRackService.findComputerRack(map.getObjectId());
                        if (computerRack != null) {
                            name = computerRack.getComputerRackName();
                            min.setStructureTypeId(9);
                        }
                        break;
                    case 5:
                        ITDevice iTDevice = iTDeviceService.findITDevice(map.getObjectId());
                        if (iTDevice != null) {
                            name = iTDevice.getITDeviceName();
                            min.setStructureTypeId(10);
                        }
                        break;
                }

                min.setResourceStructureName(name);
                min.setParentResourceStructureId(map.getParentObjectId());
                result.add(min);
            }
        }
        return result;
    }

    // 根据节点Id列表 获取节点列表
    @Override
    public List<ResourceStructure> GetEnergyOverViewResourceStructure(List<Integer> roleStructureIds) {
        try {
            if (roleStructureIds.size() == 0) return null;
            return resourceStructureManager.getAll().stream().filter(i -> roleStructureIds.contains(i.getResourceStructureId())).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("EnergyOverViewServiceImpl-GetEnergyOverViewResourceStructure error {}", e.getMessage());
            return null;
        }
    }

    // 赋值节点告警和颜色
    private void SetPreAlarmSeverityIdAndColor(ResourceStructureMin oneResult) {
        List<PreAlarm> thisResAlarms = lstPreAlarm.stream().filter(item ->
                item.getResourceStructureId().equals(oneResult.getResourceStructureId())).collect(Collectors.toList());
        if (thisResAlarms.size() == 0) return;

        PreAlarm minSeverityPreAlarm = thisResAlarms.stream().min(Comparator.comparing(PreAlarm::getPreAlarmSeverity)).get();
        oneResult.setMinPreAlarmSeverityId(minSeverityPreAlarm.getPreAlarmSeverity());

        PreAlarmSeverity thisSeverity = lstPreAlarmSeverity.stream().filter(item ->
                item.getPreAlarmSeverityId().equals(minSeverityPreAlarm.getPreAlarmSeverity())).findFirst().orElse(null);
        if (thisSeverity == null) return;
        oneResult.setColor(thisSeverity.getColor());
    }

    // 2. 上下级节点能耗
    @Override
    public List<StructureOfComplexIndexValue> GetEnergyOverViewResourceStructureElecConsume(Integer dimensionTypeId, Integer businessTypeId,
                                                                                            Date startTime, Date endTime, String resourceStructureIds, int personId, String timeType, String sequence) {
        //自定义层级
        if (dimensionTypeId > 0) {
            return GetEnergyOverViewStructureElecConsume(dimensionTypeId, businessTypeId, startTime, endTime, resourceStructureIds, timeType, sequence);
        }
        //以下为默认层级代码逻辑
        List<StructureOfComplexIndexValue> result = new ArrayList<>();
        StructureOfComplexIndexValue oneresult;

        try {
            ResourceStructure oneRs = new ResourceStructure();
            List<ResourceStructure> needResourceStructure = new ArrayList<>();
            if (resourceStructureIds.equals("-1")) {
                needResourceStructure = resourceStructureService.findResourceStructureByUserId(personId);
            } else {
                List<Integer> lstRes = new ArrayList<>();
                StringUtils.getStringToList(resourceStructureIds).stream().forEach(item ->
                {
                    if (item.split("_").length > 1) {
                        lstRes.add(Integer.parseInt(item.split("_")[0]));
                    } else {
                        lstRes.add(Integer.parseInt(item));
                    }
                });

                for (int id : lstRes) {
                    needResourceStructure.add(resourceStructureManager.getResourceStructureById(id));
                }
            }

            for (ResourceStructure oneNode : needResourceStructure) {
                if (oneNode.getParentResourceStructureId() == 0) continue;
                oneresult = new StructureOfComplexIndexValue();
                oneresult.setResourceStructureId(oneNode.getResourceStructureId());
                oneresult.setResourceStructureName(oneNode.getResourceStructureName());
                oneresult.setParentResourceStructureId(oneNode.getParentResourceStructureId());

                if (oneNode.getParentResourceStructureId().equals(0))
                    oneresult.setParentObjectTypeId(0);
                else {
                    oneresult.setParentObjectTypeId(resourceStructureManager.getResourceStructureById(oneNode.getParentResourceStructureId()).getStructureTypeId());
                }
                oneresult.setObjectTypeId(oneNode.getStructureTypeId());
                Integer complexIndexId = GetComplexIndexIdOfResourceStructure(businessTypeId, oneNode.getResourceStructureId(), oneNode.getStructureTypeId());
                if (complexIndexId == null) continue;
                oneresult.setComplexIndexId(complexIndexId);
                oneresult.setSumValue(0d);

                Integer countLevel = countLevel(oneNode.getLevelOfPath(), ".");
                oneresult.setLevel(countLevel);
                //创建指标的需要有结果返回
                result.add(oneresult);
            }

            // 赋值节点指标的influxdb值
            GetConsumeOfStructureList(result, businessTypeId, startTime, endTime, timeType, false);

            if (result.size() == 0)
                return null;
            Integer minLevel = Integer.MAX_VALUE;
            Integer maxLevel = 0;
            minLevel = result.stream().min(Comparator.comparing(StructureOfComplexIndexValue::getLevel)).get().getLevel();
            maxLevel = result.stream().max(Comparator.comparing(StructureOfComplexIndexValue::getLevel)).get().getLevel();

            Integer thisLevel = minLevel;
            List<StructureOfComplexIndexValue> orderResult = new ArrayList<StructureOfComplexIndexValue>();
            List<StructureOfComplexIndexValue> lastTypeStructure = new ArrayList<StructureOfComplexIndexValue>();
            List<StructureOfComplexIndexValue> thisTypeStructureAll = new ArrayList<StructureOfComplexIndexValue>();
            List<StructureOfComplexIndexValue> thisTypeStructure = new ArrayList<StructureOfComplexIndexValue>();
            List<StructureOfComplexIndexValue> childTypeStructure = new ArrayList<StructureOfComplexIndexValue>();

            while (thisLevel <= maxLevel) {
                Integer finalThisLevel = thisLevel;
                thisTypeStructureAll = result.stream()
                        .filter(item -> item.getLevel().equals(finalThisLevel))
                        .collect(Collectors.toList());

                if (sequence.equals("asc")) {
                    thisTypeStructureAll = result.stream()
                            .filter(item -> item.getLevel().equals(finalThisLevel))
                            .sorted(Comparator.comparing(StructureOfComplexIndexValue::getSumValue))
                            .collect(Collectors.toList());
                }
                if (sequence.equals("desc")) {
                    thisTypeStructureAll = result.stream()
                            .filter(item -> item.getLevel().equals(finalThisLevel))
                            .sorted(Comparator.comparing(StructureOfComplexIndexValue::getSumValue, Comparator.reverseOrder()))
                            .collect(Collectors.toList());
                }

                result.removeAll(thisTypeStructureAll);

                if (thisLevel.equals(minLevel)) {
                    lastTypeStructure = thisTypeStructureAll;
                    orderResult.addAll(lastTypeStructure);
                    thisLevel = thisLevel + 1;
                    continue;
                } else {
                    thisTypeStructure = new ArrayList<StructureOfComplexIndexValue>();
                    if (thisTypeStructureAll.size() > 0) {
                        for (StructureOfComplexIndexValue parentNode : lastTypeStructure) {
                            childTypeStructure = new ArrayList<StructureOfComplexIndexValue>();
                            childTypeStructure = thisTypeStructureAll.stream().filter(
                                    item -> item.getParentResourceStructureId().equals(parentNode.getResourceStructureId())
                            ).collect(Collectors.toList());

                            if (childTypeStructure.size() > 0) {
                                thisTypeStructure.addAll(childTypeStructure);
                                orderResult.addAll(childTypeStructure);

                                thisTypeStructureAll.removeAll(childTypeStructure);
                            }
                        }

                        for (StructureOfComplexIndexValue thisIndexValue : thisTypeStructureAll) {
                            //父节点不在上一层的节点中
                            if (lastTypeStructure.stream().filter(
                                    item -> item.getResourceStructureId().equals(thisIndexValue.getParentResourceStructureId())
                            ).collect(Collectors.toList()).size() == 0) {
                                if (thisTypeStructure.indexOf(thisIndexValue) < 0) {
                                    thisTypeStructure.add(thisIndexValue);
                                }
                                if (orderResult.indexOf(thisIndexValue) < 0) {
                                    orderResult.add(thisIndexValue);
                                }
                            }
                        }
                    }
                    lastTypeStructure = thisTypeStructure;
                    thisLevel++;
                }
            }
            return orderResult;
        } catch (Exception e) {
            log.error("EnergyOverViewServiceImpl-GetEnergyOverViewResourceStructureElecConsume error {}", e.getMessage());
            return null;
        }
    }

    // 自定义层级树节点
    private List<StructureOfComplexIndexValue> GetEnergyOverViewStructureElecConsume(Integer dimensionTypeId, Integer businessTypeId,
                                                                                     Date startTime, Date endTime, String resourceStructureIds, String timeType, String sequence) {
        //以下为自定义层级代码逻辑
        List<StructureOfComplexIndexValue> result = new ArrayList<>();
        StructureOfComplexIndexValue oneresult;

        List<ResourceStructure> lstResourceStructure = new ArrayList<>();
        ResourceStructure oneResourceStructure;
        EnergyStructure oneStructure;
        Integer countLevel;
        List<EnergyStructure> lstStructure = energyStructureService.findAll();
        List<EnergyObjectMap> lstMap = energyObjectMapService.findByDimensionTypeId(dimensionTypeId);

        List<String> lstObjectIdAndTypeId = new ArrayList<>();
        if (!resourceStructureIds.equals("-1")) {
            lstObjectIdAndTypeId = StringUtils.getStringToList(resourceStructureIds);
        }

        for (EnergyObjectMap map : lstMap) {
            oneresult = new StructureOfComplexIndexValue();

            //赋值父节点名称
            switch (SourceCategory.valueOf(map.getParentObjectIdType())) {
                case RESOURCE_STRUCTURE://原层级对象
                    oneResourceStructure = resourceStructureManager.getResourceStructureById(map.getParentObjectId());
                    //lstResourceStructure.stream().filter(item->item.getResourceStructureId().equals(map.getParentObjectId())).findFirst().orElse(null);
                    if (oneResourceStructure == null) continue;
                    oneresult.setParentResourceStructureName(oneResourceStructure.getResourceStructureName());
                    oneresult.setParentObjectTypeId(oneResourceStructure.getStructureTypeId());
                    break;
                case ENERGY_RESOURCE: //新建层级对象
                    oneStructure = lstStructure.stream().filter(item -> item.getStructureId().equals(map.getParentObjectId())).findFirst().orElse(null);
                    if (oneStructure == null) continue;
                    oneresult.setParentResourceStructureName(oneStructure.getStructureName());
                    oneresult.setParentObjectTypeId(200);
                    break;
                case EQUIPMENT:
                    Equipment equipment = equipmentManager.getEquipmentById(map.getParentObjectId());
                    if (equipment == null) continue;
                    oneresult.setParentResourceStructureName(equipment.getEquipmentName());
                    oneresult.setParentObjectTypeId(7);

                    break;
                case COMPUTERRACK:
                    ComputerRack computerRack = computerRackService.findComputerRack(map.getParentObjectId());
                    if (computerRack == null) continue;
                    oneresult.setParentResourceStructureName(computerRack.getComputerRackName());
                    oneresult.setParentObjectTypeId(9);
                    break;
                case ITDEVICE:
                    ITDevice iTDevice = iTDeviceService.findITDevice(map.getParentObjectId());
                    if (iTDevice == null) continue;
                    oneresult.setParentResourceStructureName(iTDevice.getITDeviceName());
                    oneresult.setParentObjectTypeId(10);
                    break;
            }

            Integer complexIndexId = null;
            //赋值本节点信息
            switch (SourceCategory.valueOf(map.getObjectIdType())) {
                case RESOURCE_STRUCTURE://原层级对象
                    oneResourceStructure = resourceStructureManager.getResourceStructureById(map.getObjectId());
                    //lstResourceStructure.stream().filter(item->item.getResourceStructureId().equals(map.getObjectId())).findFirst().orElse(null);
                    if (oneResourceStructure == null) continue;

                    oneresult.setResourceStructureId(map.getObjectId());
                    oneresult.setResourceStructureName(oneResourceStructure.getResourceStructureName());
                    oneresult.setParentResourceStructureId(map.getParentObjectId());
                    oneresult.setObjectTypeId(oneResourceStructure.getStructureTypeId());
                    complexIndexId = GetComplexIndexIdOfResourceStructure(businessTypeId, oneResourceStructure.getResourceStructureId(), oneResourceStructure.getStructureTypeId());
                    break;
                case ENERGY_RESOURCE: //新建层级对象
                    oneStructure = lstStructure.stream().filter(item -> item.getStructureId().equals(map.getObjectId())).findFirst().orElse(null);
                    if (oneStructure == null) continue;

                    oneresult.setResourceStructureId(map.getObjectId());
                    oneresult.setResourceStructureName(oneStructure.getStructureName());
                    oneresult.setParentResourceStructureId(map.getParentObjectId());
                    oneresult.setObjectTypeId(200);   //自定义类型暂定200 与库中objecttype表对应，但不要插入此表
                    complexIndexId = GetComplexIndexIdOfResourceStructure(businessTypeId, map.getObjectId(), 200);
                    break;
                case EQUIPMENT:
                    Equipment equipment = equipmentManager.getEquipmentById(map.getParentObjectId());
                    if (equipment == null) continue;

                    oneresult.setResourceStructureId(map.getObjectId());
                    oneresult.setResourceStructureName(equipment.getEquipmentName());
                    oneresult.setParentResourceStructureId(map.getParentObjectId());
                    oneresult.setObjectTypeId(7);   //与库中objecttype表对应
                    complexIndexId = GetComplexIndexIdOfResourceStructure(businessTypeId, map.getObjectId(), 7);
                    break;
                case COMPUTERRACK:
                    ComputerRack computerRack = computerRackService.findComputerRack(map.getParentObjectId());
                    if (computerRack == null) continue;

                    oneresult.setResourceStructureId(map.getObjectId());
                    oneresult.setResourceStructureName(computerRack.getComputerRackName());
                    oneresult.setParentResourceStructureId(map.getParentObjectId());
                    oneresult.setObjectTypeId(9);   //与库中objecttype表对应
                    complexIndexId = GetComplexIndexIdOfResourceStructure(businessTypeId, map.getObjectId(), 9);
                    break;
                case ITDEVICE:
                    ITDevice iTDevice = iTDeviceService.findITDevice(map.getParentObjectId());
                    if (iTDevice == null) continue;

                    oneresult.setResourceStructureId(map.getObjectId());
                    oneresult.setResourceStructureName(iTDevice.getITDeviceName());
                    oneresult.setParentResourceStructureId(map.getParentObjectId());
                    oneresult.setObjectTypeId(10);   //与库中objecttype表对应
                    complexIndexId = GetComplexIndexIdOfResourceStructure(businessTypeId, map.getObjectId(), 10);
                    break;
            }

            //resourceStructureIds!=-1
            if (lstObjectIdAndTypeId.size() > 0) {
                if (!lstObjectIdAndTypeId.contains(oneresult.getResourceStructureId() + "_" + oneresult.getObjectTypeId()))
                    continue;
            }

            if (complexIndexId == null) continue;
            oneresult.setComplexIndexId(complexIndexId);
            oneresult.setSumValue(0d);
            countLevel = countLevel(map.getLevelOfPath(), ".");
            oneresult.setLevel(countLevel);
            oneresult.setLevelOfPath(map.getLevelOfPath());
            result.add(oneresult);
        }

        // 赋值节点指标的influxdb值
        GetConsumeOfStructureList(result, businessTypeId, startTime, endTime, timeType, false);
        if (result.size() == 0) return null;

        Integer minLevel = Integer.MAX_VALUE;
        Integer maxLevel = 0;
        minLevel = result.stream().min(Comparator.comparing(StructureOfComplexIndexValue::getLevel)).get().getLevel();
        maxLevel = result.stream().max(Comparator.comparing(StructureOfComplexIndexValue::getLevel)).get().getLevel();

        Integer thisLevel = minLevel;
        List<StructureOfComplexIndexValue> orderResult = new ArrayList<StructureOfComplexIndexValue>();
        List<StructureOfComplexIndexValue> lastTypeStructure = new ArrayList<StructureOfComplexIndexValue>();
        List<StructureOfComplexIndexValue> thisTypeStructureAll = new ArrayList<StructureOfComplexIndexValue>();
        List<StructureOfComplexIndexValue> thisTypeStructure = new ArrayList<StructureOfComplexIndexValue>();
        List<StructureOfComplexIndexValue> childTypeStructure = new ArrayList<StructureOfComplexIndexValue>();

        while (thisLevel <= maxLevel) {
            Integer finalThisLevel = thisLevel;
            thisTypeStructureAll = result.stream()
                    .filter(item -> item.getLevel().equals(finalThisLevel))
                    .collect(Collectors.toList());

            if (sequence.equals("asc")) {
                thisTypeStructureAll = result.stream()
                        .filter(item -> item.getLevel().equals(finalThisLevel))
                        .sorted(Comparator.comparing(StructureOfComplexIndexValue::getSumValue))
                        .collect(Collectors.toList());
            }
            if (sequence.equals("desc")) {
                thisTypeStructureAll = result.stream()
                        .filter(item -> item.getLevel().equals(finalThisLevel))
                        .sorted(Comparator.comparing(StructureOfComplexIndexValue::getSumValue, Comparator.reverseOrder()))
                        .collect(Collectors.toList());
            }
            result.removeAll(thisTypeStructureAll);

            if (thisLevel.equals(minLevel)) {
                lastTypeStructure = thisTypeStructureAll;
                orderResult.addAll(lastTypeStructure);
                thisLevel = thisLevel + 1;
                continue;
            } else {
                thisTypeStructure = new ArrayList<StructureOfComplexIndexValue>();
                if (thisTypeStructureAll.size() > 0) {
                    for (StructureOfComplexIndexValue parentNode : lastTypeStructure) {
                        childTypeStructure = new ArrayList<StructureOfComplexIndexValue>();
                        childTypeStructure = thisTypeStructureAll.stream().filter(
                                item -> item.getParentResourceStructureId().equals(parentNode.getResourceStructureId())
                        ).collect(Collectors.toList());

                        if (childTypeStructure.size() > 0) {
                            thisTypeStructure.addAll(childTypeStructure);
                            orderResult.addAll(childTypeStructure);

                            thisTypeStructureAll.removeAll(childTypeStructure);
                        }
                    }

                    for (StructureOfComplexIndexValue thisIndexValue : thisTypeStructureAll) {
                        //父节点不在上一层的节点中
                        if (lastTypeStructure.stream().filter(
                                item -> item.getResourceStructureId().equals(thisIndexValue.getParentResourceStructureId())
                        ).collect(Collectors.toList()).size() == 0) {
                            if (thisTypeStructure.indexOf(thisIndexValue) < 0) {
                                thisTypeStructure.add(thisIndexValue);
                            }
                            if (orderResult.indexOf(thisIndexValue) < 0) {
                                orderResult.add(thisIndexValue);
                            }
                        }
                    }
                }
                lastTypeStructure = thisTypeStructure;
                thisLevel++;
            }
        }
        return orderResult;
    }

    // 获取层级列表，时间段总用电量
    @Override
    public List<StructureOfComplexIndexValue> GetConsumeOfResourceStructure(Integer businessTypeId, List<ResourceStructure> allResourceStructure, Date startTime, Date endTime, String timeType, boolean isFee) {
        List<StructureOfComplexIndexValue> result = new ArrayList<StructureOfComplexIndexValue>();
        StructureOfComplexIndexValue oneresult = new StructureOfComplexIndexValue();
        try {
            for (ResourceStructure oneNode : allResourceStructure) {
                oneresult = new StructureOfComplexIndexValue();
                oneresult.setResourceStructureId(oneNode.getResourceStructureId());
                oneresult.setResourceStructureName(oneNode.getResourceStructureName());
                oneresult.setLevelOfPath(oneNode.getLevelOfPath());
                oneresult.setObjectTypeId(oneNode.getStructureTypeId());

                Integer complexIndexId = GetComplexIndexIdOfResourceStructure(businessTypeId, oneNode.getResourceStructureId(), oneNode.getStructureTypeId());
                if (complexIndexId == null) continue;
                oneresult.setComplexIndexId(complexIndexId);
                oneresult.setSumValue(0d);
                result.add(oneresult);
            }
            // 赋值节点指标的influxdb值
            GetConsumeOfStructureList(result, businessTypeId, startTime, endTime, timeType, false);
            return result;
        } catch (Exception e) {
            log.error("EnergyOverViewServiceImpl-GetConsumeOfResourceStructure error {}", e.getMessage());
            return null;
        }
    }

    public Integer GetComplexIndexIdOfResourceStructure(int businessTypeId, int resourceStructureId, int structureTypeId) {
        //ComplexIndexDefinition表定义了指标类型：6总用电量；28总用水量；43总用气量；
        BusinessDefinitionMap thisMap = energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(i -> i.getBusinessTypeId().equals(businessTypeId) && i.getComplexIndexDefinitionTypeId().equals(1)).findFirst().orElse(null);
        if (thisMap == null) return null;
        int energyIndexDefinition = thisMap.getComplexIndexDefinitionId();

        ComplexIndex complexIndex = energyComplexIndexManager.GetAllTotalConsumeComplexIndex().stream().filter(
                item -> item.getObjectId().equals(resourceStructureId)
                        && item.getObjectTypeId().equals(structureTypeId)
                        && item.getComplexIndexDefinitionId().equals(energyIndexDefinition)
        ).findFirst().orElse(null);

        if (complexIndex == null)
            return null;
        return complexIndex.getComplexIndexId();
    }

    private QueryResult QueryConsumeFromInfluxDB(Date startTime, Date endTime, String selectDuration, String bsWhereComplexIndexId) {
        String strSelectDuration = selectDuration.replace("$someComplexIndex", bsWhereComplexIndexId);
        Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(strSelectDuration)
                .forDatabase(databaseEnergy)
                .bind("startTime", dateToString(startTime))
                .bind("endTime", dateToString(endTime))
                .create();

        return influxDB.query(queryBuilder);
    }

    @Override
    public void UpdateStructureOfComplexIndexSumValue(QueryResult query, List<StructureOfComplexIndexValue> paraResult, Boolean isHisDayData) {
        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
        if (query != null) {
            if (isHisDayData) {
                List<EnergyHisDayDataResult> resultComplexIndexDayQuery = new ArrayList<>();
                resultComplexIndexDayQuery = resultMapper.toPOJO(query, EnergyHisDayDataResult.class);
                if (!resultComplexIndexDayQuery.isEmpty()) {
                    for (EnergyHisDayDataResult temp : resultComplexIndexDayQuery) {
                        StructureOfComplexIndexValue st = paraResult.stream().filter(i -> i.getComplexIndexId().equals(Integer.valueOf(temp.getComplexIndexId()))).findFirst().orElse(null);
                        if (st != null) {
                            st.setSumValue(NumberUtil.doubleAccuracy(st.getSumValue() + NumberUtil.doubleAccuracy(Double.parseDouble(temp.getResult()), 2), 2));
                        }
                    }
                }
            } else {
                List<EnergyHisMonthDataResult> resultComplexIndexMonthQuery = new ArrayList<>();
                resultComplexIndexMonthQuery = resultMapper.toPOJO(query, EnergyHisMonthDataResult.class);
                if (!resultComplexIndexMonthQuery.isEmpty()) {
                    for (EnergyHisMonthDataResult temp : resultComplexIndexMonthQuery) {
                        StructureOfComplexIndexValue st = paraResult.stream().filter(i -> i.getComplexIndexId().equals(Integer.valueOf(temp.getComplexIndexId()))).findFirst().orElse(null);
                        if (st != null) {
                            st.setSumValue(NumberUtil.doubleAccuracy(st.getSumValue() + NumberUtil.doubleAccuracy(Double.parseDouble(temp.getResult()), 2), 2));
                        }
                    }
                }
            }
        }
    }

    public void GetMDConsumeOfStructureList(List<StructureOfComplexIndexValue> paraResult, Date startTime, Date endTime) {
        String selectMonthDuration = "select sum(IndexValue) as result from EnergyHisMonthData where time >=$startTime and time <=$endTime and ($someComplexIndex) group by ComplexIndexId";
        String selectDayDuration = "select sum(IndexValue) as result from EnergyHisDayData where time >=$startTime and time <=$endTime and ($someComplexIndex) group by ComplexIndexId";
        try {
            StringBuilder bsWhereComplexIndexId = new StringBuilder();
            int count = 0;
            for (StructureOfComplexIndexValue ciId : paraResult) {
                if (bsWhereComplexIndexId.length() == 0) {
                    bsWhereComplexIndexId.append(" ComplexIndexId='" + ciId.getComplexIndexId() + "' ");
                } else {
                    bsWhereComplexIndexId.append(" or ComplexIndexId='" + ciId.getComplexIndexId() + "' ");
                }
                count++;
                if (count == paraResult.size() || count % 500 == 0) {

                    Date startTimeLastDayOfMonth = DateUtil.getLastDayOfMonth(startTime);
                    Date endTimeFirstDayOfMonth = DateUtil.getFirstDayOfMonth(endTime);
                    //如果选择了一个整月的
                    if (endTimeFirstDayOfMonth.equals(startTime) && startTimeLastDayOfMonth.equals(endTime)) {
                        UpdateStructureOfComplexIndexSumValue(QueryConsumeFromInfluxDB(startTime, endTime, selectDayDuration, bsWhereComplexIndexId.toString()),
                                paraResult, true);
                        paraResult.removeIf(item -> item.getSumValue().equals(0));
                        return;
                    }
                    if (startTimeLastDayOfMonth.after(endTime)) {
                        UpdateStructureOfComplexIndexSumValue(QueryConsumeFromInfluxDB(startTime, endTime, selectDayDuration, bsWhereComplexIndexId.toString()),
                                paraResult, true);
                    } else {
                        //对时间进行分段
                        Date startTimeFirstDayOfMonth = DateUtil.getFirstDayOfMonth(startTime);
                        if (startTimeFirstDayOfMonth.equals(startTime)) {
                            UpdateStructureOfComplexIndexSumValue(QueryConsumeFromInfluxDB(startTime, startTimeLastDayOfMonth, selectMonthDuration, bsWhereComplexIndexId.toString()),
                                    paraResult, false);
                        } else {
                            UpdateStructureOfComplexIndexSumValue(QueryConsumeFromInfluxDB(startTime, startTimeLastDayOfMonth, selectDayDuration, bsWhereComplexIndexId.toString()),
                                    paraResult, true);
                        }

                        //中间是不是隔了几个月
                        if (DateUtil.dateAddSeconds(startTimeLastDayOfMonth, 1).before(endTimeFirstDayOfMonth)) {
                            UpdateStructureOfComplexIndexSumValue(QueryConsumeFromInfluxDB(startTimeLastDayOfMonth, endTimeFirstDayOfMonth, selectMonthDuration, bsWhereComplexIndexId.toString()),
                                    paraResult, false);
                        }

                        Date endTimeLastDayOfMonth = DateUtil.getLastDayOfMonth(endTime);
                        if (endTimeLastDayOfMonth.equals(endTime)) {
                            UpdateStructureOfComplexIndexSumValue(QueryConsumeFromInfluxDB(endTimeFirstDayOfMonth, endTime, selectMonthDuration, bsWhereComplexIndexId.toString()),
                                    paraResult, false);
                        } else {
                            UpdateStructureOfComplexIndexSumValue(QueryConsumeFromInfluxDB(endTimeFirstDayOfMonth, endTime, selectDayDuration, bsWhereComplexIndexId.toString()),
                                    paraResult, true);
                        }
                    }
                    bsWhereComplexIndexId = new StringBuilder();
                }
            }
            paraResult.removeIf(item -> item.getSumValue().equals(0));
        } catch (Exception e) {
            log.error("EnergyOverViewServiceImpl-GetMDConsumeOfStructureList error {}", e.getMessage());
        }
    }

    // 根据节点Id获取能耗值
    public void GetConsumeOfStructureList(List<StructureOfComplexIndexValue> paraResult, int businessTypeId, Date startTime, Date endTime, String timeType, boolean isFee) {
        String dbname = databaseEnergy;
        try {
            String selectDuration = "select sum(IndexValue) as result from EnergyHisDayData where time >=$startTime and time <=$endTime and ($someComplexIndex) group by ComplexIndexId";

            if (timeType.equals("y"))
                selectDuration = "select sum(IndexValue) as result from EnergyHisMonthData where time >=$startTime and time <=$endTime and ($someComplexIndex) group by ComplexIndexId";

            if (timeType.equals("d"))
                selectDuration = "select sum(IndexValue) as result from EnergyHisHourData where time >=$startTime and time <=$endTime and ($someComplexIndex) group by ComplexIndexId";

            if (timeType.equals("o") && !isFee) {
                GetMDConsumeOfStructureList(paraResult, startTime, endTime);
                return;
            }
            if (isFee) {
                selectDuration = "select sum(IndexFeeValue) as result from EnergyHisFeeData where CalcTime >=$startTime and CalcTime <=$endTime and ($someComplexIndex)  group by ComplexIndexId";
                //dbname = database;
            }

            String sql = null;
            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();

            StringBuilder bsWhereComplexIndexId = new StringBuilder();
            int count = 0;
            for (StructureOfComplexIndexValue ciId : paraResult) {
                if (bsWhereComplexIndexId.length() == 0) {
                    bsWhereComplexIndexId.append(" ComplexIndexId='" + ciId.getComplexIndexId() + "' ");
                } else {
                    bsWhereComplexIndexId.append(" or ComplexIndexId='" + ciId.getComplexIndexId() + "' ");
                }
                count++;
                if (count == paraResult.size() || count % 500 == 0) {
                    List<String> temComplexIndexIds = new ArrayList<>();
                    sql = String.format(selectDuration, DateUtil.dateToString(startTime), DateUtil.dateToString(endTime), bsWhereComplexIndexId.toString());

                    List<ComplexIndexFeeQueryResult> resultComplexIndexFeeQuery = new ArrayList<>();
                    String strSelectDuration = selectDuration.replace("$someComplexIndex", bsWhereComplexIndexId.toString());
                    Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(strSelectDuration)
                            .forDatabase(dbname)
                            .bind("startTime", dateToString(startTime))
                            .bind("endTime", dateToString(endTime))
                            .create();

                    query = influxDB.query(queryBuilder);
                    if (query != null) {
                        if (isFee) {
                            resultComplexIndexFeeQuery = resultMapper.toPOJO(query, ComplexIndexFeeQueryResult.class);
                            if (!resultComplexIndexFeeQuery.isEmpty()) {
                                for (QueryResult.Result resultt : query.getResults()) {
                                    List<QueryResult.Series> series = resultt.getSeries();
                                    if (series == null) continue;

                                    for (QueryResult.Series serie : series) {
                                        temComplexIndexIds.add(serie.getTags().values().stream().findFirst().get());
                                    }
                                }
                                for (int i = 0; i < resultComplexIndexFeeQuery.size(); i++) {
                                    resultComplexIndexFeeQuery.get(i).setComplexIndexId(Integer.parseInt(temComplexIndexIds.get(i)));
                                }

                                for (ComplexIndexFeeQueryResult temp : resultComplexIndexFeeQuery) {
                                    StructureOfComplexIndexValue st = paraResult.stream().filter(i -> i.getComplexIndexId().equals(Integer.valueOf(temp.getComplexIndexId()))).findFirst().orElse(null);
                                    if (st != null) {
                                        st.setSumValue(NumberUtil.doubleAccuracy(Double.parseDouble(temp.getResult()), 2));
                                    }
                                }
                            }
                        } else {

                            if (timeType.equals("d")) {
                                List<EnergyHisHourDataResult> resultComplexIndexQuery = new ArrayList<>();
                                resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisHourDataResult.class);
                                if (!resultComplexIndexQuery.isEmpty()) {
                                    for (EnergyHisHourDataResult temp : resultComplexIndexQuery) {
                                        StructureOfComplexIndexValue st = paraResult.stream().filter(i -> i.getComplexIndexId().equals(Integer.valueOf(temp.getComplexIndexId()))).findFirst().orElse(null);
                                        if (st != null) {
                                            st.setSumValue(NumberUtil.doubleAccuracy(Double.parseDouble(temp.getResult()), 2));
                                        }
                                    }
                                }

                            } else if (timeType.equals("m") || timeType.equals("o")) {
                                List<EnergyHisDayDataResult> resultComplexIndexQuery = new ArrayList<>();
                                resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisDayDataResult.class);
                                if (!resultComplexIndexQuery.isEmpty()) {
                                    for (EnergyHisDayDataResult temp : resultComplexIndexQuery) {
                                        StructureOfComplexIndexValue st = paraResult.stream().filter(i -> i.getComplexIndexId().equals(Integer.valueOf(temp.getComplexIndexId()))).findFirst().orElse(null);
                                        if (st != null) {
                                            st.setSumValue(NumberUtil.doubleAccuracy(Double.parseDouble(temp.getResult()), 2));
                                        }
                                    }
                                }
                            } else {
                                List<EnergyHisMonthDataResult> resultComplexIndexQuery = new ArrayList<>();
                                resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisMonthDataResult.class);
                                if (!resultComplexIndexQuery.isEmpty()) {
                                    for (EnergyHisMonthDataResult temp : resultComplexIndexQuery) {
                                        StructureOfComplexIndexValue st = paraResult.stream().filter(i -> i.getComplexIndexId().equals(Integer.valueOf(temp.getComplexIndexId()))).findFirst().orElse(null);
                                        if (st != null) {
                                            st.setSumValue(NumberUtil.doubleAccuracy(Double.parseDouble(temp.getResult()), 2));
                                        }
                                    }
                                }
                            }
//                            resultComplexIndexQuery= resultMapper.toPOJO(query, EnergyHisDayDataResult.class);
//                            if (!resultComplexIndexQuery.isEmpty()) {
//                                for (QueryResult.Result resultt : query.getResults()) {
//                                    List<QueryResult.Series> series = resultt.getSeries();
//                                    if(series==null) {
//                                        continue;
//                                    }
//                                    for (QueryResult.Series serie : series) {
//                                        temComplexIndexIds.add(serie.getTags().values().stream().findFirst().get());
//                                    }
//                                }
//                                for(int i = 0;i<resultComplexIndexQuery.size();i++){
//                                    resultComplexIndexQuery.get(i).setComplexIndexId(temComplexIndexIds.get(i));
//                                }
//
//                                for(EnergyHisDayDataResult temp :resultComplexIndexQuery){
//                                    StructureOfComplexIndexValue st = paraResult.stream().filter(i->i.getComplexIndexId().equals(Integer.valueOf(temp.getComplexIndexId()))).findFirst().orElse(null);
//                                    if (st != null){
//                                        st.setSumValue(NumberUtil.doubleAccuracy(Double.parseDouble(temp.getResult()),2));
//                                    }
//                                }
//                            }
                        }
                    }
                    bsWhereComplexIndexId = new StringBuilder();
                }
            }
            paraResult.removeIf(item -> item.getSumValue() == null || item.getSumValue().equals(0));
        } catch (Exception e) {
            log.error("EnergyOverViewServiceImpl-GetConsumByStructureId error {}", e.getMessage());
        }
    }

    // 拆分LevelOfPath层级。471000001.471000002.471000013.471000065返回3
    public Integer countLevel(String str, String s) {
        int c = 0;
        s = s.toLowerCase(); //大写转小写
        char ch = s.charAt(0);
        str = str.toLowerCase(); //大写转小写
        for (int i = 0; i < str.length(); i++) {
            if (ch == str.charAt(i)) c++;
        }
        return c;
    }

    // 获取所选节点子节点时间段PUE
    @Override
    public ComplexIndexValue GetUEByRsourceStructureId(int businessTypeId, String resourceStructureId, Date startTime, Date endTime, String timeType) {
        ComplexIndexValue result = new ComplexIndexValue();
        Integer rsId = Integer.parseInt(resourceStructureId.split("_")[0]);
        Integer rsTypeId = Integer.parseInt(resourceStructureId.split("_")[1]);
        ComplexIndex thisComplexIndex = new ComplexIndex();

        String pue = "-1";
        List<ComplexIndex> lstComplexIndex = null;
        try {
            BusinessDefinitionMap thisMap = energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(
                    i -> i.getBusinessTypeId().equals(businessTypeId) && i.getComplexIndexDefinitionTypeId().equals(2)).findFirst().orElse(null);
            if (thisMap == null) return null;
            lstComplexIndex = complexIndexService.findByDefinitionIdAndObjectId(thisMap.getComplexIndexDefinitionId(), Long.valueOf(rsId));

            if (lstComplexIndex.size() == 0) return result;
            thisComplexIndex = lstComplexIndex.stream().filter(item -> item.getObjectTypeId().equals(rsTypeId)).findFirst().orElse(null);
            if (thisComplexIndex == null) return result;

            result.setComplexIndexId(thisComplexIndex.getComplexIndexId());
            result.setComplexIndexName(thisComplexIndex.getComplexIndexName());

            ComplexIndexDefinition definition = energyComplexIndexManager.GetAllComplexIndexDefinition().stream().filter(i -> i.getComplexIndexDefinitionId().equals(thisMap.getComplexIndexDefinitionId())).findFirst().orElse(null);
            result.setComplexIndexDefinitionName(definition == null ? "" : definition.getComplexIndexDefinitionName());

            pue = "0";
            //测试数据时有可能表达式为空
            //if (thisComplexIndex.getExpression() == null || thisComplexIndex.getExpression().length() == 0) return pue;

            List<ComplexIndex> sumComplexIndex = new ArrayList<>();
            List<ComplexIndex> avgComplexIndex = new ArrayList<>();

            boolean needSplit = checkExpress(thisComplexIndex, sumComplexIndex, avgComplexIndex);
            if (!needSplit) {
                QueryResult query = null;
                InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
                String sql = "select mean(IndexValue) as result  from EnergyHisMonthData where time >= $startTime and time <= $endTime and ComplexIndexId = $complexIndexId  order by time asc";
                if (timeType.equals("d") || timeType.equals("o")) {
                    sql = "select mean(IndexValue) as result  from EnergyHisDayData where time >= $startTime and time <= $endTime and ComplexIndexId = $complexIndexId  order by time asc";
                }
                Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(sql)
                        .forDatabase(databaseEnergy)
                        .bind("startTime", dateToString(startTime))
                        .bind("endTime", dateToString(endTime))
                        .bind("complexIndexId", thisComplexIndex.getComplexIndexId().toString())
                        .create();
                query = influxDB.query(queryBuilder);
                if (query != null) {
                    if (timeType.equals("d") || timeType.equals("o")) {
                        List<EnergyHisDayDataResult> resultComplexIndexQuery = new ArrayList<>();
                        resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisDayDataResult.class);
                        if (!resultComplexIndexQuery.isEmpty()) {

                            result.setSumValue(String.valueOf(resultComplexIndexQuery.get(0).result));
                            return result;
                        }
                    } else {
                        List<EnergyHisMonthDataResult> resultComplexIndexQuery = new ArrayList<>();
                        resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisMonthDataResult.class);
                        if (!resultComplexIndexQuery.isEmpty()) {
                            result.setSumValue(String.valueOf(resultComplexIndexQuery.get(0).result));
                            return result;
                        }
                    }
                }
            } else if (sumComplexIndex.size() > 0) {
                StringBuilder bsWhereComplexIndexId = new StringBuilder();
                for (ComplexIndex ciId : sumComplexIndex) {
                    if (bsWhereComplexIndexId.length() == 0) {
                        bsWhereComplexIndexId.append(" ComplexIndexId='" + ciId.getComplexIndexId() + "' ");
                    } else {
                        bsWhereComplexIndexId.append(" or ComplexIndexId='" + ciId.getComplexIndexId() + "' ");
                    }
                }

                List<String> temComplexIndexIds = new ArrayList<>();
                String sql = String.format("select sum(IndexValue) as result from EnergyHisMonthData where time >=$startTime and time <=$endTime and (" + bsWhereComplexIndexId.toString() + ") group by ComplexIndexId",
                        DateUtil.dateToString(startTime), DateUtil.dateToString(endTime));
                if (timeType.equals("d") || timeType.equals("o"))
                    sql = String.format("select sum(IndexValue) as result from EnergyHisDayData where time >=$startTime and time <=$endTime and (" + bsWhereComplexIndexId.toString() + ") group by ComplexIndexId",
                            DateUtil.dateToString(startTime), DateUtil.dateToString(endTime));

                String Expression = thisComplexIndex.getExpression();

                InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
                Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(sql)
                        .forDatabase(databaseEnergy)
                        .bind("startTime", dateToString(startTime))
                        .bind("endTime", dateToString(endTime))
                        .create();
                QueryResult query = influxDB.query(queryBuilder);
                if (query != null) {
                    if (timeType.equals("d") || timeType.equals("o")) {
                        List<EnergyHisDayDataResult> resultComplexIndexQuery = new ArrayList<>();
                        resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisDayDataResult.class);

                        Pattern p = Pattern.compile("(?<=ci\\(|last\\()[^\\)]+", Pattern.CASE_INSENSITIVE);
                        Matcher m = p.matcher(thisComplexIndex.getExpression());
                        while (m.find()) {
                            Integer complexId = Integer.valueOf(m.group());
                            EnergyHisDayDataResult dataResult = resultComplexIndexQuery.stream().filter(i -> i.getComplexIndexId().equals(complexId.toString())).findFirst().orElse(null);
                            Expression = Expression.replace("ci(" + complexId + ")", dataResult == null ? "0" : dataResult.getResult()).replace("last(" + complexId + ")", dataResult == null ? "0" : dataResult.getResult());
                        }
                    } else {
                        List<EnergyHisMonthDataResult> resultComplexIndexQuery = new ArrayList<>();
                        resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisMonthDataResult.class);

                        Pattern p = Pattern.compile("(?<=ci\\(|last\\()[^\\)]+", Pattern.CASE_INSENSITIVE);
                        Matcher m = p.matcher(thisComplexIndex.getExpression());
                        while (m.find()) {
                            Integer complexId = Integer.valueOf(m.group());
                            EnergyHisMonthDataResult dataResult = resultComplexIndexQuery.stream().filter(i -> i.getComplexIndexId().equals(complexId.toString())).findFirst().orElse(null);
                            Expression = Expression.replace("ci(" + complexId + ")", dataResult == null ? "0" : dataResult.getResult()).replace("last(" + complexId + ")", dataResult == null ? "0" : dataResult.getResult());
                        }
                    }
                    try {
                        pue = NumberUtil.doubleAccuracy(Calculator.conversion(Expression), 2).toString();
                    } catch (Exception ex) {
                        log.error("Calculator.conversion(Expression) Expression=" + Expression);
                    }
                }
            }

            result.setSumValue(pue);
            return result;
        } catch (Exception e) {
            log.error("EnergyOverViewServiceImpl-GetUEByRsourceStructureId error {}", e.getMessage());
            return null;
        }
    }

    //判断是否需要拆分表达式
    private boolean checkExpress(ComplexIndex thisComplexIndex, List<ComplexIndex> sumComplexIndex, List<ComplexIndex> avgComplexIndex) {
        if (thisComplexIndex.getExpression() == null || thisComplexIndex.getExpression().length() == 0)
            return false;
        boolean result = false;
        Pattern p = Pattern.compile("(?<=ci\\(|last\\()[^\\)]+", Pattern.CASE_INSENSITIVE);
        Matcher m = p.matcher(thisComplexIndex.getExpression());
        String Expression = thisComplexIndex.getExpression();
        while (m.find()) {
            Integer complexId = Integer.valueOf(m.group());
            ComplexIndex expressComplexIndex = energyComplexIndexManager.GetAllComplexIndex().stream().filter(i -> i.getComplexIndexId().equals(complexId)).findFirst().orElse(null);
            if (expressComplexIndex == null) continue;

            BusinessDefinitionMap map = energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(
                    i -> i.getComplexIndexDefinitionId().equals(thisComplexIndex.getComplexIndexDefinitionId())).findFirst().orElse(null);
            if (map == null) continue;

            //如果是总量或者分项， 则需要拆分
            if (map.getComplexIndexDefinitionTypeId().equals(1) || map.getComplexIndexDefinitionTypeId().equals(3)) {
                sumComplexIndex.add(expressComplexIndex);
                result = true;
            } else {
                avgComplexIndex.add(expressComplexIndex);
            }
        }
        return result;
    }

    // 获取所选节点子节点时间段用电量，倒序
    @Override
    public List<StructureOfComplexIndexValue> GetEnergyOverViewResourceStructureChild(int dimensionTypeId, int businessTypeId, String resourceStructureId, Date startTime, Date endTime, String timeType, boolean isFee, String sequence) {
        List<StructureOfComplexIndexValue> result = new ArrayList<StructureOfComplexIndexValue>();
        List<ResourceStructure> childResourceStructure = new ArrayList<>();
        List<EnergyStructure> lstStructure = energyStructureService.findAll();
        ResourceStructure temp;
        Integer rsId = Integer.parseInt(resourceStructureId.split("_")[0]);
        Integer rsTypeId = Integer.parseInt(resourceStructureId.split("_")[1]);

        try {
            if (dimensionTypeId == -1) {
                childResourceStructure = resourceStructureManager.getResourceStructureLstByParentId(rsId);
            } else {
                int objectMapType = rsTypeId;
                if (rsTypeId == 200) {
                    objectMapType = SourceCategory.ENERGY_RESOURCE.value();
                } else {
                    if (resourceStructureManager.getAll().stream().anyMatch(i -> i.getStructureTypeId().equals(rsTypeId)))
                        objectMapType = SourceCategory.RESOURCE_STRUCTURE.value();
                }

                List<EnergyObjectMap> lstMap = energyObjectMapService.findByDimensionTypeIdAndParentObjectIdAndParentObjectIdType(dimensionTypeId, rsId, objectMapType);
                EnergyStructure tempstr;
                for (EnergyObjectMap oneMap : lstMap) {
                    switch (SourceCategory.valueOf(oneMap.getObjectIdType())) {
                        case RESOURCE_STRUCTURE://原层级对象
                            temp = resourceStructureManager.getResourceStructureById(oneMap.getObjectId());
                            if (temp == null) continue;
                            childResourceStructure.add(temp);
                            break;
                        case ENERGY_RESOURCE: //新建层级对象
                            ResourceStructure rs = new ResourceStructure();
                            rs.setResourceStructureId(oneMap.getObjectId());
                            rs.setStructureTypeId(200);
                            rs.setLevelOfPath(oneMap.getLevelOfPath());
                            tempstr = lstStructure.stream().filter(item -> item.getStructureId().equals(oneMap.getObjectId())).findFirst().orElse(null);
                            if (tempstr == null) continue;
                            rs.setResourceStructureName(tempstr.getStructureName());
                            childResourceStructure.add(rs);
                            break;
                        case EQUIPMENT:
                            Equipment equipment = equipmentManager.getEquipmentById(oneMap.getParentObjectId());
                            if (equipment == null) continue;

                            ResourceStructure rss = new ResourceStructure();
                            rss.setResourceStructureId(oneMap.getObjectId());
                            rss.setResourceStructureName(equipment.getEquipmentName());
                            rss.setStructureTypeId(7);
                            rss.setLevelOfPath(oneMap.getLevelOfPath());
                            childResourceStructure.add(rss);
                            break;
                        case COMPUTERRACK:
                            ComputerRack computerRack = computerRackService.findComputerRack(oneMap.getParentObjectId());
                            if (computerRack == null) continue;

                            ResourceStructure rsss = new ResourceStructure();
                            rsss.setResourceStructureId(oneMap.getObjectId());
                            rsss.setResourceStructureName(computerRack.getComputerRackName());
                            rsss.setStructureTypeId(9);
                            rsss.setLevelOfPath(oneMap.getLevelOfPath());
                            childResourceStructure.add(rsss);
                            break;
                        case ITDEVICE:
                            ITDevice iTDevice = iTDeviceService.findITDevice(oneMap.getParentObjectId());
                            if (iTDevice == null) continue;

                            ResourceStructure rs10 = new ResourceStructure();
                            rs10.setResourceStructureId(oneMap.getObjectId());
                            rs10.setResourceStructureName(iTDevice.getITDeviceName());
                            rs10.setStructureTypeId(10);
                            rs10.setLevelOfPath(oneMap.getLevelOfPath());
                            childResourceStructure.add(rs10);
                            break;
                    }
                }
            }
            result = GetConsumeOfResourceStructure(businessTypeId, childResourceStructure, startTime, endTime, timeType, isFee);
            if (sequence.equals("asc")) {
                return result.stream().sorted(Comparator.comparing(StructureOfComplexIndexValue::getSumValue)).collect(Collectors.toList());
            } else {
                return result.stream().sorted(Comparator.comparing(StructureOfComplexIndexValue::getSumValue).reversed()).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("EnergyOverViewServiceImpl-GetEnergyOverViewResourceStructureChild error {}", e.getMessage());
            return null;
        }
    }

    // 获取层级下某类型的所有指标实时值；
    @Override
    public List<ComplexIndexValue> findliveComplexIndexByResourceStructure(int businessTypeId, String resourceStructureId) {
        List<ComplexIndexValue> result = new ArrayList<>();
        try {
            Integer rsId = Integer.parseInt(resourceStructureId.split("_")[0]);
            Integer rsTypeId = Integer.parseInt(resourceStructureId.split("_")[1]);

            List<ComplexIndex> lstComplexIndex = complexIndexService.findByObjectIdAndObjectTypeId(rsId, rsTypeId);
            lstComplexIndex.removeIf(i -> i.getBusinessTypeId() == null || !i.getBusinessTypeId().equals(businessTypeId));

            ComplexIndexValue oneResult;
            for (ComplexIndex one : lstComplexIndex) {
                oneResult = new ComplexIndexValue();
                oneResult.setComplexIndexId(one.getComplexIndexId());
                oneResult.setComplexIndexName(one.getComplexIndexName());
                oneResult.setComplexIndexDefinitionId(one.getComplexIndexDefinitionId());
                oneResult.setCalcType(one.getCalcType());

                BusinessDefinitionMap businessDefinitionMap = energyComplexIndexManager.GetAllBusinessDefinitionMap().stream()
                        .filter(i -> i.getComplexIndexDefinitionId().equals(one.getComplexIndexDefinitionId())).findFirst().orElse(null);
                if (businessDefinitionMap != null)
                    oneResult.setComplexIndexDefinitionTypeId(businessDefinitionMap.getComplexIndexDefinitionTypeId());
                setliveComplexIndex(oneResult);
                result.add(oneResult);
            }
        } catch (Exception ex) {
            log.error("EnergyOverViewServiceImpl-findliveComplexIndexByGlobalSource error {}", ex.getMessage());
        } finally {
            return result;
        }
    }

    private void setliveComplexIndex(ComplexIndexValue complexIndexValue) {
        LiveComplexIndex liveComplexIndex = liveComplexIndexManager.findLiveComplexIndexById(complexIndexValue.getComplexIndexId());
        if (liveComplexIndex == null) {
            complexIndexValue.setSumValue("");
            complexIndexValue.setSampleTime(null);
            complexIndexValue.setUnit(null);
            return;
        }

        String sumValue = "0";
        if (complexIndexValue.getCalcType() == null || complexIndexValue.getCalcType() == 0) {
            //不是差值计算
            sumValue = StrUtil.isEmpty(liveComplexIndex.getCurrentValue()) ? "0" : liveComplexIndex.getCurrentValue();
        } else {
            //是差值计算
            sumValue = getLastComplexIndexValue(complexIndexValue.getComplexIndexId());
        }

        complexIndexValue.setSumValue(sumValue);
        complexIndexValue.setUnit(liveComplexIndex.getUnit());
        Timestamp ts = Timestamp.valueOf(liveComplexIndex.getTimeStamp());
        String sampleTime = new java.sql.Date(ts.getTime()).toString();
        complexIndexValue.setSampleTime(sampleTime);
    }

    // 8 所选节点实时能耗预警
    @Override
    public List<PreAlarm> liveEmsPreAlarmByResourceStructure(String resourceStructureId) {
        Integer rsId = Integer.parseInt(resourceStructureId.split("_")[0]);
        //Integer rsTypeId = Integer.parseInt(resourceStructureId.split("_")[1]);
        lstPreAlarm = preAlarmService.findByPreAlarmCategory(2);   //2是能耗
        return lstPreAlarm.stream().filter(item -> item.getObjectId().equals(rsId)).collect(Collectors.toList());
    }

    // 8.1 所选节点历史能耗预警排名
    @Override
    public List<PreAlarmHistoryCategoryOrder> hisEmsPreAlarmOrder(String resourceStructureId, Date startTime, Date endTime) {
        Integer rsId = Integer.parseInt(resourceStructureId.split("_")[0]);
        ResourceStructure rs = resourceStructureManager.getResourceStructureById(rsId);
        if (rs == null)
            return null;
        List<PreAlarmHistory> lstPreAlarmHistory = preAlarmHistoryService.findPreAlarmHistorys(
                rsId, rs.getStructureTypeId(), startTime, endTime, 2);
        Map<String, List<PreAlarmHistory>> map = lstPreAlarmHistory.stream().collect(Collectors.groupingBy(PreAlarmHistory::getMeanings));
        List<PreAlarmHistoryCategoryOrder> result = new ArrayList<>();

        map.forEach((key, value) -> {
            PreAlarmHistoryCategoryOrder one = new PreAlarmHistoryCategoryOrder();
            one.setMeaning(key);
            one.setCount(value.stream().count());
            result.add(one);
        });
        return result.stream().sorted(Comparator.comparing(PreAlarmHistoryCategoryOrder::getCount)).collect(Collectors.toList());
    }

    // 9 获取预警规则信息
    @Override
    public List<PreAlarmSeverity> getPreAlarmSeverity() {
        //查询节点的预警；
        return preAlarmSeverityService.findAllPreAlarmSeverity();
    }

    // 10 所选节点PUE/WUE等时间段历史值
    @Override
    public List<HistoryComplexIndex> hisUEOfResourceId(int businessTypeId, String resourceStructureId, Date startTime, Date endTime) {
        List<ComplexIndex> lstComplexIndex = null;
        Integer rsId = Integer.parseInt(resourceStructureId.split("_")[0]);
        Integer rsTypeId = Integer.parseInt(resourceStructureId.split("_")[1]);

        BusinessDefinitionMap thisMap = energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(
                i -> i.getBusinessTypeId().equals(businessTypeId) && i.getComplexIndexDefinitionTypeId().equals(2)).findFirst().orElse(null);
        if (thisMap == null) return null;

        lstComplexIndex = complexIndexService.findByDefinitionIdAndObjectId(thisMap.getComplexIndexDefinitionId(), Long.valueOf(rsId));
        ComplexIndex comp = lstComplexIndex.stream().filter(i -> i.getObjectTypeId().equals(rsTypeId)).findFirst().orElse(null);
        if (lstComplexIndex.size() == 0 || comp == null) return null;
        return historyComplexIndexManager.findHistoryComplexIndexByIdAndDuration(startTime, endTime, comp.getComplexIndexId(), null);
    }

    // 11 所选节点各用能类型的历史值
    @Override
    public Map<String, List<HistoryComplexIndex>> hisEventTypeOfResourceId(int businessTypeId, String resourceStructureId, Date startTime, Date endTime) {
        Map<String, List<HistoryComplexIndex>> result = new HashMap<>();

        Integer rsId = Integer.parseInt(resourceStructureId.split("_")[0]);
        Integer rsTypeId = Integer.parseInt(resourceStructureId.split("_")[1]);

        // 获取此节点的所有指标
        List<ComplexIndex> lstComplexIndexOfStructure = complexIndexService.findByObjectIdAndObjectTypeId(rsId, rsTypeId);

        List<ComplexIndex> filterComplexIndex = new ArrayList<>();
        List<BusinessDefinitionMap> thisMap =
                energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(i -> i.getBusinessTypeId().equals(businessTypeId) && i.getComplexIndexDefinitionTypeId().equals(3)).collect(Collectors.toList());
        if (thisMap == null) return null;
        List<Integer> lstDefinition = thisMap.stream().map(BusinessDefinitionMap::getComplexIndexDefinitionId).collect(Collectors.toList());
        List<Integer> finalLstDefinition = lstDefinition;
        filterComplexIndex = lstComplexIndexOfStructure.stream().filter(item -> finalLstDefinition.contains(item.getComplexIndexDefinitionId())).collect(Collectors.toList());

        List<HistoryComplexIndex> hisValues;
        List<Date> allTime = new ArrayList<>();
        List<Date> tempTime = new ArrayList<>();
        //获取指标存储值
        for (ComplexIndex com : filterComplexIndex) {
            //这里要改吧？前端说本次需求没有先不改
//            hisValues = getHistoryComplexIndexValueByTimeType(timeType,startTime,endTime,com.getComplexIndexId());
            hisValues = historyComplexIndexManager.findHistoryComplexIndexByIdAndDuration(startTime, endTime, com.getComplexIndexId(), null);
            //根据采集时间去重
            hisValues = hisValues.stream().collect(
                    Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(HistoryComplexIndex::getDateTime))), ArrayList::new)
            );
            tempTime = hisValues.stream().map(HistoryComplexIndex::getDateTime).collect(Collectors.toList());
            allTime = Stream.of(allTime, tempTime).flatMap(Collection::stream).distinct().collect(Collectors.toList());
            result.put(com.getComplexIndexName(), hisValues);
        }
        for (List<HistoryComplexIndex> value : result.values()) {
            tempTime = value.stream().map(HistoryComplexIndex::getDateTime).collect(Collectors.toList());
            List<Date> finalTempTime = tempTime;
            List<Date> diffTime = allTime.stream().filter(item -> !finalTempTime.contains(item)).collect(Collectors.toList());
            for (Date time : diffTime) {
                HistoryComplexIndex addFee = new HistoryComplexIndex();
                addFee.calcTime = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(time));
                addFee.time = addFee.calcTime;
                addFee.setIndexValue("0");
                value.add(addFee);
            }
        }
        //排序
        Iterator<Map.Entry<String, List<HistoryComplexIndex>>> it = result.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<String, List<HistoryComplexIndex>> itEntry = it.next();
            List<HistoryComplexIndex> lstValues = itEntry.getValue();
            lstValues = lstValues.stream().sorted(Comparator.comparing(HistoryComplexIndex::getDateTime)).collect(Collectors.toList());
            itEntry.setValue(lstValues);
        }
        return result;
    }

    public List<HistoryComplexIndexResult> getHistoryComplexIndexValueByTimeType(String timeType, Date startTime, Date endTime, Integer complexIndexId) {

        String sql = "SELECT * FROM EnergyHisDayData WHERE time >= $startTime and time <= $endTime and ComplexIndexId = $complexIndexId";
        String measurementName = "EnergyHisDayData";

        if (timeType.equals("y")) {
            measurementName = "EnergyHisMonthData";
            sql = "SELECT * FROM EnergyHisMonthData  WHERE time >= $startTime and time <= $endTime and ComplexIndexId = $complexIndexId";
        } else if (timeType.equals("d")) {
            measurementName = "EnergyHisHourData";
            sql = "SELECT * FROM EnergyHisHourData  WHERE time >= $startTime and time <= $endTime and ComplexIndexId = $complexIndexId";
        }
        QueryResult query = null;
        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
        List<HistoryComplexIndexResult> energyQueryResult = new ArrayList<>();
        try {
            Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(sql)
                    .forDatabase(databaseEnergy)
                    .bind("startTime", dateToString(startTime))
                    .bind("endTime", dateToString(endTime))
                    .bind("complexIndexId", String.valueOf(complexIndexId))
                    .create();
            query = influxDB.query(queryBuilder);

            if (query != null) {
                energyQueryResult = resultMapper.toPOJO(query, HistoryComplexIndexResult.class, measurementName);
            }
            if (energyQueryResult == null || energyQueryResult.size() == 0)
                return new ArrayList<>();
            return energyQueryResult;
        } catch (Exception e) {
            log.error("airGroupViewServiceImpl-getEnergyCurve error {}", e.getMessage());
            return new ArrayList<>();
        }
    }


    // 11.1 所选节点各用能类型费用的历史值
    @Override
    public Map<String, List<EnergyHisFeeDataSum>> hisEventTypeFeeOfResourceId(int businessTypeId, String resourceStructureId, Date startTime, Date endTime, String timeType) {
        Map<String, List<EnergyHisFeeDataSum>> result = new HashMap<>();

        Integer rsId = Integer.parseInt(resourceStructureId.split("_")[0]);
        Integer rsTypeId = Integer.parseInt(resourceStructureId.split("_")[1]);

        // 获取此节点的所有指标
        List<ComplexIndex> lstComplexIndexOfStructure = complexIndexService.findByObjectIdAndObjectTypeId(rsId, rsTypeId);

        List<ComplexIndex> filterComplexIndex = new ArrayList<>();
        List<BusinessDefinitionMap> thisMap =
                energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(i -> i.getBusinessTypeId().equals(businessTypeId) && i.getComplexIndexDefinitionTypeId().equals(3)).collect(Collectors.toList());
        if (thisMap == null) return null;
        List<Integer> lstDefinition = thisMap.stream().map(BusinessDefinitionMap::getComplexIndexDefinitionId).collect(Collectors.toList());
        List<Integer> finalLstDefinition = lstDefinition;
        filterComplexIndex = lstComplexIndexOfStructure.stream().filter(item -> finalLstDefinition.contains(item.getComplexIndexDefinitionId())).collect(Collectors.toList());

        List<EnergyHisFeeDataSum> hisValues = new ArrayList<>();
        //今后的数据不用查询
        SimpleDateFormat inputFormat = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (timeType.equals("y")) {
            try {
                for (ComplexIndex com : filterComplexIndex) {
                    hisValues = new ArrayList<>();
                    Date tempStartTime = startTime;
                    Date tempEndTime = DateUtil.getMonthEndTime(tempStartTime);
                    List<EnergyHisFeeDataSum> temp = new ArrayList<>();
                    for (int i = 0; i < 12; i++) {
                        Date feeTime = inputFormat.parse(tempStartTime.toString());
                        temp.clear();
                        if (tempStartTime.before(new Date())) {
                            //读取InfluxDB 查询能耗数据
                            //获取指标存储值
                            temp = historyComplexIndexFeeService.findHistoryComplexIndeFeeByIdAndDuration(tempStartTime, tempEndTime, com.getComplexIndexId(), null, timeType);
                            if (temp.isEmpty()) {
                                EnergyHisFeeDataSum tempChild = new EnergyHisFeeDataSum();
                                tempChild.setFeeTime(outputFormat.format(feeTime));
                                temp.add(tempChild);
                            }
                            hisValues.addAll(temp);
                            result.put(com.getComplexIndexName(), hisValues);
                            tempStartTime = DateUtil.getNextMonthFirstDay(tempStartTime);
                            tempEndTime = DateUtil.getMonthEndTime(tempStartTime);
                        } else {
                            EnergyHisFeeDataSum tempChild = new EnergyHisFeeDataSum();
                            tempChild.setFeeTime(outputFormat.format(feeTime));
                            temp.add(tempChild);
                            hisValues.addAll(temp);
                            result.put(com.getComplexIndexName(), hisValues);
                            tempStartTime = DateUtil.getNextMonthFirstDay(tempStartTime);
                            tempEndTime = DateUtil.getMonthEndTime(tempStartTime);
                        }
                    }
                }
            } catch (ParseException e) {
                log.error("hisEventTypeFeeOfResourceId", e);
            }
        } else {
            //获取指标存储值
            for (ComplexIndex com : filterComplexIndex) {
                hisValues = historyComplexIndexFeeService.findHistoryComplexIndeFeeByIdAndDuration(startTime, endTime, com.getComplexIndexId(), null, timeType);
                //根据采集时间去重
                result.put(com.getComplexIndexName(), hisValues);
            }
        }
        return result;
    }

    // 12 所选节点子节点的能耗用量费用  已弃用，子节点不计算电费
    @Override
    public List<StructureOfComplexIndexValue> childEnergyValueOfResourceStructureId(int dimensionTypeId, int businessTypeId, String resourceStructureId, Date startTime,
                                                                                    Date endTime, int level, int personId, int topn, boolean isFee) {
        List<StructureOfComplexIndexValue> result = new ArrayList<>();
        List<ResourceStructure> allResourceStructure = resourceStructureManager.getAll().stream().collect(Collectors.toList());

        // 有权限的站点
        List<ResourceStructure> roleResourceStructures = resourceStructureService.findResourceStructureByUserId(personId);
        // 剔除无权限的站点
        allResourceStructure.removeIf(rs -> !roleResourceStructures.contains(rs));

        //默认层级
        if (dimensionTypeId == -1) {
            ResourceStructure thisRS = resourceStructureManager.getResourceStructureById(Integer.parseInt(resourceStructureId.split("_")[0]));
            if (thisRS == null) return result;

            if (level == 1)
                result = GetEnergyOverViewResourceStructureChild(dimensionTypeId, businessTypeId, resourceStructureId, startTime, endTime, "d", isFee, "desc");//默认降序排序
            else {
                List<ResourceStructure> childResourceStructure = allResourceStructure.stream().filter(
                        item -> item.getLevelOfPath().contains(thisRS.getLevelOfPath() + ".")
                ).collect(Collectors.toList());
                int thisItemLevel = countLevel(thisRS.getLevelOfPath(), ".");
                childResourceStructure.removeIf(rs -> countLevel(rs.getLevelOfPath(), ".") - thisItemLevel != level);
                result = GetConsumeOfResourceStructure(businessTypeId, childResourceStructure, startTime, endTime, "d", isFee);
            }
        } else {
            List<EnergyStructure> lstStructure = energyStructureService.findAll();
            List<EnergyObjectMap> lstMap = energyObjectMapService.findByDimensionTypeId(dimensionTypeId);
            Integer finalResourceStructureId = Integer.parseInt(resourceStructureId.split("_")[0]);
            Integer resourceStructureTypeId = 1;
            switch (Integer.parseInt(resourceStructureId.split("_")[1])) {
                case 200:
                    resourceStructureTypeId = 2;
                    break;
                case 7:
                    resourceStructureTypeId = 3;
                    break;
                case 9:
                    resourceStructureTypeId = 4;
                    break;
                case 10:
                    resourceStructureTypeId = 5;
                    break;
            }

            Integer finalResourceStructureTypeId = resourceStructureTypeId;
            EnergyObjectMap objMap = lstMap.stream().filter(
                    item -> item.getObjectId().equals(finalResourceStructureId) && item.getObjectIdType().equals(finalResourceStructureTypeId)
            ).findFirst().orElse(null);
            if (objMap == null) return result;

            List<EnergyObjectMap> childMap = lstMap.stream().filter(
                    item -> item.getLevelOfPath().contains(objMap.getLevelOfPath() + ".")
            ).collect(Collectors.toList());
            int thisItemLevel = countLevel(objMap.getLevelOfPath(), ".");
            childMap.removeIf(rs -> countLevel(rs.getLevelOfPath(), ".") - thisItemLevel != level);
            ResourceStructure temp;
            EnergyStructure tempstr;
            List<ResourceStructure> convertResourceStructure = new ArrayList<>();

            for (EnergyObjectMap oneMap : childMap) {
                switch (SourceCategory.valueOf(oneMap.getObjectIdType())) {
                    case RESOURCE_STRUCTURE://原层级对象
                        temp = resourceStructureManager.getResourceStructureById(oneMap.getObjectId());
                        if (temp == null) continue;
                        // 无此节点的权限
                        if (!allResourceStructure.contains(temp)) continue;
                        convertResourceStructure.add(temp);
                        break;
                    case ENERGY_RESOURCE: //新建层级对象
                        ResourceStructure rs = new ResourceStructure();
                        rs.setResourceStructureId(oneMap.getObjectId());
                        rs.setStructureTypeId(200);
                        rs.setLevelOfPath(oneMap.getLevelOfPath());
                        tempstr = lstStructure.stream().filter(item -> item.getStructureId().equals(oneMap.getObjectId())).findFirst().orElse(null);
                        if (tempstr == null) continue;
                        rs.setResourceStructureName(tempstr.getStructureName());
                        convertResourceStructure.add(rs);
                        break;
                    case EQUIPMENT:
                        Equipment equipment = equipmentManager.getEquipmentById(oneMap.getParentObjectId());
                        if (equipment == null) continue;

                        ResourceStructure rss = new ResourceStructure();
                        rss.setResourceStructureId(oneMap.getObjectId());
                        rss.setResourceStructureName(equipment.getEquipmentName());
                        rss.setStructureTypeId(7);
                        rss.setLevelOfPath(oneMap.getLevelOfPath());
                        convertResourceStructure.add(rss);
                        break;
                    case COMPUTERRACK:
                        ComputerRack computerRack = computerRackService.findComputerRack(oneMap.getParentObjectId());
                        if (computerRack == null) continue;

                        ResourceStructure rsss = new ResourceStructure();
                        rsss.setResourceStructureId(oneMap.getObjectId());
                        rsss.setResourceStructureName(computerRack.getComputerRackName());
                        rsss.setStructureTypeId(9);
                        rsss.setLevelOfPath(oneMap.getLevelOfPath());
                        convertResourceStructure.add(rsss);
                        break;
                    case ITDEVICE:
                        ITDevice iTDevice = iTDeviceService.findITDevice(oneMap.getParentObjectId());
                        if (iTDevice == null) continue;

                        ResourceStructure rs10 = new ResourceStructure();
                        rs10.setResourceStructureId(oneMap.getObjectId());
                        rs10.setResourceStructureName(iTDevice.getITDeviceName());
                        rs10.setStructureTypeId(10);
                        rs10.setLevelOfPath(oneMap.getLevelOfPath());
                        convertResourceStructure.add(rs10);
                        break;
                }
            }
            result = GetConsumeOfResourceStructure(businessTypeId, convertResourceStructure, startTime, endTime, "d", isFee);
        }
        result = result.stream().sorted(Comparator.comparing(StructureOfComplexIndexValue::getSumValue).reversed()).collect(Collectors.toList());
        if (result.stream().count() <= topn || topn < 0)
            return result;
        else
            return result.subList(0, topn);
    }

    // 13 所选节点有几层子节点
    @Override
    public int childLevelsOfResourceId(int dimensionTypeId, String resourceStructureId, int personId) {
        int result = 0;
        // 默认层级
        if (dimensionTypeId == -1) {
            List<ResourceStructure> allResourceStructure = resourceStructureService.findResourceStructureByUserId(personId);
            ResourceStructure thisStructure = resourceStructureManager.getResourceStructureById(Integer.parseInt(resourceStructureId.split("_")[0]));
            int thisObjectLevel = countLevel(thisStructure.getLevelOfPath(), ".");
            List<ResourceStructure> childResourceStructure = allResourceStructure.stream().filter(
                    item -> item.getLevelOfPath().contains(thisStructure.getLevelOfPath() + ".")
            ).collect(Collectors.toList());
            int thisItemLevel = 0;
            for (ResourceStructure item : childResourceStructure) {
                thisItemLevel = countLevel(item.getLevelOfPath(), ".");
                if (thisItemLevel - thisObjectLevel > result)
                    result = thisItemLevel - thisObjectLevel;
            }
        } else {
            List<EnergyObjectMap> lstMap = energyObjectMapService.findByDimensionTypeId(dimensionTypeId);
            Integer finalResourceStructureId = Integer.parseInt(resourceStructureId.split("_")[0]);
            Integer resourceStructureTypeId = 1;
            switch (Integer.parseInt(resourceStructureId.split("_")[1])) {
                case 200:
                    resourceStructureTypeId = 2;
                    break;
                case 7:
                    resourceStructureTypeId = 3;
                    break;
                case 9:
                    resourceStructureTypeId = 4;
                    break;
                case 10:
                    resourceStructureTypeId = 5;
                    break;
            }
            Integer finalResourceStructureTypeId = resourceStructureTypeId;
            EnergyObjectMap objMap = lstMap.stream().filter(
                    item -> item.getObjectId().equals(finalResourceStructureId) && item.getObjectIdType().equals(finalResourceStructureTypeId)
            ).findFirst().orElse(null);
            if (objMap == null) return result;

            int thisObjectLevel = countLevel(objMap.getLevelOfPath(), ".");
            List<EnergyObjectMap> childMap = lstMap.stream().filter(
                    item -> item.getLevelOfPath().contains(objMap.getLevelOfPath() + ".")
            ).collect(Collectors.toList());
            int thisItemLevel = 0;
            for (EnergyObjectMap item : childMap) {
                thisItemLevel = countLevel(item.getLevelOfPath(), ".");
                if (thisItemLevel - thisObjectLevel > result)
                    result = thisItemLevel - thisObjectLevel;
            }
        }
        return result;
    }

    // 获取有费方案的节点
    @Override
    public List<ResourceStructure> GetResourceStructureOfFee(int businessTypeId, int personId) {
        List<Integer> lstResourceStructure = energyElecFeeConfigService.getAllStructureIdsHasScheme(businessTypeId);
        List<ResourceStructure> allResourceStructure = resourceStructureManager.getAll().stream().filter(
                item -> lstResourceStructure.contains(item.getResourceStructureId())).collect(Collectors.toList());
        // 有权限的站点
        List<ResourceStructure> roleResourceStructures = resourceStructureService.findResourceStructureByUserId(personId);
        // 剔除无权限的站点
        //allResourceStructure.removeIf(rs -> !roleResourceStructures.contains(rs));
        for (ResourceStructure rs : allResourceStructure) {
            ResourceStructure findResourceStructure = roleResourceStructures.stream().filter(i -> i.getResourceStructureId().equals(
                    rs.getResourceStructureId())).findFirst().orElse(null);
            if (findResourceStructure == null)
                allResourceStructure.remove(rs);
        }
        return allResourceStructure;
    }

    // 模拟指标历史数据
    @Override
    public String historyComplexIndexSimulator(Integer times, Integer seconds, Integer number, Date date, String complexIndexIds, Double value) {
        if (seconds != null && seconds.equals(-99)) {
            simulatorHistoryComplex(times, date);   //模拟能耗指标历史数据随机数
            return "";
        }
        List<ComplexIndex> complexIndices = new ArrayList<>();
        String result = "执行成功";
        try {
            if (complexIndexIds != null && !complexIndexIds.equals("")) {
                for (Integer i : StringUtils.getIntegerListByString(complexIndexIds)) {
                    complexIndices.add(complexIndexService.findByComplexIndexId(i));
                }
                // complexIndices = complexIndexService.findByComplexIndexIds(StringUtils.getIntegerListByString(complexIndexIds).toArray());
            } else if (number == null || number == 0) {
                complexIndices = complexIndexService.findAll();
            }
            if (date == null) {
                date = new Date();
            }
            if (times != null && times > 0 && seconds != null && seconds > 0) {
                for (int i = 0; i < times; i++) {
                    createHistoryComplexIndex(complexIndices, value, date);
                    date = DateUtil.dateAddSeconds(date, seconds);
                }
            } else {
                createHistoryComplexIndex(complexIndices, value, date);
            }
        } catch (Exception e) {
            result = "执行失败：" + e.getMessage();
        }
        return result;
    }

    private void createHistoryComplexIndex(List<ComplexIndex> complexIndices, Double value, Date date) {
        List<Point> lstPoint = new ArrayList<>();
        for (ComplexIndex complexIndex : complexIndices) {
//            Map<String, String> tags = new HashMap<>();
//            Map<String, Object> fields = new HashMap<>();
//            tags.put("CalcTime", (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(date));
//            //tags.put("CalcTime", String.valueOf(complexIndex.getCalcCron()));
//            tags.put("ComplexIndexId", String.valueOf(complexIndex.getComplexIndexId()));
//            tags.put("BusinessTypeId", "");
//            if (complexIndex.getComplexIndexName().toLowerCase().contains("ue")) {
//                fields.put("IndexValue", value == null ?  NumberUtil.doubleAccuracy(Math.random()*0.3 + 1.2,2) : value);
//            }else{
//                fields.put("IndexValue", value == null ?  NumberUtil.doubleAccuracy(Math.random() * 100,2) : value);
//            }
//            fields.put("Unit", complexIndex.getUnit());
//            tags.put("Abnormal", String.valueOf(0));
//            influxDBManager.insertWithTime("HistoryComplexIndex", tags, fields, DateUtil.localToUTC(date).getTime());

            Double IndexValue = 0d;
            if (complexIndex.getComplexIndexName().toLowerCase().contains("ue")) {
                IndexValue = value == null ? NumberUtil.doubleAccuracy(Math.random() * 0.3 + 1.2, 2) : value;
            } else {
                IndexValue = value == null ? NumberUtil.doubleAccuracy(Math.random() * 100, 2) : value;
            }
            // 直接加入数据库存储队列
            Point point = Point.measurement("HistoryComplexIndex")
                    .time(DateUtil.localToUTC(date).getTime(), TimeUnit.MILLISECONDS)
                    //.tag("CalcTime",dateToString(date))
                    .tag("ComplexIndexId", String.valueOf(complexIndex.getComplexIndexId()))
                    .tag("BusinessTypeId", complexIndex.getBusinessTypeId() == null ? "" : complexIndex.getBusinessTypeId().toString())
                    .tag("Abnormal", String.valueOf(0))
                    .addField("CalcTime", dateToString(date))
                    .addField("IndexValue", NumberUtil.doubleAccuracy(IndexValue, 2))
                    .addField("Unit", complexIndex.getUnit() == null ? "" : complexIndex.getUnit())
                    .build();
            lstPoint.add(point);
        }

        //数据点仓库批量存储到日表
        influxDBManager.batchInsertPoint(lstPoint);
    }

    //15.1 模拟能耗历史数据
    @Override
    public String energyHisDataSimulator(Integer tableType, Integer times, Integer number, Date date, String complexIndexIds, Double value) {
        List<ComplexIndex> complexIndices = new ArrayList<>();
        String result = "执行成功";
        try {
            if (complexIndexIds != null && !complexIndexIds.equals("")) {
                for (Integer i : StringUtils.getIntegerListByString(complexIndexIds)) {
                    complexIndices.add(complexIndexService.findByComplexIndexId(i));
                }
                // complexIndices = complexIndexService.findByComplexIndexIds(StringUtils.getIntegerListByString(complexIndexIds).toArray());
            } else if (number == null || number == 0) {

                //所有能源类型
                List<ComplexIndexBusinessType> lstEnergyComplexIndexBusinessType = complexIndexBusinessTypeService.GetComplexIndexBusinessTypeByParentid(1);
                //所有能耗指标
                complexIndices = energyComplexIndexManager.GetAllComplexIndex().stream().filter(
                        i -> lstEnergyComplexIndexBusinessType.stream().anyMatch(item -> item.getBusinessTypeId().equals(i.getBusinessTypeId()))
                ).collect(Collectors.toList());
            }
            if (date == null) {
                date = new Date();
            }
            if (times != null && times > 0) {
                for (int i = 0; i < times; i++) {
                    createEnergyHisData(tableType, complexIndices, value, date);
                    switch (tableType) {
                        case 1:
                            date = DateUtil.dateAddMonth(date, 1);
                            break;
                        case 2:
                            date = DateUtil.dateAddDays(date, 1);
                            break;
                        case 3:
                            date = DateUtil.dateAddHours(date, 1);
                            break;
                        case 4:
                            date = DateUtil.dateAddHours(date, 1);
                            break;
                    }
                }
            } else {
                createHistoryComplexIndex(complexIndices, value, date);
            }
        } catch (Exception e) {
            result = "执行失败：" + e.getMessage();
        }
        return result;
    }

    private void createEnergyHisData(Integer tableType, List<ComplexIndex> complexIndices, Double value, Date date) {
        List<Point> lstPoint = new ArrayList<>();
        String measurementName = "EnergyHisMonthData";
        switch (tableType) {
            case 1:
                measurementName = "EnergyHisMonthData";
                break;
            case 2:
                measurementName = "EnergyHisDayData";
                break;
            case 3:
                measurementName = "EnergyHisHourData";
                break;
        }

        log.error("进入createEnergyHisData" + dateToString(date));
        if (tableType == 4) {
            for (int i = 0; i < 2; i++) {
                String ComplexIndexId = "1288";
                if (i == 1) {
                    ComplexIndexId = "1289";
                }
                double pointValue = NumberUtil.doubleAccuracy(Math.random() * 100 + 1.2, 2);
                Point point = Point.measurement("EnergyHisHourData")
                        .time(DateUtil.localToUTC(date).getTime(), TimeUnit.MILLISECONDS)
                        //.tag("CalcTime", dateToString(date))
                        .tag("ComplexIndexId", ComplexIndexId)
                        .tag("Abnormal", String.valueOf(0))
                        .tag("BusinessTypeId", "2")
                        .addField("IndexValue", NumberUtil.doubleAccuracy(pointValue, 2))
                        .addField("OriginalValue", NumberUtil.doubleAccuracy(pointValue, 2))
                        .addField("Unit", "KWh")
                        .build();
                lstPoint.add(point);

                log.error("开始batchInsertPoint" + dateToString(new Date()) + "  " + dateToString(date));
                //数据点仓库批量存储到表
                influxDBManager.batchInsertPoint(lstPoint, "sitewebenergy_v2");
            }
            return;
        }

        for (ComplexIndex complexIndex : complexIndices) {
            Double IndexValue = 0d;
            if (complexIndex.getComplexIndexName().toLowerCase().contains("ue")) {
                IndexValue = value == null ? NumberUtil.doubleAccuracy(Math.random() * 0.3 + 1.2, 2) : value;
            } else {
                IndexValue = value == null ? NumberUtil.doubleAccuracy(Math.random() * 100, 2) : value;

                if (tableType == 1)
                    IndexValue = IndexValue * 30 * 24;
                else if (tableType == 2)
                    IndexValue = IndexValue * 24;
            }

            // 直接加入数据库存储队列
            Point point = Point.measurement(measurementName)
                    .time(DateUtil.localToUTC(date).getTime(), TimeUnit.MILLISECONDS)
                    //.tag("CalcTime", dateToString(date))
                    .tag("ComplexIndexId", String.valueOf(complexIndex.getComplexIndexId()))
                    .tag("Abnormal", String.valueOf(0))
                    .tag("BusinessTypeId", "2")
                    .addField("IndexValue", NumberUtil.doubleAccuracy(IndexValue, 2))
                    .addField("OriginalValue", NumberUtil.doubleAccuracy(IndexValue, 2))
                    .addField("Unit", complexIndex.getUnit() == null ? "" : complexIndex.getUnit())
                    .build();
            lstPoint.add(point);
        }
        log.error("开始batchInsertPoint" + dateToString(new Date()) + "  " + dateToString(date));
        //数据点仓库批量存储到表
        influxDBManager.batchInsertPoint(lstPoint, "sitewebenergy_v2");
        log.error("结束batchInsertPoint" + dateToString(new Date()));
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            log.error("createEnergyHisData", e);
        }
    }


    // 能耗小时日月表数据模拟
    @Override
    public String energyHisDataSimulatorMonthDayHour(Date startTime, Date endTime, Integer complexIndexId, Double hourValue, boolean isAvg) {
        String result = "执行成功";
        List<Point> lstPoint = new ArrayList<>();
        Date tempTime = startTime;

        Random random = new Random();
        Double minHourValue = hourValue * 0.95;
        Double maxHourValue = hourValue * 1.05;
//        //插入小时数据
        while (tempTime.before(endTime)) {
            Double randomHourValue = NumberUtil.doubleAccuracy(Math.random() * (maxHourValue - minHourValue + 1) + minHourValue, 2);
            //Double randomHourValue = NumberUtil.doubleAccuracy(random.nextDouble(maxHourValue-minHourValue+1)+minHourValue,2);
            if (isAvg)
                randomHourValue = NumberUtil.doubleAccuracy(Math.random() * 0.15 + 1.2, 2);
            // 直接加入数据库存储队列
            Point point = Point.measurement("EnergyHisHourData")
                    .time(DateUtil.localToUTC(tempTime).getTime(), TimeUnit.MILLISECONDS)
                    //.tag("CalcTime", dateToString(tempTime))
                    .tag("ComplexIndexId", complexIndexId.toString())
                    .tag("Abnormal", String.valueOf(0))
                    .tag("BusinessTypeId", "2")
                    .addField("IndexValue", randomHourValue)
                    .addField("OriginalValue", randomHourValue)
                    .addField("Unit", "KWh")
                    .build();
            lstPoint.add(point);
            tempTime = DateUtil.dateAddHours(tempTime, 1);
        }
        if (lstPoint.size() > 0) {
            log.info("开始batchInsertPoint小时表" + dateToString(new Date()));
            //数据点仓库批量存储到表
            influxDBManager.batchInsertPoint(lstPoint, "sitewebenergy_v2");
            log.info("结束batchInsertPoint小时表" + dateToString(new Date()));
        }


        //处理日表
        lstPoint = new ArrayList<>();
        String selectDuration = "select sum(IndexValue) as result from EnergyHisHourData where time >=$startTime and time <=$endTime and ComplexIndexId = $complexIndexId GROUP BY time(1d)";
        if (isAvg) {
            selectDuration = "select mean(IndexValue) as result from EnergyHisHourData where time >=$startTime and time <=$endTime and ComplexIndexId = $complexIndexId GROUP BY time(1d)";
        }

        QueryResult query = null;
        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();

        Date dayStartTime = DateUtil.dateFirstTimeFormat(startTime);
        Date dayEndTime = DateUtil.dateAddSeconds(DateUtil.dateAddDays(DateUtil.dateFirstTimeFormat(endTime), 1), -1);

        Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                .forDatabase(databaseEnergy)
                .bind("complexIndexId", complexIndexId.toString())
                .bind("startTime", dateToString(dayStartTime))
                .bind("endTime", dateToString(dayEndTime))
                .create();
        query = influxDB.query(queryBuilder);
        if (query != null) {
            List<EnergyHisDataResult> resultComplexIndexQuery = new ArrayList<>();
            resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisDataResult.class, "EnergyHisHourData");
            if (!resultComplexIndexQuery.isEmpty()) {
                for (EnergyHisDataResult temp : resultComplexIndexQuery) {
                    try {
                        Date time = stringToDate(temp.getTime().replace("T", " ").replace("Z", ""));
                        Double tempValue = NumberUtil.doubleAccuracy(Double.parseDouble(temp.getResult()), 2);

                        Point point = Point.measurement("EnergyHisDayData")
                                .time(DateUtil.localToUTC(time).getTime(), TimeUnit.MILLISECONDS)
                                //.tag("CalcTime", dateToString(time))
                                .tag("ComplexIndexId", complexIndexId.toString())
                                .tag("Abnormal", String.valueOf(0))
                                .tag("BusinessTypeId", "2")
                                .addField("IndexValue", tempValue)
                                .addField("OriginalValue", tempValue)
                                .addField("Unit", "KWh")
                                .build();
                        lstPoint.add(point);

                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        }

        if (lstPoint.size() > 0) {
            log.info("开始batchInsertPoint日表" + dateToString(new Date()));
            //数据点仓库批量存储到表
            influxDBManager.batchInsertPoint(lstPoint, "sitewebenergy_v2");
            log.info("结束batchInsertPoint日表" + dateToString(new Date()));
        }


        //月表
        lstPoint = new ArrayList<>();
        selectDuration = "select sum(IndexValue) as result from EnergyHisDayData where time >=$startTime and time <=$endTime and ComplexIndexId = $complexIndexId GROUP BY ComplexIndexId";
        if (isAvg) {
            selectDuration = "select mean(IndexValue) as result from EnergyHisDayData where time >=$startTime and time <=$endTime and ComplexIndexId = $complexIndexId GROUP BY ComplexIndexId";
        }
        query = null;
        resultMapper = new InfluxDBResultMapper();

        tempTime = startTime;

        while (tempTime.before(endTime)) {

            Date monthStartTime = DateUtil.getMonthStartTime(tempTime);
            Date monthEndTime = DateUtil.getMonthEndTime(monthStartTime);

            queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                    .forDatabase(databaseEnergy)
                    .bind("complexIndexId", complexIndexId.toString())
                    .bind("startTime", dateToString(monthStartTime))
                    .bind("endTime", dateToString(monthEndTime))
                    .create();
            query = influxDB.query(queryBuilder);
            if (query != null) {
                List<EnergyHisDataResult> resultComplexIndexQuery = new ArrayList<>();
                resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisDataResult.class, "EnergyHisDayData");
                if (!resultComplexIndexQuery.isEmpty()) {
                    for (EnergyHisDataResult temp : resultComplexIndexQuery) {
                        Double tempValue = NumberUtil.doubleAccuracy(Double.parseDouble(temp.getResult()), 2);
                        Date time = stringToDate(temp.getTime().replace("T", " ").replace("Z", ""));
                        Point point = Point.measurement("EnergyHisMonthData")
                                .time(DateUtil.localToUTC(monthStartTime).getTime(), TimeUnit.MILLISECONDS)
                                //.tag("CalcTime", dateToString(time))
                                .tag("ComplexIndexId", complexIndexId.toString())
                                .tag("Abnormal", String.valueOf(0))
                                .tag("BusinessTypeId", "2")
                                .addField("IndexValue", tempValue)
                                .addField("OriginalValue", tempValue)
                                .addField("Unit", "KWh")
                                .build();
                        lstPoint.add(point);
                    }
                }
            }

            tempTime = DateUtil.dateAddMonth(monthStartTime, 1);
        }

        if (lstPoint.size() > 0) {
            log.info("开始batchInsertPoint月表" + dateToString(new Date()));
            //数据点仓库批量存储到表
            influxDBManager.batchInsertPoint(lstPoint, "sitewebenergy_v2");
            log.info("结束batchInsertPoint月表" + dateToString(new Date()));
        }

        return result;
    }

    @Override
    public String WriteEnergyHisMonthUEData(Date startTime, Integer complexIndexId, Double value) {

        try {
            List<Point> lstPointUE = new ArrayList<>();
            Point pointue = Point.measurement("EnergyHisMonthUEData")
                    .time(DateUtil.localToUTC(startTime).getTime(), TimeUnit.MILLISECONDS)
                    .tag("ComplexIndexId", String.valueOf(complexIndexId))
                    .addField("IndexValue", NumberUtil.doubleAccuracy(value, 2))
                    .addField("CalcTime", dateToString(startTime))
                    .build();
            //加入数据点仓库
            lstPointUE.add(pointue);
            influxDBManager.batchInsertPoint(lstPointUE, databaseEnergy);
        } catch (Exception ex) {
            return "执行失败";
        }
        return "执行成功";
    }

    @Override
    public String WriteEnergyHisData(Date startTime, Integer complexIndexId, Double value, String tableName) {
        try {
            List<Point> lstPoint = new ArrayList<>();
            Point point = Point.measurement(tableName)
                    .time(DateUtil.localToUTC(startTime).getTime(), TimeUnit.MILLISECONDS)
                    //.tag("CalcTime", dateToString(startTime))
                    .tag("ComplexIndexId", String.valueOf(complexIndexId))
                    .tag("Abnormal", "0")
                    .tag("BusinessTypeId", "2")
                    .addField("IndexValue", NumberUtil.doubleAccuracy(value, 2))
                    .addField("OriginalValue", NumberUtil.doubleAccuracy(value, 2))
                    .addField("Unit", "")
                    .build();
            //加入数据点仓库
            lstPoint.add(point);
            influxDBManager.batchInsertPoint(lstPoint, databaseEnergy);
        } catch (Exception ex) {
            return "执行失败";
        }
        return "执行成功";
    }

    @Override
    public String DeleteEnergyHisData(Date startTime, Integer complexIndexId, String tableName) {
        String deleteDuration = "delete from " + tableName + " where time = $startTime and ComplexIndexId='" + complexIndexId + "'";
        try {
            //删除数据
            Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(deleteDuration)
                    .forDatabase(databaseEnergy)
                    .bind("startTime", dateToString(startTime))
                    .create();
            boolean delResult = true;
            int delCount = 0;
            do {
                try {
                    influxDB.query(queryBuilder);
                    delResult = true;
                } catch (Exception ex) {
                    delResult = false;
                    delCount++;
                }
            } while (!delResult && delCount < 3);

        } catch (Exception ex) {
            log.error(ex.getMessage());
            return "执行失败";
        }
        return "执行成功";
    }

    @Override
    public List<Object> getAllRootInflowNodes(Integer dimensionTypeId) {
        if (dimensionTypeId == null || dimensionTypeId <= 0) {
            return new ArrayList<>();
        }
        List<EnergyResourceStructure> nodeList = objectMapMapper.findAllRootInflowNodeByDimensionTypeId(dimensionTypeId, SourceCategory.ENERGY_RESOURCE.value(), SourceCategory.RESOURCE_STRUCTURE.value(),
                EnergyDimensionComplexIndexServiceImpl.DIMENSION_STRUCTURE_OBJECTTYPEID, SourceCategory.EQUIPMENT.value(), SourceCategory.COMPUTERRACK.value(), SourceCategory.ITDEVICE.value());
        if (nodeList == null || nodeList.size() < 1) {
            return new ArrayList<>();
        } else {
            List<Object> list = new ArrayList<>();
            EnergyResourceStructure rootNode = nodeList.stream().filter(item -> Objects.equals(item.getParentObjectId(), 0)).findFirst().orElse(null);
            if (rootNode == null) {
                log.error("dimensionTypeId=" + dimensionTypeId + " do not has rootNode");
                rootNode = new EnergyResourceStructure();
            }
            for (EnergyResourceStructure node : nodeList) {
                if (Objects.equals(node.getAsRootInflowNode(), EnergyStructureServiceImpl.IS_ROOT_INFLOW_NODE)) {
                    EnergyResourceStructure finalRootNode = rootNode;
                    list.add(new Object() {
                        public Integer getNodeId() {
                            return node.getObjectId();
                        }

                        public String getNodeName() {
                            return node.getResourceStructureName();
                        }

                        public Integer getNodeTypeId() {
                            return node.getObjectTypeId();
                        }

                        public Integer getParentNodeId() {
                            return finalRootNode.getObjectId();
                        }

                        public String getParentNodeName() {
                            return finalRootNode.getResourceStructureName();
                        }

                        public Integer getParentNodeTypeId() {
                            return finalRootNode.getObjectTypeId();
                        }
                    });
                }
            }
            return list;
        }
    }

    //台玻三路进线根节点父节点供电量
    @Override
    public Object getPowerRatio(Integer dimensionTypeId, Integer businessTypeId, Date startTime, Date endTime, String timeType, Integer complexIndexDefinitionIds) {
        List<EnergyResourceStructure> rootFatherObject = objectMapMapper.findAllRootFatherInflowNodeByDimensionTypeId(dimensionTypeId, SourceCategory.ENERGY_RESOURCE.value(), SourceCategory.RESOURCE_STRUCTURE.value(),
                EnergyDimensionComplexIndexServiceImpl.DIMENSION_STRUCTURE_OBJECTTYPEID, SourceCategory.EQUIPMENT.value(), SourceCategory.COMPUTERRACK.value(), SourceCategory.ITDEVICE.value());

        if (rootFatherObject.isEmpty())
            return null;

        List<EnergyTaiBoResultDTO> result = new ArrayList<>();
        List<EnergyReportTrendResultDTO> lstObjectAndComplexIndexId = new ArrayList<>();
        for (EnergyResourceStructure es : rootFatherObject) {
            ComplexIndex complexIndex = energyComplexIndexManager.GetAllComplexIndex().stream().filter(
                    i -> i.getObjectId().equals(es.getObjectId()) && i.getObjectTypeId().equals(es.getObjectTypeId())
                            && i.getBusinessTypeId().equals(businessTypeId) && i.getComplexIndexDefinitionId().equals(complexIndexDefinitionIds)
            ).findFirst().orElse(null);
            EnergyReportTrendResultDTO en = new EnergyReportTrendResultDTO();
            en.setObjectId(es.getObjectId());
            en.setObjectName(es.getResourceStructureName());
            en.setObjectTypeId(es.getObjectTypeId());
            en.setComplexIndexId(complexIndex == null ? null : complexIndex.getComplexIndexId());
            en.setComplexIndexDefinitionId(complexIndexDefinitionIds);
            en.setUnit(complexIndex == null ? null : complexIndex.getUnit());
            lstObjectAndComplexIndexId.add(en);
        }
        lstObjectAndComplexIndexId.forEach(dto -> result.add(new EnergyTaiBoResultDTO(dto)));
        getPowerRatioList(result, startTime, endTime, timeType, false);
        return result;
    }


    private void getPowerRatioList(List<EnergyTaiBoResultDTO> result, Date startTime, Date endTime, String timeType, Boolean isAvg) {
        try {
            String selectDuration = "";
            if (isAvg) {
                selectDuration = "select mean(IndexValue) as result from EnergyHisDayData where time >=$startTime and time <=$endTime and ($someComplexIndex) group by ComplexIndexId";

                if (timeType.equals("y"))
                    selectDuration = "select mean(IndexValue) as result from EnergyHisMonthData where time >=$startTime and time <=$endTime and ($someComplexIndex) group by ComplexIndexId";

                if (timeType.equals("d"))
                    selectDuration = "select mean(IndexValue) as result from EnergyHisHourData where time >=$startTime and time <=$endTime and ($someComplexIndex) group by ComplexIndexId";
            } else {
                selectDuration = "select sum(IndexValue) as result from EnergyHisDayData where time >=$startTime and time <=$endTime and ($someComplexIndex) group by ComplexIndexId";

                if (timeType.equals("y"))
                    selectDuration = "select sum(IndexValue) as result from EnergyHisMonthData where time >=$startTime and time <=$endTime and ($someComplexIndex) group by ComplexIndexId";

                if (timeType.equals("d"))
                    selectDuration = "select sum(IndexValue) as result from EnergyHisHourData where time >=$startTime and time <=$endTime and ($someComplexIndex) group by ComplexIndexId";
            }

            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();

            int count = 0;
            StringBuilder bsWhereComplexIndexId = new StringBuilder();
            for (EnergyTaiBoResultDTO eto : result) {
                if (bsWhereComplexIndexId.length() == 0) {
                    bsWhereComplexIndexId.append(" ComplexIndexId='" + eto.getComplexIndexId() + "' ");
                } else {
                    bsWhereComplexIndexId.append(" or ComplexIndexId='" + eto.getComplexIndexId() + "' ");
                }
                count++;
                if (count == result.size() || count % 500 == 0) {

                    String strSelectDuration = selectDuration.replace("$someComplexIndex", bsWhereComplexIndexId.toString());
                    Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(strSelectDuration)
                            .forDatabase(databaseEnergy)
                            .bind("startTime", dateToString(startTime))
                            .bind("endTime", dateToString(endTime))
                            .create();

                    query = influxDB.query(queryBuilder);
                    if (query != null) {
                        if (timeType.equals("d")) {
                            List<EnergyHisHourDataResult> resultComplexIndexQuery = new ArrayList<>();
                            resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisHourDataResult.class);
                            if (!resultComplexIndexQuery.isEmpty()) {
                                for (EnergyHisHourDataResult temp : resultComplexIndexQuery) {
                                    EnergyTaiBoResultDTO et = result.stream().filter(i -> i.getComplexIndexId().equals(Integer.valueOf(temp.getComplexIndexId()))).findFirst().orElse(null);
                                    if (et != null) {
                                        et.setSumValue(NumberUtil.doubleAccuracy(Double.parseDouble(temp.getResult()), 2));
                                    }
                                }
                            }
                        } else if (timeType.equals("m") || timeType.equals("o")) {
                            List<EnergyHisDayDataResult> resultComplexIndexQuery = new ArrayList<>();
                            resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisDayDataResult.class);
                            if (!resultComplexIndexQuery.isEmpty()) {
                                for (EnergyHisDayDataResult temp : resultComplexIndexQuery) {
                                    EnergyTaiBoResultDTO et = result.stream().filter(i -> i.getComplexIndexId().equals(Integer.valueOf(temp.getComplexIndexId()))).findFirst().orElse(null);
                                    if (et != null) {
                                        et.setSumValue(NumberUtil.doubleAccuracy(Double.parseDouble(temp.getResult()), 2));
                                    }
                                }
                            }
                        } else {
                            List<EnergyHisMonthDataResult> resultComplexIndexQuery = new ArrayList<>();
                            resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisMonthDataResult.class);
                            if (!resultComplexIndexQuery.isEmpty()) {
                                for (EnergyHisMonthDataResult temp : resultComplexIndexQuery) {
                                    EnergyTaiBoResultDTO et = result.stream().filter(i -> i.getComplexIndexId().equals(Integer.valueOf(temp.getComplexIndexId()))).findFirst().orElse(null);
                                    if (et != null) {
                                        et.setSumValue(NumberUtil.doubleAccuracy(Double.parseDouble(temp.getResult()), 2));
                                    }
                                }
                            }
                        }
                    }
                    bsWhereComplexIndexId = new StringBuilder();
                }
            }
        } catch (Exception e) {
            log.error("EnergyOverViewServiceImpl-getPowerRatioList error{}" + e.getMessage());
        }
    }

    @Override
    public Object getWaterType(String resourceStructureId, Integer dimensionTypeId, Integer businessTypeId, Date startTime, Date endTime, String timeType) {
        Integer objectId = Integer.parseInt(resourceStructureId.split("_")[0]);
        //生产水
        Integer waterComplexIndexDefinitionId1 = 40;
        //回收水
        Integer waterComplexIndexDefinitionId2 = 41;
        //此指标的所有用能分项
        List<BusinessDefinitionMap> thisMap = energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(
                i -> i.getBusinessTypeId().equals(businessTypeId) && i.getComplexIndexDefinitionTypeId().equals(1)).toList();

        if (thisMap.isEmpty()) return null;
        EnergyTaiBoObjectData currentObject;
        //该多维度上该节点的所有子节点ID
        List<EnergyTaiBoObjectData> allDimensionChildObject = new ArrayList<>();
        if (dimensionTypeId == -1) {
            allDimensionChildObject = objectMapMapper.findChildObjectInResourcestructureById(objectId);
        } else {
            currentObject = objectMapMapper.findObjectInfoInDimensionMap(dimensionTypeId, objectId);
            currentObject.setAsRootInflowNode(currentObject.getAsRootInflowNode() == null ? 0 : currentObject.getAsRootInflowNode());

            //当前节点是根节点的父节点
            if (currentObject.getAsRootInflowNode() == 1) {
                EnergyObjectMap rootObject = objectMapMapper.findRootNodeByDimensionTypeId(dimensionTypeId).get(0);
                EnergyTaiBoObjectData temp = new EnergyTaiBoObjectData();
                temp.setObjectId(rootObject.getObjectId());
                allDimensionChildObject.add(temp);
            } else {
                allDimensionChildObject = objectMapMapper.findChildObjectById(dimensionTypeId, objectId);
            }
        }

        List<EnergyPowerType> energyPowerTypes = new ArrayList<>();
        EnergyPowerType energypowertype = new EnergyPowerType();
        List<ComplexIndex> allObjectFilterComplexIndex = new ArrayList<>();
        //配置节点的指标信息
        for (EnergyTaiBoObjectData em : allDimensionChildObject) {
            // 获取此节点符合的指标
            List<ComplexIndex> ComplexIndexOfStructure = energyComplexIndexManager.GetAllComplexIndex().stream().filter(
                    i -> i.getObjectId().equals(em.getObjectId())
                            && i.getBusinessTypeId().equals(businessTypeId)
                            && (i.getComplexIndexDefinitionId().equals(waterComplexIndexDefinitionId1)
                            || i.getComplexIndexDefinitionId().equals(waterComplexIndexDefinitionId2))).toList();
            allObjectFilterComplexIndex.addAll(ComplexIndexOfStructure);
        }
        if (allObjectFilterComplexIndex.isEmpty())
            return null;
        //将所有指标按分分项类型把值分隔开
        Map<Integer, List<ComplexIndex>> groupedMap = allObjectFilterComplexIndex.stream()
                .collect(Collectors.groupingBy(ComplexIndex::getComplexIndexDefinitionId));

        List<List<ComplexIndex>> groupedLists = new ArrayList<>(groupedMap.values());

        String complexIndexSum = null;
        for (List<ComplexIndex> list : groupedLists) {
            energypowertype = new EnergyPowerType();
            energypowertype.ComplexIndexName = list.get(0).getComplexIndexName();
            complexIndexSum = getHistoryComplexIndexSum(startTime, endTime, list, timeType);
            if (complexIndexSum == null) {
                energypowertype.ComplexIndexValue = 0.0;
            } else {
                energypowertype.ComplexIndexValue = Double.parseDouble(String.format("%.2f", Double.parseDouble(complexIndexSum)));
            }
            energyPowerTypes.add(energypowertype);
        }

        return energyPowerTypes;
    }


    private String getHistoryComplexIndexSum(Date startTime, Date endTime, List<ComplexIndex> complexIndexList, String timeType) {
        try {
            String selectDuration = "select sum(IndexValue) as result from EnergyHisDayData where time >=$startTime and time <=$endTime and ($someComplexIndex) ";

            if (timeType.equals("y"))
                selectDuration = "select sum(IndexValue) as result from EnergyHisMonthData where time >=$startTime and time <=$endTime and ($someComplexIndex) ";

            if (timeType.equals("d"))
                selectDuration = "select sum(IndexValue) as result from EnergyHisHourData where time >=$startTime and time <=$endTime and ($someComplexIndex) ";

            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
            StringBuilder bsWhereComplexIndexId = new StringBuilder();
            int count = 0;
            for (ComplexIndex ci : complexIndexList) {
                if (bsWhereComplexIndexId.length() == 0) {
                    bsWhereComplexIndexId.append(" ComplexIndexId='" + ci.getComplexIndexId() + "' ");
                } else {
                    bsWhereComplexIndexId.append(" or ComplexIndexId='" + ci.getComplexIndexId() + "' ");
                }
                count++;
                if (count == complexIndexList.size() || count % 500 == 0) {

                    String strSelectDuration = selectDuration.replace("$someComplexIndex", bsWhereComplexIndexId.toString());
                    Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(strSelectDuration)
                            .forDatabase(databaseEnergy)
                            .bind("startTime", dateToString(startTime))
                            .bind("endTime", dateToString(endTime))
                            .create();

                    query = influxDB.query(queryBuilder);
                    if (query != null) {
                        if (timeType.equals("m") || timeType.equals("o")) {
                            List<EnergyHisDayDataResult> result = new ArrayList<>();
                            result = resultMapper.toPOJO(query, EnergyHisDayDataResult.class);
                            return result.isEmpty() ? null : result.get(0).getResult();

                        } else if (timeType.equals("d")) {
                            List<EnergyHisHourDataResult> result = new ArrayList<>();
                            result = resultMapper.toPOJO(query, EnergyHisHourDataResult.class);
                            return result.isEmpty() ? null : result.get(0).getResult();

                        } else {
                            List<EnergyHisMonthDataResult> result = new ArrayList<>();
                            result = resultMapper.toPOJO(query, EnergyHisMonthDataResult.class);
                            return result.isEmpty() ? null : result.get(0).getResult();

                        }
                    }
                    bsWhereComplexIndexId = new StringBuilder();
                }
            }
        } catch (Exception e) {
            log.error("EnergyOverViewServiceImpl-getHistoryComplexIndexSum error{}" + e.getMessage());
            return null;
        }
        return null;
    }


    @Override
    public Object getAirHistogram(String resourceStructureId, Integer dimensionTypeId, Integer businessTypeId, Date startTime, Date endTime, String timeType, int complexIndexDefinitionId) {
        List<EnergyTaiBoResultDTO> result = new ArrayList<>();
        Integer objectId = Integer.parseInt(resourceStructureId.split("_")[0]);
        //当前节点信息
        EnergyTaiBoObjectData currentObject;

        List<EnergyTaiBoObjectData> allDimensionChildObject = new ArrayList<>();
        //查询当前节点的字节点
        if (dimensionTypeId == -1) {
            currentObject = objectMapMapper.findObjectInfoByObjectId(objectId);
            allDimensionChildObject = objectMapMapper.findChildObjectInResourcestructureById(objectId);
        } else {
            currentObject = objectMapMapper.findObjectInfoInDimensionMap(dimensionTypeId, objectId);
            currentObject.setAsRootInflowNode(currentObject.getAsRootInflowNode() == null ? 0 : currentObject.getAsRootInflowNode());
            //当前节点是根节点的父节点
            if (currentObject.getAsRootInflowNode() == 1) {
                EnergyTaiBoObjectData rootObject = objectMapMapper.findRootNodeInfoByDimensionTypeId(dimensionTypeId);
                EnergyTaiBoObjectData temp = new EnergyTaiBoObjectData();
                temp.setObjectId(rootObject.getObjectId());
                temp.setStructureName(rootObject.getStructureName());
                allDimensionChildObject.add(temp);
            } else {
                allDimensionChildObject = objectMapMapper.findChildObjectById(dimensionTypeId, objectId);
            }
        }
        List<EnergyTaiBoObjectData> allNeedObject = new ArrayList<>(allDimensionChildObject);
        allNeedObject.add(currentObject);
        EnergyReportTrendResultDTO oneResult;
        List<EnergyReportTrendResultDTO> lstObjectAndComplexIndexId = new ArrayList<>();
        for (EnergyTaiBoObjectData object : allNeedObject) {
            oneResult = new EnergyReportTrendResultDTO();
            oneResult.setObjectName(object.getResourceStructureName() == null ? object.getStructureName() : object.getResourceStructureName());
            oneResult.setObjectId(object.getObjectId());
            //查找指标Id
            ComplexIndex complexIndex = energyComplexIndexManager.GetAllComplexIndex().stream().filter(
                    i -> i.getObjectId().equals(object.getObjectId())
                            && i.getBusinessTypeId().equals(businessTypeId) && i.getComplexIndexDefinitionId().equals(complexIndexDefinitionId)
            ).findFirst().orElse(null);
            oneResult.setComplexIndexId(complexIndex == null ? null : complexIndex.getComplexIndexId());
            oneResult.setComplexIndexDefinitionId(complexIndex == null ? null : complexIndex.getComplexIndexDefinitionId());
            lstObjectAndComplexIndexId.add(oneResult);
        }
        lstObjectAndComplexIndexId.forEach(dto -> result.add(new EnergyTaiBoResultDTO(dto)));
        getPowerRatioList(result, startTime, endTime, timeType, true);
        return result;
    }

    @Override
    public boolean getCustomerConfigResult(Integer configId) {
        EnergyCustomerConfig result = energyCustomerConfigMapper.selectById(configId);
        if (result == null)
            return false;
        return result.getEnable();
    }

    @Override
    public ComplexIndexValue GetUEByRsourceStructureIdByTimeType(int businessTypeId, String resourceStructureId, String timeType) {
        List<Date> timePeriod = getTimePeriod(timeType);
        return this.GetUEByRsourceStructureId(businessTypeId, resourceStructureId, timePeriod.get(0), timePeriod.get(1), timeType);
    }

    private static List<Date> getTimePeriod(String timetype) {
        List<Date> res = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime start, end;
        switch (timetype) {
            case "m" -> {
                start = now.with(TemporalAdjusters.firstDayOfMonth()).with(LocalTime.MIN);
                end = now.with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX);
            }
            case "y" -> {
                start = now.with(TemporalAdjusters.firstDayOfYear()).with(LocalTime.MIN);
                end = now.with(TemporalAdjusters.lastDayOfYear()).with(LocalTime.MAX);
            }
            case "d" -> {
                start = now.with(LocalTime.MIN);
                end = now.with(LocalTime.MAX);
            }
            default -> throw new IllegalArgumentException("Invalid timetype: " + timetype);
        }
        Date dateStart = Date.from(start.atZone(ZoneId.systemDefault()).toInstant());
        Date dateEnd = Date.from(end.atZone(ZoneId.systemDefault()).toInstant());
        res.add(dateStart);
        res.add(dateEnd);
        return res;
    }

    @Override
    public String getUnitByBusinessTypeId(Integer businessTypeId) {
        String result = "";
        List<ComplexIndex> lstThisBusinessTypeComplexIndex = complexIndexService.findByBusinessTypeId(businessTypeId);
        if (lstThisBusinessTypeComplexIndex != null && lstThisBusinessTypeComplexIndex.size() > 0) {
            ComplexIndex thisComplexIndex = lstThisBusinessTypeComplexIndex.stream().filter(i -> i.getUnit() != null && i.getUnit().length() > 0).findFirst().orElse(null);
            if (thisComplexIndex != null)
                result = thisComplexIndex.getUnit();
        }
        return result;
    }


    private String getLastComplexIndexValue(Integer complexIndexId) {
        String dvalue = null;
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT last(IndexValue) as IndexValue FROM HistoryComplexIndex ");
        sql.append(String.format(" WHERE ComplexIndexId = '%d' and  time >= now() - 1h", complexIndexId));
        List<HistoryComplexIndex> historyComplexIndices = influxDBManager.list(sql.toString(), HistoryComplexIndex.class);
        if (CollectionUtil.isNotEmpty(historyComplexIndices)) {
            dvalue = historyComplexIndices.get(0).getIndexValue();
        }
        dvalue = dvalue == null ? "0" : dvalue;
        return dvalue;
    }

    @Override
    public EnergyCustomerConfig getCustomerConfig(Integer configId) {
        return energyCustomerConfigMapper.selectById(configId);
    }

    /// 设计院模拟数据
    @Override
    public String simulateSheJiYuan() {
        List<Point> lstPoint = new ArrayList<>();
        List<EnergyHisDataMin> lstHourData = new ArrayList<>();
        List<EnergyHisDataMin> lstDayData = new ArrayList<>();

        //找到所有的主设备用电
        List<ComplexIndex> lstItComplexIndex = energyComplexIndexManager.GetAllComplexIndex().stream().filter(i -> i.getComplexIndexDefinitionId().equals(7)).collect(Collectors.toList());
        Date startTime = null, endTime = null, tempTime = null;
        try {
            startTime = (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).parse("2022-10-01 0:00:00");
            endTime = DateUtil.dateAddMonth(DateUtil.getMonthStartTime(new Date()), 2);
            tempTime = startTime;
        } catch (Exception ex) {
            log.error(ex.getMessage());
        }
        for (ComplexIndex itComplexIndex : lstItComplexIndex) {
            tempTime = startTime;
            lstPoint = new ArrayList<>();
            lstHourData = new ArrayList<>();
            lstDayData = new ArrayList<>();

            Double itValue = 0d, pueValue = 0d, upsValue = 0d, totalValue = 0d;
            Double shidianValue = 0d, youjiValue = 0d, guangfuValue = 0d, fengliValue = 0d;

            ComplexIndex upsComplexIndex = energyComplexIndexManager.GetAllComplexIndex()
                    .stream().filter(i -> i.getObjectId().equals(itComplexIndex.getObjectId()) && i.getComplexIndexDefinitionId().equals(8))
                    .findFirst().orElse(null);
            ComplexIndex pueComplexIndex = energyComplexIndexManager.GetAllComplexIndex()
                    .stream().filter(i -> i.getObjectId().equals(itComplexIndex.getObjectId()) && i.getComplexIndexDefinitionId().equals(5))
                    .findFirst().orElse(null);

            ComplexIndex shidianComplexIndex = energyComplexIndexManager.GetAllComplexIndex()
                    .stream().filter(i -> i.getObjectId().equals(itComplexIndex.getObjectId()) && i.getComplexIndexDefinitionId().equals(6))
                    .findFirst().orElse(null);
            ComplexIndex youjiComplexIndex = energyComplexIndexManager.GetAllComplexIndex()
                    .stream().filter(i -> i.getObjectId().equals(itComplexIndex.getObjectId()) && i.getComplexIndexDefinitionId().equals(24))
                    .findFirst().orElse(null);
            ComplexIndex guangfuComplexIndex = energyComplexIndexManager.GetAllComplexIndex()
                    .stream().filter(i -> i.getObjectId().equals(itComplexIndex.getObjectId()) && i.getComplexIndexDefinitionId().equals(25))
                    .findFirst().orElse(null);
            ComplexIndex fengliComplexIndex = energyComplexIndexManager.GetAllComplexIndex()
                    .stream().filter(i -> i.getObjectId().equals(itComplexIndex.getObjectId()) && i.getComplexIndexDefinitionId().equals(26))
                    .findFirst().orElse(null);

            if (shidianComplexIndex == null || youjiComplexIndex == null || guangfuComplexIndex == null || fengliComplexIndex == null)
                continue;

            //处理小时数据
            while (tempTime.before(endTime)) {
                itValue = NumberUtil.doubleAccuracy(ThreadLocalRandom.current().nextDouble(5, 10), 2);
                pueValue = NumberUtil.doubleAccuracy(ThreadLocalRandom.current().nextDouble(1.11, 1.49), 2);
                upsValue = itValue * pueValue - itValue;
                totalValue = NumberUtil.doubleAccuracy(itValue + upsValue, 2);

                youjiValue = NumberUtil.doubleAccuracy(ThreadLocalRandom.current().nextDouble(1.0, 1.2), 2);
                guangfuValue = NumberUtil.doubleAccuracy(ThreadLocalRandom.current().nextDouble(0.5, 0.8), 2);
                fengliValue = NumberUtil.doubleAccuracy(ThreadLocalRandom.current().nextDouble(0.2, 0.5), 2);
                shidianValue = NumberUtil.doubleAccuracy(totalValue - youjiValue - guangfuValue - fengliValue, 2);

                EnergyHisDataMin itHisHourData = getEnergyHisData(tempTime, itValue, itComplexIndex.getComplexIndexId());
                lstHourData.add(itHisHourData);
                insertListPoint(lstPoint, itHisHourData, "EnergyHisHourData");

                EnergyHisDataMin upsHisHourData = getEnergyHisData(tempTime, upsValue, upsComplexIndex.getComplexIndexId());
                lstHourData.add(upsHisHourData);
                insertListPoint(lstPoint, upsHisHourData, "EnergyHisHourData");

                EnergyHisDataMin pueHisHourData = getEnergyHisData(tempTime, pueValue, pueComplexIndex.getComplexIndexId());
                lstHourData.add(pueHisHourData);
                insertListPoint(lstPoint, pueHisHourData, "EnergyHisHourData");


                EnergyHisDataMin shidianHisHourData = getEnergyHisData(tempTime, shidianValue, shidianComplexIndex.getComplexIndexId());
                lstHourData.add(shidianHisHourData);
                insertListPoint(lstPoint, shidianHisHourData, "EnergyHisHourData");

                EnergyHisDataMin youjiHisHourData = getEnergyHisData(tempTime, youjiValue, youjiComplexIndex.getComplexIndexId());
                lstHourData.add(youjiHisHourData);
                insertListPoint(lstPoint, youjiHisHourData, "EnergyHisHourData");

                EnergyHisDataMin guangfuHisHourData = getEnergyHisData(tempTime, guangfuValue, guangfuComplexIndex.getComplexIndexId());
                lstHourData.add(guangfuHisHourData);
                insertListPoint(lstPoint, guangfuHisHourData, "EnergyHisHourData");

                EnergyHisDataMin fengliHisHourData = getEnergyHisData(tempTime, fengliValue, fengliComplexIndex.getComplexIndexId());
                lstHourData.add(fengliHisHourData);
                insertListPoint(lstPoint, fengliHisHourData, "EnergyHisHourData");

                tempTime = DateUtil.dateAddHours(tempTime, 1);
            }

            if (lstPoint.size() > 0)
                influxDBManager.batchInsertPoint(lstPoint, databaseEnergy);

            //处理天数据
            tempTime = startTime;
            lstPoint = new ArrayList<>();
            Date thisDayEndTime = null;
            while (tempTime.before(endTime)) {
                thisDayEndTime = DateUtil.dateAddSeconds(DateUtil.dateAddDays(tempTime, 1), -1);

                Date finalTempTime = tempTime;
                Date finalThisDayEndTime = thisDayEndTime;
                itValue = lstHourData.stream().filter(i -> i.getComplexIndexId().equals(itComplexIndex.getComplexIndexId())
                                && i.getTime().before(finalThisDayEndTime) && i.getTime().compareTo(finalTempTime) >= 0)
                        .mapToDouble(EnergyHisDataMin::getIndexValue)
                        .sum();

                EnergyHisDataMin itHisDayData = getEnergyHisData(tempTime, itValue, itComplexIndex.getComplexIndexId());
                lstDayData.add(itHisDayData);
                insertListPoint(lstPoint, itHisDayData, "EnergyHisDayData");

                upsValue = lstHourData.stream().filter(i -> i.getComplexIndexId().equals(upsComplexIndex.getComplexIndexId())
                                && i.getTime().before(finalThisDayEndTime) && i.getTime().compareTo(finalTempTime) >= 0)
                        .mapToDouble(EnergyHisDataMin::getIndexValue)
                        .sum();
                EnergyHisDataMin upsHisHourData = getEnergyHisData(tempTime, upsValue, upsComplexIndex.getComplexIndexId());
                lstDayData.add(upsHisHourData);
                insertListPoint(lstPoint, upsHisHourData, "EnergyHisDayData");

                shidianValue = lstHourData.stream().filter(i -> i.getComplexIndexId().equals(shidianComplexIndex.getComplexIndexId())
                                && i.getTime().before(finalThisDayEndTime) && i.getTime().compareTo(finalTempTime) >= 0)
                        .mapToDouble(EnergyHisDataMin::getIndexValue)
                        .sum();
                EnergyHisDataMin shidianHisHourData = getEnergyHisData(tempTime, shidianValue, shidianComplexIndex.getComplexIndexId());
                lstDayData.add(shidianHisHourData);
                insertListPoint(lstPoint, shidianHisHourData, "EnergyHisDayData");


                youjiValue = lstHourData.stream().filter(i -> i.getComplexIndexId().equals(youjiComplexIndex.getComplexIndexId())
                                && i.getTime().before(finalThisDayEndTime) && i.getTime().compareTo(finalTempTime) >= 0)
                        .mapToDouble(EnergyHisDataMin::getIndexValue)
                        .sum();
                EnergyHisDataMin youjiHisHourData = getEnergyHisData(tempTime, youjiValue, youjiComplexIndex.getComplexIndexId());
                lstDayData.add(youjiHisHourData);
                insertListPoint(lstPoint, youjiHisHourData, "EnergyHisDayData");

                guangfuValue = lstHourData.stream().filter(i -> i.getComplexIndexId().equals(fengliComplexIndex.getComplexIndexId())
                                && i.getTime().before(finalThisDayEndTime) && i.getTime().compareTo(finalTempTime) >= 0)
                        .mapToDouble(EnergyHisDataMin::getIndexValue)
                        .sum();
                EnergyHisDataMin guangfuHisHourData = getEnergyHisData(tempTime, guangfuValue, guangfuComplexIndex.getComplexIndexId());
                lstDayData.add(guangfuHisHourData);
                insertListPoint(lstPoint, guangfuHisHourData, "EnergyHisDayData");

                fengliValue = lstHourData.stream().filter(i -> i.getComplexIndexId().equals(fengliComplexIndex.getComplexIndexId())
                                && i.getTime().before(finalThisDayEndTime) && i.getTime().compareTo(finalTempTime) >= 0)
                        .mapToDouble(EnergyHisDataMin::getIndexValue)
                        .sum();
                EnergyHisDataMin fengliHisHourData = getEnergyHisData(tempTime, fengliValue, fengliComplexIndex.getComplexIndexId());
                lstDayData.add(fengliHisHourData);
                insertListPoint(lstPoint, fengliHisHourData, "EnergyHisDayData");

                pueValue = NumberUtil.doubleAccuracy((shidianValue + youjiValue + guangfuValue + fengliValue) / itValue, 2);
                EnergyHisDataMin pueHisHourData = getEnergyHisData(tempTime, pueValue, pueComplexIndex.getComplexIndexId());
                lstDayData.add(pueHisHourData);
                insertListPoint(lstPoint, pueHisHourData, "EnergyHisDayData");

                tempTime = DateUtil.dateAddDays(tempTime, 1);
            }

            if (lstPoint.size() > 0)
                influxDBManager.batchInsertPoint(lstPoint, databaseEnergy);


            //处理月数据
            tempTime = startTime;
            lstPoint = new ArrayList<>();
            Date thisMonthEndTime = null;
            while (tempTime.before(endTime)) {
                thisMonthEndTime = DateUtil.dateAddSeconds(DateUtil.dateAddMonth(tempTime, 1), -1);

                Date finalTempTime = tempTime;
                Date finalThisMonthEndTime = thisMonthEndTime;
                itValue = lstDayData.stream().filter(i -> i.getComplexIndexId().equals(itComplexIndex.getComplexIndexId())
                                && i.getTime().before(finalThisMonthEndTime) && i.getTime().compareTo(finalTempTime) >= 0)
                        .mapToDouble(EnergyHisDataMin::getIndexValue)
                        .sum();

                EnergyHisDataMin itHisDayData = getEnergyHisData(tempTime, itValue, itComplexIndex.getComplexIndexId());
                insertListPoint(lstPoint, itHisDayData, "EnergyHisMonthData");

                upsValue = lstDayData.stream().filter(i -> i.getComplexIndexId().equals(upsComplexIndex.getComplexIndexId())
                                && i.getTime().before(finalThisMonthEndTime) && i.getTime().compareTo(finalTempTime) >= 0)
                        .mapToDouble(EnergyHisDataMin::getIndexValue)
                        .sum();
                EnergyHisDataMin upsHisHourData = getEnergyHisData(tempTime, upsValue, upsComplexIndex.getComplexIndexId());
                insertListPoint(lstPoint, upsHisHourData, "EnergyHisMonthData");

                shidianValue = lstDayData.stream().filter(i -> i.getComplexIndexId().equals(shidianComplexIndex.getComplexIndexId())
                                && i.getTime().before(finalThisMonthEndTime) && i.getTime().compareTo(finalTempTime) >= 0)
                        .mapToDouble(EnergyHisDataMin::getIndexValue)
                        .sum();
                EnergyHisDataMin shidianHisHourData = getEnergyHisData(tempTime, shidianValue, shidianComplexIndex.getComplexIndexId());
                insertListPoint(lstPoint, shidianHisHourData, "EnergyHisMonthData");


                youjiValue = lstDayData.stream().filter(i -> i.getComplexIndexId().equals(youjiComplexIndex.getComplexIndexId())
                                && i.getTime().before(finalThisMonthEndTime) && i.getTime().compareTo(finalTempTime) >= 0)
                        .mapToDouble(EnergyHisDataMin::getIndexValue)
                        .sum();
                EnergyHisDataMin youjiHisHourData = getEnergyHisData(tempTime, youjiValue, youjiComplexIndex.getComplexIndexId());
                insertListPoint(lstPoint, youjiHisHourData, "EnergyHisMonthData");

                guangfuValue = lstDayData.stream().filter(i -> i.getComplexIndexId().equals(fengliComplexIndex.getComplexIndexId())
                                && i.getTime().before(finalThisMonthEndTime) && i.getTime().compareTo(finalTempTime) >= 0)
                        .mapToDouble(EnergyHisDataMin::getIndexValue)
                        .sum();
                EnergyHisDataMin guangfuHisHourData = getEnergyHisData(tempTime, guangfuValue, guangfuComplexIndex.getComplexIndexId());
                insertListPoint(lstPoint, guangfuHisHourData, "EnergyHisMonthData");

                fengliValue = lstDayData.stream().filter(i -> i.getComplexIndexId().equals(fengliComplexIndex.getComplexIndexId())
                                && i.getTime().before(finalThisMonthEndTime) && i.getTime().compareTo(finalTempTime) >= 0)
                        .mapToDouble(EnergyHisDataMin::getIndexValue)
                        .sum();
                EnergyHisDataMin fengliHisHourData = getEnergyHisData(tempTime, fengliValue, fengliComplexIndex.getComplexIndexId());
                insertListPoint(lstPoint, fengliHisHourData, "EnergyHisMonthData");

                pueValue = NumberUtil.doubleAccuracy((shidianValue + youjiValue + guangfuValue + fengliValue) / itValue, 2);
                EnergyHisDataMin pueHisHourData = getEnergyHisData(tempTime, pueValue, pueComplexIndex.getComplexIndexId());
                insertListPoint(lstPoint, pueHisHourData, "EnergyHisMonthData");

                tempTime = DateUtil.dateAddMonth(tempTime, 1);
            }

            if (lstPoint.size() > 0)
                influxDBManager.batchInsertPoint(lstPoint, databaseEnergy);

        }
        return "成功!";
    }

    public EnergyHisDataMin getEnergyHisData(Date time, Double value, Integer complexIndexId) {
        EnergyHisDataMin hourData = new EnergyHisDataMin();
        hourData.setTime(time);
        hourData.setComplexIndexId(complexIndexId);
        hourData.setIndexValue(value);
        return hourData;
    }

    public void insertListPoint(List<Point> lstPoint, EnergyHisDataMin hisData, String tableName) {
        try {
            Point point = Point.measurement(tableName)
                    .time(DateUtil.localToUTC(hisData.getTime()).getTime(), TimeUnit.MILLISECONDS)
                    //.tag("CalcTime", (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(hisData.getTime()))
                    .tag("ComplexIndexId", hisData.getComplexIndexId().toString())
                    .tag("Abnormal", "0")
                    .tag("BusinessTypeId", "2")
                    .addField("IndexValue", hisData.getIndexValue())
                    .addField("OriginalValue", hisData.getIndexValue())
                    .addField("Unit", "kWh")
                    .build();
            //加入数据点仓库
            lstPoint.add(point);
        } catch (Exception ex) {
            log.error(ex.getMessage());
        }
    }


    @Override
    public Object modifymonthpuehebei(Integer complexIndexId, Double value) {
        ComplexIndex thisComplexIndex = energyComplexIndexManager.GetAllComplexIndex().stream().filter(i -> i.getComplexIndexId().equals(complexIndexId))
                .findFirst().orElse(null);
        if (ObjectUtil.isEmpty(thisComplexIndex) || !thisComplexIndex.getComplexIndexDefinitionId().equals(5))
            return "指标不存在或非PUE指标";

        // 改写月PUE指标值
        Date startTime = DateUtil.getMonthStartTime(new Date());
        List<Point> lstPoint = new ArrayList<>();
        Point point = Point.measurement("EnergyHisMonthData")
                .time(DateUtil.localToUTC(startTime).getTime(), TimeUnit.MILLISECONDS)
                //.tag("CalcTime", dateToString(startTime))
                .tag("ComplexIndexId", complexIndexId.toString())
                .tag("Abnormal", "0")
                .tag("BusinessTypeId", "2")
                .addField("IndexValue", NumberUtil.doubleAccuracy(value, 2))
                .addField("OriginalValue", value)
                .addField("Unit", thisComplexIndex.getUnit() == null ? "" : thisComplexIndex.getUnit())
                .build();
        lstPoint.add(point);
        influxDBManager.batchInsertPoint(lstPoint, databaseEnergy);

        // 查找对应的分子分母指标Id, 写入UE表，值为-1
        Pattern p = Pattern.compile("(?<=ci\\(|last\\()[^\\)]+", Pattern.CASE_INSENSITIVE);
        Matcher m = p.matcher(thisComplexIndex.getExpression().toLowerCase());
        List<Point> tempLstPointUE = new ArrayList<>();
        while (m.find()) {
            Integer complexId = Integer.valueOf(m.group());
            Point pointue = Point.measurement("EnergyHisMonthUEData")
                    .time(DateUtil.localToUTC(startTime).getTime(), TimeUnit.MILLISECONDS)
                    .tag("ComplexIndexId", String.valueOf(complexId))
                    .addField("IndexValue", NumberUtil.doubleAccuracy(-1d, 2))
                    .addField("CalcTime", dateToString(new Date()))  //非tag  可记录为修改时间
                    .build();
            //加入数据点仓库
            tempLstPointUE.add(pointue);
        }
        if (tempLstPointUE.size() > 0) {
            influxDBManager.batchInsertPoint(tempLstPointUE, databaseEnergy);
        }
        return "成功，同步修改UE表指标数量：" + tempLstPointUE.size();
    }

    @Override
    public Object modifyhourdatahebei(Date modifyTime, Date usedTime, Integer hours, Integer tableType) {

        try {
            if (tableType == 1) {
                String selectDuration = "select * from EnergyHisHourData where time =$startTime  ";
                List<Point> lstPoint = new ArrayList<>();
                QueryResult query = null;
                InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
                Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                        .forDatabase(databaseEnergy)
                        .bind("startTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(usedTime))
                        .create();

                query = influxDB.query(queryBuilder);
                if (query != null) {
                    List<EnergyHisHourData> resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisHourData.class);
                    if (!resultComplexIndexQuery.isEmpty()) {
                        if (hours != null && hours > 0) {
                            // 按要求插入N个小时的
                            for (int h = 0; h < hours; h++) {
                                for (EnergyHisHourData temp : resultComplexIndexQuery) {
                                    Point point = Point.measurement("EnergyHisHourData")
                                            .time(DateUtil.localToUTC(modifyTime).getTime(), TimeUnit.MILLISECONDS)
                                            //.tag("CalcTime", dateToString(modifyTime))
                                            .tag("ComplexIndexId", String.valueOf(temp.getComplexIndexId()))
                                            .tag("Abnormal", "0")
                                            .tag("BusinessTypeId", "2")
                                            .addField("IndexValue", NumberUtil.doubleAccuracy(Double.parseDouble(temp.getIndexValue()), 2))
                                            .addField("OriginalValue", 0.00)
                                            .addField("Unit", temp.getUnit())
                                            .build();
                                    lstPoint.add(point);
                                }

                                // 这批小时数据插入能耗小时表
                                if (lstPoint.size() > 0) {
                                    try {
                                        influxDBManager.batchInsertPointRetry(lstPoint, databaseEnergy);
                                    } catch (Exception ex) {
                                        log.error("** 指标小时表写入报错。指标数量=" + lstPoint.size() + ex);
                                    }
                                }
                                modifyTime = DateUtil.dateAddHours(modifyTime, 1);
                                lstPoint.clear();
                            }
                            modifyTime = DateUtil.dateAddHours(modifyTime, -1);
                        }
                    }
                }

                return "成功！插入指标数量：" + lstPoint.size() + ", 最后小时时间为：" + dateToString(modifyTime);
            } else if (tableType == 2) {
                String selectDuration = "select * from EnergyHisDayData where time =$startTime  ";
                List<Point> lstPoint = new ArrayList<>();
                QueryResult query = null;
                InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
                Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                        .forDatabase(databaseEnergy)
                        .bind("startTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(usedTime))
                        .create();

                query = influxDB.query(queryBuilder);
                if (query != null) {
                    List<EnergyHisDayData> resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisDayData.class);
                    if (!resultComplexIndexQuery.isEmpty()) {
                        if (hours != null && hours > 0) {
                            // 按要求插入N个 的
                            for (int h = 0; h < hours; h++) {
                                for (EnergyHisDayData temp : resultComplexIndexQuery) {
                                    Point point = Point.measurement("EnergyHisDayData")
                                            .time(DateUtil.localToUTC(modifyTime).getTime(), TimeUnit.MILLISECONDS)
                                            //.tag("CalcTime", dateToString(modifyTime))
                                            .tag("ComplexIndexId", String.valueOf(temp.getComplexIndexId()))
                                            .tag("Abnormal", "0")
                                            .tag("BusinessTypeId", "2")
                                            .addField("IndexValue", NumberUtil.doubleAccuracy(Double.parseDouble(temp.getIndexValue()), 2))
                                            .addField("OriginalValue", 0.00)
                                            .addField("Unit", temp.getUnit())
                                            .build();
                                    lstPoint.add(point);
                                }
                                // 这批 数据插入
                                if (lstPoint.size() > 0) {
                                    try {
                                        influxDBManager.batchInsertPointRetry(lstPoint, databaseEnergy);
                                    } catch (Exception ex) {
                                        log.error("** 指标日表写入报错。指标数量=" + lstPoint.size() + ex);
                                    }
                                }

                                modifyTime = DateUtil.dateAddDays(modifyTime, 1);
                                lstPoint.clear();
                            }
                            modifyTime = DateUtil.dateAddDays(modifyTime, -1);
                        }
                    }
                }

                return "成功！插入指标数量：" + lstPoint.size() + ", 最后日时间为：" + dateToString(modifyTime);
            } else if (tableType == 3) {
                String selectDuration = "select * from EnergyHisMonthData where time =$startTime  ";
                List<Point> lstPoint = new ArrayList<>();
                QueryResult query = null;
                InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
                Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                        .forDatabase(databaseEnergy)
                        .bind("startTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(usedTime))
                        .create();

                query = influxDB.query(queryBuilder);
                if (query != null) {
                    List<EnergyHisMonthData> resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisMonthData.class);
                    if (!resultComplexIndexQuery.isEmpty()) {
                        if (hours != null && hours > 0) {
                            // 按要求插入N个 的
                            for (int h = 0; h < hours; h++) {
                                for (EnergyHisMonthData temp : resultComplexIndexQuery) {
                                    Point point = Point.measurement("EnergyHisMonthData")
                                            .time(DateUtil.localToUTC(modifyTime).getTime(), TimeUnit.MILLISECONDS)
                                            //.tag("CalcTime", dateToString(modifyTime))
                                            .tag("ComplexIndexId", String.valueOf(temp.getComplexIndexId()))
                                            .tag("Abnormal", "0")
                                            .tag("BusinessTypeId", "2")
                                            .addField("IndexValue", NumberUtil.doubleAccuracy(Double.parseDouble(temp.getIndexValue()), 2))
                                            .addField("OriginalValue", 0.00)
                                            .addField("Unit", temp.getUnit())
                                            .build();
                                    lstPoint.add(point);
                                }
                                // 这批 数据插入能耗
                                if (lstPoint.size() > 0) {
                                    try {
                                        influxDBManager.batchInsertPointRetry(lstPoint, databaseEnergy);
                                    } catch (Exception ex) {
                                        log.error("** 指标月表写入报错。指标数量=" + lstPoint.size() + ex);
                                    }
                                }
                                modifyTime = DateUtil.dateAddMonth(modifyTime, 1);
                                lstPoint.clear();
                            }
                            modifyTime = DateUtil.dateAddMonth(modifyTime, -1);
                        }
                    }
                }

                return "成功！插入指标数量：" + lstPoint.size() + ", 最后月时间为：" + dateToString(modifyTime);
            }
        } catch (Exception e) {
            return e.getMessage();
        }
        return "成功";
    }

    @Override
    public Boolean simulatorenergydata(Date startTime, Date endTime) {
        try {
            //所有能源类型
            List<ComplexIndexBusinessType> lstEnergyComplexIndexBusinessType = complexIndexBusinessTypeService.GetComplexIndexBusinessTypeByParentid(1);
            //所有能耗指标
            List<ComplexIndex> complexIndices = energyComplexIndexManager.GetAllComplexIndex().stream().filter(
                    i -> lstEnergyComplexIndexBusinessType.stream().anyMatch(item -> item.getBusinessTypeId().equals(i.getBusinessTypeId()))
            ).collect(Collectors.toList());
            List<Point> lstPoint = new ArrayList<>();

            //小时表
            Date tempTime = new Date(startTime.getTime());
            while (tempTime.before(endTime)) {
                lstPoint.clear();
                for (ComplexIndex complexIndex : complexIndices) {
                    Point point = Point.measurement("EnergyHisHourData")
                            .time(DateUtil.localToUTC(tempTime).getTime(), TimeUnit.MILLISECONDS)
                            .tag("ComplexIndexId", complexIndex.getComplexIndexId().toString())
                            .tag("Abnormal", String.valueOf(0))
                            .tag("BusinessTypeId", "2")
                            .addField("IndexValue", NumberUtil.doubleAccuracy(10.0, 2))
                            .addField("OriginalValue", NumberUtil.doubleAccuracy(10.0, 2))
                            .addField("Unit", complexIndex.getUnit() == null ? "" : complexIndex.getUnit())
                            .build();
                    lstPoint.add(point);
                }
                try {
                    if (!lstPoint.isEmpty()) {
                        long startTime2 = System.nanoTime();
                        //influxDBManager.batchInsertPoint(lstPoint, databaseEnergy, 8000);
                        influxDBManager.batchInsertPointRetry(lstPoint, databaseEnergy);
                        long endTime2 = System.nanoTime();
                        double durationInMillis = (endTime2 - startTime2) / 1_000_000.0;
                        log.info("插入小时表数量" + lstPoint.size() + "时间：" + dateToString(tempTime) + " 耗时：" + durationInMillis + " 毫秒");
                    }
                    tempTime = DateUtil.dateAddHours(tempTime, 1);
                } catch (Exception ex) {
                    log.error("插入小时表数量" + lstPoint.size() + "时间：" + dateToString(tempTime) + "报错，继续执行！");
                    try {
                        Thread.sleep(1000 * 10);
                    } catch (InterruptedException exx) {
                    }
                }

                try {
                    Thread.sleep(500);
                } catch (InterruptedException exx) {
                }
            }


//            //日表
//            //tempTime = new Date(startTime.getTime());
//
//            String dateStr = "2023-01-01 00:00:00";
//            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//            // 解析字符串为 LocalDateTime
//            LocalDateTime dateTime = LocalDateTime.parse(dateStr, formatter);
//            // 转换为 Date 对象
//            tempTime = Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());
//
//            while (tempTime.before(endTime)) {
//                lstPoint.clear();
//                for (ComplexIndex complexIndex : complexIndices) {
//                    Point point = Point.measurement("EnergyHisDayData")
//                            .time(DateUtil.localToUTC(tempTime).getTime(), TimeUnit.MILLISECONDS)
//                            .tag("ComplexIndexId", complexIndex.getComplexIndexId().toString())
//                            .tag("Abnormal", String.valueOf(0))
//                            .tag("BusinessTypeId", "2")
//                            .addField("IndexValue", NumberUtil.doubleAccuracy(240.0, 2))
//                            .addField("OriginalValue", NumberUtil.doubleAccuracy(240.0, 2))
//                            .addField("Unit", complexIndex.getUnit() == null ? "" : complexIndex.getUnit())
//                            .build();
//                    lstPoint.add(point);
//                }
//                try {
//                    if (!lstPoint.isEmpty()) {
//                        long startTime2 = System.nanoTime();
//                        influxDBManager.batchInsertPoint(lstPoint, databaseEnergy, 8000);
//                        long endTime2 = System.nanoTime();
//                        double durationInMillis = (endTime2 - startTime2) / 1_000_000.0;
//                        log.info("插入日表数量" + lstPoint.size() + "时间：" + dateToString(tempTime) + " 耗时：" + durationInMillis + " 毫秒");
//                    }
//                    tempTime = DateUtil.dateAddDays(tempTime, 1);
//                } catch (Exception ex) {
//                    log.error("插入日表数量" + lstPoint.size() + "时间：" + dateToString(tempTime) + "超过3次报错，继续执行！");
//                }
//
//                try {
//                    Thread.sleep(1000);
//                } catch (InterruptedException exx) {
//                }
//            }
//
//            //月表
//            //tempTime = new Date(startTime.getTime());
//            tempTime = Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());
//            while (tempTime.before(endTime)) {
//                lstPoint.clear();
//                for (ComplexIndex complexIndex : complexIndices) {
//                    Point point = Point.measurement("EnergyHisMonthData")
//                            .time(DateUtil.localToUTC(tempTime).getTime(), TimeUnit.MILLISECONDS)
//                            .tag("ComplexIndexId", complexIndex.getComplexIndexId().toString())
//                            .tag("Abnormal", String.valueOf(0))
//                            .tag("BusinessTypeId", "2")
//                            .addField("IndexValue", NumberUtil.doubleAccuracy(7200.0, 2))
//                            .addField("OriginalValue", NumberUtil.doubleAccuracy(7200.0, 2))
//                            .addField("Unit", complexIndex.getUnit() == null ? "" : complexIndex.getUnit())
//                            .build();
//                    lstPoint.add(point);
//                }
//                try {
//                    if (!lstPoint.isEmpty()) {
//                        long startTime2 = System.nanoTime();
//                        influxDBManager.batchInsertPoint(lstPoint, databaseEnergy, 8000);
//                        long endTime2 = System.nanoTime();
//                        double durationInMillis = (endTime2 - startTime2) / 1_000_000.0;
//                        log.info("插入月表数量" + lstPoint.size() + "时间：" + dateToString(tempTime) + " 耗时：" + durationInMillis + " 毫秒");
//                        tempTime = DateUtil.dateAddMonth(tempTime, 1);
//                    }
//                } catch (Exception ex) {
//                    log.error("插入月表数量" + lstPoint.size() + "时间：" + dateToString(tempTime) + "超过3次报错，继续执行！");
//                }
//
//                try {
//                    Thread.sleep(1000);
//                } catch (InterruptedException exx) {
//                }
//
//            }
//
//            //月UE表
//            tempTime = Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());
//            while (tempTime.before(endTime)) {
//                lstPoint.clear();
//                for (ComplexIndex complexIndex : complexIndices) {
//                    if (complexIndex.getComplexIndexDefinitionId().equals(6) || complexIndex.getComplexIndexDefinitionId().equals(7)) {
//                        Point pointue = Point.measurement("EnergyHisMonthUEData")
//                                .time(DateUtil.localToUTC(tempTime).getTime(), TimeUnit.MILLISECONDS)
//                                .tag("ComplexIndexId", String.valueOf(complexIndex.getComplexIndexId()))
//                                .addField("IndexValue", NumberUtil.doubleAccuracy(7200.0, 2))
//                                .addField("CalcTime", dateToString(tempTime))
//                                .build();
//                        lstPoint.add(pointue);
//                    }
//                }
//                try {
//                    if (!lstPoint.isEmpty()) {
//                        long startTime2 = System.nanoTime();
//                        influxDBManager.batchInsertPoint(lstPoint, databaseEnergy, 8000);
//                        long endTime2 = System.nanoTime();
//                        double durationInMillis = (endTime2 - startTime2) / 1_000_000.0;
//                        log.info("插入月UE表数量" + lstPoint.size() + "时间：" + dateToString(tempTime) + " 耗时：" + durationInMillis + " 毫秒");
//                        tempTime = DateUtil.dateAddMonth(tempTime, 1);
//                    }
//                } catch (Exception ex) {
//                    log.error("插入月UE表数量" + lstPoint.size() + "时间：" + dateToString(tempTime) + "超过3次报错，继续执行！");
//                }
//
//                try {
//                    Thread.sleep(1000);
//                } catch (InterruptedException exx) {
//                }
//
//            }
        } catch (Exception ex) {
            log.error("报错", ex);
            return false;
        }
        return true;
    }

    /// 模拟能耗指标历史数据随机数
    private boolean simulatorHistoryComplex(Integer times, Date startTime) {
        try {
            //所有能源类型
            List<ComplexIndexBusinessType> lstEnergyComplexIndexBusinessType = complexIndexBusinessTypeService.GetComplexIndexBusinessTypeByParentid(1);
            //所有能耗指标
            List<ComplexIndex> complexIndices = energyComplexIndexManager.GetAllComplexIndex().stream().filter(
                    i -> lstEnergyComplexIndexBusinessType.stream().anyMatch(item -> item.getBusinessTypeId().equals(i.getBusinessTypeId()))
            ).collect(Collectors.toList());

            int i = 0;
            List<Point> lstPoint = new ArrayList<>();
            List<Point> lstHourPoint = new ArrayList<>();
            List<Point> lstDayPoint = new ArrayList<>();
            while (i < times) {
                lstPoint.clear();
                lstHourPoint.clear();
                lstDayPoint.clear();
                for (ComplexIndex complexIndex : complexIndices) {
                    double IndexValue = 1000.0d;
                    if (complexIndex.getComplexIndexDefinitionId().equals(5)) {    //PUE
                        IndexValue = NumberUtil.doubleAccuracy(Math.random() * 0.3 + 1.2, 2);
                    }
                    // 直接加入数据库存储队列
                    Point point = Point.measurement("HistoryComplexIndex")
                            .time(DateUtil.localToUTC(startTime).getTime(), TimeUnit.MILLISECONDS)
                            //.tag("CalcTime",dateToString(date))
                            .tag("ComplexIndexId", String.valueOf(complexIndex.getComplexIndexId()))
                            .tag("BusinessTypeId", complexIndex.getBusinessTypeId() == null ? "" : complexIndex.getBusinessTypeId().toString())
                            .tag("Abnormal", String.valueOf(0))
                            .addField("CalcTime", dateToString(startTime))
                            .addField("IndexValue", NumberUtil.doubleAccuracy(IndexValue, 2))
                            .addField("Unit", complexIndex.getUnit() == null ? "" : complexIndex.getUnit())
                            .build();
                    lstPoint.add(point);


//                    Point point2 = Point.measurement("EnergyHisHourData")
//                            .time(DateUtil.localToUTC(startTime).getTime(), TimeUnit.MILLISECONDS)
//                            .tag("ComplexIndexId", String.valueOf(complexIndex.getComplexIndexId()))
//                            .tag("Abnormal", "0")
//                            .tag("BusinessTypeId", complexIndex.getBusinessTypeId() == null ? "" : complexIndex.getBusinessTypeId().toString())
//                            .addField("IndexValue", NumberUtil.doubleAccuracy(IndexValue, 2))
//                            .addField("OriginalValue", NumberUtil.doubleAccuracy(IndexValue, 2))
//                            .addField("Unit", complexIndex.getUnit() == null ? "" : complexIndex.getUnit())
//                            .build();
//                    //加入数据点仓库
//                    lstHourPoint.add(point2);
//
//                    Point point3 = Point.measurement("EnergyHisDayData")
//                            .time(DateUtil.localToUTC(startTime).getTime(), TimeUnit.MILLISECONDS)
//                            .tag("ComplexIndexId", String.valueOf(complexIndex.getComplexIndexId()))
//                            .tag("Abnormal", "0")
//                            .tag("BusinessTypeId", complexIndex.getBusinessTypeId() == null ? "" : complexIndex.getBusinessTypeId().toString())
//                            .addField("IndexValue", NumberUtil.doubleAccuracy(IndexValue, 2))
//                            .addField("OriginalValue", NumberUtil.doubleAccuracy(IndexValue, 2))
//                            .addField("Unit", complexIndex.getUnit() == null ? "" : complexIndex.getUnit())
//                            .build();
//                    //加入数据点仓库
//                    lstDayPoint.add(point3);
                }
                if (!lstPoint.isEmpty()) {

                    try {
                        long dTime1 = System.nanoTime();
                        influxDBManager.batchInsertPointRetry(lstPoint, database);//, 8000, 5000);
                        long dTime2 = System.nanoTime();
                        log.info("插入指标历史数据表第" + (i + 1) + "次，数量" + lstPoint.size() + "时间：" + dateToString(startTime) + "耗时：" + (dTime2 - dTime1) / 1_000_000.0 + " 毫秒");
                        Thread.sleep(1000);
                    } catch (Exception ex) {
                        log.error("** 插入指标历史数据表超时。时间：" + dateToString(startTime) + "。指标数量=" + lstPoint.size() + ex);
                    }
                }
//                if (lstHourPoint.isEmpty()) {
//                    try {
//                        long dTime1 = System.nanoTime();
//                        influxDBManager.batchInsertPointRetry(lstHourPoint, databaseEnergy);//, 8000, 5000);
//                        long dTime2 = System.nanoTime();
//                        log.info("插入能耗小时表数据表第" + (i + 1) + "次，数量" + lstHourPoint.size() + "时间：" + dateToString(startTime)+"耗时：" + (dTime2 - dTime1) / 1_000_000.0 + " 毫秒");
//                        Thread.sleep(1000);
//                    } catch (Exception ex) {
//                        log.error("** 插入能耗小时表数据表超时。时间：" + dateToString(startTime)+ "。指标数量=" + lstHourPoint.size() + ex);
//                    }
//                }
//                if (lstDayPoint.isEmpty()) {
//                    try {
//                        long dTime1 = System.nanoTime();
//                        influxDBManager.batchInsertPointRetry(lstDayPoint, databaseEnergy);//, 8000, 5000);
//                        long dTime2 = System.nanoTime();
//                        log.info("插入能耗日表数据表第" + (i + 1) + "次，数量" + lstDayPoint.size() + "时间：" + dateToString(startTime)+"耗时：" + (dTime2 - dTime1) / 1_000_000.0 + " 毫秒");
//                        Thread.sleep(1000);
//                    } catch (Exception ex) {
//                        log.error("** 插入能耗日表数据表超时。时间：" + dateToString(startTime)+ "。指标数量=" + lstDayPoint.size() + ex);
//                    }
//                }

                startTime = DateUtil.dateAddHours(startTime, 1);
                i++;

                Thread.sleep(200);
            }
        } catch (Exception ex) {
            log.error("报错", ex);
            return false;
        }
        return true;
    }

    @Override
    public void downLoadExcel(OverViewParameterDTO para, HttpServletResponse response) {
        try {
            String fileName = "用能同环比报告(" +
                    new SimpleDateFormat("yyyy-MM-dd").format(para.getStartTime()) + "至" +
                    new SimpleDateFormat("yyyy-MM-dd").format(para.getEndTime()) + ").xlsx";

            // **UTF-8 编码 (适用于 Chrome、Edge、Firefox)**
            String encodedFileNameUTF8 = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replace("+", "%20");
            // **设置 Content-Disposition 头**
            response.setHeader("Content-Disposition","attachment; filename*=UTF-8''" + encodedFileNameUTF8);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

            // 创建 Excel 工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("数据列表");

            // 创建表头
            Row headerRow = sheet.createRow(0);
            String[] columns = {"层级名称", "层级类型", "指标名称", "总量", "单位", "同比总量","同比(增长或减少比例)", "环比总量","环比(增长或减少比例)"};
            for (int i = 0; i < columns.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(columns[i]);
            }

            //获取本期能耗
            List<StructureOfComplexIndexValue> thisTimeValue = GetEnergyOverViewResourceStructureElecConsume(para.getDimensionTypeId(),
                    para.getBusinessTypeId(), para.getStartTime(),
                    para.getEndTime(), para.getResourceStructureIds(), para.getUserId().intValue(), para.getTimeType(), para.getSequence());

            //同比
            List<Date> YoYDate = energyConfigTelComService.getQoQDateOrYoYDate(para.getStartTime(), para.getEndTime(), "YoY", para.getTimeType());
            List<StructureOfComplexIndexValue> thisTimeValueYoY = GetEnergyOverViewResourceStructureElecConsume(para.getDimensionTypeId(),
                    para.getBusinessTypeId(), YoYDate.get(0),
                    YoYDate.get(1), para.getResourceStructureIds(), para.getUserId().intValue(), para.getTimeType(), para.getSequence());

            //环比
            List<Date> QoQDate = energyConfigTelComService.getQoQDateOrYoYDate(para.getStartTime(), para.getEndTime(), "QoQ", para.getTimeType());
            List<StructureOfComplexIndexValue> thisTimeValueQoQ = GetEnergyOverViewResourceStructureElecConsume(para.getDimensionTypeId(),
                    para.getBusinessTypeId(), QoQDate.get(0),
                    QoQDate.get(1), para.getResourceStructureIds(), para.getUserId().intValue(), para.getTimeType(), para.getSequence());

            //ComplexIndexDefinition表定义了指标类型：6总用电量；28总用水量；43总用气量；
            BusinessDefinitionMap thisMap = energyComplexIndexManager.GetAllBusinessDefinitionMap().stream().filter(i -> i.getBusinessTypeId().equals(para.getBusinessTypeId())
                    && i.getComplexIndexDefinitionTypeId().equals(1)).findFirst().orElse(null);
            int energyIndexDefinition = thisMap.getComplexIndexDefinitionId();

            List<Object[]> data = new ArrayList<>();
            //"层级名称", "层级类型", "指标名称", "总量", "单位", "同比", "环比"
            for (StructureOfComplexIndexValue thisValue : thisTimeValue) {
                //"层级类型"
                String structureTypeName = "自定义节点";
                if (!thisValue.getObjectTypeId().equals(200)) {
                    structureTypeName = resourceStructureTypeManager.findById(thisValue.getObjectTypeId()).getResourceStructureTypeName();
                }
                //"指标名称"
                ComplexIndex thisComplexIndex = energyComplexIndexManager.GetAllTotalConsumeComplexIndex().stream().filter(
                        item -> item.getObjectId().equals(thisValue.getResourceStructureId())
                                && item.getObjectTypeId().equals(thisValue.getObjectTypeId())
                                && item.getComplexIndexDefinitionId().equals(energyIndexDefinition)
                ).findFirst().orElse(null);

                // 同比
                StructureOfComplexIndexValue thisValueYoY = thisTimeValueYoY.stream().filter(i -> i.getResourceStructureId().equals(thisValue.getResourceStructureId())).findFirst().orElse(null);
                String strYoY = "";
                String valueYoY = "";
                if (ObjectUtil.isNotEmpty(thisValueYoY)) {
                    valueYoY = NumberUtil.doubleAccuracy(thisValueYoY.getSumValue(),2).toString();
                    if (thisValueYoY.getSumValue() == 0d) {
                        if (thisValue.getSumValue() > 0d) strYoY = "100%";
                        else strYoY = "";
                    } else {
                        if (thisValue.getSumValue() > 0d) {
                            strYoY = NumberUtil.doubleAccuracy(Math.abs((thisValue.getSumValue() - thisValueYoY.getSumValue()) / thisValueYoY.getSumValue() * 100), 2).toString()
                                    + "%";
                        } else strYoY = "100%";
                    }
                }

                // 环比
                StructureOfComplexIndexValue thisValueQoQ = thisTimeValueQoQ.stream().filter(i -> i.getResourceStructureId().equals(thisValue.getResourceStructureId())).findFirst().orElse(null);
                String strQoQ = "";
                String valueQoQ = "";
                if (ObjectUtil.isNotEmpty(thisValueQoQ)) {
                    valueQoQ = NumberUtil.doubleAccuracy(thisValueQoQ.getSumValue(),2).toString();
                    if (thisValueQoQ.getSumValue() == 0d) {
                        if (thisValue.getSumValue() > 0d) strQoQ = "100%";
                        else strQoQ = "";
                    } else {
                        if (thisValue.getSumValue() > 0d) {
                            strQoQ = NumberUtil.doubleAccuracy(Math.abs((thisValue.getSumValue() - thisValueQoQ.getSumValue()) / thisValueQoQ.getSumValue() * 100), 2).toString()
                                    + "%";
                        } else strQoQ = "100%";
                    }
                }
                //"层级名称", "层级类型", "指标名称", "总量", "单位", "同比总量","同比(增长或减少比例)", "环比总量","环比(增长或减少比例)"
                data.add(new Object[]{
                        thisValue.getResourceStructureName()
                        , structureTypeName
                        , thisComplexIndex.getComplexIndexName()
                        , NumberUtil.doubleAccuracy(thisValue.getSumValue(), 2).toString()
                        , thisComplexIndex.getUnit()
                        , valueYoY
                        , strYoY
                        , valueQoQ
                        , strQoQ
                });
            }

            // 创建字体样式
            CellStyle redStyle = workbook.createCellStyle();
            Font redFont = workbook.createFont();
            redFont.setColor(IndexedColors.RED.getIndex());
            redStyle.setFont(redFont);

            CellStyle greenStyle = workbook.createCellStyle();
            Font greenFont = workbook.createFont();
            greenFont.setColor(IndexedColors.GREEN.getIndex());
            greenStyle.setFont(greenFont);

            int rowNum = 1;
            for (Object[] rowData : data) {
                Row row = sheet.createRow(rowNum++);
                for (int i = 0; i < rowData.length; i++) {
                    Cell cell = row.createCell(i);
                    cell.setCellValue(rowData[i].toString());

                    // 处理 "同比" 和 "环比" 这两列
                    if (i == 6 || i == 8) { // i=6 (同比), i=8 (环比)
                        String percentStr = rowData[i].toString().replace("%", ""); // 去掉百分号
                        if (!percentStr.isEmpty()) {
                            try {
                                double percent = Double.parseDouble(percentStr);
                                if (percent > 0) {
                                    cell.setCellStyle(redStyle); // 设置红色字体
                                } else if (percent < 0) {
                                    cell.setCellStyle(greenStyle); // 设置绿色字体
                                }
                            } catch (NumberFormatException ignored) {
                                // 避免转换异常，忽略非数值的情况
                            }
                        }
                    }
                }
            }

            // 自动调整列宽
            //for (int i = 0; i < columns.length; i++) {
            //    sheet.autoSizeColumn(i, true);
            //}
            //"层级名称", "层级类型", "指标名称", "总量", "单位", "同比总量","同比(增长或减少比例)", "环比总量","环比(增长或减少比例)"
            sheet.setColumnWidth(0, 30 * 256);
            sheet.setColumnWidth(1, 10 * 256);
            sheet.setColumnWidth(2, 30 * 256);
            sheet.setColumnWidth(3, 10 * 256);
            sheet.setColumnWidth(4, 5 * 256);
            sheet.setColumnWidth(5, 10 * 256);
            sheet.setColumnWidth(6, 20 * 256);
            sheet.setColumnWidth(7, 10 * 256);
            sheet.setColumnWidth(8, 20 * 256);

            // 将 Excel 文件写入 HTTP 响应流
            workbook.write(response.getOutputStream());
            workbook.close();
            response.getOutputStream().flush();
            response.getOutputStream().close();
        } catch (Exception ex) {
            log.error("能耗全景图导出失败！", ex);
        }
    }
}
