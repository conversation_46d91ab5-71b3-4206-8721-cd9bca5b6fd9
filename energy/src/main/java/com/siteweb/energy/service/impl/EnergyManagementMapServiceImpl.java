package com.siteweb.energy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.energy.dto.EnergyManagementMapInfo;
import com.siteweb.energy.dto.EnergyManagementStructure;
import com.siteweb.energy.entity.EnergyManagementMap;
import com.siteweb.energy.mapper.EnergyManagementMapMapper;
import com.siteweb.energy.service.EnergyManagementMapService;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import static com.siteweb.common.util.StringUtils.getIntegerListByString;

@Service
public class EnergyManagementMapServiceImpl implements EnergyManagementMapService {

    private final Logger log = LoggerFactory.getLogger(EnergyManagementMapServiceImpl.class);

    @Autowired
    EnergyManagementMapMapper mapMapper;
    @Autowired
    private ResourceStructureManager resourceStructureManager;

    @Override
    public List<EnergyManagementMap> findByEnergyId(Integer energyId) {
        return mapMapper.selectList(new QueryWrapper<EnergyManagementMap>().eq("EnergyId", energyId));
    }

    @Override
    public Boolean saveEnergyStructure(EnergyManagementStructure energyStructure) {

        List<Integer> postIds = getIntegerListByString(energyStructure.getStructureIds());
        List<EnergyManagementMap> lstMap = findByEnergyId(energyStructure.getEnergyId());
        for(EnergyManagementMap map:lstMap){
            if(!postIds.contains(map.getResourceStructureId())) {
                mapMapper.deleteById(map.getMapId());
            }
        }

        //获取新增的
        if (energyStructure.getStructureIds().length()>0) {
            postIds.removeIf(i-> lstMap.stream().filter(item->item.getResourceStructureId().equals(i)).findFirst().orElse(null) != null);

            EnergyManagementMap newMap = new EnergyManagementMap();
            for (Integer id : postIds) {
                newMap = new EnergyManagementMap();
                newMap.setEnergyId(energyStructure.getEnergyId());
                newMap.setIsMarker(0);
                newMap.setResourceStructureId(id);
                mapMapper.insert(newMap);
            }
        }
        return true;

    }

    @Override
    public Boolean saveEnergyStructureMarker(EnergyManagementStructure energyStructure, Integer isMarker) {
        //设置标杆站
        if (energyStructure.getStructureIds().length()>0) {
            List<Integer> postIds = getIntegerListByString(energyStructure.getStructureIds());
            List<EnergyManagementMap> lstMap = findByEnergyId(energyStructure.getEnergyId());
            for(EnergyManagementMap map:lstMap){
                if(postIds.contains(map.getResourceStructureId())) {
                    map.setIsMarker(isMarker);
                    mapMapper.updateById(map);
                }
            }
        }
        return true;
    }

    @Override
    public List<EnergyManagementMapInfo> GetEnergyManagementMapList(Integer energyId) {
        List<EnergyManagementMapInfo> result = new ArrayList<>();

        List<EnergyManagementMap> lstm = findByEnergyId(energyId);
        EnergyManagementMapInfo oneInfo;
        ResourceStructure rs,rsParent;

        for(EnergyManagementMap map: lstm)
        {
            rs = resourceStructureManager.getResourceStructureById(map.getResourceStructureId());
            if (rs != null) {
                oneInfo = new EnergyManagementMapInfo(map);
                oneInfo.setResourceStructureName(rs.getResourceStructureName());
                oneInfo.setLevelOfPath(rs.getLevelOfPath());

                rsParent = resourceStructureManager.getResourceStructureById(rs.getParentResourceStructureId());
                if (rsParent != null) {
                    oneInfo.setParentResourceStructureName(rsParent.getResourceStructureName());
                }
                result.add(oneInfo);
            }
        }
        return result.stream().sorted(Comparator.comparing(EnergyManagementMapInfo::getLevelOfPath)).collect(Collectors.toList());
    }
}
