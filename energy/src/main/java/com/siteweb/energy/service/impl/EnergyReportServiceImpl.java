package com.siteweb.energy.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.complexindex.entity.ComplexIndexDefinition;
import com.siteweb.complexindex.mapper.ComplexIndexDefinitionMapper;
import com.siteweb.computerrack.entity.ComputerRack;
import com.siteweb.computerrack.entity.ITDevice;
import com.siteweb.computerrack.service.ComputerRackService;
import com.siteweb.computerrack.service.ITDeviceService;
import com.siteweb.energy.dto.*;
import com.siteweb.energy.entity.EnergyComplexIndexClassificationMap;
import com.siteweb.energy.entity.EnergyMultiLevelObject;
import com.siteweb.energy.entity.EnergyObjectMap;
import com.siteweb.energy.entity.EnergyStructure;
import com.siteweb.energy.manager.EnergyComplexIndexManager;
import com.siteweb.energy.mapper.EnergyComplexIndexClassificationMapMapper;
import com.siteweb.energy.mapper.EnergyMultiLevelObjectMapper;
import com.siteweb.energy.service.EnergyObjectMapService;
import com.siteweb.energy.service.EnergyReportService;
import com.siteweb.energy.service.EnergyStandardApiService;
import com.siteweb.energy.service.EnergyStructureService;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.service.ResourceStructureService;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BoundParameterQuery;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBResultMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.siteweb.common.util.DateUtil.dateToString;

@Service
@Slf4j
public class EnergyReportServiceImpl  implements EnergyReportService {

    @Autowired
    private ResourceStructureService resourceStructureService;
    @Autowired
    private ResourceStructureManager resourceStructureManager;
    @Autowired
    private EnergyObjectMapService energyObjectMapService;
    @Autowired
    private EnergyComplexIndexManager energyComplexIndexManager;
    @Autowired
    private ComplexIndexDefinitionMapper complexIndexDefinitionMapper;
    @Autowired
    private EnergyStructureService energyStructureService;
    @Autowired
    private EquipmentManager equipmentManager;
    @Autowired
    private ComputerRackService computerRackService;
    @Autowired
    private EnergyMultiLevelObjectMapper energyMultiLevelObjectMapper;
    @Autowired
    private EnergyComplexIndexClassificationMapMapper energyComplexIndexClassificationMapMapper;
    @Autowired
    private EnergyStandardApiService energyStandardApiService;
    @Autowired
    private ITDeviceService iTDeviceService;
    @Autowired
    private InfluxDB influxDB;

    @Value("${spring.influx.database3}")
    private String databaseEnergy;

    private List<EnergyMultiLevelObject> lstEnergyMultiLevelObject = new ArrayList<>();

    //日趋势
    @Override
    public List<EnergyReportDayTrendResultDTO> EnergyConsumeDayTrendReport(EnergyReportParameterDTO para) {
        List<EnergyReportDayTrendResultDTO> result = new ArrayList<>();
        EnergyReportDayTrendResultDTO oneResult;

        Integer complexIndexDefinitionId = 0;
        String complexIndexDefinitionTypeName = "";

        if (para.getClassificationIds() == null){
            //不是四级分类
            for(String definitionId : StringUtils.split(para.getComplexIndexDefinitionIds(), ',')){
                complexIndexDefinitionId = NumberUtil.parseInt(definitionId);
                //获取查询能源类型对应的名称
                complexIndexDefinitionTypeName = GetComplexIndexDefinitionTypeName(complexIndexDefinitionId);

                //获取节点信息和指标ID
                List<EnergyReportTrendResultDTO> lstObjectAndComplexIndexId = new ArrayList<>();
                SetObjectAndComplexIndexId(para, lstObjectAndComplexIndexId, complexIndexDefinitionId, complexIndexDefinitionTypeName);

                lstObjectAndComplexIndexId.forEach(dto->result.add(new EnergyReportDayTrendResultDTO(dto)));
            }
        }else{
            if (para.getClassificationIds().equals("") || para.getResourceStructureIds().equals(""))
                return result;
            //是四级分类
            lstEnergyMultiLevelObject = energyMultiLevelObjectMapper.selectList(null);

            if (para.getResourceStructureIds().equals("-1")) {
                List<ResourceStructure> needResourceStructure = resourceStructureService.findResourceStructureByUserId(para.getUserId());
                String rds = needResourceStructure.stream()
                        .map(ResourceStructure::getResourceStructureId) // 提取 ResourceStructureId
                        .map(Object::toString) // 将每个 ID 转换为字符串
                        .collect(Collectors.joining(", "));
                para.setResourceStructureIds(rds);
            }

            List<EnergyComplexIndexClassificationMap> needComplexIndex =
                    energyComplexIndexClassificationMapMapper.getComplexIndexByResIdAndClassificationId(para.getResourceStructureIds(),para.getClassificationIds());

            for(EnergyComplexIndexClassificationMap map : needComplexIndex){
                EnergyReportDayTrendResultDTO dto = new EnergyReportDayTrendResultDTO();
                EnergyMultiLevelObject multiLevelObject = lstEnergyMultiLevelObject.stream().filter(i->i.getObjectId().equals(map.getClassificationId())).findFirst().orElse(null);
                if (multiLevelObject == null) continue;
                dto.setClassificationName(multiLevelObject.getObjectName());
                dto.setClassificationId(map.getClassificationId());
                dto.setObjectId(map.getResourceStructureId());
                dto.setObjectName(resourceStructureManager.getResourceStructureById(map.getResourceStructureId()).getResourceStructureName());
                dto.setComplexIndexId(map.getComplexIndexId());

                ComplexIndex thisComplexIndex = energyComplexIndexManager.GetAllComplexIndex().stream().filter(i->i.getComplexIndexId().equals(map.getComplexIndexId())).findFirst().orElse(null);
                if (thisComplexIndex == null) continue;
                dto.setComplexIndexName(thisComplexIndex.getComplexIndexName());
                dto.setObjectTypeId(thisComplexIndex.getObjectTypeId());
                dto.setComplexIndexDefinitionId(1);
                result.add(dto);
            }
            para.setComplexIndexDefinitionTypeId(1);
        }

        //读取InfluxDB 查询能耗数据
        GetDayConsumeTrend(result, para.getComplexIndexDefinitionTypeId(), para.getStartTime(), para.getEndTime(),1.0);
        //赋值总量字段
        result.forEach(dto->{
            if(para.getComplexIndexDefinitionTypeId().equals(2) || para.getComplexIndexDefinitionTypeId().equals(100))
                dto.SetAvgConsume();
            else
                dto.SetTotalConsume();
        });

        return result.stream().sorted(
                Comparator.comparing(EnergyReportDayTrendResultDTO::getObjectTypeId)
                        .thenComparing(EnergyReportDayTrendResultDTO::getObjectId)
                        .thenComparing(EnergyReportDayTrendResultDTO::getComplexIndexDefinitionId)
        ).collect(Collectors.toList());
    }
    //赋值日趋势
    private void GetDayConsumeTrend(List<EnergyReportDayTrendResultDTO> paraResult, int complexIndexDefinitionTypeId,Date startTime, Date endTime, Double coefficient){
        try {
            String selectDuration = "select sum(IndexValue) as result from EnergyHisHourData where time >=$startTime and time <= $endTime and ( $someComplexIndex ) group by time(1h),ComplexIndexId ";
            if (complexIndexDefinitionTypeId == 2)
                selectDuration = "select mean(IndexValue) as result from EnergyHisHourData where time >=$startTime and time <= $endTime and ( $someComplexIndex ) group by time(1h),ComplexIndexId ";

            String sql = null;
            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();

            StringBuilder bsWhereComplexIndexId = new StringBuilder();
            int count = 0;
            for(EnergyReportDayTrendResultDTO ciId:paraResult) {
                if (bsWhereComplexIndexId.length() == 0) {
                    bsWhereComplexIndexId.append(" ComplexIndexId='" + ciId.getComplexIndexId()+"' ");
                }else{
                    bsWhereComplexIndexId.append(" or ComplexIndexId='" + ciId.getComplexIndexId()+"' ");
                }
                count++;
                if (count == paraResult.size() || count % 500 == 0){
                    List<String> temComplexIndexIds = new ArrayList<>();
                    //sql = String.format(selectDuration, DateUtil.dateToString(startTime), DateUtil.dateToString(endTime),bsWhereComplexIndexId.toString());
                    List<EnergyHisHourDataResult> resultComplexIndexQuery = new ArrayList<>();
                    String selectDurationByQuery = selectDuration.replace("$someComplexIndex",bsWhereComplexIndexId.toString());
                    Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDurationByQuery)
                            .forDatabase(databaseEnergy)
                            .bind("startTime", dateToString(startTime))
                            .bind("endTime", dateToString(endTime))
                            .create();

                    query =  influxDB.query(queryBuilder);
                    if (query != null) {
                        resultComplexIndexQuery= resultMapper.toPOJO(query, EnergyHisHourDataResult.class);
                        if (!resultComplexIndexQuery.isEmpty()) {
                            for(EnergyHisHourDataResult temp :resultComplexIndexQuery){

                                EnergyReportDayTrendResultDTO st = paraResult.stream().filter(i->i.getComplexIndexId() != null && i.getComplexIndexId().equals(Integer.valueOf(temp.getComplexIndexId()))).findFirst().orElse(null);
                                if (st != null){
                                    if (temp.getResult() == null) continue;
                                    Double valueTemp = com.siteweb.common.util.NumberUtil.doubleAccuracy(Double.parseDouble(temp.getResult())*coefficient,2);

                                    Calendar ca = Calendar.getInstance();
                                    ca.setTime((new SimpleDateFormat("yyyy-MM-dd HH")).parse(temp.getTime().replace('T',' ')));
                                    switch (ca.get(Calendar.HOUR_OF_DAY)+1){
                                        case 1:st.setH1(valueTemp);break;
                                        case 2:st.setH2(valueTemp);break;
                                        case 3:st.setH3(valueTemp);break;
                                        case 4:st.setH4(valueTemp);break;
                                        case 5:st.setH5(valueTemp);break;
                                        case 6:st.setH6(valueTemp);break;
                                        case 7:st.setH7(valueTemp);break;
                                        case 8:st.setH8(valueTemp);break;
                                        case 9:st.setH9(valueTemp);break;
                                        case 10:st.setH10(valueTemp);break;
                                        case 11:st.setH11(valueTemp);break;
                                        case 12:st.setH12(valueTemp);break;
                                        case 13:st.setH13(valueTemp);break;
                                        case 14:st.setH14(valueTemp);break;
                                        case 15:st.setH15(valueTemp);break;
                                        case 16:st.setH16(valueTemp);break;
                                        case 17:st.setH17(valueTemp);break;
                                        case 18:st.setH18(valueTemp);break;
                                        case 19:st.setH19(valueTemp);break;
                                        case 20:st.setH20(valueTemp);break;
                                        case 21:st.setH21(valueTemp);break;
                                        case 22:st.setH22(valueTemp);break;
                                        case 23:st.setH23(valueTemp);break;
                                        case 24:st.setH24(valueTemp);break;
                                    }
                                }
                            }
                        }
                    }
                    bsWhereComplexIndexId = new StringBuilder();
                }
            }
        }catch (Exception e){
            log.error("EnergyReportServiceImpl-GetDayConsumeTrend error {}", e.getMessage());
        }
    }

    //月趋势
    @Override
    public List<EnergyReportMonthTrendResultDTO> EnergyConsumeMonthTrendReport(EnergyReportParameterDTO para) {
        List<EnergyReportMonthTrendResultDTO> result = new ArrayList<>();
        EnergyReportMonthTrendResultDTO oneResult;

        Integer complexIndexDefinitionId = 0;
        String complexIndexDefinitionTypeName = "";

        if (para.getClassificationIds() == null){
            //不是四级分类
            for(String definitionId : StringUtils.split(para.getComplexIndexDefinitionIds(), ',')){
                complexIndexDefinitionId = NumberUtil.parseInt(definitionId);
                //获取查询能源类型对应的名称
                complexIndexDefinitionTypeName = GetComplexIndexDefinitionTypeName(complexIndexDefinitionId);

                //获取节点信息和指标ID
                List<EnergyReportTrendResultDTO> lstObjectAndComplexIndexId = new ArrayList<>();
                SetObjectAndComplexIndexId(para, lstObjectAndComplexIndexId, complexIndexDefinitionId, complexIndexDefinitionTypeName);
                lstObjectAndComplexIndexId.forEach(dto->result.add(new EnergyReportMonthTrendResultDTO(dto)));
            }
        }else{
            if (para.getClassificationIds().equals("") || para.getResourceStructureIds().equals(""))
                return result;
            //是四级分类
            lstEnergyMultiLevelObject = energyMultiLevelObjectMapper.selectList(null);

            if (para.getResourceStructureIds().equals("-1")) {
                List<ResourceStructure> needResourceStructure = resourceStructureService.findResourceStructureByUserId(para.getUserId());
                String rds = needResourceStructure.stream()
                        .map(ResourceStructure::getResourceStructureId) // 提取 ResourceStructureId
                        .map(Object::toString) // 将每个 ID 转换为字符串
                        .collect(Collectors.joining(", "));
                para.setResourceStructureIds(rds);
            }

            List<EnergyComplexIndexClassificationMap> needComplexIndex =
                    energyComplexIndexClassificationMapMapper.getComplexIndexByResIdAndClassificationId(para.getResourceStructureIds(),para.getClassificationIds());

            for(EnergyComplexIndexClassificationMap map : needComplexIndex){
                EnergyReportMonthTrendResultDTO dto = new EnergyReportMonthTrendResultDTO();
                EnergyMultiLevelObject multiLevelObject = lstEnergyMultiLevelObject.stream().filter(i->i.getObjectId().equals(map.getClassificationId())).findFirst().orElse(null);
                if (multiLevelObject == null) continue;
                dto.setClassificationName(multiLevelObject.getObjectName());
                dto.setClassificationId(map.getClassificationId());
                dto.setObjectId(map.getResourceStructureId());
                dto.setObjectName(resourceStructureManager.getResourceStructureById(map.getResourceStructureId()).getResourceStructureName());
                dto.setComplexIndexId(map.getComplexIndexId());

                ComplexIndex thisComplexIndex = energyComplexIndexManager.GetAllComplexIndex().stream().filter(i->i.getComplexIndexId().equals(map.getComplexIndexId())).findFirst().orElse(null);
                if (thisComplexIndex == null) continue;
                dto.setComplexIndexName(thisComplexIndex.getComplexIndexName());
                dto.setObjectTypeId(thisComplexIndex.getObjectTypeId());
                dto.setComplexIndexDefinitionId(1);
                result.add(dto);
            }
            para.setComplexIndexDefinitionTypeId(1);
        }

        //读取InfluxDB 查询能耗数据
        GetMonthConsumeTrend(result, para.getComplexIndexDefinitionTypeId(), para.getStartTime(), para.getEndTime(),1.0);
        //赋值总量字段
        result.forEach(dto->{
            if(para.getComplexIndexDefinitionTypeId().equals(2) || para.getComplexIndexDefinitionTypeId().equals(100))
                dto.SetAvgConsume();
            else
                dto.SetTotalConsume();
        });

        return result.stream().sorted(
                Comparator.comparing(EnergyReportMonthTrendResultDTO::getObjectTypeId)
                .thenComparing(EnergyReportMonthTrendResultDTO::getObjectId)
                .thenComparing(EnergyReportMonthTrendResultDTO::getComplexIndexDefinitionId)
        ).collect(Collectors.toList());
    }
    //赋值月趋势
    private void GetMonthConsumeTrend(List<EnergyReportMonthTrendResultDTO> paraResult, int complexIndexDefinitionTypeId,Date startTime, Date endTime, Double coefficient){
        try {
            String selectDuration = "select IndexValue as result from EnergyHisDayData where time >=$startTime and time <= $endTime and ( $someComplexIndex ) group by ComplexIndexId";
            if (complexIndexDefinitionTypeId == 2)
                selectDuration = "select IndexValue as result from EnergyHisDayData where time >=$startTime and time <= $endTime and ( $someComplexIndex ) group by ComplexIndexId";

            String sql = null;
            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();

            StringBuilder bsWhereComplexIndexId = new StringBuilder();
            int count = 0;
            for(EnergyReportMonthTrendResultDTO ciId:paraResult) {
                if (bsWhereComplexIndexId.length() == 0) {
                    bsWhereComplexIndexId.append(" ComplexIndexId='" + ciId.getComplexIndexId()+"' ");
                }else{
                    bsWhereComplexIndexId.append(" or ComplexIndexId='" + ciId.getComplexIndexId()+"' ");
                }
                count++;
                if (count == paraResult.size() || count % 500 == 0){
                    List<String> temComplexIndexIds = new ArrayList<>();
                    //sql = String.format(selectDuration, DateUtil.dateToString(startTime), DateUtil.dateToString(endTime),bsWhereComplexIndexId.toString());
                    List<EnergyHisDayDataResult> resultComplexIndexQuery = new ArrayList<>();
                    String selectDurationByQuery = selectDuration.replace("$someComplexIndex",bsWhereComplexIndexId.toString());
                    Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDurationByQuery)
                            .forDatabase(databaseEnergy)
                            .bind("startTime", dateToString(startTime))
                            .bind("endTime", dateToString(endTime))
                            .create();
                    query =  influxDB.query(queryBuilder);
                    if (query != null) {
                        resultComplexIndexQuery= resultMapper.toPOJO(query, EnergyHisDayDataResult.class);
                        if (!resultComplexIndexQuery.isEmpty()) {
                            for(EnergyHisDayDataResult temp :resultComplexIndexQuery){

                                EnergyReportMonthTrendResultDTO st = paraResult.stream().filter(i->i.getComplexIndexId() != null && i.getComplexIndexId().equals(Integer.valueOf(temp.getComplexIndexId()))).findFirst().orElse(null);
                                if (st != null){
                                    if (temp.getResult() == null) continue;
                                    Double valueTemp = com.siteweb.common.util.NumberUtil.doubleAccuracy(Double.parseDouble(temp.getResult())*coefficient,2);

                                    Calendar ca = Calendar.getInstance();
                                    ca.setTime((new SimpleDateFormat("yyyy-MM-dd")).parse(temp.getTime()));
                                    switch (ca.get(Calendar.DAY_OF_MONTH)){
                                        case 1:st.setD1(valueTemp);break;
                                        case 2:st.setD2(valueTemp);break;
                                        case 3:st.setD3(valueTemp);break;
                                        case 4:st.setD4(valueTemp);break;
                                        case 5:st.setD5(valueTemp);break;
                                        case 6:st.setD6(valueTemp);break;
                                        case 7:st.setD7(valueTemp);break;
                                        case 8:st.setD8(valueTemp);break;
                                        case 9:st.setD9(valueTemp);break;
                                        case 10:st.setD10(valueTemp);break;
                                        case 11:st.setD11(valueTemp);break;
                                        case 12:st.setD12(valueTemp);break;
                                        case 13:st.setD13(valueTemp);break;
                                        case 14:st.setD14(valueTemp);break;
                                        case 15:st.setD15(valueTemp);break;
                                        case 16:st.setD16(valueTemp);break;
                                        case 17:st.setD17(valueTemp);break;
                                        case 18:st.setD18(valueTemp);break;
                                        case 19:st.setD19(valueTemp);break;
                                        case 20:st.setD20(valueTemp);break;
                                        case 21:st.setD21(valueTemp);break;
                                        case 22:st.setD22(valueTemp);break;
                                        case 23:st.setD23(valueTemp);break;
                                        case 24:st.setD24(valueTemp);break;
                                        case 25:st.setD25(valueTemp);break;
                                        case 26:st.setD26(valueTemp);break;
                                        case 27:st.setD27(valueTemp);break;
                                        case 28:st.setD28(valueTemp);break;
                                        case 29:st.setD29(valueTemp);break;
                                        case 30:st.setD30(valueTemp);break;
                                        case 31:st.setD31(valueTemp);break;
                                    }
                                }
                            }
                        }
                    }
                    bsWhereComplexIndexId = new StringBuilder();
                }
            }
        }catch (Exception e){
            log.error("EnergyReportServiceImpl-GetMonthConsumeTrend error {}", e.getMessage());
        }
    }

    //年趋势
    @Override
    public List<EnergyReportYearTrendResultDTO> EnergyConsumeYearTrendReport(EnergyReportParameterDTO para) {
        List<EnergyReportYearTrendResultDTO> result = new ArrayList<>();
        EnergyReportYearTrendResultDTO oneResult = new EnergyReportYearTrendResultDTO();

        Integer complexIndexDefinitionId = 0;
        String complexIndexDefinitionTypeName = "";

        if (para.getClassificationIds() == null){
            //不是四级分类
            for(String definitionId : StringUtils.split(para.getComplexIndexDefinitionIds(), ',')){
                complexIndexDefinitionId = NumberUtil.parseInt(definitionId);
                //获取查询能源类型对应的名称
                complexIndexDefinitionTypeName = GetComplexIndexDefinitionTypeName(complexIndexDefinitionId);

                //获取节点信息和指标ID
                List<EnergyReportTrendResultDTO> lstObjectAndComplexIndexId = new ArrayList<>();
                SetObjectAndComplexIndexId(para, lstObjectAndComplexIndexId, complexIndexDefinitionId, complexIndexDefinitionTypeName);

                lstObjectAndComplexIndexId.forEach(dto->result.add(new EnergyReportYearTrendResultDTO(dto)));
            }
        }else{
            if (para.getClassificationIds().equals("") || para.getResourceStructureIds().equals(""))
                return result;

            para.setComplexIndexDefinitionTypeId(1);
            //是四级分类
            lstEnergyMultiLevelObject = energyMultiLevelObjectMapper.selectList(null);

            if (para.getResourceStructureIds().equals("-1")) {
                List<ResourceStructure> needResourceStructure = resourceStructureService.findResourceStructureByUserId(para.getUserId());
                String rds = needResourceStructure.stream()
                        .map(ResourceStructure::getResourceStructureId) // 提取 ResourceStructureId
                        .map(Object::toString) // 将每个 ID 转换为字符串
                        .collect(Collectors.joining(", "));
                para.setResourceStructureIds(rds);
            }

            List<EnergyComplexIndexClassificationMap> needComplexIndex =
                    energyComplexIndexClassificationMapMapper.getComplexIndexByResIdAndClassificationId(para.getResourceStructureIds(),para.getClassificationIds());

            for(EnergyComplexIndexClassificationMap map : needComplexIndex){
                EnergyReportYearTrendResultDTO dto = new EnergyReportYearTrendResultDTO();
                EnergyMultiLevelObject multiLevelObject = lstEnergyMultiLevelObject.stream().filter(i->i.getObjectId().equals(map.getClassificationId())).findFirst().orElse(null);
                if (multiLevelObject == null) continue;
                dto.setClassificationName(multiLevelObject.getObjectName());
                dto.setClassificationId(map.getClassificationId());
                dto.setObjectId(map.getResourceStructureId());
                dto.setObjectName(resourceStructureManager.getResourceStructureById(map.getResourceStructureId()).getResourceStructureName());
                dto.setComplexIndexId(map.getComplexIndexId());

                ComplexIndex thisComplexIndex = energyComplexIndexManager.GetAllComplexIndex().stream().filter(i->i.getComplexIndexId().equals(map.getComplexIndexId())).findFirst().orElse(null);
                if (thisComplexIndex == null) continue;
                dto.setComplexIndexName(thisComplexIndex.getComplexIndexName());
                dto.setObjectTypeId(thisComplexIndex.getObjectTypeId());
                dto.setComplexIndexDefinitionId(1);
                result.add(dto);
            }
        }

        //今后的数据不用查询
        Date tempStartTime = para.getStartTime();
        Date tempEndTime = DateUtil.getMonthEndTime(tempStartTime);
        for (int i=0;i<12;i++){
            if (tempStartTime.before(new Date())) {
                //读取InfluxDB 查询能耗数据
                GetYearConsumeTrend(result, para.getComplexIndexDefinitionTypeId(), tempStartTime, tempEndTime,1.0);
                tempStartTime = DateUtil.getNextMonthFirstDay(tempStartTime);
                tempEndTime = DateUtil.getMonthEndTime(tempStartTime);
            }
            else
                break;
        }

        //赋值总量字段
        result.forEach(dto->{
            if(para.getComplexIndexDefinitionTypeId().equals(2) || para.getComplexIndexDefinitionTypeId().equals(100))
                dto.SetAvgConsume();
            else
                dto.SetTotalConsume();
        });

        return result.stream().sorted(
                Comparator.comparing(EnergyReportYearTrendResultDTO::getObjectTypeId)
                        .thenComparing(EnergyReportYearTrendResultDTO::getObjectId)
                        .thenComparing(EnergyReportYearTrendResultDTO::getComplexIndexDefinitionId)
        ).collect(Collectors.toList());
    }
    //赋值年趋势
    private void GetYearConsumeTrend(List<EnergyReportYearTrendResultDTO> paraResult, int complexIndexDefinitionTypeId,Date startTime, Date endTime, Double coefficient){
        try {
            String selectDuration = "select IndexValue as result from EnergyHisMonthData where time >=$startTime and time <= $endTime and ( $someComplexIndex ) group by ComplexIndexId";

            if (complexIndexDefinitionTypeId == 2)
                selectDuration = "select IndexValue as result from EnergyHisMonthData where time >=$startTime and time <= $endTime and ( $someComplexIndex ) group by ComplexIndexId";

            String sql = null;
            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();

            StringBuilder bsWhereComplexIndexId = new StringBuilder();
            int count = 0;
            for(EnergyReportYearTrendResultDTO ciId:paraResult) {
                if (bsWhereComplexIndexId.length() == 0) {
                    bsWhereComplexIndexId.append(" ComplexIndexId='" + ciId.getComplexIndexId()+"' ");
                }else{
                    bsWhereComplexIndexId.append(" or ComplexIndexId='" + ciId.getComplexIndexId()+"' ");
                }
                count++;
                if (count == paraResult.size() || count % 500 == 0){
                    List<String> temComplexIndexIds = new ArrayList<>();
                    //sql = String.format(selectDuration, DateUtil.dateToString(startTime), DateUtil.dateToString(endTime),bsWhereComplexIndexId.toString());
                    List<EnergyHisMonthDataResult> resultComplexIndexQuery = new ArrayList<>();
                    String selectDurationByQuery = selectDuration.replace("$someComplexIndex",bsWhereComplexIndexId.toString());

                    Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDurationByQuery)
                            .forDatabase(databaseEnergy)
                            .bind("startTime", dateToString(startTime))
                            .bind("endTime", dateToString(endTime))
                            .create();
                    query =  influxDB.query(queryBuilder);
                    if (query != null) {
                        resultComplexIndexQuery= resultMapper.toPOJO(query, EnergyHisMonthDataResult.class);
                        if (!resultComplexIndexQuery.isEmpty()) {
                            for(EnergyHisMonthDataResult temp :resultComplexIndexQuery){
                                EnergyReportYearTrendResultDTO st = paraResult.stream().filter(i->i.getComplexIndexId() != null && i.getComplexIndexId().equals(Integer.valueOf(temp.getComplexIndexId()))).findFirst().orElse(null);
                                if (st != null){
                                    if (temp.getResult() == null) continue;
                                    Double valueTemp = com.siteweb.common.util.NumberUtil.doubleAccuracy(Double.parseDouble(temp.getResult())*coefficient,2);

                                    Calendar ca = Calendar.getInstance();
                                    ca.setTime(startTime);
                                    switch (ca.get(Calendar.MONTH)+1){
                                        case 1:st.setM1(valueTemp);break;
                                        case 2:st.setM2(valueTemp);break;
                                        case 3:st.setM3(valueTemp);break;
                                        case 4:st.setM4(valueTemp);break;
                                        case 5:st.setM5(valueTemp);break;
                                        case 6:st.setM6(valueTemp);break;
                                        case 7:st.setM7(valueTemp);break;
                                        case 8:st.setM8(valueTemp);break;
                                        case 9:st.setM9(valueTemp);break;
                                        case 10:st.setM10(valueTemp);break;
                                        case 11:st.setM11(valueTemp);break;
                                        case 12:st.setM12(valueTemp);break;
                                    }
                                }
                            }
                        }
                    }
                    bsWhereComplexIndexId = new StringBuilder();
                }
            }
        }catch (Exception e){
            log.error("EnergyReportServiceImpl-GetYearConsumeTrend error {}", e.getMessage());
        }
    }


    @Override
    public List<EnergyReportDayTrendResultDTO> EnergyCarbonDayTrendReport(EnergyReportParameterDTO para) {
        EnergyCoefficientDTO energyCoefficientDTO = energyStandardApiService.getCoefficient(para.getBusinessTypeId());
        double coefficient = 0d;
        if (energyCoefficientDTO != null  &&  energyCoefficientDTO.getCoal() != 0)
            coefficient = energyCoefficientDTO.getCoal();
        List<EnergyReportDayTrendResultDTO> result = new ArrayList<>();
        EnergyReportDayTrendResultDTO oneResult;

        Integer complexIndexDefinitionId = 0;
        String complexIndexDefinitionTypeName = "";

        for(String definitionId : StringUtils.split(para.getComplexIndexDefinitionIds(), ',')){
            complexIndexDefinitionId = NumberUtil.parseInt(definitionId);
            //获取查询能源类型对应的名称
            complexIndexDefinitionTypeName = GetComplexIndexDefinitionTypeName(complexIndexDefinitionId);

            //获取节点信息和指标ID
            List<EnergyReportTrendResultDTO> lstObjectAndComplexIndexId = new ArrayList<>();
            SetObjectAndComplexIndexId(para, lstObjectAndComplexIndexId, complexIndexDefinitionId, complexIndexDefinitionTypeName);

            lstObjectAndComplexIndexId.forEach(dto->result.add(new EnergyReportDayTrendResultDTO(dto)));
        }

        //读取InfluxDB 查询能耗数据
        GetDayConsumeTrend(result, para.getComplexIndexDefinitionTypeId(), para.getStartTime(), para.getEndTime(),coefficient);
        //赋值总量字段
        result.forEach(dto->{
            if(para.getComplexIndexDefinitionTypeId().equals(2) || para.getComplexIndexDefinitionTypeId().equals(100))
                dto.SetAvgConsume();
            else
                dto.SetTotalConsume();
        });

        return result.stream().sorted(
                Comparator.comparing(EnergyReportDayTrendResultDTO::getObjectTypeId)
                        .thenComparing(EnergyReportDayTrendResultDTO::getObjectId)
                        .thenComparing(EnergyReportDayTrendResultDTO::getComplexIndexDefinitionId)
        ).collect(Collectors.toList());
    }

    @Override
    public List<EnergyReportMonthTrendResultDTO> EnergyCarbonMonthTrendReport(EnergyReportParameterDTO para) {
        EnergyCoefficientDTO energyCoefficientDTO = energyStandardApiService.getCoefficient(para.getBusinessTypeId());
        double coefficient = 0d;
        if (energyCoefficientDTO != null  &&  energyCoefficientDTO.getCoal() != 0)
            coefficient = energyCoefficientDTO.getCoal();
        List<EnergyReportMonthTrendResultDTO> result = new ArrayList<>();
        EnergyReportMonthTrendResultDTO oneResult;

        Integer complexIndexDefinitionId = 0;
        String complexIndexDefinitionTypeName = "";

        for(String definitionId : StringUtils.split(para.getComplexIndexDefinitionIds(), ',')){
            complexIndexDefinitionId = NumberUtil.parseInt(definitionId);
            //获取查询能源类型对应的名称
            complexIndexDefinitionTypeName = GetComplexIndexDefinitionTypeName(complexIndexDefinitionId);

            //获取节点信息和指标ID
            List<EnergyReportTrendResultDTO> lstObjectAndComplexIndexId = new ArrayList<>();
            SetObjectAndComplexIndexId(para, lstObjectAndComplexIndexId, complexIndexDefinitionId, complexIndexDefinitionTypeName);

            lstObjectAndComplexIndexId.forEach(dto->result.add(new EnergyReportMonthTrendResultDTO(dto)));
        }

        //读取InfluxDB 查询能耗数据
        GetMonthConsumeTrend(result, para.getComplexIndexDefinitionTypeId(), para.getStartTime(), para.getEndTime(),coefficient);
        //赋值总量字段
        result.forEach(dto->{
            if(para.getComplexIndexDefinitionTypeId().equals(2) || para.getComplexIndexDefinitionTypeId().equals(100))
                dto.SetAvgConsume();
            else
                dto.SetTotalConsume();
        });

        return result.stream().sorted(
                Comparator.comparing(EnergyReportMonthTrendResultDTO::getObjectTypeId)
                        .thenComparing(EnergyReportMonthTrendResultDTO::getObjectId)
                        .thenComparing(EnergyReportMonthTrendResultDTO::getComplexIndexDefinitionId)
        ).collect(Collectors.toList());
    }

    @Override
    public List<EnergyReportYearTrendResultDTO> EnergyCarbonYearTrendReport(EnergyReportParameterDTO para) {
        EnergyCoefficientDTO energyCoefficientDTO = energyStandardApiService.getCoefficient(para.getBusinessTypeId());
        double coefficient = 0d;
        if (energyCoefficientDTO != null  &&  energyCoefficientDTO.getCoal() != 0)
            coefficient = energyCoefficientDTO.getCoal();
        List<EnergyReportYearTrendResultDTO> result = new ArrayList<>();
        EnergyReportYearTrendResultDTO oneResult = new EnergyReportYearTrendResultDTO();

        Integer complexIndexDefinitionId = 0;
        String complexIndexDefinitionTypeName = "";
        for(String definitionId : StringUtils.split(para.getComplexIndexDefinitionIds(), ',')){
            complexIndexDefinitionId = NumberUtil.parseInt(definitionId);
            //获取查询能源类型对应的名称
            complexIndexDefinitionTypeName = GetComplexIndexDefinitionTypeName(complexIndexDefinitionId);

            //获取节点信息和指标ID
            List<EnergyReportTrendResultDTO> lstObjectAndComplexIndexId = new ArrayList<>();
            SetObjectAndComplexIndexId(para, lstObjectAndComplexIndexId, complexIndexDefinitionId, complexIndexDefinitionTypeName);

            lstObjectAndComplexIndexId.forEach(dto->result.add(new EnergyReportYearTrendResultDTO(dto)));
        }

        //今后的数据不用查询
        Date tempStartTime = para.getStartTime();
        Date tempEndTime = DateUtil.getMonthEndTime(tempStartTime);
        for (int i=0;i<12;i++){
            if (tempStartTime.before(new Date())) {
                //读取InfluxDB 查询能耗数据
                GetYearConsumeTrend(result, para.getComplexIndexDefinitionTypeId(), tempStartTime, tempEndTime,coefficient);
                tempStartTime = DateUtil.getNextMonthFirstDay(tempStartTime);
                tempEndTime = DateUtil.getMonthEndTime(tempStartTime);
            }
            else
                break;
        }

        //赋值总量字段
        result.forEach(dto->{
            if(para.getComplexIndexDefinitionTypeId().equals(2) || para.getComplexIndexDefinitionTypeId().equals(100))
                dto.SetAvgConsume();
            else
                dto.SetTotalConsume();
        });

        return result.stream().sorted(
                Comparator.comparing(EnergyReportYearTrendResultDTO::getObjectTypeId)
                        .thenComparing(EnergyReportYearTrendResultDTO::getObjectId)
                        .thenComparing(EnergyReportYearTrendResultDTO::getComplexIndexDefinitionId)
        ).collect(Collectors.toList());
    }



    //获取节点信息和指标ID
    private void SetObjectAndComplexIndexId(EnergyReportParameterDTO para, List<EnergyReportTrendResultDTO> result
            ,Integer complexIndexDefinitionId, String complexIndexDefinitionTypeName){
        EnergyReportTrendResultDTO oneResult = new EnergyReportTrendResultDTO();
        List<ResourceStructure> needResourceStructure = new ArrayList<>();
        //默认层级
        if(para.getDimensionTypeId() <1 ){

            if (para.getResourceStructureIds().equals("-1"))
                needResourceStructure = resourceStructureService.findResourceStructureByUserId(para.getUserId());
            else{
                for(String resourceStructureId :  para.getResourceStructureIds().split(",")){
                    needResourceStructure.add(resourceStructureManager.getResourceStructureById(Integer.parseInt(resourceStructureId)));
                }
            }
            for(ResourceStructure rs :  needResourceStructure){
                oneResult = new EnergyReportMonthTrendResultDTO();
                oneResult.setSearchTypeName(complexIndexDefinitionTypeName);
                oneResult.setComplexIndexDefinitionId(complexIndexDefinitionId);
                oneResult.setObjectName(rs.getResourceStructureName());
                oneResult.setObjectTypeId(rs.getStructureTypeId());
                oneResult.setObjectId(rs.getResourceStructureId());
                //查找指标Id
                ComplexIndex complexIndex = energyComplexIndexManager.GetAllComplexIndex().stream().filter(
                        i->i.getObjectId().equals(rs.getResourceStructureId()) && i.getObjectTypeId().equals(rs.getStructureTypeId())
                                && i.getBusinessTypeId().equals(para.getBusinessTypeId()) && i.getComplexIndexDefinitionId().equals(complexIndexDefinitionId)
                ).findFirst().orElse(null);

                if (complexIndex != null){
                    oneResult.setComplexIndexId(complexIndex.getComplexIndexId());
                    oneResult.setUnit(complexIndex.getUnit());
                    result.add(oneResult);
                }
            }
        }
        else{    //自定义层级
            List<EnergyObjectMap> lstMap = energyObjectMapService.findByDimensionTypeId(para.getDimensionTypeId());
            List<EnergyStructure> lstStructure = energyStructureService.findAll();

            if (para.getResourceStructureIds().equals("-1")){
                String resourceStructureIds = "";
                for (EnergyObjectMap map:lstMap) {
                    int tempType= 200;
                    switch (map.getObjectIdType()){
                        case 2:
                            tempType = 200;break;
                        case 3:
                            tempType = 7;break;
                        case 4:
                            tempType = 9;break;
                        case 5:
                            tempType = 10;break;
                        default:
                            tempType = 1;break;
                    }
                    resourceStructureIds = resourceStructureIds + map.getObjectId().toString()+"_"+tempType+",";
                }
                if (resourceStructureIds.endsWith(",")){
                    resourceStructureIds = resourceStructureIds.substring(0,resourceStructureIds.length()-1);
                }
                para.setResourceStructureIds(resourceStructureIds);
            }


            for(String objectIdAndObjectTypeId : para.getResourceStructureIds().split(",")){
                String[] objectIdAndObjectTypeIdSplit = objectIdAndObjectTypeId.split("_");
                if (objectIdAndObjectTypeIdSplit.length != 2){
                    log.error("EnergyConsumeYearTrendReport objectIdAndObjectTypeIdSplit error"+objectIdAndObjectTypeId);
                    continue;
                }
                Integer objectId = NumberUtil.parseInt(objectIdAndObjectTypeIdSplit[0]);
                Integer objectTypeId = NumberUtil.parseInt(objectIdAndObjectTypeIdSplit[1]);

                oneResult = new EnergyReportMonthTrendResultDTO();
                oneResult.setSearchTypeName(complexIndexDefinitionTypeName);
                oneResult.setComplexIndexDefinitionId(complexIndexDefinitionId);

                switch (objectTypeId){
                    case 7:
                        Equipment equipment = equipmentManager.getEquipmentById(objectId);
                        if (equipment == null) continue;
                        oneResult.setObjectId(equipment.getEquipmentId());
                        oneResult.setObjectName(equipment.getEquipmentName());
                        objectTypeId = 7;   //设备节点在指标表ObjectTypeId=7;
                        break;
                    case 9:
                        ComputerRack computerRack = computerRackService.findComputerRack(objectId);
                        if (computerRack == null) continue;
                        oneResult.setObjectId(computerRack.getComputerRackId());
                        oneResult.setObjectName(computerRack.getComputerRackName());
                        objectTypeId = 9;   //机架节点在指标表ObjectTypeId=9;
                        break;
                    case 10:
                        ITDevice iTDevice = iTDeviceService.findITDevice(objectId);
                        if (iTDevice == null) continue;
                        oneResult.setObjectId(iTDevice.getITDeviceId());
                        oneResult.setObjectName(iTDevice.getITDeviceName());
                        objectTypeId = 10;   //IT设备节点在指标表ObjectTypeId=10;
                        break;
                    case 200:
                        EnergyStructure oneStructure = lstStructure.stream().filter(item->item.getStructureId().equals(objectId)).findFirst().orElse(null);
                        if (oneStructure == null) continue;
                        oneResult.setObjectId(oneStructure.getStructureId());
                        oneResult.setObjectName(oneStructure.getStructureName());
                        objectTypeId = 200;   //自定义层级节点在指标表ObjectTypeId=200;
                        break;
                    default:
                        ResourceStructure oneResourceStructure =  resourceStructureManager.getResourceStructureById(objectId);
                        if (oneResourceStructure == null) continue;
                        oneResult.setObjectId(oneResourceStructure.getResourceStructureId());
                        oneResult.setObjectName(oneResourceStructure.getResourceStructureName());
                        objectTypeId = oneResourceStructure.getStructureTypeId(); //原层级的类型
                        break;
                }
                oneResult.setObjectTypeId(objectTypeId);

                //查找指标Id
                Integer finalObjectTypeId = objectTypeId;
                ComplexIndex complexIndex = energyComplexIndexManager.GetAllComplexIndex().stream().filter(
                        i->i.getObjectId().equals(objectId) && i.getObjectTypeId().equals(finalObjectTypeId)
                                && i.getBusinessTypeId().equals(para.getBusinessTypeId()) && i.getComplexIndexDefinitionId().equals(complexIndexDefinitionId)
                ).findFirst().orElse(null);

                if (complexIndex != null){
                    oneResult.setComplexIndexId(complexIndex.getComplexIndexId());
                    oneResult.setUnit(complexIndex.getUnit());
                    result.add(oneResult);
                }
            }
        }
    }

    //获取损耗查询报表的节点及子节点信息、指标ID
    private void SetLossObjectAndComplexIndexId(EnergyReportParameterDTO para, List<EnergyReportTrendResultDTO> result
            ,Integer complexIndexDefinitionId, String complexIndexDefinitionTypeName){
        EnergyReportTrendResultDTO oneResult = new EnergyReportTrendResultDTO();
        ResourceStructure lossResourceStructure = new ResourceStructure();
        List<ResourceStructure> needResourceStructure = new ArrayList<>();

        String resourceStructureId = para.getResourceStructureIds();
        lossResourceStructure = resourceStructureManager.getResourceStructureById(Integer.parseInt(resourceStructureId));
        if(lossResourceStructure != null) {
            needResourceStructure.add(lossResourceStructure);
            if (lossResourceStructure.getChildren() != null && !lossResourceStructure.getChildren().isEmpty()) {
                needResourceStructure.addAll(lossResourceStructure.getChildren());
            }
        }
       for(ResourceStructure rs :  needResourceStructure){
           oneResult = new EnergyReportMonthTrendResultDTO();
           oneResult.setSearchTypeName(complexIndexDefinitionTypeName);
           oneResult.setComplexIndexDefinitionId(complexIndexDefinitionId);
           oneResult.setObjectName(rs.getResourceStructureName());
            oneResult.setObjectTypeId(rs.getStructureTypeId());
            oneResult.setObjectId(rs.getResourceStructureId());
            //查找指标Id
            ComplexIndex complexIndex = energyComplexIndexManager.GetAllComplexIndex().stream().filter(
                    i->i.getObjectId().equals(rs.getResourceStructureId()) && i.getObjectTypeId().equals(rs.getStructureTypeId())
                            && i.getBusinessTypeId().equals(para.getBusinessTypeId()) && i.getComplexIndexDefinitionId().equals(complexIndexDefinitionId)
            ).findFirst().orElse(null);

            if (complexIndex != null){
               oneResult.setComplexIndexId(complexIndex.getComplexIndexId());
                oneResult.setUnit(complexIndex.getUnit());
                result.add(oneResult);
            }
        }
    }

    //获取查询能源类型对应的名称
    private String GetComplexIndexDefinitionTypeName(Integer complexIndexDefinitionId){
        ComplexIndexDefinition complexIndexDefinition = energyComplexIndexManager.GetAllComplexIndexDefinition().stream().filter(
                i->i.getComplexIndexDefinitionId().equals(complexIndexDefinitionId)
        ).findFirst().orElse(null);
        if (complexIndexDefinition == null){
            log.error("GetComplexIndexDefinitionTypeName error complexIndexDefinitionId=" + complexIndexDefinitionId);
            return null;
        }
        return complexIndexDefinition.getComplexIndexDefinitionName();
    }


    @Override
    public List<EnergyReportYearTrendResultDTO> EnergyCostYearTrendReport(EnergyReportParameterDTO para) {
        List<EnergyReportYearTrendResultDTO> result = new ArrayList<>();
        EnergyReportYearTrendResultDTO oneResult;

        //获取节点信息和指标ID
        List<EnergyReportTrendResultDTO> lstObjectAndComplexIndexId = new ArrayList<>();
        setObjectComplexIndexInfo(para, lstObjectAndComplexIndexId);

        lstObjectAndComplexIndexId.forEach(dto -> result.add(new EnergyReportYearTrendResultDTO(dto)));


        //今后的数据不用查询
        Date tempStartTime = para.getStartTime();
        Date tempEndTime = DateUtil.getMonthEndTime(tempStartTime);
        for (int i = 0; i < 12; i++) {
            if (tempStartTime.before(new Date())) {
                //读取InfluxDB 查询能耗数据
                GetYearFeeConsumeTrend(result, tempStartTime, tempEndTime);
                tempStartTime = DateUtil.getNextMonthFirstDay(tempStartTime);
                tempEndTime = DateUtil.getMonthEndTime(tempStartTime);
            } else
                break;
        }

        //赋值总量字段
        result.forEach(EnergyReportYearTrendResultDTO::SetTotalConsume);

        return result.stream()
                .filter(dto -> dto.getSearchTypeName().equals("yes"))
                .sorted(Comparator.comparing(EnergyReportYearTrendResultDTO::getObjectId)
                ).collect(Collectors.toList());
    }

    private void GetYearFeeConsumeTrend(List<EnergyReportYearTrendResultDTO> result, Date startTime, Date endTime) {
        try {
            String selectDuration = "select sum(IndexFeeValue) as  result from EnergyHisFeeData where time >=$startTime and time <= $endTime and ( $someComplexIndex ) group by fpgDescKey,ComplexIndexId";

            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();

            StringBuilder bsWhereComplexIndexId = new StringBuilder();
            int count = 0;
            for(EnergyReportYearTrendResultDTO ciId:result) {
                if (bsWhereComplexIndexId.length() == 0) {
                    bsWhereComplexIndexId.append(" ComplexIndexId='" + ciId.getComplexIndexId()+"' ");
                }else{
                    bsWhereComplexIndexId.append(" or ComplexIndexId='" + ciId.getComplexIndexId()+"' ");
                }
                count++;
                if (count == result.size() || count % 500 == 0){
                    List<HistoryComplexIndexFeeSumResult> resultComplexIndexQuery = new ArrayList<>();
                    String selectDurationByQuery = selectDuration.replace("$someComplexIndex",bsWhereComplexIndexId.toString());

                    Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDurationByQuery)
                            .forDatabase(databaseEnergy)
                            .bind("startTime", dateToString(startTime))
                            .bind("endTime", dateToString(endTime))
                            .create();
                    query =  influxDB.query(queryBuilder);
                    if (query != null) {
                        resultComplexIndexQuery= resultMapper.toPOJO(query, HistoryComplexIndexFeeSumResult.class);
                        if (!resultComplexIndexQuery.isEmpty()) {
                            for(HistoryComplexIndexFeeSumResult temp :resultComplexIndexQuery){
                                EnergyReportYearTrendResultDTO st = result.stream().filter(i -> i.getComplexIndexId() != null&& i.getObjectTypeId().toString().equals(temp.getFpgDescKey()) && i.getComplexIndexId().equals(Integer.valueOf(temp.getComplexIndexId()))).findFirst().orElse(null);
                                if (st != null){
                                    st.setSearchTypeName("yes");
                                    if (temp.getResult() == null) continue;
                                    Double valueTemp = com.siteweb.common.util.NumberUtil.doubleAccuracy(Double.parseDouble(temp.getResult()),2);

                                    Calendar ca = Calendar.getInstance();
                                    ca.setTime(startTime);
                                    switch (ca.get(Calendar.MONTH) + 1) {
                                        case 1:st.setM1(valueTemp);break;
                                        case 2:st.setM2(valueTemp);break;
                                        case 3:st.setM3(valueTemp);break;
                                        case 4:st.setM4(valueTemp);break;
                                        case 5:st.setM5(valueTemp);break;
                                        case 6:st.setM6(valueTemp);break;
                                        case 7:st.setM7(valueTemp);break;
                                        case 8:st.setM8(valueTemp);break;
                                        case 9:st.setM9(valueTemp);break;
                                        case 10:st.setM10(valueTemp);break;
                                        case 11:st.setM11(valueTemp);break;
                                        case 12:st.setM12(valueTemp);break;
                                    }
                                }
                            }
                        }
                    }
                    bsWhereComplexIndexId = new StringBuilder();
                }
            }
        }catch (Exception e){
            log.error("EnergyReportServiceImpl-GetYearConsumeTrend error {}", e.getMessage());
        }
    }

    @Override
    public List<EnergyReportMonthTrendResultDTO> EnergyCostMonthTrendReport(EnergyReportParameterDTO para) {
        List<EnergyReportMonthTrendResultDTO> result = new ArrayList<>();
        EnergyReportMonthTrendResultDTO oneResult;

        //获取节点信息和指标ID
        List<EnergyReportTrendResultDTO> lstObjectAndComplexIndexId = new ArrayList<>();
        setObjectComplexIndexInfo(para, lstObjectAndComplexIndexId);

        lstObjectAndComplexIndexId.forEach(dto -> result.add(new EnergyReportMonthTrendResultDTO(dto)));
        //读取InfluxDB 查询能耗数据
        GetMonthFeeConsumeTrend(result, para.getStartTime(), para.getEndTime());

        //赋值总量字段
        result.forEach(EnergyReportMonthTrendResultDTO::SetTotalConsume);
        return result.stream()
                .filter(dto -> dto.getSearchTypeName().equals("yes"))
                .sorted(Comparator.comparing(EnergyReportMonthTrendResultDTO::getObjectId)
                ).collect(Collectors.toList());
    }

    private void GetMonthFeeConsumeTrend(List<EnergyReportMonthTrendResultDTO> result, Date startTime, Date endTime) {
        try {
            String selectDuration = "select sum(IndexFeeValue) as result from EnergyHisFeeData where time >=$startTime and time <= $endTime and ( $someComplexIndex ) group by time(1d), fpgDescKey,ComplexIndexId ";

            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();

            StringBuilder bsWhereComplexIndexId = new StringBuilder();
            int count = 0;
            for (EnergyReportMonthTrendResultDTO ciId : result) {
                if (bsWhereComplexIndexId.length() == 0) {
                    bsWhereComplexIndexId.append(" ComplexIndexId='").append(ciId.getComplexIndexId()).append("' ");
                } else {
                    bsWhereComplexIndexId.append(" or ComplexIndexId='").append(ciId.getComplexIndexId()).append("' ");
                }
                count++;
                if (count == result.size() || count % 500 == 0) {
                    List<HistoryComplexIndexFeeSumResult> resultComplexIndexQuery = new ArrayList<>();
                    String selectDurationByQuery = selectDuration.replace("$someComplexIndex", bsWhereComplexIndexId.toString());
                    Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDurationByQuery)
                            .forDatabase(databaseEnergy)
                            .bind("startTime", dateToString(startTime))
                            .bind("endTime", dateToString(endTime))
                            .create();

                    query = influxDB.query(queryBuilder);
                    if (query != null) {
                        resultComplexIndexQuery = resultMapper.toPOJO(query, HistoryComplexIndexFeeSumResult.class);
                        if (!resultComplexIndexQuery.isEmpty()) {
                            for (HistoryComplexIndexFeeSumResult temp : resultComplexIndexQuery) {

                                EnergyReportMonthTrendResultDTO st = result.stream().filter(i -> i.getComplexIndexId() != null&& i.getObjectTypeId().toString().equals(temp.getFpgDescKey()) && i.getComplexIndexId().equals(Integer.valueOf(temp.getComplexIndexId()))).findFirst().orElse(null);
                                if (st != null) {
                                    st.setSearchTypeName("yes");
                                    if (temp.getResult() == null) continue;
                                    Double valueTemp = com.siteweb.common.util.NumberUtil.doubleAccuracy(Double.parseDouble(temp.getResult()), 2);

                                    Calendar ca = Calendar.getInstance();
                                    ca.setTime((new SimpleDateFormat("yyyy-MM-dd HH")).parse(temp.getTime().replace('T',' ')));
                                    switch (ca.get(Calendar.DAY_OF_MONTH)){
                                        case 1:st.setD1(valueTemp);break;
                                        case 2:st.setD2(valueTemp);break;
                                        case 3:st.setD3(valueTemp);break;
                                        case 4:st.setD4(valueTemp);break;
                                        case 5:st.setD5(valueTemp);break;
                                        case 6:st.setD6(valueTemp);break;
                                        case 7:st.setD7(valueTemp);break;
                                        case 8:st.setD8(valueTemp);break;
                                        case 9:st.setD9(valueTemp);break;
                                        case 10:st.setD10(valueTemp);break;
                                        case 11:st.setD11(valueTemp);break;
                                        case 12:st.setD12(valueTemp);break;
                                        case 13:st.setD13(valueTemp);break;
                                        case 14:st.setD14(valueTemp);break;
                                        case 15:st.setD15(valueTemp);break;
                                        case 16:st.setD16(valueTemp);break;
                                        case 17:st.setD17(valueTemp);break;
                                        case 18:st.setD18(valueTemp);break;
                                        case 19:st.setD19(valueTemp);break;
                                        case 20:st.setD20(valueTemp);break;
                                        case 21:st.setD21(valueTemp);break;
                                        case 22:st.setD22(valueTemp);break;
                                        case 23:st.setD23(valueTemp);break;
                                        case 24:st.setD24(valueTemp);break;
                                        case 25:st.setD25(valueTemp);break;
                                        case 26:st.setD26(valueTemp);break;
                                        case 27:st.setD27(valueTemp);break;
                                        case 28:st.setD28(valueTemp);break;
                                        case 29:st.setD29(valueTemp);break;
                                        case 30:st.setD30(valueTemp);break;
                                        case 31:st.setD31(valueTemp);break;
                                    }
                                }
                            }
                        }
                    }
                    bsWhereComplexIndexId = new StringBuilder();
                }
            }
        }catch (Exception e){
            log.error("EnergyReportServiceImpl-GetDayFeeConsumeTrend error {}", e.getMessage());
        }
    }

    @Override
    public List<EnergyReportDayTrendResultDTO> EnergyCostDayTrendReport(EnergyReportParameterDTO para) {
        List<EnergyReportDayTrendResultDTO> result = new ArrayList<>();
        EnergyReportDayTrendResultDTO oneResult;

        //获取节点信息和指标ID
        List<EnergyReportTrendResultDTO> lstObjectAndComplexIndexId = new ArrayList<>();
        setObjectComplexIndexInfo(para, lstObjectAndComplexIndexId);

        lstObjectAndComplexIndexId.forEach(dto -> result.add(new EnergyReportDayTrendResultDTO(dto)));
        //读取InfluxDB 查询能耗数据
        GetDayFeeConsumeTrend(result, para.getStartTime(), para.getEndTime());
        //赋值总量字段
        result.forEach(EnergyReportDayTrendResultDTO::SetTotalConsume);

        return result.stream()
                .filter(dto -> dto.getSearchTypeName().equals("yes"))
                .sorted(Comparator.comparing(EnergyReportDayTrendResultDTO::getObjectId)
                ).collect(Collectors.toList());
    }

    private void GetDayFeeConsumeTrend(List<EnergyReportDayTrendResultDTO> result, Date startTime, Date endTime) {
        try {
            String selectDuration = "select sum(IndexFeeValue) as result from EnergyHisFeeData where time >=$startTime and time <= $endTime and ( $someComplexIndex ) group by time(1h), fpgDescKey,ComplexIndexId ";

            QueryResult query = null;
            InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();

            StringBuilder bsWhereComplexIndexId = new StringBuilder();
            int count = 0;
            for (EnergyReportDayTrendResultDTO ciId : result) {
                if (bsWhereComplexIndexId.length() == 0) {
                    bsWhereComplexIndexId.append(" ComplexIndexId='").append(ciId.getComplexIndexId()).append("' ");
                } else {
                    bsWhereComplexIndexId.append(" or ComplexIndexId='").append(ciId.getComplexIndexId()).append("' ");
                }
                count++;
                if (count == result.size() || count % 500 == 0) {
                    List<HistoryComplexIndexFeeSumResult> resultComplexIndexQuery = new ArrayList<>();
                    String selectDurationByQuery = selectDuration.replace("$someComplexIndex", bsWhereComplexIndexId.toString());
                    Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDurationByQuery)
                            .forDatabase(databaseEnergy)
                            .bind("startTime", dateToString(startTime))
                            .bind("endTime", dateToString(endTime))
                            .create();

                    query = influxDB.query(queryBuilder);
                    if (query != null) {
                        resultComplexIndexQuery = resultMapper.toPOJO(query, HistoryComplexIndexFeeSumResult.class);
                        if (!resultComplexIndexQuery.isEmpty()) {
                            for (HistoryComplexIndexFeeSumResult temp : resultComplexIndexQuery) {

                                EnergyReportDayTrendResultDTO st = result.stream().filter(i -> i.getComplexIndexId() != null&& i.getObjectTypeId().toString().equals(temp.getFpgDescKey()) && i.getComplexIndexId().equals(Integer.valueOf(temp.getComplexIndexId()))).findFirst().orElse(null);
                                if (st != null) {
                                    st.setSearchTypeName("yes");
                                    if (temp.getResult() == null) continue;
                                    Double valueTemp = com.siteweb.common.util.NumberUtil.doubleAccuracy(Double.parseDouble(temp.getResult()), 2);

                                    Calendar ca = Calendar.getInstance();
                                    ca.setTime((new SimpleDateFormat("yyyy-MM-dd HH")).parse(temp.getTime().replace('T',' ')));
                                    switch (ca.get(Calendar.HOUR_OF_DAY)+1){
                                        case 1:st.setH1(valueTemp);break;
                                        case 2:st.setH2(valueTemp);break;
                                        case 3:st.setH3(valueTemp);break;
                                        case 4:st.setH4(valueTemp);break;
                                        case 5:st.setH5(valueTemp);break;
                                        case 6:st.setH6(valueTemp);break;
                                        case 7:st.setH7(valueTemp);break;
                                        case 8:st.setH8(valueTemp);break;
                                        case 9:st.setH9(valueTemp);break;
                                        case 10:st.setH10(valueTemp);break;
                                        case 11:st.setH11(valueTemp);break;
                                        case 12:st.setH12(valueTemp);break;
                                        case 13:st.setH13(valueTemp);break;
                                        case 14:st.setH14(valueTemp);break;
                                        case 15:st.setH15(valueTemp);break;
                                        case 16:st.setH16(valueTemp);break;
                                        case 17:st.setH17(valueTemp);break;
                                        case 18:st.setH18(valueTemp);break;
                                        case 19:st.setH19(valueTemp);break;
                                        case 20:st.setH20(valueTemp);break;
                                        case 21:st.setH21(valueTemp);break;
                                        case 22:st.setH22(valueTemp);break;
                                        case 23:st.setH23(valueTemp);break;
                                        case 24:st.setH24(valueTemp);break;
                                    }
                                }
                            }
                        }
                    }
                    bsWhereComplexIndexId = new StringBuilder();
                }
            }
        }catch (Exception e){
            log.error("EnergyReportServiceImpl-GetDayFeeConsumeTrend error {}", e.getMessage());
        }
    }


    private void setObjectComplexIndexInfo(EnergyReportParameterDTO para, List<EnergyReportTrendResultDTO> result){
        EnergyReportTrendResultDTO oneResult = new EnergyReportTrendResultDTO();
        List<ResourceStructure> needResourceStructure = new ArrayList<>();
        Integer feePeriod = 4;//尖峰平谷fpgDescKey对应1，2，3，4
        if (para.getResourceStructureIds().equals("-1"))
            needResourceStructure = resourceStructureService.findResourceStructureByUserId(para.getUserId());
        else {
            for (String resourceStructureId : para.getResourceStructureIds().split(",")) {
                needResourceStructure.add(resourceStructureManager.getResourceStructureById(Integer.parseInt(resourceStructureId)));
            }
        }
        for (int j = 1; j <= feePeriod; j++) {
            for (ResourceStructure rs : needResourceStructure) {
                oneResult = new EnergyReportMonthTrendResultDTO();
                oneResult.setObjectName(rs.getResourceStructureName());
                oneResult.setObjectTypeId(j);
                oneResult.setObjectId(rs.getResourceStructureId());
                oneResult.setSearchTypeName("no");
                //查找指标Id
                ComplexIndex complexIndex = energyComplexIndexManager.GetAllComplexIndex().stream().filter(
                        i -> i.getObjectId().equals(rs.getResourceStructureId()) && i.getObjectTypeId().equals(rs.getStructureTypeId())
                                && i.getBusinessTypeId().equals(para.getBusinessTypeId())&& i.getComplexIndexDefinitionId().equals(NumberUtil.parseInt(para.getComplexIndexDefinitionIds()))
                ).findFirst().orElse(null);
                oneResult.setComplexIndexId(complexIndex == null ? null : complexIndex.getComplexIndexId());
                result.add(oneResult);
            }
        }
    }

    //损耗日趋势
    @Override
    public List<EnergyReportDayTrendResultDTO> EnergyLossDayTrendReport(EnergyReportParameterDTO para) {
        List<EnergyReportDayTrendResultDTO> result = new ArrayList<>();
        EnergyReportDayTrendResultDTO oneResult;

        Integer complexIndexDefinitionId = 0;
        String complexIndexDefinitionTypeName = "";

        String resourceStructureId = para.getResourceStructureIds();
        String definitionId = para.getComplexIndexDefinitionIds();
        complexIndexDefinitionId = NumberUtil.parseInt(definitionId);
        complexIndexDefinitionTypeName = GetComplexIndexDefinitionTypeName(complexIndexDefinitionId);

        //获取节点信息和指标ID
        List<EnergyReportTrendResultDTO> lstObjectAndComplexIndexId = new ArrayList<>();
        SetLossObjectAndComplexIndexId(para, lstObjectAndComplexIndexId, complexIndexDefinitionId, complexIndexDefinitionTypeName);
        lstObjectAndComplexIndexId.forEach(dto->result.add(new EnergyReportDayTrendResultDTO(dto)));

        //读取InfluxDB 查询能耗数据
        GetDayConsumeTrend(result, para.getComplexIndexDefinitionTypeId(), para.getStartTime(), para.getEndTime(),1.0);
        //赋值总量字段
        result.forEach(dto->{
            if(para.getComplexIndexDefinitionTypeId().equals(2) || para.getComplexIndexDefinitionTypeId().equals(100))
                dto.SetAvgConsume();
            else
                dto.SetTotalConsume();
        });
        List<EnergyReportDayTrendResultDTO> lossResult = GetLossDayResult(result, resourceStructureId);
        return lossResult.stream().sorted(
                Comparator.comparing(EnergyReportDayTrendResultDTO::getObjectTypeId)
                        .thenComparing(EnergyReportDayTrendResultDTO::getObjectId)
                        .thenComparing(EnergyReportDayTrendResultDTO::getComplexIndexDefinitionId)
        ).collect(Collectors.toList());
    }

    //赋值日损耗数据
    private List<EnergyReportDayTrendResultDTO> GetLossDayResult(List<EnergyReportDayTrendResultDTO> result, String resourceStructureId) {
        if(result == null || result.isEmpty())
            return new ArrayList<>();
        try {
            if(result.size() == 1) {
                String objectName = result.get(0).getObjectName();
                result.get(0).setObjectName(objectName + "-损耗");
                return result;
            }
            Optional<EnergyReportDayTrendResultDTO> targetDtoOptional = result.stream()
                    .filter(dto -> String.valueOf(dto.getObjectId()).equals(resourceStructureId))
                    .findFirst();
            if (targetDtoOptional.isPresent()) {
                EnergyReportDayTrendResultDTO targetDto = targetDtoOptional.get();
                int targetIndex = result.indexOf(targetDto);
                Collections.swap(result, targetIndex, 0);
            }
            EnergyReportDayTrendResultDTO firstDto = result.get(0);
            double[] sums = new double[25];
            for(int i = 1; i < result.size(); i++) {
                EnergyReportDayTrendResultDTO dto = result.get(i);
                sums[0] += (dto.getH1() != null ? dto.getH1() : 0);
                sums[1] += (dto.getH2() != null ? dto.getH2() : 0);
                sums[2] += (dto.getH3() != null ? dto.getH3() : 0);
                sums[3] += (dto.getH4() != null ? dto.getH4() : 0);
                sums[4] += (dto.getH5() != null ? dto.getH5() : 0);
                sums[5] += (dto.getH6() != null ? dto.getH6() : 0);
                sums[6] += (dto.getH7() != null ? dto.getH7() : 0);
                sums[7] += (dto.getH8() != null ? dto.getH8() : 0);
                sums[8] += (dto.getH9() != null ? dto.getH9() : 0);
                sums[9] += (dto.getH10() != null ? dto.getH10() : 0);
                sums[10] += (dto.getH11() != null ? dto.getH11() : 0);
                sums[11] += (dto.getH12() != null ? dto.getH12() : 0);
                sums[12] += (dto.getH13() != null ? dto.getH13() : 0);
                sums[13] += (dto.getH14() != null ? dto.getH14() : 0);
                sums[14] += (dto.getH15() != null ? dto.getH15() : 0);
                sums[15] += (dto.getH16() != null ? dto.getH16() : 0);
                sums[16] += (dto.getH17() != null ? dto.getH17() : 0);
                sums[17] += (dto.getH18() != null ? dto.getH18() : 0);
                sums[18] += (dto.getH19() != null ? dto.getH19() : 0);
                sums[19] += (dto.getH20() != null ? dto.getH20() : 0);
                sums[20] += (dto.getH21() != null ? dto.getH21() : 0);
                sums[21] += (dto.getH22() != null ? dto.getH22() : 0);
                sums[22] += (dto.getH23() != null ? dto.getH23() : 0);
                sums[23] += (dto.getH24() != null ? dto.getH24() : 0);
                sums[24] += (dto.getTotal() != null ? dto.getTotal() : 0);
            }
           firstDto.setH1(formateValue((firstDto.getH1() != null ? firstDto.getH1() : 0) - sums[0]));
            firstDto.setH2(formateValue((firstDto.getH2() != null ? firstDto.getH2() : 0) - sums[1]));
            firstDto.setH3(formateValue((firstDto.getH3() != null ? firstDto.getH3() : 0) - sums[2]));
            firstDto.setH4(formateValue((firstDto.getH4() != null ? firstDto.getH4() : 0) - sums[3]));
            firstDto.setH5(formateValue((firstDto.getH5() != null ? firstDto.getH5() : 0) - sums[4]));
            firstDto.setH6(formateValue((firstDto.getH6() != null ? firstDto.getH6() : 0) - sums[5]));
            firstDto.setH7(formateValue((firstDto.getH7() != null ? firstDto.getH7() : 0) - sums[6]));
            firstDto.setH8(formateValue((firstDto.getH8() != null ? firstDto.getH8() : 0) - sums[7]));
            firstDto.setH9(formateValue((firstDto.getH9() != null ? firstDto.getH9() : 0) - sums[8]));
            firstDto.setH10(formateValue((firstDto.getH10() != null ? firstDto.getH10() : 0) - sums[9]));
            firstDto.setH11(formateValue((firstDto.getH11() != null ? firstDto.getH11() : 0) - sums[10]));
            firstDto.setH12(formateValue((firstDto.getH12() != null ? firstDto.getH12() : 0) - sums[11]));
            firstDto.setH13(formateValue((firstDto.getH13() != null ? firstDto.getH13() : 0) - sums[12]));
            firstDto.setH14(formateValue((firstDto.getH14() != null ? firstDto.getH14() : 0) - sums[13]));
            firstDto.setH15(formateValue((firstDto.getH15() != null ? firstDto.getH15() : 0) - sums[14]));
            firstDto.setH16(formateValue((firstDto.getH16() != null ? firstDto.getH16() : 0) - sums[15]));
            firstDto.setH17(formateValue((firstDto.getH17() != null ? firstDto.getH17() : 0) - sums[16]));
            firstDto.setH18(formateValue((firstDto.getH18() != null ? firstDto.getH18() : 0) - sums[17]));
            firstDto.setH19(formateValue((firstDto.getH19() != null ? firstDto.getH19() : 0) - sums[18]));
            firstDto.setH20(formateValue((firstDto.getH20() != null ? firstDto.getH20() : 0) - sums[19]));
            firstDto.setH21(formateValue((firstDto.getH21() != null ? firstDto.getH21() : 0) - sums[20]));
            firstDto.setH22(formateValue((firstDto.getH22() != null ? firstDto.getH22() : 0) - sums[21]));
            firstDto.setH23(formateValue((firstDto.getH23() != null ? firstDto.getH23() : 0) - sums[22]));
            firstDto.setH24(formateValue((firstDto.getH24() != null ? firstDto.getH24() : 0) - sums[23]));
            firstDto.setTotal(formateValue((firstDto.getTotal() != null ? firstDto.getTotal() : 0) - sums[24]));
            firstDto.setObjectName(firstDto.getObjectName() + "-损耗");
            return result;
        } catch (Exception e) {
            log.error("EnergyReportServiceImpl-GetLossDayResult error {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    private double formateValue(double value) {
        DecimalFormat df = new DecimalFormat("#.00");
        return Double.parseDouble(df.format(value));
    }

    //损耗月趋势
    @Override
    public List<EnergyReportMonthTrendResultDTO> EnergyLossMonthTrendReport(EnergyReportParameterDTO para) {
        List<EnergyReportMonthTrendResultDTO> result = new ArrayList<>();
        EnergyReportMonthTrendResultDTO oneResult;

        int complexIndexDefinitionId = 0;
        String complexIndexDefinitionTypeName = "";

        String resourceStructureId = para.getResourceStructureIds();
        String definitionId = para.getComplexIndexDefinitionIds();
        complexIndexDefinitionId = NumberUtil.parseInt(definitionId);
        //获取查询能源类型对应的名称
        complexIndexDefinitionTypeName = GetComplexIndexDefinitionTypeName(complexIndexDefinitionId);
        //获取损耗节点信息和指标ID
        List<EnergyReportTrendResultDTO> lstObjectAndComplexIndexId = new ArrayList<>();
        SetLossObjectAndComplexIndexId(para, lstObjectAndComplexIndexId, complexIndexDefinitionId, complexIndexDefinitionTypeName);
        lstObjectAndComplexIndexId.forEach(dto->result.add(new EnergyReportMonthTrendResultDTO(dto)));

        //读取InfluxDB 查询能耗数据
        GetMonthConsumeTrend(result, para.getComplexIndexDefinitionTypeId(), para.getStartTime(), para.getEndTime(),1.0);
        //赋值总量字段
        result.forEach(dto->{
            if(para.getComplexIndexDefinitionTypeId().equals(2) || para.getComplexIndexDefinitionTypeId().equals(100))
                dto.SetAvgConsume();
            else
                dto.SetTotalConsume();
        });

        List<EnergyReportMonthTrendResultDTO> lossResult = GetLossMonthResult(result, resourceStructureId);
        return lossResult.stream().sorted(
                Comparator.comparing(EnergyReportMonthTrendResultDTO::getObjectTypeId)
                        .thenComparing(EnergyReportMonthTrendResultDTO::getObjectId)
                        .thenComparing(EnergyReportMonthTrendResultDTO::getComplexIndexDefinitionId)
        ).collect(Collectors.toList());
    }

    //赋值月损耗数据
    private List<EnergyReportMonthTrendResultDTO> GetLossMonthResult(List<EnergyReportMonthTrendResultDTO> result, String resourceStructureId) {
        if(result == null || result.isEmpty())
            return new ArrayList<>();
        try {
            if(result.size() == 1) {
                String objectName = result.get(0).getObjectName();
                result.get(0).setObjectName(objectName + "-损耗");
                return result;
            }
            Optional<EnergyReportMonthTrendResultDTO> targetDtoOptional = result.stream()
                    .filter(dto -> String.valueOf(dto.getObjectId()).equals(resourceStructureId))
                    .findFirst();
            if (targetDtoOptional.isPresent()) {
                EnergyReportMonthTrendResultDTO targetDto = targetDtoOptional.get();
                int targetIndex = result.indexOf(targetDto);
                Collections.swap(result, targetIndex, 0);
            }
            EnergyReportMonthTrendResultDTO firstDto = result.get(0);
            double[] sums = new double[32];
            for(int i = 1; i < result.size(); i++) {
                EnergyReportMonthTrendResultDTO dto = result.get(i);
                sums[0] += (dto.getD1() != null ? dto.getD1() : 0);
                sums[1] += (dto.getD2() != null ? dto.getD2() : 0);
                sums[2] += (dto.getD3() != null ? dto.getD3() : 0);
                sums[3] += (dto.getD4() != null ? dto.getD4() : 0);
                sums[4] += (dto.getD5() != null ? dto.getD5() : 0);
                sums[5] += (dto.getD6() != null ? dto.getD6() : 0);
                sums[6] += (dto.getD7() != null ? dto.getD7() : 0);
                sums[7] += (dto.getD8() != null ? dto.getD8() : 0);
                sums[8] += (dto.getD9() != null ? dto.getD9() : 0);
                sums[9] += (dto.getD10() != null ? dto.getD10() : 0);
                sums[10] += (dto.getD11() != null ? dto.getD11() : 0);
                sums[11] += (dto.getD12() != null ? dto.getD12() : 0);
                sums[12] += (dto.getD13() != null ? dto.getD13() : 0);
                sums[13] += (dto.getD14() != null ? dto.getD14() : 0);
                sums[14] += (dto.getD15() != null ? dto.getD15() : 0);
                sums[15] += (dto.getD16() != null ? dto.getD16() : 0);
                sums[16] += (dto.getD17() != null ? dto.getD17() : 0);
                sums[17] += (dto.getD18() != null ? dto.getD18() : 0);
                sums[18] += (dto.getD19() != null ? dto.getD19() : 0);
                sums[19] += (dto.getD20() != null ? dto.getD20() : 0);
                sums[20] += (dto.getD21() != null ? dto.getD21() : 0);
                sums[21] += (dto.getD22() != null ? dto.getD22() : 0);
                sums[22] += (dto.getD23() != null ? dto.getD23() : 0);
                sums[23] += (dto.getD24() != null ? dto.getD24() : 0);
                sums[24] += (dto.getD25() != null ? dto.getD25() : 0);
                sums[25] += (dto.getD26() != null ? dto.getD26() : 0);
                sums[26] += (dto.getD27() != null ? dto.getD27() : 0);
                sums[27] += (dto.getD28() != null ? dto.getD28() : 0);
                sums[28] += (dto.getD29() != null ? dto.getD29() : 0);
                sums[29] += (dto.getD30() != null ? dto.getD30() : 0);
                sums[30] += (dto.getD31() != null ? dto.getD31() : 0);
                sums[31] += (dto.getTotal() != null ? dto.getTotal() : 0);
            }
            firstDto.setD1(formateValue((firstDto.getD1() != null ? firstDto.getD1() : 0) - sums[0]));
            firstDto.setD2(formateValue((firstDto.getD2() != null ? firstDto.getD2() : 0) - sums[1]));
            firstDto.setD3(formateValue((firstDto.getD3() != null ? firstDto.getD3() : 0) - sums[2]));
            firstDto.setD4(formateValue((firstDto.getD4() != null ? firstDto.getD4() : 0) - sums[3]));
            firstDto.setD5(formateValue((firstDto.getD5() != null ? firstDto.getD5() : 0) - sums[4]));
            firstDto.setD6(formateValue((firstDto.getD6() != null ? firstDto.getD6() : 0) - sums[5]));
            firstDto.setD7(formateValue((firstDto.getD7() != null ? firstDto.getD7() : 0) - sums[6]));
            firstDto.setD8(formateValue((firstDto.getD8() != null ? firstDto.getD8() : 0) - sums[7]));
            firstDto.setD9(formateValue((firstDto.getD9() != null ? firstDto.getD9() : 0) - sums[8]));
            firstDto.setD10(formateValue((firstDto.getD10() != null ? firstDto.getD10() : 0) - sums[9]));
            firstDto.setD11(formateValue((firstDto.getD11() != null ? firstDto.getD11() : 0) - sums[10]));
            firstDto.setD12(formateValue((firstDto.getD12() != null ? firstDto.getD12() : 0) - sums[11]));
            firstDto.setD13(formateValue((firstDto.getD13() != null ? firstDto.getD13() : 0) - sums[12]));
            firstDto.setD14(formateValue((firstDto.getD14() != null ? firstDto.getD14() : 0) - sums[13]));
            firstDto.setD15(formateValue((firstDto.getD15() != null ? firstDto.getD15() : 0) - sums[14]));
            firstDto.setD16(formateValue((firstDto.getD16() != null ? firstDto.getD16() : 0) - sums[15]));
            firstDto.setD17(formateValue((firstDto.getD17() != null ? firstDto.getD17() : 0) - sums[16]));
            firstDto.setD18(formateValue((firstDto.getD18() != null ? firstDto.getD18() : 0) - sums[17]));
            firstDto.setD19(formateValue((firstDto.getD19() != null ? firstDto.getD19() : 0) - sums[18]));
            firstDto.setD20(formateValue((firstDto.getD20() != null ? firstDto.getD20() : 0) - sums[19]));
            firstDto.setD21(formateValue((firstDto.getD21() != null ? firstDto.getD21() : 0) - sums[20]));
            firstDto.setD22(formateValue((firstDto.getD22() != null ? firstDto.getD22() : 0) - sums[21]));
            firstDto.setD23(formateValue((firstDto.getD23() != null ? firstDto.getD23() : 0) - sums[22]));
            firstDto.setD24(formateValue((firstDto.getD24() != null ? firstDto.getD24() : 0) - sums[23]));
            firstDto.setD25(formateValue((firstDto.getD25() != null ? firstDto.getD25() : 0) - sums[24]));
            firstDto.setD26(formateValue((firstDto.getD26() != null ? firstDto.getD26() : 0) - sums[25]));
            firstDto.setD27(formateValue((firstDto.getD27() != null ? firstDto.getD27() : 0) - sums[26]));
            firstDto.setD28(formateValue((firstDto.getD28() != null ? firstDto.getD28() : 0) - sums[27]));
            firstDto.setD29(formateValue((firstDto.getD29() != null ? firstDto.getD29() : 0) - sums[28]));
            firstDto.setD30(formateValue((firstDto.getD30() != null ? firstDto.getD30() : 0) - sums[29]));
            firstDto.setD31(formateValue((firstDto.getD31() != null ? firstDto.getD31() : 0) - sums[30]));
            firstDto.setTotal(formateValue((firstDto.getTotal() != null ? firstDto.getTotal() : 0) - sums[31]));
            firstDto.setObjectName(firstDto.getObjectName() + "-损耗");
            return result;
        } catch (Exception e) {
            log.error("EnergyReportServiceImpl-GetLossResult error {}", e.getMessage());
            return new ArrayList<>();
        }
    }

    //损耗年趋势
    @Override
    public List<EnergyReportYearTrendResultDTO> EnergyLossYearTrendReport(EnergyReportParameterDTO para) {
        List<EnergyReportYearTrendResultDTO> result = new ArrayList<>();
        EnergyReportYearTrendResultDTO oneResult = new EnergyReportYearTrendResultDTO();

        Integer complexIndexDefinitionId = 0;
        String complexIndexDefinitionTypeName = "";

        String resourceStructureId = para.getResourceStructureIds();
        String definitionId = para.getComplexIndexDefinitionIds();
        complexIndexDefinitionId = NumberUtil.parseInt(definitionId);
        //获取查询能源类型对应的名称
        complexIndexDefinitionTypeName = GetComplexIndexDefinitionTypeName(complexIndexDefinitionId);

        //获取节点信息和指标ID
        List<EnergyReportTrendResultDTO> lstObjectAndComplexIndexId = new ArrayList<>();
        SetLossObjectAndComplexIndexId(para, lstObjectAndComplexIndexId, complexIndexDefinitionId, complexIndexDefinitionTypeName);
        lstObjectAndComplexIndexId.forEach(dto->result.add(new EnergyReportYearTrendResultDTO(dto)));

        //今后的数据不用查询
        Date tempStartTime = para.getStartTime();
        Date tempEndTime = DateUtil.getMonthEndTime(tempStartTime);
        for (int i=0;i<12;i++){
            if (tempStartTime.before(new Date())) {
                //读取InfluxDB 查询能耗数据
                GetYearConsumeTrend(result, para.getComplexIndexDefinitionTypeId(), tempStartTime, tempEndTime,1.0);
                tempStartTime = DateUtil.getNextMonthFirstDay(tempStartTime);
                tempEndTime = DateUtil.getMonthEndTime(tempStartTime);
            }
            else
                break;
        }
        //赋值总量字段
        result.forEach(dto->{
            if(para.getComplexIndexDefinitionTypeId().equals(2) || para.getComplexIndexDefinitionTypeId().equals(100))
                dto.SetAvgConsume();
            else
                dto.SetTotalConsume();
        });
        List<EnergyReportYearTrendResultDTO> lossResult = GetLossYearResult(result, resourceStructureId);
        return lossResult.stream().sorted(
                Comparator.comparing(EnergyReportYearTrendResultDTO::getObjectTypeId)
                        .thenComparing(EnergyReportYearTrendResultDTO::getObjectId)
                        .thenComparing(EnergyReportYearTrendResultDTO::getComplexIndexDefinitionId)
        ).collect(Collectors.toList());
    }

    //赋值年损耗数据
    private List<EnergyReportYearTrendResultDTO> GetLossYearResult(List<EnergyReportYearTrendResultDTO> result, String resourceStructureId) {
        if(result == null || result.isEmpty())
            return new ArrayList<>();
        try {
            if(result.size() == 1) {
                String objectName = result.get(0).getObjectName();
                result.get(0).setObjectName(objectName + "-损耗");
                return result;
            }
            Optional<EnergyReportYearTrendResultDTO> targetDtoOptional = result.stream()
                    .filter(dto -> String.valueOf(dto.getObjectId()).equals(resourceStructureId))
                    .findFirst();
            if (targetDtoOptional.isPresent()) {
                EnergyReportYearTrendResultDTO targetDto = targetDtoOptional.get();
                int targetIndex = result.indexOf(targetDto);
                Collections.swap(result, targetIndex, 0);
            }
            EnergyReportYearTrendResultDTO firstDto = result.get(0);
            double[] sums = new double[13];
            for(int i = 1; i < result.size(); i++) {
                EnergyReportYearTrendResultDTO dto = result.get(i);
                sums[0] += (dto.getM1() != null ? dto.getM1() : 0);
                sums[1] += (dto.getM2() != null ? dto.getM2() : 0);
                sums[2] += (dto.getM3() != null ? dto.getM3() : 0);
                sums[3] += (dto.getM4() != null ? dto.getM4() : 0);
                sums[4] += (dto.getM5() != null ? dto.getM5() : 0);
                sums[5] += (dto.getM6() != null ? dto.getM6() : 0);
                sums[6] += (dto.getM7() != null ? dto.getM7() : 0);
                sums[7] += (dto.getM8() != null ? dto.getM8() : 0);
                sums[8] += (dto.getM9() != null ? dto.getM9() : 0);
                sums[9] += (dto.getM10() != null ? dto.getM10() : 0);
                sums[10] += (dto.getM11() != null ? dto.getM11() : 0);
                sums[11] += (dto.getM12() != null ? dto.getM12() : 0);
                sums[12] += (dto.getTotal() != null ? dto.getTotal() : 0);
            }
            firstDto.setM1(formateValue((firstDto.getM1() != null ? firstDto.getM1() : 0) - sums[0]));
            firstDto.setM2(formateValue((firstDto.getM2() != null ? firstDto.getM2() : 0) - sums[1]));
            firstDto.setM3(formateValue((firstDto.getM3() != null ? firstDto.getM3() : 0) - sums[2]));
            firstDto.setM4(formateValue((firstDto.getM4() != null ? firstDto.getM4() : 0) - sums[3]));
            firstDto.setM5(formateValue((firstDto.getM5() != null ? firstDto.getM5() : 0) - sums[4]));
            firstDto.setM6(formateValue((firstDto.getM6() != null ? firstDto.getM6() : 0) - sums[5]));
            firstDto.setM7(formateValue((firstDto.getM7() != null ? firstDto.getM7() : 0) - sums[6]));
            firstDto.setM8(formateValue((firstDto.getM8() != null ? firstDto.getM8() : 0) - sums[7]));
            firstDto.setM9(formateValue((firstDto.getM9() != null ? firstDto.getM9() : 0) - sums[8]));
            firstDto.setM10(formateValue((firstDto.getM10() != null ? firstDto.getM10() : 0) - sums[9]));
            firstDto.setM11(formateValue((firstDto.getM11() != null ? firstDto.getM11() : 0) - sums[10]));
            firstDto.setM12(formateValue((firstDto.getM12() != null ? firstDto.getM12() : 0) - sums[11]));
            firstDto.setTotal(formateValue((firstDto.getTotal() != null ? firstDto.getTotal() : 0) - sums[12]));
            firstDto.setObjectName(firstDto.getObjectName() + "-损耗");
            return result;
        } catch (Exception e) {
            log.error("EnergyReportServiceImpl-GetLossYearResult error {}", e.getMessage());
            return new ArrayList<>();
        }
    }
}
