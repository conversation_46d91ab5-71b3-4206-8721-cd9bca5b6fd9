package com.siteweb.energy.service.impl;

import com.siteweb.complexindex.entity.HistoryComplexIndex;
import com.siteweb.complexindex.manager.HistoryComplexIndexManager;
import com.siteweb.complexindex.mapper.ComplexIndexMapper;
import com.siteweb.energy.dto.EnergyAirConditionDataDTO;
import com.siteweb.energy.dto.EnergyLoadRateTempDTO;
import com.siteweb.energy.dto.EnergyTempInfo;
import com.siteweb.energy.service.EnergyAirConditionInfoService;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.mapper.EquipmentMapper;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BoundParameterQuery;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Service
@Slf4j
public class EnergyAirConditionInfoServiceImpl implements EnergyAirConditionInfoService {
    @Autowired
    private EquipmentMapper equipmentMapper;

    @Autowired
    HistoryComplexIndexManager historyComplexIndexManager;

    @Override
    public List<Equipment> getAirConditionlist() {
        return equipmentMapper.getAirConditionlist();
    }

    @Override
    public List<EnergyAirConditionDataDTO> getAirConditionData(Date startTime, Date endTime, String equipmentId) {
        List<EnergyAirConditionDataDTO> resultList = new ArrayList<>();
        try {
            String modeAndAirPowerIndex = getAirOperateModeAndPower(equipmentId);
            if (!modeAndAirPowerIndex.isEmpty()) {
                String[] Indexs = modeAndAirPowerIndex.split(",");
                int modeIndex = Integer.parseInt(Indexs[0]);
                int airPowerIndex = Integer.parseInt(Indexs[1]);
                int[] ids = { modeIndex, 42, 30, airPowerIndex, 47, 40, 39, 38, 37 };
                List<Integer> complexIndexIds = new ArrayList<>(ids.length);
                for (int value : ids) {
                    complexIndexIds.add(value);
                }
                Map<Integer, List<HistoryComplexIndex>> complexList = historyComplexIndexManager.findHistoryComplexIndexsByIdAndDuration(startTime, endTime, complexIndexIds, null);
                if (complexList != null && !complexList.isEmpty()) {
                    List<HistoryComplexIndex> modeList = complexList.get(modeIndex);
                    if (modeList.isEmpty()) return null;
                    Map<String, String> modeChangeMap = getModeChangeListMap(modeList);
                    if (modeChangeMap != null && !modeChangeMap.isEmpty()) {
                        for (int i = 0; i < modeChangeMap.size(); i++) {
                            String currentKey = getKeyAtIndex(modeChangeMap, i);
                            String currentValue = modeChangeMap.get(currentKey);
                            Date currentTime = parseDateString(currentKey);

                            EnergyAirConditionDataDTO result = new EnergyAirConditionDataDTO();
                            result.setOperateMode(currentValue);
                            result.setOperateDateTime(currentKey);

                            Date nextTime = null;
                            if(i+1 < modeChangeMap.size()) {
                                String nextKey = getKeyAtIndex(modeChangeMap, i+1);
                                nextTime = parseDateString(nextKey);
                            } else {
                                nextTime = endTime;
                            }
                            String dryTemperature = getdryTempRange(currentTime, nextTime, complexList.get(42));
                            String totalITPower = getAirOtherData(currentTime, nextTime, complexList.get(30), 0);
                            String airPower = getAirOtherData(currentTime, nextTime, complexList.get(airPowerIndex), 0);
                            String totalDisLosses = getAirOtherData(currentTime, nextTime, complexList.get(47), 1);
                            String totalOtherPower = getAirOtherData(currentTime, nextTime, complexList.get(40), 0);
                            String clf = getAirOtherData(currentTime, nextTime, complexList.get(39), 0);
                            String plf = getAirOtherData(currentTime, nextTime, complexList.get(38), 0);
                            String pue = getAirOtherData(currentTime, nextTime, complexList.get(37), 0);
                            String clfPlfPue = clf + "/" + plf + "/" + pue;
                            result.setDryTemperatureRange(dryTemperature);
                            result.setTotalITPower(totalITPower);
                            result.setAirConditioningPower(airPower);
                            result.setTotalDistributionLosses(totalDisLosses);
                            result.setTotalOtherPower(totalOtherPower);
                            result.setClfPlfPue(clfPlfPue);
                            if (currentTime != null && nextTime != null) {
                                long durationMillis = nextTime.getTime() - currentTime.getTime();
                                double durationHours = durationMillis / (1000.0 * 60.0 * 60.0);
                                String totalOperateTime = String.format("%.2f", durationHours);
                                result.setTotalOperateTime(totalOperateTime);
                            }
                            resultList.add(result);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            log.error("空调运行模式-功率及PUE查询getAirConditionData；", ex);
            return null;
        }
        return resultList;
    }

    // 获取指定索引位置的键
    private static <K, V> K getKeyAtIndex(Map<K, V> map, int index) {
        int i = 0;
        for (K key : map.keySet()) {
            if (i == index) {
                return key;
            }
            i++;
        }
        return null;
    }

    // 解析 String 类型的时间字符串为 Date 对象
    private static Date parseDateString(String dateString) {
        try {
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); // 这里假设时间字符串的格式为 "yyyy-MM-dd HH:mm:ss"，你需要根据实际情况修改
            return dateFormat.parse(dateString);
        } catch (ParseException ex) {
            log.error("空调运行模式-功率及PUE查询parseDateString；", ex);
            return null;
        }
    }

    private String getAirOperateModeAndPower(String equipmentId) {
        String mode = "";
        try {
            Map<String, String> map = new HashMap<>();
            map.put("474000087", "43,48");
            map.put("474000089", "44,49");
            map.put("474000085", "45,50");
            map.put("474000114", "46,51");
            if (map.containsKey(equipmentId)) {
                mode = map.get(equipmentId);
            }
        } catch (Exception ex) {
            log.error("空调运行模式-功率及PUE查询getAirOperateMode；", ex);
            return "";
        }
        return mode;
    }

    //返回key为运行模式变化的时间，value为空调运行模式
    @Nullable
    private Map<String, String> getModeChangeListMap(List<HistoryComplexIndex> modeList) {
        TreeMap<String, String> modeChangeMap = new TreeMap<>();
        try {
            String currentModeValue = null;
            for (HistoryComplexIndex item: modeList) {
                String modeValue = item.getIndexValue();
                String timeValue = item.getTime();
                if (!modeValue.equals(currentModeValue)) {
                    currentModeValue = modeValue;
                    String modeName = "";
                    if (!modeValue.isEmpty()) {
                        double value = Double.parseDouble(modeValue);
                        int indexValue = (int)value;
                        modeName = switch (indexValue) {
                            case 1 -> "自然冷却模式";
                            case 2 -> "压缩机模式";
                            case 3 -> "混合模式";
                            default -> "";
                        };
                    }
                    modeChangeMap.put(timeValue, modeName);
                }
            }
        } catch (Exception ex) {
            log.error("空调运行模式-功率及PUE查询getModeChangeListMap；", ex);
            return null;
        }
        return modeChangeMap;
    }

    //获取平均值或者总和 flag为0是平均，flag为1是总和
    private String getAirOtherData(Date startTime, Date endTime, List<HistoryComplexIndex> complexList, int flag) {
        String result = "0.00";
        try {
            List<HistoryComplexIndex> filteredList = getComplexTimeRange(startTime, endTime, complexList);
            if (filteredList == null || filteredList.isEmpty()) return "0.00";
            double sum = 0.00;
            for (HistoryComplexIndex item: filteredList) {
                sum += Double.parseDouble(item.getIndexValue());
            }
            if(flag == 0) {
                double average = sum/ filteredList.size();
                result = String.format("%.2f", average);
            } else {
                result = String.format("%.2f", sum);
            }
            return result;
        } catch (Exception ex) {
            log.error("空调运行模式-功率及PUE查询getAirOtherData；", ex);
            return "0.00";
        }
    }

    // 获取干球温度区间
    public String getdryTempRange(Date startTime, Date endTime, List<HistoryComplexIndex> complexList) {
        String range = "";
        try {
            List<HistoryComplexIndex> filteredList = getComplexTimeRange(startTime, endTime,complexList);
            if (filteredList == null || filteredList.isEmpty()) return "";
            // 找到最大值
            String maxValue = "";
            Optional<HistoryComplexIndex> max = filteredList.stream()
                    .max(Comparator.comparing(HistoryComplexIndex::getIndexValue));
            if (max.isPresent()){
                HistoryComplexIndex maxIndex = max.get();
                maxValue = maxIndex.getIndexValue();
            }
            // 找到最小值
            String minValue = "";
            Optional<HistoryComplexIndex> min = filteredList.stream()
                    .min(Comparator.comparing(HistoryComplexIndex::getIndexValue));
            if (min.isPresent()) {
                HistoryComplexIndex minIndex = min.get();
                minValue = minIndex.getIndexValue();
            }
            range = "[" + minValue + "," + maxValue + "]";
        } catch (Exception ex) {
            log.error("空调运行模式-功率及PUE查询getdryTempRange；", ex);
            return "";
        }
        return range;
    }

    @Override
    public List<EnergyLoadRateTempDTO> getLoadRateTempData(Date startTime, Date endTime, String loadranges, String tdranges) {
        List<EnergyLoadRateTempDTO> resultList = new ArrayList<>();
        try {
            int[] ids = { 41, 42, 39, 38, 37 };
            List<Integer> complexIndexIds = new ArrayList<>(ids.length);
            for (int value : ids) {
                complexIndexIds.add(value);
            }
            Map<Integer, List<HistoryComplexIndex>> complexList = historyComplexIndexManager.findHistoryComplexIndexsByIdAndDuration(startTime, endTime, complexIndexIds, null);
            if (complexList != null && !complexList.isEmpty()) {
                List<HistoryComplexIndex> loadComplexList = complexList.get(41);
                if (loadComplexList == null || loadComplexList.isEmpty()) return null;

                String[] loadRangeArray = loadranges.split(",");
                String[] tdRangeArray = tdranges.split(",");
                for (String loadRange: loadRangeArray) {
//                    EnergyLoadRateTempDTO result = new EnergyLoadRateTempDTO();
//                    result.setLoadRate(loadRange);

                    List<String> timeList = getTimeListByValueRange(loadRange, loadComplexList);
                    if (timeList != null && !timeList.isEmpty()) {
                        Map<String, List<String>> tempTimeMap = new HashMap<>();
                        for (String rangeTime: timeList) {
                            String[] timeRanges = rangeTime.split(",");
                            Date minTime = parseDateString(timeRanges[0]);
                            Date maxTime = parseDateString(timeRanges[1]);
                            List<HistoryComplexIndex> tempComplexList = getComplexTimeRange(minTime, maxTime, complexList.get(42));
                            for (String tdRange: tdRangeArray) {
                                String tdRangeStr = convertRangeString(tdRange);
                                List<String> tempTimeList = getTimeListByValueRange(tdRangeStr, tempComplexList);
                                if (tempTimeList != null && !tempTimeList.isEmpty()) {
                                    if (tempTimeMap.containsKey(tdRange)) {
                                        tempTimeMap.get(tdRange).addAll(tempTimeList);
                                    } else {
                                        tempTimeMap.put(tdRange, tempTimeList);
                                    }
                                }
                            }
                        }
                        if (!tempTimeMap.isEmpty()) {
                            EnergyLoadRateTempDTO result = new EnergyLoadRateTempDTO();
                            result.setLoadRate(loadRange);
                            List<EnergyTempInfo> tempInfoList = new ArrayList<>();
                            for (Map.Entry<String, List<String>> entry: tempTimeMap.entrySet()) {
                                String key = entry.getKey(); //温度
                                List<String> valueList = entry.getValue(); // 获取值，即列表
                                String clfValue = getClfPlfPue(valueList, complexList.get(39));
                                String plfValue = getClfPlfPue(valueList, complexList.get(38));
                                String pueValue = getClfPlfPue(valueList, complexList.get(37));

                                EnergyTempInfo tempInfo = new EnergyTempInfo();
                                tempInfo.setTempature(key);
                                tempInfo.setClfValue(clfValue);
                                tempInfo.setPlfValue(plfValue);
                                tempInfo.setPueValue(pueValue);
                                tempInfoList.add(tempInfo);
                            }
                            result.setTempInfo(tempInfoList);
                            resultList.add(result);
                        }
                    }
//                    resultList.add(result);
                }
            }
        } catch (Exception ex) {
            log.error(" IT设备负载率查询getLoadRateTempData；", ex);
            return null;
        }
        return resultList;
    }

    private String convertRangeString(String range) {
        try {
            String[] parts = range.split("[^0-9.]+");

            // 提取有效的数字部分
            String lowerBound = parts[0];
            String upperBound = parts[1];
//            double lowerBound = Double.parseDouble(parts[0]);
//            double upperBound = Double.parseDouble(parts[1]);

            return lowerBound + "-" + upperBound;
        } catch (Exception ex) {
            log.error(" IT设备负载率查询convertRangeString；", ex);
            return "";
        }
    }

    //找出符合多个时间范围段内的指标列表
    private String getClfPlfPue(List<String> valueList, List<HistoryComplexIndex> complexList) {
        String resultValue = "0.00";
        List<HistoryComplexIndex> resultList = new ArrayList<>();
        try {
            for (String time: valueList) {
                String[] timeRanges = time.split(",");
                Date minTime = parseDateString(timeRanges[0]);
                Date maxTime = parseDateString(timeRanges[1]);
                List<HistoryComplexIndex> filterList = getComplexTimeRange(minTime, maxTime, complexList);
                if (filterList != null) {
                    resultList.addAll(filterList);
                }
            }
            if (resultList.isEmpty()) return "0.00";
            double sum = 0.00;
            for (HistoryComplexIndex item: resultList) {
                sum += Double.parseDouble(item.getIndexValue());
            }
            double average = sum / resultList.size();
            resultValue = String.format("%.2f", average);
        } catch (Exception ex) {
            log.error(" IT设备负载率查询getClfPlfPue；", ex);
            return "0.00";
        }
        return resultValue;
    }


    //根据值是否在范围内，取出各个范围值对应的时间范围列表
    private List<String> getTimeListByValueRange(String valueRange, List<HistoryComplexIndex> complexList) {
        List<String> timeList = new ArrayList<>();
        List<List<HistoryComplexIndex>> result = new ArrayList<>();
        List<HistoryComplexIndex> sublist = new ArrayList<>();
        try {
            String[] loads = valueRange.split("-");
            double minLoad = Double.parseDouble(loads[0]);
            double maxLoad = Double.parseDouble(loads[1]);
            for (HistoryComplexIndex item: complexList) {
                double value = Double.parseDouble(item.getIndexValue());
                if (value >= minLoad && value < maxLoad) {
                    sublist.add(item);
                } else {
                    if (!sublist.isEmpty()) {
                        result.add(sublist);
                        sublist = new ArrayList<>();
                    }
                }
            }
            if (!sublist.isEmpty()) {
                result.add(sublist);
            }

            if (!result.isEmpty()) {
                for (List<HistoryComplexIndex> item: result) {
                    String minTime = item.get(0).getTime();
                    String maxTime = item.get(item.size()-1).getTime();
                    timeList.add(minTime + "," + maxTime);
                }
            }

        } catch (Exception ex) {
            log.error(" IT设备负载率查询getTimeByValueRange；", ex);
            return null;
        }
        return timeList;
    }

    //获取时间范围内的指标列表
    private List<HistoryComplexIndex> getComplexTimeRange(Date minTime, Date maxTime, List<HistoryComplexIndex> complexList) {
        List<HistoryComplexIndex> resultList = new ArrayList<>();
        try {
            if (complexList == null || complexList.isEmpty()) return null;
            for (HistoryComplexIndex index: complexList) {
                Date indexTime = parseDateString(index.getTime());
                if (indexTime != null && indexTime.compareTo(minTime) >= 0 && indexTime.compareTo(maxTime) <= 0) {
                    resultList.add(index);
                }
            }
        } catch (Exception ex) {
            log.error("getComplexTimeRange；", ex);
            return null;
        }
        return resultList;
    }
}
