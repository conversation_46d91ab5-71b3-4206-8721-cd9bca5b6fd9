package com.siteweb.energy.service.impl;

import cn.hutool.core.math.Calculator;
import com.siteweb.common.influxdb.InfluxDBManager;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.NumberUtil;
import com.siteweb.complexindex.entity.BusinessDefinitionMap;
import com.siteweb.complexindex.entity.ComplexIndex;
import com.siteweb.complexindex.entity.ComplexIndexBusinessType;
import com.siteweb.complexindex.entity.HistoryComplexIndex;
import com.siteweb.complexindex.service.ComplexIndexBusinessTypeService;
import com.siteweb.energy.entity.*;
import com.siteweb.energy.manager.EnergyComplexIndexManager;
import com.siteweb.energy.mapper.EnergyCustomerConfigMapper;
import com.siteweb.energy.service.EnergyComplexPreAlarmService;
import com.siteweb.energy.service.EnergyDataConfigItemService;
import com.siteweb.energy.service.EnergyHistoryDataHBDXReCalcService;
import com.siteweb.energy.service.EnergyOverViewService;
import lombok.extern.slf4j.Slf4j;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BoundParameterQuery;
import org.influxdb.dto.Point;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.influxdb.impl.InfluxDBResultMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.siteweb.common.util.DateUtil.dateToString;

@Service
@Slf4j
public class EnergyHistoryDataHBDXReCalcServiceImpl implements EnergyHistoryDataHBDXReCalcService {
    @Value("${spring.influx.database}")
    private String database;
    @Value("${spring.influx.database3}")
    private String databaseEnergy;

    @Autowired
    private InfluxDB influxDB;
    @Autowired
    private InfluxDBManager influxDBManager;
    @Autowired
    private EnergyComplexIndexManager energyComplexIndexManager;
    @Autowired
    private ComplexIndexBusinessTypeService complexIndexBusinessTypeService;
    @Autowired
    private EnergyOverViewService energyOverViewService;
    @Autowired
    private EnergyCustomerConfigMapper energyCustomerConfigMapper;
    @Autowired
    private EnergyDataConfigItemService energyDataConfigItemService;
    @Autowired
    @Lazy
    private EnergyComplexPreAlarmService energyComplexPreAlarmService;

    //所有能耗指标
    private Map<String, ComplexIndex> lstEnergyComplexIndex;
    private List<BusinessDefinitionMap> allBusinessDefinitionMap;

    private Map<String, EnergyHisHourData> lstEnergyHisHourData = new HashMap<>();
    private Map<String, EnergyHisHourData> lstEnergyHisHourData_Sum = new HashMap<>();
    private Map<String, EnergyHisHourData> lstEnergyHisHourData_Efficiency = new HashMap<>();
    private Map<String, EnergyHisHourData> lstEnergyHisHourData_Other = new HashMap<>();
    // 河北电信上一小时能耗数据
    private Map<String, EnergyHisHourData> lstLastEnergyHisHourData = new HashMap<>();
    // 日数据队列
    private Map<String, EnergyHisDayData> lstEnergyHisDayData = new HashMap<>();
    // 月数据队列
    private Map<String, EnergyHisMonthData> lstEnergyHisMonthData = new HashMap<>();
    // UE数据队列
    private Map<String, EnergyHisMonthUEData> lstEnergyHisMonthUEData = new HashMap<>();
    //能耗数据库小时表
    private static final String EnergyHisHourDataTable = "EnergyHisHourData";
    //能耗数据库天表
    private static final String EnergyHisDayDataTable = "EnergyHisDayData";
    //能耗数据库月表
    private static final String EnergyHisMonthDataTable = "EnergyHisMonthData";
    private double maxElecHour = 8000d;    //指标每小时值为8000 现场认为是异常数据。
    private int influxdbBatchSize = 8000;    //小时表，日表，月表存储时每次写多少个指标数据
    private int influxdbDataSaveBeginTable = 2;  //能耗存储的最小颗粒度表1小时表，2日表，3月表
    private int hourThreadSleep = 1000;  // 每小时完后休息毫秒
    private Date dayStartTime, monthStartTime;

    List<Point> lstDayPoint = new ArrayList<>();
    List<Point> lstMonthPoint = new ArrayList<>();
    List<Point> lstMonthUEPoint = new ArrayList<>();

    @Override
    public Object energyHistoryDataHBDXService(Date startTime, Date endTime) {
        //取每小时的异常值
        try {
            EnergyCustomerConfig result = energyCustomerConfigMapper.selectById(2);
            if (result == null || result.getNotes() == null || result.getNotes().equals("")) {
                maxElecHour = 8000d;
            } else {
                String[] paras = result.getNotes().split(",");          //现场notes字段8000,8000,1,1,2000
                maxElecHour = Double.parseDouble(paras[0]);
                if (paras.length > 1) {
                    influxdbBatchSize = Integer.parseInt(paras[1]);
                }
                if (paras.length > 3) {
                    influxdbDataSaveBeginTable = Integer.parseInt(paras[3]);
                }
                if (paras.length > 4) {
                    hourThreadSleep = Integer.parseInt(paras[4]);
                }
            }
        } catch (Exception ex) {
            log.error("小时值配置异常：energy_customer_config-id2:", ex);
        }

        //所有能源类型
        List<ComplexIndexBusinessType> lstEnergyComplexIndexBusinessType = complexIndexBusinessTypeService.GetComplexIndexBusinessTypeByParentid(1);
        //所有能耗指标
        lstEnergyComplexIndex = energyComplexIndexManager.GetAllComplexIndex()
                .stream()
                .filter(i -> lstEnergyComplexIndexBusinessType
                        .stream()
                        .anyMatch(item -> item.getBusinessTypeId().equals(i.getBusinessTypeId())))
                .collect(Collectors.toMap(
                        i -> String.valueOf(i.getComplexIndexId()),// 使用 ComplexIndex 的 ID 作为键
                        i -> i // 使用 ComplexIndex 本身作为值
                ));

        //没有能耗指标则退出
        if (lstEnergyComplexIndex.size() == 0) {
            log.info("能耗数据转存，能耗指标数量为空");
        }
        //能源类型与指标类型的关系
        allBusinessDefinitionMap = energyComplexIndexManager.GetAllBusinessDefinitionMap();

        while (startTime.before(endTime)) {
            lstEnergyHisHourData_Sum = new HashMap<>();
            lstEnergyHisHourData_Other = new HashMap<>();
            lstEnergyHisHourData_Efficiency = new HashMap<>();

            // 判断是否新的一天，需要读取日月数据
            LocalDateTime localDateTime = startTime.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            int hour = localDateTime.getHour(); // 获取小时数（24小时制）
            int day = localDateTime.getDayOfMonth();

            if (lstLastEnergyHisHourData.size() == 0)
                setLstLastEnergyHisHourData(startTime);

            if (hour == 0) { //凌晨
                lstEnergyHisDayData = new HashMap<>();
                if (day == 1) {  //月初的凌晨
                    lstEnergyHisMonthData = new HashMap<>();
                    lstEnergyHisMonthUEData = new HashMap<>();
                }
            } else {    //其他时间的程序重启
                if (lstEnergyHisDayData.size() == 0)
                    setLstEnergyHisDayData(startTime);
                if (lstEnergyHisMonthData.size() == 0)
                    setLstEnergyHisMonthData(startTime);
                if (lstEnergyHisMonthUEData.size() == 0)
                    setLstEnergyHisMonthUEData(startTime);
            }

            log.info("开始1小时能耗数据转存time=" + dateToString(startTime));
            //某1小时的数据转存
            processStatisticEnergyHistoryData(startTime,endTime);
            log.error("1这不是报错！完成1小时能耗数据转存time=" + dateToString(startTime));
            //开始时间增加1小时
            startTime = DateUtil.dateAddHours(startTime, 1);

            log.info("日志：本小时转移指标数量lstThisHourHistoryComplexIndex长度=" + lstEnergyHisHourData.size());
            lstLastEnergyHisHourData.clear();
            lstLastEnergyHisHourData.putAll(lstEnergyHisHourData);
            lstEnergyHisHourData.clear();
            try {
                Thread.sleep(hourThreadSleep);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

        lstEnergyComplexIndex.clear();
        lstEnergyHisHourData.clear();
        lstEnergyHisHourData_Sum.clear();
        lstEnergyHisHourData_Efficiency.clear();
        lstEnergyHisHourData_Other.clear();
        lstLastEnergyHisHourData.clear();
        lstEnergyHisDayData.clear();
        lstEnergyHisMonthData.clear();
        lstEnergyHisMonthUEData.clear();
        lstDayPoint.clear();
        lstMonthPoint.clear();
        lstMonthUEPoint.clear();
        return "成功";
    }

    // 可能程序重启，重新读取本日和本月的能耗数据
    private void setLstEnergyHisDayData(Date startTime) {

        log.info("** 开始读取能耗日数据"+ dateToString(startTime));
        long dTime1 = System.nanoTime();

        lstEnergyHisDayData.clear();
        Date dayTime = DateUtil.dateFirstTimeFormat(startTime);
        //Date monthTime = DateUtil.getMonthStartTime(startTime);
        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
        //取日数据
        String selectDuration = "select * from EnergyHisDayData where time =$startTime ";
        try {
            Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                    .forDatabase(databaseEnergy)
                    .bind("startTime", dateToString(dayTime))
                    .create();
            QueryResult query = influxDB.query(queryBuilder);
            if (query != null) {
                List<EnergyHisDayData> resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisDayData.class);
                if (!resultComplexIndexQuery.isEmpty()) {
                    lstEnergyHisDayData = resultComplexIndexQuery.stream().collect(Collectors.toMap(
                            EnergyHisDayData::getComplexIndexId,
                            i -> i // 使用 ComplexIndex 本身作为值
                    ));
                    log.info("读取日表指标数据数量："+lstEnergyHisDayData.size());
                } else {
                    lstEnergyHisDayData = new HashMap<>();
                    log.info("读取日表指标数据，列表为空");
                }
            } else {
                log.error("读取日表指标数据，query=null");
            }
        } catch (Exception e) {
            log.error("EnergyHistoryDataHBDXServiceImpl-setLstEnergyHisDayMonthData error {}", e);
        }

        long dTime2 = System.nanoTime();
        log.info("** 读取能耗日数据完成耗时：" + (dTime2 - dTime1) / 1_000_000.0 + " 毫秒");
    }

    private void setLstEnergyHisMonthData(Date startTime) {
        log.info("** 开始读取能耗月数据"+ dateToString(startTime));
        long dTime1 = System.nanoTime();

        lstEnergyHisMonthData.clear();
        Date monthTime = DateUtil.getMonthStartTime(startTime);
        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
        // 取月数据
        String selectDuration = "select * from EnergyHisMonthData where time =$startTime ";
        try {
            Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                    .forDatabase(databaseEnergy)
                    .bind("startTime", dateToString(monthTime))
                    .create();
            QueryResult query = influxDB.query(queryBuilder);
            if (query != null) {
                List<EnergyHisMonthData> resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisMonthData.class );
                if (!resultComplexIndexQuery.isEmpty()) {
                    lstEnergyHisMonthData = resultComplexIndexQuery.stream().collect(Collectors.toMap(
                            EnergyHisMonthData::getComplexIndexId,
                            i -> i // 使用 ComplexIndex 本身作为值
                    ));
                    log.info("读取月表指标数据数量："+lstEnergyHisMonthData.size());
                } else {
                    lstEnergyHisMonthData = new HashMap<>();
                    log.info("读取当月指标数据，列表为空");
                }
            } else {
                log.error("读取当月指标数据，query=null");
            }
            long dTime2 = System.nanoTime();
            log.info("** 读取能耗月数据完成耗时：" + (dTime2 - dTime1) / 1_000_000.0 + " 毫秒");
        } catch (Exception e) {
            log.error("EnergyHistoryDataHBDXServiceImpl-setLstEnergyHisDayMonthData error {}", e);
        }
    }

    private void setLstEnergyHisMonthUEData(Date startTime) {
        log.info("** 开始读取能耗月UE数据"+ dateToString(startTime));
        long dTime1 = System.nanoTime();
        Date monthTime = DateUtil.getMonthStartTime(startTime);
        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();

        lstEnergyHisMonthUEData.clear();
        String selectDuration = "select * from EnergyHisMonthUEData where time =$startTime";
        try {
            Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                    .forDatabase(databaseEnergy)
                    .bind("startTime", dateToString(monthTime))
                    .create();
            QueryResult query = influxDB.query(queryBuilder);
            if (query != null) {
                List<EnergyHisMonthUEData> resultComplexIndexQuery = resultMapper.toPOJO(query, EnergyHisMonthUEData.class);
                if (!resultComplexIndexQuery.isEmpty()) {
                    lstEnergyHisMonthUEData = resultComplexIndexQuery.stream().collect(Collectors.toMap(
                            EnergyHisMonthUEData::getComplexIndexId,
                            i -> i // 使用 ComplexIndex 本身作为值
                    ));
                    log.info("读取月UE表指标数据数量："+lstEnergyHisMonthUEData.size());
                } else {
                    lstEnergyHisMonthUEData = new HashMap<>();
                    log.info("读取当月UE指标数据，列表为空");
                }
            } else {
                log.error("读取当月UE指标数据，query=null");
            }
            long dTime2 = System.nanoTime();
            log.info("** 读取能耗月UE数据完成耗时：" + (dTime2 - dTime1) / 1_000_000.0 + " 毫秒");
        } catch (Exception e) {
            log.error("EnergyHistoryDataHBDXServiceImpl-setLstEnergyHisDayMonthUEData error {}", e);
        }
    }

    private void setLstLastEnergyHisHourData(Date thisHourTime) {
        // 前一小时
        Date startTime = DateUtil.dateAddHours(thisHourTime, -1);
        Date endTime = DateUtil.dateAddSeconds(thisHourTime, -1);
        log.info("** 开始读取前一小时指标历史数据"+ dateToString(startTime));
        long dTime1 = System.nanoTime();

        lstLastEnergyHisHourData.clear();

        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
        String selectDuration = "select * from HistoryComplexIndex where time >=$startTime and time <= $endTime ";
        Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                .forDatabase(database)
                .bind("startTime", dateToString(startTime))
                .bind("endTime", dateToString(endTime))
                .create();
        QueryResult query = influxDB.query(queryBuilder);

        if (query != null) {
            List<HistoryComplexIndex> resultComplexIndexQuery = resultMapper.toPOJO(query, HistoryComplexIndex.class);
            if (!resultComplexIndexQuery.isEmpty()) {
                for (HistoryComplexIndex temp : resultComplexIndexQuery) {
                    ComplexIndex thisIndex = lstEnergyComplexIndex.getOrDefault(temp.getComplexIndexId(), null);

                    if (thisIndex != null && !lstLastEnergyHisHourData.containsKey(temp.getComplexIndexId())) {
                        EnergyHisHourData hourData = new EnergyHisHourData();
                        hourData.setAbnormal(temp.getAbnormal());
                        hourData.setTime(temp.getTime());
                        //hourData.setCalcTime(temp.getCalcTime());
                        hourData.setIndexValue(temp.getIndexValue());
                        hourData.setOriginalValue(temp.getIndexValue());
                        hourData.setComplexIndexId(temp.getComplexIndexId());
                        hourData.setUnit(temp.getUnit());
                        hourData.setBusinessTypeId(temp.getBusinessTypeId());
                        lstLastEnergyHisHourData.put(temp.getComplexIndexId(), hourData);
                    }
                }

                log.info("读取前一小时指标历史数据数量："+resultComplexIndexQuery.size());
            }
        }
        long dTime2 = System.nanoTime();
        log.info("** 读取前一小时指标历史数据完成耗时：" + (dTime2 - dTime1) / 1_000_000.0 + " 毫秒");
    }

    //某1小时的转存过程
    private void processStatisticEnergyHistoryData(Date startTime,Date endTime) {
        dayStartTime = DateUtil.dateFirstTimeFormat(startTime);
        monthStartTime = DateUtil.getMonthStartTime(startTime);
        lstDayPoint.clear();
        lstMonthPoint.clear();
        lstMonthUEPoint.clear();

        //处理能耗小时数据
        log.info("**开始处理能耗小时数据 time=" + dateToString(startTime));
        processEnergyHisHourData(startTime);
        log.info("**结束处理能耗小时数据 time=" + dateToString(startTime));

        //处理总量类指标日月
        log.info("**开始处理总量日月 time=" + dateToString(startTime));
        processSumEnergyHistoryData();
        log.info("**结束处理总量日月 time=" + dateToString(startTime));

        //处理其他类指标日月
        log.info("**开始处理其他类指标日月 time=" + dateToString(startTime));
        processOtherEnergyHistoryData(startTime);
        log.info("**结束处理其他类指标日月 time=" + dateToString(startTime));

        //处理其他类指标日月
        log.info("**开始处理效率类指标日月 time=" + dateToString(startTime));
        processEfficiencyEnergyHistoryData(startTime);
        log.info("**结束处理效率类指标日月 time=" + dateToString(startTime));

        // 日指标写库
        if (isEndHourOfDay(startTime,endTime) && lstDayPoint.size() > 0 && influxdbDataSaveBeginTable <= 2) {
            try {
                log.info("** 指标日表数据队列开始入库，数量：" + lstDayPoint.size());
                long dTime1 = System.nanoTime();
                influxDBManager.batchInsertPoint(lstDayPoint, databaseEnergy, influxdbBatchSize);
                long dTime2 = System.nanoTime();
                log.error("**1这不是报错！ 指标日表数据队列入库完成耗时：" + (dTime2 - dTime1) / 1_000_000.0 + " 毫秒");
            } catch (Exception ex) {
                log.error("** 指标日表写入报错。指标数量=" + lstDayPoint.size() ,ex);
            }
        }
        // 月指标写库
        if (isEndHourOfMonth(startTime,endTime) && lstMonthPoint.size() > 0 && influxdbDataSaveBeginTable <= 3) {
            try {
                log.info("** 指标月表数据队列开始入库，数量：" + lstMonthPoint.size());
                long dTime1 = System.nanoTime();
                influxDBManager.batchInsertPoint(lstMonthPoint, databaseEnergy, influxdbBatchSize);
                long dTime2 = System.nanoTime();
                log.error("**1这不是报错！ 指标月表数据队列入库完成耗时：" + (dTime2 - dTime1) / 1_000_000.0 + " 毫秒");
            } catch (Exception ex) {
                log.error("** 指标月表写入报错。指标数量=" + lstMonthPoint.size() ,ex);
            }
        }
        // 月效率指标写库UE
        if (isEndHourOfMonth(startTime,endTime) && lstMonthUEPoint.size() > 0) {
            try {
                log.info("** 指标月UE表数据队列开始入库，数量：" + lstMonthUEPoint.size());
                long dTime1 = System.nanoTime();
                influxDBManager.batchInsertPoint(lstMonthUEPoint, databaseEnergy, influxdbBatchSize);
                long dTime2 = System.nanoTime();
                log.error("**1这不是报错！ 指标月UE表数据队列入库完成耗时：" + (dTime2 - dTime1) / 1_000_000.0 + " 毫秒");
            } catch (Exception ex) {
                log.error("** 指标UE表写入报错。指标数量=" + lstMonthUEPoint.size() , ex);
            }
        }
        log.info("指标日值，月值计算完成写库完成！！");
    }

    private void processEnergyHisHourData(Date startTime) {
        Date endTime = DateUtil.dateAddSeconds(startTime, 60 * 60 - 1);

        InfluxDBResultMapper resultMapper = new InfluxDBResultMapper();
        String selectDuration = "select * from HistoryComplexIndex where time >=$startTime and time <= $endTime ";
        Query queryBuilder = BoundParameterQuery.QueryBuilder.newQuery(selectDuration)
                .forDatabase(database)
                .bind("startTime", dateToString(startTime))
                .bind("endTime", dateToString(endTime))
                .create();
        QueryResult query = influxDB.query(queryBuilder);

        if (query != null) {
            List<HistoryComplexIndex> resultComplexIndexQuery = resultMapper.toPOJO(query, HistoryComplexIndex.class);
            if (!resultComplexIndexQuery.isEmpty()) {
                //能耗库 数据点仓库
                List<Point> lstPoint = new ArrayList<>();
                //List<Point> lstErrorPoint = new ArrayList<>();

                for (HistoryComplexIndex temp : resultComplexIndexQuery) {
                    //ComplexIndex thisIndex = lstEnergyComplexIndex.stream().filter(i -> i.getComplexIndexId().toString().equals(temp.getComplexIndexId())).findFirst().orElse(null);
                    ComplexIndex thisIndex = lstEnergyComplexIndex.getOrDefault(temp.getComplexIndexId(), null);

                    if (thisIndex == null) continue;   //此指标不是能耗指标
                    try {
                        //指标原始值
                        Double originalIndexValue = NumberUtil.doubleAccuracy(Double.parseDouble(temp.getIndexValue()), 2);

                        //如果小时值<0 设置为前面几个小时平均值
                        if (originalIndexValue < 0 || originalIndexValue > maxElecHour || temp.getAbnormal().equals("1")) {
                            Calendar calendar = Calendar.getInstance();
                            calendar.setTime(temp.getDateTime());
                            // 将 Calendar 的日期减去几小时
                            calendar.add(Calendar.HOUR_OF_DAY, -5);
                            //Double cleanValue = GetCleanIndexValue(temp.getComplexIndexId(), calendar.getTime());
                            temp.setIndexValue("0");
                        }

                        // 区分汇总、其他、效率类指标
                        BusinessDefinitionMap map = allBusinessDefinitionMap.stream().filter(
                                i -> i.getComplexIndexDefinitionId().equals(thisIndex.getComplexIndexDefinitionId())).findFirst().orElse(null);
                        if (map == null) continue;

                        //河北电信添加上一小时记录判断
                        if (Double.parseDouble(temp.getIndexValue()) > 1000d) {
                            //log.info("日志：发现指标本小时大于1000度; complexIndexId=" + temp.getComplexIndexId() + ":小时值=" + temp.getIndexValue());
                            if (map.getComplexIndexDefinitionTypeId().equals(1) || map.getComplexIndexDefinitionTypeId().equals(3) || map.getComplexIndexDefinitionTypeId().equals(5)) {
                                //EnergyHisHourData lastHourData = lstLastEnergyHisHourData.stream().filter(i -> i.getComplexIndexId().equals(temp.getComplexIndexId())).findFirst().orElse(null);
                                EnergyHisHourData lastHourData = lstLastEnergyHisHourData.getOrDefault(temp.getComplexIndexId(), null);
                                if (lastHourData != null && Math.abs(Double.parseDouble(lastHourData.getIndexValue()) - 0.0) < 1e-10) {
                                    //现场只保留了error日志
                                    //log.info("日志：发现指标上一小时用电量为0,本小时大于1000度,置用电量为1; complexIndexId=" + temp.getComplexIndexId() + ":小时值=" + temp.getIndexValue());
                                    temp.setIndexValue("1");
                                } else {
                                    if (lastHourData == null) {
                                        //log.info("日志：发现指标本小时大于1000度; lastHourData == null" + " lstLastEnergyHisHourData数量=" + lstLastEnergyHisHourData.size());
                                    } else {
                                        //log.info("日志：发现指标本小时大于1000度; lastHourData = " + lastHourData.getIndexValue());
                                    }
                                }
                            } else {
                                //log.info("日志：发现指标本小时大于1000度; complexIndexId = " + temp.getComplexIndexId() + ";complexIndexDefinitionId:" + thisIndex.getComplexIndexDefinitionId());
                            }
                        }
                        if (lstEnergyHisHourData.containsKey(temp.getComplexIndexId()))
                            continue;

                        EnergyHisHourData hourData = new EnergyHisHourData();
                        hourData.setAbnormal(temp.getAbnormal());
                        hourData.setTime(temp.getTime());
                        //hourData.setCalcTime(temp.getCalcTime());
                        hourData.setIndexValue(temp.getIndexValue());
                        hourData.setOriginalValue(originalIndexValue.toString());
                        hourData.setComplexIndexId(temp.getComplexIndexId());
                        hourData.setUnit(temp.getUnit());
                        hourData.setBusinessTypeId(temp.getBusinessTypeId());
                        lstEnergyHisHourData.put(temp.getComplexIndexId(), hourData);

                        Point point = Point.measurement(EnergyHisHourDataTable)
                                .time(DateUtil.localToUTC(startTime).getTime(), TimeUnit.MILLISECONDS)
                                .tag("ComplexIndexId", String.valueOf(temp.getComplexIndexId()))
                                .tag("Abnormal", "0")
                                .tag("BusinessTypeId", temp.getBusinessTypeId())
                                .addField("IndexValue", NumberUtil.doubleAccuracy(Double.parseDouble(temp.getIndexValue()), 2))
                                .addField("OriginalValue", originalIndexValue)
                                .addField("Unit", temp.getUnit() == null ? "" : temp.getUnit())
                                .build();
                        //加入数据点仓库
                        lstPoint.add(point);


                        //如果是总量或者分项，或者空调用电量 (字典1,3,4) 5重要设备 则直接求和
                        if (map.getComplexIndexDefinitionTypeId().equals(1) || map.getComplexIndexDefinitionTypeId().equals(3) || map.getComplexIndexDefinitionTypeId().equals(5)) {
                            //lstEnergyHisHourData_Sum.add(hourData);
                            lstEnergyHisHourData_Sum.put(hourData.getComplexIndexId(), hourData);
                        }
                        //效率和其他： 差值计算的需要拆分表达式重新计算， 非差值计算的取平均值
                        else if (map.getComplexIndexDefinitionTypeId().equals(100) || map.getComplexIndexDefinitionTypeId().equals(4)) {
                            //lstEnergyHisHourData_Other.add(hourData);
                            lstEnergyHisHourData_Other.put(hourData.getComplexIndexId(), hourData);
                        } else if (map.getComplexIndexDefinitionTypeId().equals(2)) {
                            //lstEnergyHisHourData_Efficiency.add(hourData);
                            lstEnergyHisHourData_Efficiency.put(hourData.getComplexIndexId(), hourData);
                        }
                    } catch (Exception ex) {
                        log.error("获取小时数据后加入lstPoint出错，" , ex);
                    }
                }

                // 能耗小时数据存储
                if (lstPoint.size() > 0 && influxdbDataSaveBeginTable == 1) {   //influxdbDataSaveBeginTable 1存小时表，2存日表，3存月表
                    try {
                        log.info("** 能耗小时数据队列开始入库，数量：" + lstPoint.size());
                        long dTime1 = System.nanoTime();
                        influxDBManager.batchInsertPoint(lstPoint, databaseEnergy, influxdbBatchSize);
                        long dTime2 = System.nanoTime();
                        log.error("**1这不是报错. 能耗小时数据队列入库完成耗时：" + (dTime2 - dTime1) / 1_000_000.0 + " 毫秒");
                    } catch (Exception ex) {
                        log.error("** 能耗小时表写入报错。小时指标数量=" + lstPoint.size() + "小时为：" + dateToString(startTime) , ex);
                    }
                }
            } else {
                log.info("startStatisticEnergyHistoryData resultComplexIndexQuery empty，StartTime=" + dateToString(startTime));
            }
        } else {
            log.info("HistoryComplexIndex query查询为空=null，StartTime=" + dateToString(startTime));
        }

    }

    //处理总量日月
    private void processSumEnergyHistoryData() {
        //存在汇总的小时值
        if (lstEnergyHisHourData_Sum.size() > 0) {
            for (EnergyHisHourData thisHourData : lstEnergyHisHourData_Sum.values()) {
                //日查找
                //EnergyHisDataResult thisDayValue = lstEnergyHisDayData.stream().filter(i -> i.getComplexIndexId().equals(thisHourData.getComplexIndexId())).findFirst().orElse(null);
                EnergyHisDayData thisDayValue = lstEnergyHisDayData.getOrDefault(thisHourData.getComplexIndexId(), null);
                if (thisDayValue != null) {
                    double value = NumberUtil.formatNumeric(thisDayValue.getIndexValue()) + NumberUtil.formatNumeric(thisHourData.getIndexValue());
                    thisDayValue.setIndexValue(NumberUtil.doubleAccuracy(value, 2).toString());
                } else {
                    thisDayValue = new EnergyHisDayData();
                    thisDayValue.setComplexIndexId(thisHourData.getComplexIndexId());
                    thisDayValue.setIndexValue(thisHourData.getIndexValue());
                    thisDayValue.setUnit(thisHourData.getUnit());
                    thisDayValue.setTime(dateToString(dayStartTime));

                    lstEnergyHisDayData.put(thisDayValue.getComplexIndexId(), thisDayValue);
                }

                Point point = Point.measurement(EnergyHisDayDataTable)
                        .time(DateUtil.localToUTC(dayStartTime).getTime(), TimeUnit.MILLISECONDS)
                        .tag("ComplexIndexId", String.valueOf(thisHourData.getComplexIndexId()))
                        .tag("Abnormal", "0")
                        .tag("BusinessTypeId", thisHourData.getBusinessTypeId())
                        .addField("IndexValue", NumberUtil.doubleAccuracy(Double.parseDouble(thisDayValue.getIndexValue()), 2))
                        .addField("OriginalValue", NumberUtil.doubleAccuracy(Double.parseDouble(thisDayValue.getIndexValue()), 2))
                        .addField("Unit", thisHourData.getUnit() == null ? "" : thisHourData.getUnit())
                        .build();
                //加入日数据点仓库
                lstDayPoint.add(point);

                // 月查找
                //EnergyHisDataResult thisMonthValue = lstEnergyHisMonthData.stream().filter(i -> i.getComplexIndexId().equals(thisHourData.getComplexIndexId())).findFirst().orElse(null);
                EnergyHisMonthData thisMonthValue = lstEnergyHisMonthData.getOrDefault(thisHourData.getComplexIndexId(), null);

                if (thisMonthValue != null) {
                    double value = NumberUtil.formatNumeric(thisMonthValue.getIndexValue()) + NumberUtil.formatNumeric(thisHourData.getIndexValue());
                    thisMonthValue.setIndexValue(NumberUtil.doubleAccuracy(value, 2).toString());
                } else {
                    thisMonthValue = new EnergyHisMonthData();
                    thisMonthValue.setComplexIndexId(thisHourData.getComplexIndexId());
                    thisMonthValue.setIndexValue(thisHourData.getIndexValue());
                    thisMonthValue.setUnit(thisHourData.getUnit());
                    thisMonthValue.setTime(dateToString(monthStartTime));

                    lstEnergyHisMonthData.put(thisMonthValue.getComplexIndexId(), thisMonthValue);
                }

                Point point2 = Point.measurement(EnergyHisMonthDataTable)
                        .time(DateUtil.localToUTC(monthStartTime).getTime(), TimeUnit.MILLISECONDS)
                        .tag("ComplexIndexId", String.valueOf(thisHourData.getComplexIndexId()))
                        .tag("Abnormal", "0")
                        .tag("BusinessTypeId", thisHourData.getBusinessTypeId())
                        .addField("IndexValue", NumberUtil.doubleAccuracy(Double.parseDouble(thisMonthValue.getIndexValue()), 2))
                        .addField("OriginalValue", NumberUtil.doubleAccuracy(Double.parseDouble(thisMonthValue.getIndexValue()), 2))
                        .addField("Unit", thisHourData.getUnit() == null ? "" : thisHourData.getUnit())
                        .build();
                //加入日数据点仓库
                lstMonthPoint.add(point2);
            }
        } else {
            log.info("当前小时数据属于汇总类指标的个数为0.");
        }
    }

    //处理其他类指标日月
    private void processOtherEnergyHistoryData(Date calTime) {
        if (lstEnergyHisHourData_Other.size() == 0) {
            log.info("当前小时数据属于取平均值类指标的个数为0.");
            return;
        }
        // 判断是否新的一天，需要读取日月数据
        LocalDateTime localDateTime = calTime.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        int hour = localDateTime.getHour(); // 获取小时数（24小时制）
        int day = localDateTime.getDayOfMonth();

        // lstEfficiencyAndOther中的只需要求平均值。
        for (EnergyHisHourData thisHourData : lstEnergyHisHourData_Other.values()) {
            //日查找
            //EnergyHisDataResult thisDayValue = lstEnergyHisDayData.stream().filter(i -> i.getComplexIndexId().equals(thisHourData.getComplexIndexId())).findFirst().orElse(null);
            EnergyHisDayData thisDayValue = lstEnergyHisDayData.getOrDefault(thisHourData.getComplexIndexId(), null);
            if (thisDayValue != null) {
                double value = (NumberUtil.formatNumeric(thisDayValue.getIndexValue()) * hour + NumberUtil.formatNumeric(thisHourData.getIndexValue())) / (hour + 1);
                thisDayValue.setIndexValue(NumberUtil.doubleAccuracy(value, 2).toString());
            } else {
                thisDayValue = new EnergyHisDayData();
                thisDayValue.setComplexIndexId(thisHourData.getComplexIndexId());
                thisDayValue.setIndexValue(thisHourData.getIndexValue());
                thisDayValue.setUnit(thisHourData.getUnit());
                thisDayValue.setTime(dateToString(dayStartTime));

                lstEnergyHisDayData.put(thisDayValue.getComplexIndexId(), thisDayValue);
            }

            Point point = Point.measurement(EnergyHisDayDataTable)
                    .time(DateUtil.localToUTC(dayStartTime).getTime(), TimeUnit.MILLISECONDS)
                    .tag("ComplexIndexId", String.valueOf(thisHourData.getComplexIndexId()))
                    .tag("Abnormal", "0")
                    .tag("BusinessTypeId", thisHourData.getBusinessTypeId())
                    .addField("IndexValue", NumberUtil.doubleAccuracy(Double.parseDouble(thisDayValue.getIndexValue()), 2))
                    .addField("OriginalValue", NumberUtil.doubleAccuracy(Double.parseDouble(thisDayValue.getIndexValue()), 2))
                    .addField("Unit", thisHourData.getUnit() == null ? "" : thisHourData.getUnit())
                    .build();
            //加入日数据点仓库
            lstDayPoint.add(point);

            // 月查找
            //EnergyHisDataResult thisMonthValue = lstEnergyHisMonthData.stream().filter(i -> i.getComplexIndexId().equals(thisHourData.getComplexIndexId())).findFirst().orElse(null);
            EnergyHisMonthData thisMonthValue = lstEnergyHisMonthData.getOrDefault(thisHourData.getComplexIndexId(), null);
            if (thisMonthValue != null) {
                double value = (NumberUtil.formatNumeric(thisMonthValue.getIndexValue()) * ((day - 1) * 24 + hour) + NumberUtil.formatNumeric(thisHourData.getIndexValue())) / ((day - 1) * 24 + hour + 1);
                thisMonthValue.setIndexValue(NumberUtil.doubleAccuracy(value, 2).toString());
            } else {
                thisMonthValue = new EnergyHisMonthData();
                thisMonthValue.setComplexIndexId(thisHourData.getComplexIndexId());
                thisMonthValue.setIndexValue(thisHourData.getIndexValue());
                thisMonthValue.setUnit(thisHourData.getUnit());
                thisMonthValue.setTime(dateToString(monthStartTime));

                lstEnergyHisMonthData.put(thisMonthValue.getComplexIndexId(), thisMonthValue);
            }

            Point point2 = Point.measurement(EnergyHisMonthDataTable)
                    .time(DateUtil.localToUTC(monthStartTime).getTime(), TimeUnit.MILLISECONDS)
                    .tag("ComplexIndexId", String.valueOf(thisHourData.getComplexIndexId()))
                    .tag("Abnormal", "0")
                    .tag("BusinessTypeId", thisHourData.getBusinessTypeId())
                    .addField("IndexValue", NumberUtil.doubleAccuracy(Double.parseDouble(thisMonthValue.getIndexValue()), 2))
                    .addField("OriginalValue", NumberUtil.doubleAccuracy(Double.parseDouble(thisMonthValue.getIndexValue()), 2))
                    .addField("Unit", thisHourData.getUnit() == null ? "" : thisHourData.getUnit())
                    .build();
            //加入日数据点仓库
            lstMonthPoint.add(point2);
        }
    }

    //处理效率类指标日月
    private void processEfficiencyEnergyHistoryData(Date calTime) {
        if (lstEnergyHisHourData_Efficiency.size() == 0) {
            log.info("当前小时数据属于效率类指标的个数为0.");
            return;
        }

        for (EnergyHisHourData thisHourData : lstEnergyHisHourData_Efficiency.values()) {

            ComplexIndex thisIndex = lstEnergyComplexIndex.getOrDefault(thisHourData.getComplexIndexId(), null);
//                    energyComplexIndexManager.GetAllComplexIndex().stream().filter(
//                    i -> i.getComplexIndexId().toString().equals(thisHourData.getComplexIndexId())).findFirst().orElse(null);
            if (thisIndex == null || thisIndex.getExpression() == null || thisIndex.getExpression().length() == 0)
                continue;
            String Expression = thisIndex.getExpression().toLowerCase();
            Pattern p = Pattern.compile("(?<=ci\\(|last\\()[^\\)]+", Pattern.CASE_INSENSITIVE);
            Matcher m = p.matcher(thisIndex.getExpression().toLowerCase());

            //日查找
            while (m.find()) {
                int complexId = Integer.parseInt(m.group());
                //EnergyHisDataResult thisDayValue = lstEnergyHisDayData.stream().filter(i -> i.getComplexIndexId().equals(Integer.toString(complexId))).findFirst().orElse(null);
                EnergyHisDayData thisDayValue = lstEnergyHisDayData.getOrDefault(Integer.toString(complexId), null);
                String replaceContent = thisDayValue == null ? "0" : thisDayValue.getIndexValue();
                Expression = Expression.replace("ci(" + complexId + ")", replaceContent).replace("last(" + complexId + ")", replaceContent);
            }

            double indexValue = CalculatorConvert(thisIndex.getComplexIndexId().toString(), Expression);
            //EnergyHisDataResult thisDayValue = lstEnergyHisDayData.stream().filter(i -> i.getComplexIndexId().equals(thisHourData.getComplexIndexId())).findFirst().orElse(null);
            EnergyHisDayData thisDayValue = lstEnergyHisDayData.getOrDefault(thisHourData.getComplexIndexId(), null);
            if (thisDayValue != null) {
                thisDayValue.setIndexValue(NumberUtil.doubleAccuracy(indexValue, 2).toString());
            } else {
                thisDayValue = new EnergyHisDayData();
                thisDayValue.setComplexIndexId(thisHourData.getComplexIndexId());
                thisDayValue.setIndexValue(NumberUtil.doubleAccuracy(indexValue, 2).toString());
                thisDayValue.setUnit(thisHourData.getUnit());
                thisDayValue.setTime(dateToString(dayStartTime));
                lstEnergyHisDayData.put(thisDayValue.getComplexIndexId(), thisDayValue);
            }

            Point point = Point.measurement(EnergyHisDayDataTable)
                    .time(DateUtil.localToUTC(dayStartTime).getTime(), TimeUnit.MILLISECONDS)
                    .tag("ComplexIndexId", String.valueOf(thisHourData.getComplexIndexId()))
                    .tag("Abnormal", "0")
                    .tag("BusinessTypeId", thisHourData.getBusinessTypeId())
                    .addField("IndexValue", NumberUtil.doubleAccuracy(Double.parseDouble(thisDayValue.getIndexValue()), 2))
                    .addField("OriginalValue", NumberUtil.doubleAccuracy(Double.parseDouble(thisDayValue.getIndexValue()), 2))
                    .addField("Unit", thisHourData.getUnit() == null ? "" : thisHourData.getUnit())
                    .build();
            //加入日数据点仓库
            lstDayPoint.add(point);

            //月效率处理
            boolean isLock = false;
            boolean bothValided = true;

            m = p.matcher(thisIndex.getExpression().toLowerCase());
            while (m.find()) {
                int complexId = Integer.parseInt(m.group());
                //EnergyHisDataResult ueDataResult = lstEnergyHisMonthUEData.stream().filter(i -> i.getComplexIndexId().equals(Integer.toString(complexId))).findFirst().orElse(null);
                EnergyHisMonthUEData ueDataResult = lstEnergyHisMonthUEData.getOrDefault(Integer.toString(complexId), null);
                if (ueDataResult != null && (ueDataResult.getIndexValue().equals("-1") || ueDataResult.getIndexValue().equals("-1.0") || ueDataResult.getIndexValue().equals("-1.00"))) {  //有其他接口锁定月PUE时会给分子分母置为-1
                    log.info("指标:" + thisIndex.getComplexIndexId() + "表达式(id:" + complexId + ")在月UE表被锁定");
                    isLock = true;
                }
                //EnergyHisHourData hourData = lstEnergyHisHourData_Sum.stream().filter(i -> i.getComplexIndexId().equals(Integer.toString(complexId))).findFirst().orElse(null);
                EnergyHisHourData hourData = lstEnergyHisHourData_Sum.getOrDefault(Integer.toString(complexId), null);
                // 河北电信认为每小时用电量超过maxElecHour为异常，可能是指标服务未作差值计算电表接入后正向有功电能信号值较大。此种情况不计入月PUE计算有效值
                if (hourData == null || NumberUtil.formatNumeric(hourData.getIndexValue()) <= 0 || NumberUtil.formatNumeric(hourData.getIndexValue()) > maxElecHour) {
                    bothValided = false;
                    log.info("指标:" + thisIndex.getComplexIndexId() + "表达式(id:" + complexId + ")本小时采集值无效(" + (hourData == null ? "null" : hourData.indexValue) + ")");
                }
            }
            // 没有被锁定 且有效
            if (!isLock && bothValided) {
                Expression = thisIndex.getExpression().toLowerCase();
                m = p.matcher(thisIndex.getExpression().toLowerCase());
                while (m.find()) {
                    Integer complexId = Integer.valueOf(m.group());
                    //EnergyHisHourData hourData = lstEnergyHisHourData_Sum.stream().filter(i -> i.getComplexIndexId().equals(complexId.toString())).findFirst().orElse(null);
                    EnergyHisHourData hourData = lstEnergyHisHourData_Sum.getOrDefault(complexId.toString(), null);
                    String hourResult = hourData == null ? "0" : hourData.getIndexValue();

                    //EnergyHisDataResult ueDataResult = lstEnergyHisMonthUEData.stream().filter(i -> i.getComplexIndexId().equals(complexId.toString())).findFirst().orElse(null);
                    EnergyHisMonthUEData ueDataResult = lstEnergyHisMonthUEData.getOrDefault(complexId.toString(), null);
                    if (ueDataResult == null) {
                        ueDataResult = new EnergyHisMonthUEData();
                        ueDataResult.setComplexIndexId(complexId.toString());
                        ueDataResult.setIndexValue(hourResult);
                        ueDataResult.setTime(dateToString(monthStartTime));
                        ueDataResult.setComplexIndexId(complexId.toString());
                        lstEnergyHisMonthUEData.put(ueDataResult.getComplexIndexId(), ueDataResult);
                    } else {
                        Double value = Double.parseDouble(ueDataResult.getIndexValue()) + Double.parseDouble(hourResult);
                        ueDataResult.setIndexValue(NumberUtil.doubleAccuracy(value, 2).toString());
                    }

                    Point pointue = Point.measurement("EnergyHisMonthUEData")
                            .time(DateUtil.localToUTC(monthStartTime).getTime(), TimeUnit.MILLISECONDS)
                            .tag("ComplexIndexId", String.valueOf(complexId))
                            .addField("IndexValue", NumberUtil.doubleAccuracy(Double.parseDouble(ueDataResult.getIndexValue()), 2))
                            .addField("CalcTime", dateToString(monthStartTime))
                            .build();
                    lstMonthUEPoint.add(pointue);
                    Expression = Expression.replace("ci(" + complexId + ")", ueDataResult.getIndexValue()).replace("last(" + complexId + ")", ueDataResult.getIndexValue());
                }

                indexValue = CalculatorConvert(thisIndex.getComplexIndexId().toString(), Expression);
                Point point2 = Point.measurement(EnergyHisMonthDataTable)
                        .time(DateUtil.localToUTC(monthStartTime).getTime(), TimeUnit.MILLISECONDS)
                        .tag("ComplexIndexId", String.valueOf(thisIndex.getComplexIndexId()))
                        .tag("Abnormal", "0")
                        .tag("BusinessTypeId", thisIndex.getBusinessTypeId().toString())
                        .addField("IndexValue", NumberUtil.doubleAccuracy(indexValue, 2))
                        .addField("OriginalValue", NumberUtil.doubleAccuracy(indexValue, 2))
                        .addField("Unit", "")
                        .build();
                //加入数据点仓库
                lstMonthPoint.add(point2);
            }
        }
    }

    private Double CalculatorConvert(String complexIndexId, String expression) {
        Double result = 0d;
        try {
            result = NumberUtil.doubleAccuracy(Calculator.conversion(expression), 2);
        } catch (Exception ex) {
            log.info("CalculatorConvert complexIndexId=" + complexIndexId + " Expression=" + expression);
        }
        return result;
    }


    private boolean isEndHourOfDay(Date time,Date endTime){
        if (DateUtil.dateAddHours(time,1).equals(endTime)){
            return true;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(time);
        int hour = calendar.get(Calendar.HOUR_OF_DAY);

        Calendar now = Calendar.getInstance();
        if (isToday(calendar, now)) {
            return true;
        }
        return hour == 23; // 一天的最后一个小时是 23:00-23:59
    }
    private boolean isEndHourOfMonth(Date time,Date endTime){
        if (DateUtil.dateAddHours(time,1).equals(endTime)){
            return true;
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(time);
        int day = calendar.get(Calendar.DAY_OF_MONTH);
        int hour = calendar.get(Calendar.HOUR_OF_DAY);

        Calendar now = Calendar.getInstance();
        if (isToday(calendar, now)) {
            return true;
        }
        // 检查是否是当月最后一天的最后一个小时
        calendar.add(Calendar.DAY_OF_MONTH, 1); // 跳到第二天
        boolean isLastDay = calendar.get(Calendar.DAY_OF_MONTH) == 1; // 如果第二天是1号，说明今天是最后一天
        return isLastDay && hour == 23; // 最后一小时判断
    }

    private boolean isToday(Calendar calendar1, Calendar calendar2) {
        return calendar1.get(Calendar.YEAR) == calendar2.get(Calendar.YEAR)
                && calendar1.get(Calendar.DAY_OF_YEAR) == calendar2.get(Calendar.DAY_OF_YEAR);
    }
}
