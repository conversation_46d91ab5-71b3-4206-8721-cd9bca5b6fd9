package com.siteweb.powerdistribution.startup;


import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.common.event.HAStatusChanged;
import com.siteweb.monitoring.mamager.ActiveEventManager;
import com.siteweb.powerdistribution.business.DiagramRunner;
import com.siteweb.powerdistribution.business.PlayBackRunner;
import com.siteweb.powerdistribution.common.DiagramTask;
import com.siteweb.powerdistribution.domain.config.DocumentConfig;
import com.siteweb.powerdistribution.domain.config.DocumentIndexConfig;
import com.siteweb.powerdistribution.domain.document.DesignerDocument;
import com.siteweb.powerdistribution.mapper.AlarmPDMapper;
import com.siteweb.powerdistribution.schedule.playback.ScheduledTaskManager;
import com.siteweb.powerdistribution.services.DataProviderService;
import com.siteweb.powerdistribution.services.DistributionFileService;
import com.siteweb.powerdistribution.services.DistributionScheduledService;
import com.siteweb.powerdistribution.websocket.WebSocketServer;
import com.siteweb.powerdistribution.common.PDCommonCache;
import com.siteweb.utility.service.HAStatusService;
import lombok.Data;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


@Data
@Component
public class PowerDistributionDiagramManager implements ApplicationListener<BaseSpringEvent<HAStatusChanged>> {
    private final Logger log = LoggerFactory.getLogger(PowerDistributionDiagramManager.class);

    @Autowired
    public DistributionFileService distributionFileService;

    @Autowired
    private DistributionScheduledService distributionScheduledService;

    @Autowired
    private DataProviderService dataProvider;
    @Autowired
    private PDCommonCache pdCommonCache;
    @Autowired
    private ActiveEventManager activeEventManager;
    @Autowired
    private AlarmPDMapper alarmPDMapper;
    @Autowired
    HAStatusService haStatusService;


    /***
     * 配电图列表
     */
    private Map<Integer, DiagramRunner> diagrams;


    private Map<String, PlayBackRunner> playBackDiagrams;

    /**
     * 配电图索引
     */
    private DocumentIndexConfig indexConfig;


    public PowerDistributionDiagramManager() {
        diagrams = new HashMap<>();
        playBackDiagrams = new HashMap<>();
        indexConfig = null;
    }


    /**
     * 模块入口
     * 启动加载初始化 所有配电图
     * 后续由 WebSocketServer 的 onOpen 内触发
     */
    @PostConstruct
    public void init() {
        this.setup();
    }

    private void setup() {
        try {
            this.distributionFileService.InitializationDirectory("/powerDistribution/");
            indexConfig = this.distributionFileService.loadIndex();
            // load diagrams config
            for (DocumentConfig diagramConfigure : indexConfig.getDiagrams()) {
                // diagram.DiagramId
                Integer diagramId = diagramConfigure.getDiagramId();
                DesignerDocument document = this.distributionFileService.loadDiagram(diagramId);
                DiagramRunner diagramRunner = new DiagramRunner(diagramId);
                diagramRunner.setTask(new DiagramTask(diagramRunner));
                diagramRunner.loadConfigure(document);
                diagrams.put(diagramId, diagramRunner);
            }
            ScheduledTaskManager.distributionDiagramLoadCompleted = true;
        } catch (Exception ex) {
            log.error("error:{}", ex);
        }

    }


    /**
     * 创建一张配电图
     *
     * @param diagramName
     * @return
     * @throws IOException
     */
    public DocumentConfig createDiagram(String diagramName) throws IOException {
        Integer diagramId = getNextId(indexConfig.getIdIndex());//hxh: 新增配电图在这里，只要给个配电图名称就可以了，id通过本行代码从DiagramIndex.json/IDIndex字段获取
        DocumentConfig info = new DocumentConfig();
        info.setDiagramId(diagramId);
        info.setDiagramName(diagramName);
        indexConfig.getDiagrams().add(info);
        indexConfig.setIdIndex(diagramId + 1);
        DesignerDocument document = new DesignerDocument();
        this.distributionFileService.saveIndex(indexConfig);
        this.distributionFileService.saveDiagram(document, diagramId);
        DiagramRunner diagramRunner = new DiagramRunner(diagramId);
        diagramRunner.setTask(new DiagramTask(diagramRunner));
        diagramRunner.loadConfigure(document);
        diagrams.put(diagramId, diagramRunner);
        return info;

    }

    /**
     * 删除一张配电图
     *
     * @param diagramId
     * @return
     */
    public Boolean deleteDiagram(Integer diagramId) {
        try {
            if (diagramId == null) return false;
            for (DocumentConfig document : indexConfig.getDiagrams()) {
                if (document.getDiagramId().equals(diagramId)) {
                    indexConfig.getDiagrams().remove(document);
                    this.distributionFileService.saveIndex(indexConfig);
                    diagrams.remove(diagramId);
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("error:{}", e);
            return false;
        }
        return false;
    }

    private Integer getNextId(Integer diagramid) {
        while (diagrams.containsKey(diagramid)) {
            diagramid++;
        }
        return diagramid;
    }

    public DiagramRunner getDiagram(Integer diagramId) {
        return diagrams.get(diagramId);
    }

    public Boolean updateDiagramName(Integer diagramId, String diagramName) throws IOException {
        try {
            if (diagramId == null) return false;
            for (DocumentConfig document : indexConfig.getDiagrams()) {
                if (document.getDiagramId().equals(diagramId)) {
                    document.setDiagramName(diagramName);
                    this.distributionFileService.saveIndex(indexConfig);
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("error:{}", e);
            return false;
        }
        return false;
    }

    public void createPlayBackDiagram(WebSocketServer webSocketServer, PlayBackRunner playBackRunner) {
        this.playBackDiagrams.put(webSocketServer.getSession().getId(), playBackRunner);
    }

    public void deletePlayBackDiagram(WebSocketServer webSocketServer) {
        this.playBackDiagrams.remove(webSocketServer.getSession().getId());
    }


    public Boolean isPlayBackDiagram(WebSocketServer webSocketServer) {
        if (webSocketServer.getSession() == null) return false;
        return this.playBackDiagrams.containsKey(webSocketServer.getSession().getId());
    }

    public PlayBackRunner runPlayBack(WebSocketServer webSocketServer, DiagramRunner diagramRunner, Date time, boolean isFirst) {
        if (isPlayBackDiagram(webSocketServer)) {
            PlayBackRunner playBackRunner = this.playBackDiagrams.get(webSocketServer.getSession().getId());
            if(isFirst) {
                playBackRunner.cacheEvent(time, new Date(), pdCommonCache, activeEventManager, alarmPDMapper);
            }
            playBackRunner.run(time);
            return playBackRunner;
        } else {
            PlayBackRunner playBackRunner = new PlayBackRunner(webSocketServer, diagramRunner);
            this.createPlayBackDiagram(webSocketServer, playBackRunner);
            if(isFirst) {
                playBackRunner.cacheEvent(time, new Date(), pdCommonCache, activeEventManager, alarmPDMapper);
            }
            playBackRunner.run(time);
            return playBackRunner;
        }
    }




    public void rePlay(WebSocketServer webSocketServer) {
        if (isPlayBackDiagram(webSocketServer)) {
            this.playBackDiagrams.get(webSocketServer.getSession().getId()).rePlay();
        }
    }

    /**
     * 移除一个websocket客户端
     *
     * @param webSocket
     */
    public void removeWebSocket(WebSocketServer webSocket) {
        if (isPlayBackDiagram(webSocket)) {
            this.playBackDiagrams.get(webSocket.getSession().getId()).removeWebSocket(webSocket);
            this.playBackDiagrams.remove(webSocket.getSession().getId());
        }
    }


    @Override
    public void onApplicationEvent(@NotNull BaseSpringEvent<HAStatusChanged> event) {
        if (!haStatusService.isEnabled()) {
            return;
        }
        HAStatusChanged haStatusChanged = event.getData();
        log.info("powerDistributionDiagramManager receive HAStatusChanged event, lastHAStatus is {}, haStatus is {}", haStatusChanged.getLastHAStatus(), haStatusChanged.getHaStatus());
        init();
    }
}

