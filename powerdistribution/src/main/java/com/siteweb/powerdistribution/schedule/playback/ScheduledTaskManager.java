package com.siteweb.powerdistribution.schedule.playback;

import com.siteweb.common.util.DateUtil;
import com.siteweb.powerdistribution.business.DiagramRunner;
import com.siteweb.powerdistribution.common.PDCommonCache;
import com.siteweb.powerdistribution.common.RunnerContext;
import com.siteweb.powerdistribution.domain.document.DesignerDocument;
import com.siteweb.powerdistribution.startup.PowerDistributionDiagramManager;
import org.javatuples.Pair;
import org.quartz.*;
import org.quartz.impl.StdSchedulerFactory;
import org.quartz.impl.matchers.GroupMatcher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Calendar;

/***
 * 定时任务管理器：
 *    用于为容量配电图回放功能循环保存历史信号数据
 */
@Component
public class ScheduledTaskManager {
    private final Logger log = LoggerFactory.getLogger(ScheduledTaskManager.class);

    @Autowired
    private PDCommonCache pdCommonCache;

    /** 默认线程池的线程数 */
    private static final Integer DEFAULT_THREAD_POLL_COUNT = 10;
    /** 最长等待时间(单位：秒) */
    private static final Integer WAIT_MAX_SECOND = 600;
    public static final Integer MIN_INTERVAL_TIME_SECOND = 60;

    private static final String JOB_GROUP = "PlaybackJobs";
    private static final String TRIGGER_GROUP = "PlaybackTriggers";

    /** 标识所有配电图信息加载完成与否 */
    public static Boolean distributionDiagramLoadCompleted = false;

    @Autowired
    private PowerDistributionDiagramManager diagramManager;

    private Properties backupProps;

    private Scheduler scheduler;

    @PostConstruct
    private void init() {
        //校验总开关参数，如果开启了配电V2版功能才继续往下走
        if(!pdCommonCache.getPdV2Enable()) {
            log.info("PdV2Enable = false, not need init.");
            return;
        }
        try {
            int threadCount = DEFAULT_THREAD_POLL_COUNT;
            waitDistributionDiagramLoadCompleted();
            Map<Integer, DiagramRunner> diagrams = new HashMap<>();
            Integer diagramCounts = 0;
            if(distributionDiagramLoadCompleted) {
                diagrams = diagramManager.getDiagrams();
                if(diagrams == null || diagrams.size() < 1) {
                    log.warn("[PlayBack HistoryData] diagrams==null or diagrams.size() < 1");
                } else {
                    threadCount = diagrams.size() < DEFAULT_THREAD_POLL_COUNT ? DEFAULT_THREAD_POLL_COUNT : diagrams.size();
                }
            } else {
                log.error("[PlayBack HistoryData] After waiting for " + WAIT_MAX_SECOND + " (s), the distributionDiagram is still not load completed.");
            }
            //初始化Scheduler
            StdSchedulerFactory sf = new StdSchedulerFactory();
            Properties props = new Properties();
            props.put("org.quartz.scheduler.instanceName", "playBackHistoryDataSaveScheduler");
            props.put("org.quartz.scheduler.instanceId", "AUTO");
            props.put("spring.quartz.job-store-type", "memory");
            props.put("org.quartz.scheduler.batchTriggerAcquisitionMaxCount", "100");
            props.put("org.quartz.threadPool.threadCount", (threadCount + 10) + "");//+10是为了可能的新增配电图预留
            props.put("org.quartz.threadPool.class", "org.quartz.simpl.SimpleThreadPool");
            props.put("org.quartz.jobStore.class", "org.quartz.simpl.RAMJobStore");
            props.put("org.quartz.jobStore.misfireThreshold", "30000");

            try {
                sf.initialize(props);
                scheduler = sf.getScheduler();
                backupProps = props;
            } catch (SchedulerException e1) {
                log.error("[PlayBack HistoryData] playBackHistoryDataSaveScheduler.init throw SchedulerException:" , e1);
                scheduler = null;
                return;
            } catch (Exception e2) {
                log.error("[PlayBack HistoryData] playBackHistoryDataSaveScheduler throw Exception:" , e2);
                scheduler = null;
                return;
            }
            //开始启动任务
            scheduler.start();
            if(diagrams == null || diagrams.size() < 1) {
                log.error("[PlayBack HistoryData] diagrams == null or diagrams.size() < 1");
            } else {
                diagramCounts = diagrams.size();
                for (Integer diagramId : diagrams.keySet()) {
                    DiagramRunner diagramRunner = diagrams.get(diagramId);
                    RunnerContext context = diagramRunner.getContext();
                    if(context == null) {
                        log.error("[PlayBack HistoryData] diagramId=" + diagramId + ", RunnerContext is null");
                        continue;
                    }
                    pdCommonCache.reloadDiagramCache(diagramRunner);
                    DesignerDocument designerDocument = context.getDocument();
                    if(designerDocument == null) {
                        log.error("[PlayBack HistoryData] diagramId=" + diagramId + " designerDocument is null");
                        continue;
                    }
                    boolean needSave = designerDocument.getEnabledSnapshot() != null && designerDocument.getEnabledSnapshot();
                    if(!needSave) {
                        log.info("[PlayBack HistoryData] diagramId=" + diagramId + " not need save HistoryData, needSave=" + needSave);
                        continue;
                    }
                    int intervalTime = designerDocument.getSnapshotStoragePeriod() == null ? MIN_INTERVAL_TIME_SECOND : designerDocument.getSnapshotStoragePeriod();
                    if(intervalTime < MIN_INTERVAL_TIME_SECOND) {
                        log.info("[PlayBack HistoryData] diagramId=" + diagramId + " , intervalTime=" + intervalTime + ", invalid. Using default " + MIN_INTERVAL_TIME_SECOND + " s.");
                        intervalTime = MIN_INTERVAL_TIME_SECOND;
                    }
                    createNewTask(diagramId, intervalTime);
                }
            }
            log.info("[PlayBack HistoryData] scheduler init completed. task count=" + getTotalTaskCount() + "; threadCount=" + threadCount + "; diagramCounts=" + diagramCounts);
        } catch (Exception unknownException) {
            log.error("[PlayBack HistoryData] playBackHistoryDataSaveScheduler throw unknown Exception:" , unknownException);
            if(scheduler != null) {
                try {
                    scheduler.shutdown();
                    scheduler.clear();
                } catch (Exception ee) {
                    log.error("[PlayBack HistoryData] scheduler.shutdown() throw unknown Exception:" , ee);
                } finally {
                    scheduler = null;
                }
            }
        }
    }

    /** 等待配电图数据加载完毕 */
    private void waitDistributionDiagramLoadCompleted() {
        int hasWaitSecond = 0;
        int waitStep = 3;//单次wait的最小间隔秒数
        do {
            if(distributionDiagramLoadCompleted || hasWaitSecond > WAIT_MAX_SECOND) {
                log.warn("[PlayBack HistoryData] waitDistributionDiagramLoadCompleted BREAK: distributionDiagramLoadCompleted="
                        + distributionDiagramLoadCompleted + ";hasWaitSecond=" + hasWaitSecond + ";WAIT_MAX_SECOND=" + WAIT_MAX_SECOND);
                break;
            }
            try {
                Thread.sleep(waitStep * 1000);
                hasWaitSecond += waitStep;
                log.info("[PlayBack HistoryData] waitDistributionDiagramLoadCompleted：has wait " + hasWaitSecond + " (s).");
            } catch (InterruptedException e) {
                log.error("[PlayBack HistoryData] waitDistributionDiagramLoadCompleted throw Excetion: ", e);
                break;
            }
        } while(true);
    }

    public boolean createNewTask(int taskId, int intervalTime) {

        if(scheduler == null) {
            log.error("[PlayBack HistoryData] createNewTask stop: scheduler == null");
            return false;
        }
        Map<Integer, DiagramRunner> diagrams = diagramManager.getDiagrams();
        if(diagrams == null || diagrams.size() < 1) {
            log.error("[PlayBack HistoryData] createNewTask(" + taskId + ") fail, because diagrams == null || diagrams.size() < 1");
            return false;
        }
        DiagramRunner diagramRunner;
        if(diagrams.containsKey(taskId)) {
            diagramRunner = diagrams.get(taskId);
        } else {
            log.error("[PlayBack HistoryData] createNewTask(" + taskId + ") fail, because diagrams do not containsKey ");
            return false;
        }

        try {
            JobKey jobKey = JobKey.jobKey(getJobKeyByTaskId(taskId), JOB_GROUP);
            if(scheduler.checkExists(jobKey)) {
                closeTask(taskId);
                log.error("[PlayBack HistoryData] When create job(" + taskId + "), it was found that the task already exists, close it first(closeTask)");
            }
            JobDetail job = JobBuilder.newJob(HistoryDataSaveTask.class)
                    .withIdentity(getJobKeyByTaskId(taskId), JOB_GROUP)
                    .usingJobData("taskId", taskId)
                    .build();

            pdCommonCache.getAllJobMap().put(taskId, job);

            //计算任务的起始触发时间
            Date curDate = HistoryDataSaveTask.getCurDate();
            Date after30s = DateUtil.dateAddSeconds(curDate, 30);//保证最少间隔30秒
            Date nextMinute = DateBuilder.nextGivenMinuteDate(after30s, 1);//再取最近的整分钟
            Calendar c = Calendar.getInstance();
            c.setTime(nextMinute);
            c.set(Calendar.MILLISECOND, 100);//发现偶尔会有提前一毫秒执行导致记录时间是59的情况，将起始时间略向后延几毫秒
            Date nextMinuteNew = c.getTime();

            log.info("TaskId(" + taskId + ") new Task, startTime=" + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS").format(nextMinuteNew));

            Trigger trigger = TriggerBuilder.newTrigger()
                    .withIdentity(getTriggerKeyByTaskId(taskId), TRIGGER_GROUP)
                    .startAt(nextMinuteNew) // 设置初始开始都是在HH:mm:00执行(即每分钟的第00秒执行)
                    .withSchedule(SimpleScheduleBuilder.repeatSecondlyForever(intervalTime))
                    .build();

            job.getJobDataMap().put("diagramInfo", diagramRunner);
            job.getJobDataMap().put("taskId", taskId);
            scheduler.scheduleJob(job, trigger);
            log.info("[PlayBack HistoryData] createNewTask(" + taskId + ") success. intervalTime: " + intervalTime + " (second)");
        } catch (SchedulerException e1) {
            log.error("[PlayBack HistoryData] createNewTask(" + taskId + ") throw SchedulerException:" , e1);
            return false;
        } catch (Exception e2) {
            log.error("[PlayBack HistoryData] createNewTask(" + taskId + ") throw unknown Exception:" , e2);
            return false;
        }
        return true;
    }

    public boolean closeTask(int taskId) {

        if(scheduler == null) {
            log.error("[PlayBack HistoryData] closeTask stop: scheduler == null");
            return false;
        }
        try {
            JobKey jobKey = JobKey.jobKey(getJobKeyByTaskId(taskId), JOB_GROUP);
            TriggerKey triggerKey = TriggerKey.triggerKey(getTriggerKeyByTaskId(taskId), TRIGGER_GROUP);
            if(scheduler.checkExists(jobKey)) {
                scheduler.pauseJob(jobKey);
                scheduler.deleteJob(jobKey);
                scheduler.unscheduleJob(triggerKey);
                log.info("[PlayBack HistoryData] closeTask(" + taskId + ") success (deleteJob).");
            } else {
                log.info("[PlayBack HistoryData] Task(" + taskId + ") not exists, not need to close.");
            }
            pdCommonCache.getAllJobMap().remove(taskId);
        } catch (SchedulerException e1) {
            log.error("[PlayBack HistoryData] closeTask(" + taskId + ") throw SchedulerException:" , e1);
            return false;
        } catch (Exception e2) {
            log.error("[PlayBack HistoryData] closeTask(" + taskId + ") throw unknown Exception:" , e2);
            return false;
        }
        return true;
    }

    public boolean restartTaskWithNewInterval(int taskId, int newIntervalTime) {

        if(scheduler == null) {
            log.error("[PlayBack HistoryData] restartTaskWithNewInterval stop: scheduler == null");
            return false;
        }
        boolean closeResult = closeTask(taskId);
        if(closeResult) {
            boolean createResult = createNewTask(taskId, newIntervalTime);
            if(createResult) {
                log.info("[PlayBack HistoryData] restartTask(" + taskId + ") success. newIntervalTime=" + newIntervalTime);
            } else {
                log.error("[PlayBack HistoryData] restartTaskWithNewInterval(" + taskId + "," + newIntervalTime + ") createNewTask fail:" + createResult);
                return false;
            }
        } else {
            log.error("[PlayBack HistoryData] restartTaskWithNewInterval(" + taskId + "," + newIntervalTime + ") close fail:" + closeResult);
            return false;
        }
        return true;
    }

    private String getJobKeyByTaskId(int taskId) {
        return "job_" + taskId;
    }
    private String getTriggerKeyByTaskId(int taskId) {
        return "trigger_" + taskId;
    }

    public int getTotalTaskCount() {
        try {
            return scheduler == null ? -2 : scheduler.getJobKeys(GroupMatcher.anyJobGroup()).size();
        } catch (Exception e) {
            log.error("[PlayBack HistoryData] getTotalTaskCount() throw exception" , e);
            return -1;
        }
    }

    public String getPropertiesInfo() {
        try {
            if(backupProps != null && backupProps.size() > 0) {
                StringBuilder str = new StringBuilder("[Properties]:  \n");
                for (Map.Entry<Object, Object> entry : backupProps.entrySet()) {
                    str.append("    ").append(entry.getKey()).append(" : ").append(entry.getValue()).append("  \n");
                }
                return str.toString();
            } else {
                return "[Properties]: -2";
            }
        } catch (Exception e) {
            log.error("[PlayBack HistoryData] getPropertiesInfo() throw exception" , e);
            return "[Properties]: -1";
        }
    }

    public String getAllTaskInfo() {

        try {
            StringBuilder str = new StringBuilder("[Task Info] :  \n");
            if(scheduler == null) return "[Task Info] : -2";
            Set<JobKey> jobKeys = scheduler.getJobKeys(GroupMatcher.anyJobGroup());
            for (JobKey jobKey : jobKeys) {
                JobDetail jobDetail = scheduler.getJobDetail(jobKey);
                TriggerKey triggerKey = scheduler.getTriggersOfJob(jobKey).get(0).getKey();//现今都只有一个Trigger
                Trigger trigger = scheduler.getTrigger(triggerKey);
                str.append("    ").append(jobDetail.getKey()).append(" : RepeatInterval = ").
                        append(((SimpleTrigger) trigger).getRepeatInterval()).append("  \n");
            }
            return str.toString();
        } catch (SchedulerException e) {
            log.error("[PlayBack HistoryData] getAllTaskInfo() throw exception" , e);
            return "Task Info : -1";
        }
    }

    /**
     * 检查此配置图的快照存储任务是否已启动（如果启动顺带返回存储周期）
     * @param diagramId 配电图id
     * @return Pair.value0: 1-表示已启动，0-表示未启动，其他-未知；
     *         Pair.value1: 如果启动，此处保存存储周期（单位：毫秒）
     */
    public Pair<Integer, Long> taskRunningState(int diagramId) {
        try {
            int state = 0;
            long intervalTime = -9999;
            if(scheduler == null) return new Pair<>(-1, -1L);
            JobKey jobKey = JobKey.jobKey(getJobKeyByTaskId(diagramId), JOB_GROUP);
            if(scheduler.checkExists(jobKey)) {
                TriggerKey triggerKey = TriggerKey.triggerKey(getTriggerKeyByTaskId(diagramId), TRIGGER_GROUP);
                Trigger trigger = scheduler.getTrigger(triggerKey);
                intervalTime = ((SimpleTrigger) trigger).getRepeatInterval();
                state = 1;
            }
            return new Pair<>(state, intervalTime);
        } catch (Exception e) {
            log.error("get task run state throw Exception: ", e);
            return new Pair<>(-2, -999L);
        }
    }

    public PowerDistributionDiagramManager getDiagramManager() {
        return diagramManager;
    }
    public PDCommonCache getPdCommonCache() {
        return pdCommonCache;
    }
}
