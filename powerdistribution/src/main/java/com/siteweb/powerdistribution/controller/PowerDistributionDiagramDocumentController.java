package com.siteweb.powerdistribution.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.powerdistribution.business.DiagramRunner;
import com.siteweb.powerdistribution.business.NodeObject;
import com.siteweb.powerdistribution.business.protocols.MessagePacket;
import com.siteweb.powerdistribution.common.PDCommonCache;
import com.siteweb.powerdistribution.common.RunnerContext;
import com.siteweb.powerdistribution.domain.document.DesignerDocument;
import com.siteweb.powerdistribution.domain.document.GraphicDocument;
import com.siteweb.powerdistribution.domain.enums.MessageCommand;
import com.siteweb.powerdistribution.schedule.playback.ScheduledTaskManager;
import com.siteweb.powerdistribution.services.DistributionFileService;
import com.siteweb.powerdistribution.services.HistoryDataService;
import com.siteweb.powerdistribution.services.SimulationPowerAttachedService;
import com.siteweb.powerdistribution.startup.PowerDistributionDiagramManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.core.io.UrlResource;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.UUID;

@RestController
@RequestMapping("/api")
@Api(tags = {"配电图管理控制器"})
public class PowerDistributionDiagramDocumentController {
    private final Logger log = LoggerFactory.getLogger(PowerDistributionDiagramDocumentController.class);
    @Autowired
    private PowerDistributionDiagramManager diagramManager;


    @Autowired
    public DistributionFileService distributionFileService;
    @Autowired
    public HistoryDataService historyDataService;
    @Autowired
    public PDCommonCache pdCommonCache;
    @Autowired
    private SimulationPowerAttachedService simulationPowerAttachedService;

    /**
     * 读取配电图配置
     *
     * @param diagramId 配电图ID
     * @return 配电图配置文档
     */
    @ApiOperation("读取配电图配置")
    @GetMapping(value = "/readpowerdistributiondocument", params = {"diagramId"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> readDocument(@RequestParam(required = true) int diagramId) {
        DiagramRunner diagramRunner = diagramManager.getDiagram(diagramId);
        if (diagramRunner != null) {
            return ResponseHelper.successful(diagramRunner.getContext().getDocument());
        }
        return ResponseHelper.failed("-1", "没有找到配电图。", HttpStatus.BAD_REQUEST);
    }


    /**
     * 保存配电图配置
     *
     * @param diagramId 配电图ID
     * @param document  配电图配置文档
     * @return
     */
    @ApiOperation("保存并重新加载配电图")
    @PostMapping(value = "/storepowerdistributiondocument", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> storeDocument(@RequestParam(required = true) int diagramId, @Valid @RequestBody DesignerDocument document) {
        DiagramRunner diagramRunner = diagramManager.getDiagram(diagramId);
        try {
            if (diagramRunner != null) {
                RunnerContext context = diagramRunner.getContext();
                diagramRunner.getTask().cancel();
                diagramRunner.loadConfigure(document);
                this.distributionFileService.saveDiagram(document, diagramId);
                diagramRunner.onProcess(0);
                diagramRunner.onProcess(1);
                context.getSubscriber().clearMessage();
                MessagePacket<GraphicDocument> mp = new MessagePacket<>(MessageCommand.ReLoad, diagramRunner.getGraphicDocument());
                context.getSubscriber().broadcast(mp);
                if (context.getSubscriber().numberOfConnections() > 0) {
                    diagramRunner.getTask().run();
                }
                historyDataService.ReStartTaskByDiagramRunner(diagramRunner);
                return ResponseHelper.successful(true);
            }
            return ResponseHelper.failed("-1", "没有找到配电图。", HttpStatus.NOT_FOUND);
        } catch (Exception e) {
            if(diagramRunner != null) {//异常时重新load缓存数据
                pdCommonCache.reloadDiagramCache(diagramRunner);
            }
            log.error("error:{}",e);
            return ResponseHelper.failed("-1", e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @ApiOperation("获取配电图元件（测试）")
    @GetMapping(value = "/readpowerdistributiondocument", params = {"diagramId", "uuid"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getDocumentNodeObject(@RequestParam(required = true) int diagramId, @RequestParam String uuid) {
        DiagramRunner diagramRunner = diagramManager.getDiagram(diagramId);
        NodeObject nodeObject = diagramRunner.getChild(UUID.fromString(uuid));
        return ResponseHelper.successful(nodeObject);
    }


    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(Exception.class)
    public String handleException(Exception e) {
        return e.getMessage();
    }

    @ApiOperation("获取控制信号当前状态值")
    @GetMapping(value = "/getcontrolsignalvalue",  produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getControlSignalValue(@RequestParam Integer stationId, @RequestParam Integer equipmentId,@RequestParam Integer controlId) {
        return ResponseHelper.successful(simulationPowerAttachedService.getControlSignalValue(stationId,equipmentId,controlId));
    }

}