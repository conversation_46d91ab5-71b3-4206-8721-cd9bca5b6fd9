<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.powerdistribution.mapper.SimulationPowerRecordMapper">
    <insert id="addReportInfo">
        INSERT INTO powersimulationrecord (
        recordName,
        diagramId,
        nodeId,
        onOffState,
        downloadPath,
        attributeObjects,
        reportName)
        VALUES
        (#{recordName},#{diagramId},#{nodeId},#{onOffState},#{downloadPath},#{attributeObjects},#{reportName}
        );
    </insert>
    <delete id="deleteRecordById">
        delete from powersimulationrecord where recordId = #{recordId};
    </delete>
    <select id="getReportById" resultType="com.siteweb.powerdistribution.model.SimulationPowerRecordParams">
        select * from powersimulationrecord where recordId = #{recordId};
    </select>
    <select id="getAllRecord" resultType="com.siteweb.powerdistribution.model.SimulationPowerRecordParams">
        select * from powersimulationrecord where diagramId = #{diagramId} order by recordId desc;
    </select>
    <select id="getMaxRecordId" resultType="java.lang.Integer">
        select max(recordId) from powersimulationrecord;
    </select>
    <select id="getLastReport" resultType="com.siteweb.powerdistribution.model.SimulationPowerRecordParams">
        select * from powersimulationrecord order by recordId desc Limit 1;
    </select>
</mapper>