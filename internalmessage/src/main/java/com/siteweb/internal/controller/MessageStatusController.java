package com.siteweb.internal.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.internal.service.MessageStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/messagestatus")
public class MessageStatusController {
    @Autowired
    MessageStatusService messageStatusService;

    @GetMapping(value = "/readmessage",params = "internalMessageId")
    public ResponseEntity<ResponseResult> readMessage(Integer internalMessageId) {
        Integer userId = TokenUserUtil.getLoginUserId();
        return ResponseHelper.successful(messageStatusService.readMessage(userId, internalMessageId));
    }
}
