package com.siteweb.utility.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.utility.constans.StandardCategoryEnum;
import com.siteweb.utility.constans.SysConfigEnum;
import com.siteweb.utility.entity.StandardType;
import com.siteweb.utility.entity.SysConfig;
import com.siteweb.utility.mapper.StandardTypeMapper;
import com.siteweb.utility.service.StandardVerService;
import com.siteweb.utility.service.SysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.Objects;


/**
 * <AUTHOR> zhou
 * @description StandardVerServiceImpl
 * @createTime 2022-08-10 14:08:06
 */
@Service
public class StandardVerServiceImpl implements StandardVerService {

    @Autowired
    NamedParameterJdbcTemplate jdbcTemplate;
    @Autowired
    SysConfigService sysConfigService;
    @Autowired
    StandardTypeMapper standardTypeMapper;

    @Override
    public int getStandardVer() {
        String sql = "SELECT B.StandardId FROM TBL_SysConfig A INNER JOIN TBL_StandardType B ON A.ConfigValue = B.StandardAlias WHERE A.ConfigKey = 'StandardVer'";
        final int[] standardIds = new int[1];
        standardIds[0] = 0;
        jdbcTemplate.query(sql, rs -> {
            standardIds[0] = rs.getInt("StandardId");
        });
        return standardIds[0];
    }

    @Override
    public StandardCategoryEnum getCurrentStandardType() {
        SysConfig sysConfig = sysConfigService.findByConfigKey(SysConfigEnum.STANDARD_VER.getConfigKey());
        if (Objects.isNull(sysConfig) || CharSequenceUtil.isBlank(sysConfig.getConfigValue())) {
            //没有设置b接口类型
            return StandardCategoryEnum.EMR;
        }
        StandardType standardType = findByStandardAlias(sysConfig.getConfigValue());
        if (Objects.isNull(standardType) || Objects.isNull(standardType.getStandardId())) {
            //没有设置b接口类型
            return StandardCategoryEnum.EMR;
        }
        return StandardCategoryEnum.getByValue(standardType.getStandardId());
    }

    private StandardType findByStandardId(Integer standardId) {
        return standardTypeMapper.selectOne(Wrappers.lambdaQuery(StandardType.class).eq(StandardType::getStandardId, standardId));
    }
    private StandardType findByStandardAlias(String alias) {
        return standardTypeMapper.selectOne(Wrappers.lambdaQuery(StandardType.class)
                                                    .eq(StandardType::getStandardAlias, alias));
    }
}
