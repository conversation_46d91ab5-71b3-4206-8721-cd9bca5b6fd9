<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.eventconvergence.mapper.ConvergenceEventMapper">
    <update id="batchConfirmEvent">
        update ConvergenceEvent
        set confirmTime=now(), confirmerId=#{userId}, confirmerName =#{userName}
        where id in
        <foreach collection="list" item="groupId" open="(" close=")" separator=",">
            #{groupId}
        </foreach>
    </update>

    <select id="findConvergenceEventsByConvergenceTypeAndStatus"
            resultType="com.siteweb.eventconvergence.entity.ConvergenceEvent">
        SELECT *
        FROM convergenceevent
        WHERE ConvergenceType=#{convergenceType} AND Status= #{status};
    </select>
    <select id="getEventConvergenceDailyStatisticsFromActiveEvent"
            resultType="com.siteweb.eventconvergence.dto.EventConvergenceDailyStatistics">
        SELECT TO_CHAR(startTime, 'MMDD')                              AS statisticsDate,
               COUNT(*)                                                AS totalCount,
               SUM(CASE WHEN ConvergenceEventId = 0 THEN 0 ELSE 1 END) AS convergenceCount
        FROM TBL_ActiveEvent
        WHERE startTime &gt;= #{startDay}
          AND startTime &lt; #{endDay}
        GROUP BY statisticsDate;
    </select>
    <select id="getEventConvergenceDailyStatisticsFromHistoryEvent"
            resultType="com.siteweb.eventconvergence.dto.EventConvergenceDailyStatistics">
        SELECT TO_CHAR(startTime, 'MMDD')                              AS statisticsDate,
               COUNT(*)                                                AS totalCount,
               SUM(CASE WHEN ConvergenceEventId = 0 THEN 0 ELSE 1 END) AS convergenceCount
        FROM TBL_HistoryEvent
        WHERE startTime &gt;= #{startDay}
          AND startTime &lt; #{endDay}
        GROUP BY statisticsDate;
    </select>

    <select id="getActiveEventSequenceIdByConvergenceEventId" resultType="java.lang.String">
        select SequenceId from TBL_ActiveEvent where ConvergenceEventId = #{convergenceEventId}
    </select>
    <select id="findConvergenceEvents" resultType="com.siteweb.eventconvergence.dto.ConvergenceEventDTO">
        SELECT a.Id,
        a.EventName,
        a.ConvergenceType,
        a.ConvergenceRuleId,
        a.BirthTime,
        a.ConvergenceCount,
        a.Status,
        a.ConfirmTime,
        a.PossibleCauses,
        a.EquipmentId,
        a.EventId,
        a.EventConditionId,
        a.ClearTime,
        a.ConfirmerId,
        a.ConfirmerName,
        SUM(case when b.SequenceId is not null then 1 else 0 end) AS liveCount
        FROM ConvergenceEvent a
        LEFT JOIN tbl_activeEvent b ON a.Id = b.ConvergenceEventId and b.endTime is null
        WHERE a.BirthTime &gt; #{startDay}
        AND a.BirthTime &lt;= #{endDay}
        AND a.ConvergenceCount &gt; 0
        GROUP BY a.Id, a.EventName, a.ConvergenceType, a.ConvergenceRuleId, a.BirthTime, a.ConvergenceCount, a.Status, a.ConfirmTime, a.PossibleCauses, a.EquipmentId, a.EventId, a.EventConditionId, a.ClearTime, a.ConfirmerId, a.ConfirmerName
        ORDER BY a.BirthTime DESC
    </select>
    <select id="findConvergedEventsPageable" resultType="com.siteweb.eventconvergence.dto.ConvergedEventDTO">
        SELECT startTime as birthTime,EndTime, EquipmentName,EventName,BaseTypeName,Meanings, ConvergenceEventId
        FROM ((SELECT startTime, EndTime, EquipmentName, EventName, Meanings, BaseTypeName, ConvergenceEventId
               FROM tbl_activeevent
               WHERE ConvergenceEventId &gt; 0
                 AND startTime &gt;= TO_TIMESTAMP(#{startTime},'YYYY-MM-DD HH24:MI:SS')
                 AND startTime &lt;= TO_TIMESTAMP(#{endTime},'YYYY-MM-DD HH24:MI:SS')
                 AND (EquipmentName LIKE CONCAT('%', #{keywords}, '%') OR
                      EventName LIKE CONCAT('%', #{keywords}, '%') OR BaseTypeName LIKE CONCAT('%', #{keywords}, '%'))
               ORDER BY startTime DESC)
              UNION ALL
              (SELECT startTime, EndTime, EquipmentName, EventName, BaseTypeName, Meanings, ConvergenceEventId
               FROM TBL_historyevent
               WHERE ConvergenceEventId &gt; 0
                 AND startTime &gt;= TO_TIMESTAMP(#{startTime},'YYYY-MM-DD HH24:MI:SS')
                 AND startTime &lt;= TO_TIMESTAMP(#{endTime},'YYYY-MM-DD HH24:MI:SS')
                 AND (EquipmentName LIKE CONCAT('%', #{keywords}, '%') OR
                      EventName LIKE CONCAT('%', #{keywords}, '%') OR
                      BaseTypeName LIKE CONCAT('%', #{keywords}, '%'))
               ORDER BY startTime DESC)) AS a
        order by startTime desc
    </select>

    <update id="processConvergenceSecondary">
        UPDATE TBL_ActiveEvent a
        SET ConvergenceEventId = #{id}
        WHERE a.ConvergenceEventId = 0
        AND a.EquipmentId IN (${equipmentId})
        AND a.StartTime &gt; TO_TIMESTAMP(#{birthTime},'YYYY-MM-DD HH24:MI:SS') - INTERVAL '1 minute'
        AND a.StartTime &lt;= TO_TIMESTAMP(#{birthTime},'YYYY-MM-DD HH24:MI:SS') + INTERVAL '5 minutes';
    </update>

</mapper>