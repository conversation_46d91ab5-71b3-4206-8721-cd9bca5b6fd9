package com.siteweb.service;

import com.siteweb.domain.DimensionModel;
import com.siteweb.dto.DimensionModelDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface DimensionModelService {

    List<DimensionModel> findDimensionModels();

    DimensionModel createDimensionModel(DimensionModelDTO dimensionModelDTO);

    DimensionModel createDimensionModel(Integer dimensionModelCategory, MultipartFile file);

    void deleteById(Integer dimensionModelId);

    DimensionModel updateDimensionModel(DimensionModel dimensionModel);

    DimensionModel findById(Integer dimensionModelId);

    List<DimensionModel> findDimensionModelsByDimensionModelCategory(Integer dimensionModelCategory);

    DimensionModel deleteDimensionModel(String fileName);

}

