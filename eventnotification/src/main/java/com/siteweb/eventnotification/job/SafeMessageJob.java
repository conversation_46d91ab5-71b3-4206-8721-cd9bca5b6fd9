package com.siteweb.eventnotification.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.admin.entity.Employee;
import com.siteweb.admin.service.EmployeeService;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.eventnotification.entity.SafeMessage;
import com.siteweb.eventnotification.service.SafeMessageContentService;
import com.siteweb.eventnotification.service.SafeMessageRecordService;
import com.siteweb.eventnotification.service.SafeMessageService;
import com.siteweb.utility.dto.PhoneSmsMsgRequestDTO;
import com.siteweb.utility.quartz.job.BaseJob;
import com.siteweb.utility.service.HAStatusService;
import com.siteweb.utility.util.MessageSendUtil;
import lombok.extern.slf4j.Slf4j;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SafeMessageJob implements BaseJob {
    /**
     * 默认发送方式 短信发送
     */
    public static final String DEFAULT_SEND_MOD = "1";
    @Autowired
    private SafeMessageContentService safeMessageContentService;
    @Autowired
    private SafeMessageRecordService safeMessageRecordService;
    @Autowired
    private SafeMessageService safeMessageService;
    @Autowired
    private EmployeeService employeeService;
    @Autowired
    private MessageSendUtil messageSendUtil;
    @Autowired
    private LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    HAStatusService haStatusService;
    @Override
    public void execute(JobExecutionContext context) {
        if (!haStatusService.isMasterHost()) {
            log.info("不是主机 退出定时任务");
            return;
        }
        JobDataMap params = context.getJobDetail().getJobDataMap();
        if (CollUtil.isEmpty(params)) {
            return;
        }
        int safeMessageId = params.getInt("safeMessageId");
        log.info("{}，safeMessageId:{}，开始执行平安消息逻辑", DateUtil.now(), safeMessageId);
        SafeMessage safeMessage = safeMessageService.findById(safeMessageId);
        if (ObjectUtil.isNull(safeMessage) || !Boolean.TRUE.equals(safeMessage.getUsedStatus())) {
            log.error("safeMessageId：{}，平安短信配置不存在或未开启，请检查配置", safeMessageId);
            return;
        }
        //发送平安消息
        this.sendSafeMessage(safeMessage);
    }

    /**
     * 发送平安短信
     *
     * @param safeMessage 平安短信配置
     */
    public void sendSafeMessage(SafeMessage safeMessage) {
        //为了兼容老版本，如果未配置发送方式，默认配置为1，发送平安短信通知
        if (CharSequenceUtil.isBlank(safeMessage.getReceiveMode())) {
            safeMessage.setReceiveMode(DEFAULT_SEND_MOD);
        }
        //解析模板
        String content = safeMessageContentService.analysisSafeMessageTemplate(safeMessage.getContentTemplate());
        List<Employee> employeeList = getReceiver(safeMessage.getReceiver());
        List<Integer> receiveModeList = StringUtils.splitToIntegerList(safeMessage.getReceiveMode());
        //处理接收方式
        for (Integer receiveMode : receiveModeList) {
            switch (receiveMode) {
                case 1 ->
                    //发送平安短信
                        sendVoiceOrMsm(employeeList, safeMessage.getSafeMessageId(), content, "2");
                case 2 ->
                    //发送平安语音电话
                        sendVoiceOrMsm(employeeList, safeMessage.getSafeMessageId(), content, "1");
                case 3 ->
                    //发送平安邮件
                        sendEmailMessage(employeeList, safeMessage.getSafeMessageId(), content);
                default -> log.error("safeMessageId:{},未知的平安信息发送方式{}", safeMessage.getSafeMessageId(), receiveMode);
            }
        }
    }

    private void sendEmailMessage(List<Employee> employeeList, Integer safeMessageId, String content) {
        String employeeNameJoin = employeeList.stream().map(Employee::getEmployeeName).collect(Collectors.joining(","));
        String emailJoin = employeeList.stream().map(Employee::getEmail).collect(Collectors.joining(";"));
        boolean success = messageSendUtil.sendEmail(emailJoin, messageSourceUtil.getMessage("eventNotification.safeMessage.safeEmail"), content);
        //记录日志
        this.recordSendRecord(safeMessageId, content, employeeNameJoin, success);
    }

    /**
     * 发送语音消息
     *
     * @param employeeList  员工列表
     * @param safeMessageId 安全消息id
     * @param content       内容
     * @param subType       子类型 0 –同时拨打语音和发短信，1- 只拨打语音电话， 2-只发短信
     */
    private void sendVoiceOrMsm(List<Employee> employeeList, Integer safeMessageId, String content, String subType) {
        String employeeNameJoin = employeeList.stream().map(Employee::getEmployeeName).collect(Collectors.joining(","));
        String phoneJoin = employeeList.stream().map(Employee::getPhone).collect(Collectors.joining(","));
        PhoneSmsMsgRequestDTO requestDTO = new PhoneSmsMsgRequestDTO();
        requestDTO.setContent(content);
        requestDTO.setDelimiter(":,");
        requestDTO.setSubtype(subType);
        requestDTO.setTo(String.join(",", phoneJoin));
        //直接发送content内容，网关不作处理
        requestDTO.setIsassemble("2");
        boolean success = messageSendUtil.sendVoiceOrSms(requestDTO);
        //记录日志
        this.recordSendRecord(safeMessageId, content, employeeNameJoin, success);
    }


    /**
     * 记录发送记录
     *
     * @param safeMessageId           平安消息id
     * @param content                 内容
     * @param receiveEmployeeNameJoin 接收人员信息
     * @param success                 是否成功
     */
    private void recordSendRecord(Integer safeMessageId, String content, String receiveEmployeeNameJoin, boolean success) {
        String remark = success ? messageSourceUtil.getMessage("send.result.success") : messageSourceUtil.getMessage("send.result.fail");
        safeMessageRecordService.saveMessageRecord(safeMessageId, content, receiveEmployeeNameJoin, remark);
    }

    /**
     * 获取接收人信息
     * @param receiver 接收人
     * @return {@link List}<{@link Employee}>
     */
    private List<Employee> getReceiver(String receiver) {
        List<Integer> receiverList = StringUtils.splitToIntegerList(receiver);
        return employeeService.findByEmployeeIds(receiverList);
    }
}
