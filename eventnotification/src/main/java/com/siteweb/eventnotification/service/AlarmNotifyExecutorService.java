package com.siteweb.eventnotification.service;

import com.siteweb.eventnotification.dto.AlarmNotifyElementConfigDTO;
import com.siteweb.eventnotification.dto.AlarmNotifyExecutorDTO;

import java.util.HashMap;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifyExecutorService
 * @createTime 2022-07-11 13:47:20
 */
public interface AlarmNotifyExecutorService {

    int alarmBoxAlarmNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap);

    int smsAlarmNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap);

    int emailAlarmNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap);

    int phoneSmsAlarmNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap);

    int weComGroupNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap);

    int LarkNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap);

    int alarmLightAlarmNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap);

    int alarmAppNotifySend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap);
    int alarmAppNotifyByJobNumberSend(AlarmNotifyElementConfigDTO elementConfigDTO, AlarmNotifyExecutorDTO alarmNotifyExecutorDTO, HashMap<Integer, String> nodeExpressionHashMap);
}
