package com.siteweb.eventnotification.service.impl;

import com.siteweb.eventnotification.dto.AlarmNotifySegmentDTO;
import com.siteweb.eventnotification.entity.AlarmNotifySegment;
import com.siteweb.eventnotification.mapper.AlarmNotifySegmentMapper;
import com.siteweb.eventnotification.service.AlarmNotifySegmentService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> zhou
 * @description AlarmNotifySegmentServiceImpl
 * @createTime 2022-04-24 09:27:00
 */
@Service
public class AlarmNotifySegmentServiceImpl implements AlarmNotifySegmentService {

    @Autowired
    AlarmNotifySegmentMapper alarmNotifySegmentMapper;

    @Override
    public List<AlarmNotifySegmentDTO> findAlarmNotifySegmentsByAlarmNotifyConfigId(Integer alarmNotifyConfigId) {
        List<AlarmNotifySegment> segments = alarmNotifySegmentMapper.findAlarmNotifySegmentsByAlarmNotifyConfigId(alarmNotifyConfigId);
        List<AlarmNotifySegmentDTO> segmentDTOList = new ArrayList<>();
        for (AlarmNotifySegment alarmNotifySegment : segments) {
            AlarmNotifySegmentDTO segmentDTO = new AlarmNotifySegmentDTO();
            BeanUtils.copyProperties(alarmNotifySegment, segmentDTO);
            segmentDTOList.add(segmentDTO);
        }
        return segmentDTOList;
    }
}
