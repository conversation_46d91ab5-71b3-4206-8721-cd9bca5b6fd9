package com.siteweb.batchtool.basic.anno;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 驱动结构占位符
 *
 * @Author: lzy
 * @Date: 2022/5/11 13:24
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DriveStructurePlaceholderAnno {
    /**
     * 描述信息
     */
    String value();
}
