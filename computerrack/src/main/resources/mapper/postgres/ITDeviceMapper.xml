<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.computerrack.mapper.ITDeviceMapper">


    <resultMap id="defaultResultMap" type="com.siteweb.computerrack.entity.ITDevice">
        <result property="iTDeviceId" column="iTDeviceId"/>
        <result property="iTDeviceName" column="iTDeviceName"/>
        <result property="iTDeviceModelId" column="iTDeviceModelId"/>
        <result property="computerRackId" column="computerRackId"/>
        <result property="customer" column="customer"/>
        <result property="business" column="business"/>
        <result property="purchaseDate" column="purchaseDate"/>
        <result property="launchDate" column="launchDate"/>
        <result property="uIndex" column="uIndex"/>
        <result property="serialNumber" column="serialNumber"/>
        <result property="remark" column="remark"/>
        <result property="computerRackName" column="computerRackName"/>
        <result property="ipaddr" column="ipaddr"/>
        <association property="itDeviceModel" column="iTDeviceModelId" javaType="com.siteweb.computerrack.entity.ITDeviceModel"
                     select="com.siteweb.computerrack.mapper.ITDeviceModelMapper.findByiITDeviceModelId"/>
    </resultMap>



    <select id="findITDevices" resultMap="defaultResultMap">
        SELECT
        A.ITDeviceId,
        A.SerialNumber,
        A.ITDeviceName,
        A.ITDeviceModelId,
        A.ComputerRackId,
        A.UIndex,
        A.Customer,
        A.Business,
        A.PurchaseDate,
        A.LaunchDate,
        A.Remark,
        A.AssetDeviceId,
        A.ipaddr,
        B.ComputerRackName
        FROM ITDevice A Left JOIN ComputerRack B ON  A.ComputerRackId = B.ComputerRackId
    </select>



    <select id="findByITDeviceName" resultMap="defaultResultMap">
        SELECT
        A.ITDeviceId,
        A.SerialNumber,
        A.ITDeviceName,
        A.ITDeviceModelId,
        A.ComputerRackId,
        A.UIndex,
        A.Customer,
        A.Business,
        A.PurchaseDate,
        A.LaunchDate,
        A.Remark,
        A.ipaddr,
        B.ComputerRackName
        FROM ITDevice A Left JOIN ComputerRack B ON  A.ComputerRackId = B.ComputerRackId
        WHERE A.ITDeviceName = #{itDeviceModelName}
        LIMIT 1
    </select>

    <select id="findByComputerRackId" resultMap="defaultResultMap">
        SELECT
        A.ITDeviceId,
        A.SerialNumber,
        A.ITDeviceName,
        A.ITDeviceModelId,
        A.ComputerRackId,
        A.UIndex,
        A.Customer,
        A.Business,
        A.PurchaseDate,
        A.LaunchDate,
        A.Remark,
        B.ComputerRackName
        FROM ITDevice A Left JOIN ComputerRack B ON  A.ComputerRackId = B.ComputerRackId
        WHERE A.ComputerRackId = #{computerRackId}
    </select>

    <select id="findByComputerRackPos" resultMap="defaultResultMap">
        SELECT
        device.ITDeviceId,
        device.SerialNumber,
        device.ITDeviceName,
        device.ITDeviceModelId,
        device.ComputerRackId,
        device.UIndex,
        device.Customer,
        device.Business,
        device.PurchaseDate,
        device.LaunchDate,
        device.Remark,
        device.ipaddr
        FROM ITDevice device JOIN ITDeviceModel model ON device.ITDeviceModelId = model.ITDeviceModelId
        WHERE ComputerRackId = #{rackId} and device.ITDeviceId = #{itDeviceId} AND device.UIndex = #{uIndex}
        limit 1
    </select>
    <select id="findITDevicesByModelId" resultMap="defaultResultMap">
        SELECT
        A.ITDeviceId,
        A.SerialNumber,
        A.ITDeviceName,
        A.ITDeviceModelId,
        A.ComputerRackId,
        A.UIndex,
        A.Customer,
        A.Business,
        A.PurchaseDate,
        A.LaunchDate,
        A.Remark,
        A.ipaddr,
        B.ComputerRackName
        FROM ITDevice A Left JOIN ComputerRack B ON  A.ComputerRackId = B.ComputerRackId
        WHERE A.ITDeviceModelId = #{iTDeviceModelId}
    </select>
    <select id="findResourceObjectByITDeviceIds" resultType="com.siteweb.monitoring.dto.ResourceObjectEntity">
        SELECT
            it.ITDeviceId as objectId,
            10 as objectTypeId,
            it.ITDeviceName as resourceName,
            resrouce.ResourceStructureId as resourceStructureId,
            resrouce.ResourceStructureId as parentResourceStructureId,
            resrouce.StructureTypeId as parentResourceStructureTypeId
        FROM itdevice it
        INNER JOIN computerrack computer ON it.ComputerRackId = computer.ComputerRackId
        INNER JOIN resourcestructure resrouce ON computer.ResourceStructureId = resrouce.ResourceStructureId
        WHERE it.ITDeviceId IN
        <foreach item="item" collection="iTDeviceIds" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="findResourceObjectByResourceStructureIds"
            resultType="com.siteweb.monitoring.dto.ResourceObjectEntity">
        SELECT
        it.ITDeviceId as objectId,
        10 as objectTypeId,
        it.ITDeviceName as resourceName,
        resrouce.ResourceStructureId as resourceStructureId,
        resrouce.ResourceStructureId as parentResourceStructureId,
        resrouce.StructureTypeId as parentResourceStructureTypeId
        FROM itdevice it
        INNER JOIN computerrack computer ON it.ComputerRackId = computer.ComputerRackId
        INNER JOIN resourcestructure resrouce ON computer.ResourceStructureId = resrouce.ResourceStructureId
        WHERE resrouce.ResourceStructureId IN
        <foreach item="item" collection="resourceStructureIds" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="findITDeviceByResourceStructureId" resultType="com.siteweb.computerrack.entity.ITDevice">
        SELECT it.ITDeviceId,
        it.SerialNumber,
        it.ITDeviceName,
        it.ITDeviceModelId,
        it.ComputerRackId,
        it.UIndex,
        it.Customer,
        it.Business,
        it.PurchaseDate,
        it.LaunchDate,
        it.Remark
        FROM itdevice it
        INNER JOIN computerrack computer ON it.ComputerRackId = computer.ComputerRackId
        INNER JOIN resourcestructure resrouce ON computer.ResourceStructureId = resrouce.ResourceStructureId
        WHERE resrouce.ResourceStructureId IN
        <foreach collection="resourceStructureIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="findITDeviceModelCategoryPieChartByResourceStructureIds"
            resultType="com.siteweb.utility.vo.NameValueVO">
        SELECT
        item.ItemValue as "name",
        count(*) as "value"
        FROM itdevice it
        INNER JOIN computerrack computer ON it.ComputerRackId = computer.ComputerRackId
        INNER JOIN itdevicemodel model ON it.ITDeviceModelId = model.ITDeviceModelId
        INNER JOIN tbl_dataitem item ON item.EntryId = 3003 AND model.categoryId = item.ItemId
        WHERE computer.ResourceStructureId IN
        <foreach collection="resourceStructureIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        GROUP BY item.ItemValue
    </select>

    <select id="findITDeviceTypePieChartByResourceStructureIds" resultType="com.siteweb.utility.vo.NameValueVO">
        SELECT
        model.ITDeviceModelName as "name",
        count(*) as "value"
        FROM itdevice it
        INNER JOIN computerrack computer ON it.ComputerRackId = computer.ComputerRackId
        INNER JOIN resourcestructure resrouce ON computer.ResourceStructureId = resrouce.ResourceStructureId
        INNER JOIN itdevicemodel model ON it.ITDeviceModelId = model.ITDeviceModelId
        WHERE resrouce.ResourceStructureId IN
        <foreach collection="resourceStructureIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        GROUP BY model.ITDeviceModelName
    </select>
    <select id="findUTagDeviceSelect" resultType="com.siteweb.computerrack.dto.UTagDeviceSelectDto">
        SELECT it.ITDeviceId as asserId,
               it.ITDeviceName,
               CASE WHEN tag.UTagId IS NOT NULL THEN 1 ELSE 0 END as bindState
        FROM itdevice it
                 LEFT JOIN tbl_utag tag ON it.ITDeviceId = tag.AsserId;
    </select>
    <select id="findById" resultMap="defaultResultMap">
        SELECT A.ITDeviceId,
               A.SerialNumber,
               A.ITDeviceName,
               A.ITDeviceModelId,
               A.ComputerRackId,
               A.UIndex,
               A.Customer,
               A.Business,
               A.PurchaseDate,
               A.LaunchDate,
               A.Remark,
               A.AssetDeviceId,
               A.ipaddr,
               B.ComputerRackName
        FROM ITDevice A
                 LEFT JOIN ComputerRack B ON A.ComputerRackId = B.ComputerRackId
        WHERE A.ITDeviceId = #{iTDeviceId}
    </select>
    <select id="findITDeviceByIds" resultMap="defaultResultMap">
        SELECT A.ITDeviceId,
        A.SerialNumber,
        A.ITDeviceName,
        A.ITDeviceModelId,
        A.ComputerRackId,
        A.UIndex,
        A.Customer,
        A.Business,
        A.PurchaseDate,
        A.LaunchDate,
        A.Remark,
        A.ipaddr,
        B.ComputerRackName
        FROM ITDevice A
        LEFT JOIN ComputerRack B ON A.ComputerRackId = B.ComputerRackId
        WHERE A.ITDeviceId in
        <foreach collection="iTDeviceIdList" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="findResourceObjectByITDeviceId" resultType="com.siteweb.monitoring.dto.ResourceObjectEntity">
        SELECT it.ITDeviceId                AS objectId,
               10                           AS objectTypeId,
               it.ITDeviceName              AS resourceName,
               resrouce.ResourceStructureId AS resourceStructureId,
               resrouce.ResourceStructureId AS parentResourceStructureId,
               resrouce.StructureTypeId     AS parentResourceStructureTypeId
        FROM itdevice it
                 INNER JOIN computerrack computer ON it.ComputerRackId = computer.ComputerRackId
                 INNER JOIN resourcestructure resrouce ON computer.ResourceStructureId = resrouce.ResourceStructureId
        WHERE it.ITDeviceId = #{iTDeviceId}
    </select>

    <select id="findITDevicesByQuery" resultMap="defaultResultMap">
        select * from (
              SELECT
                  A.ITDeviceId,
                  A.SerialNumber,
                  A.ITDeviceName,
                  A.ITDeviceModelId,
                  A.ComputerRackId,
                  A.UIndex,
                  A.Customer,
                  A.Business,
                  A.PurchaseDate,
                  A.LaunchDate,
                  A.Remark,
                  A.ipaddr,
                  B.ComputerRackName,
                  C.UnitHeight
              FROM ITDevice A Left JOIN ComputerRack B ON  A.ComputerRackId = B.ComputerRackId
                              Left JOIN itdevicemodel C on A.ITDeviceModelId = C.ITDeviceModelId
          ) t
        ${ew.customSqlSegment}
    </select>

    <select id="findIdDeviceByuTagValue" resultMap="defaultResultMap">
        SELECT
            A.ITDeviceId,
            A.SerialNumber,
            A.ITDeviceName,
            A.ITDeviceModelId,
            A.ComputerRackId,
            A.UIndex,
            A.Customer,
            A.Business,
            A.PurchaseDate,
            A.LaunchDate,
            A.Remark,
            A.ipaddr,
            B.ComputerRackName,
            C.UnitHeight
        FROM ITDevice A Left JOIN ComputerRack B ON  A.ComputerRackId = B.ComputerRackId
                        Left JOIN itdevicemodel C on A.ITDeviceModelId = C.ITDeviceModelId
                        INNER JOIN tbl_utag D on D.AsserId = A.ITDeviceId
        where D.TagValue = #{uTagValue}
    </select>
</mapper>