<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.computerrack.mapper.ComputerRackSignalMapper">

    <select id="findComputerRackSignalList" resultType="com.siteweb.computerrack.vo.ComputerRackSignalVO">
        SELECT
        r.ComputerRackId,
        r.ComputerRackName,
        r.ComputerRackNumber,
        r.position AS computerRackPosition,
        m.openExpression,
        m.powerExpression
        FROM
        computerrack r
        LEFT JOIN computerracksignalmap m ON r.ComputerRackId = m.ComputerRackId
        where r.ResourceStructureId in
        <foreach collection="resourceStructureIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>