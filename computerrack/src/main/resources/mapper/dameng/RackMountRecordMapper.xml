<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.computerrack.mapper.RackMountRecordMapper">
    <resultMap id="rackMountRecordResultMap" type="com.siteweb.computerrack.entity.RackMountRecord">
        <id column="Id" property="id"/>
        <result column="RackId" property="computerRackId"/>
        <result column="RackDeviceId" property="iTDeviceId"/>
        <result column="RackPosition" property="uIndex"/>
        <result column="OperateTime" property="operateTime"/>
        <result column="OperateState" property="operateState"/>
        <result column="Expired" property="expired"/>
    </resultMap>
    <select id="findAll" resultType="com.siteweb.computerrack.entity.RackMountRecord"
            resultMap="rackMountRecordResultMap">
        SELECT mountRecord.Id,
        mountRecord.RackId,
        mountRecord.RackDeviceId,
        mountRecord.RackPosition,
        mountRecord.OperateTime,
        mountRecord.OperateState,
        mountRecord.Expired
        FROM tbl_rackmountrecord mountRecord;
    </select>
    <select id="findUnexpired" resultType="com.siteweb.computerrack.entity.RackMountRecord">
        SELECT mountRecord.Id,
        mountRecord.RackId computerRackId,
        mountRecord.RackDeviceId iTDeviceId,
        mountRecord.RackPosition uIndex,
        mountRecord.OperateTime,
        mountRecord.OperateState,
        mountRecord.Expired
        FROM tbl_rackmountrecord mountRecord WHERE mountRecord.Expired is null ORDER BY OperateTime;
    </select>
    <select id="findTopN" resultType="com.siteweb.computerrack.vo.RackChangeRecordVo">
        SELECT
            c.Position as position,
            c.ComputerRackName,
            it.ITDeviceName,
            record.RackPosition as uIndex,
            record.OperateState,
            record.OperateTime
        FROM tbl_rackmountrecord record
                 INNER JOIN itdevice it ON record.RackDeviceId = it.ITDeviceId
                 INNER JOIN computerrack c ON c.ComputerRackId = record.RackId
        <where>
            <if test="resourceStructureIds != null and resourceStructureIds.size > 0">
                AND c.ResourceStructureId IN
                <foreach collection="resourceStructureIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY record.OperateTime DESC
        LIMIT #{top}
    </select>
    <select id="findRackChangeRecord" resultType="com.siteweb.computerrack.vo.RackChangeRecordVo">
        SELECT
        c.Position as position,
        c.ComputerRackName,
        it.ITDeviceName,
        it.Customer,
        it.Business,
        it.PurchaseDate,
        record.RackPosition as uIndex,
        record.OperateState,
        record.OperateTime
        FROM tbl_rackmountrecord record
        INNER JOIN itdevice it ON record.RackDeviceId = it.ITDeviceId
        INNER JOIN computerrack c ON c.ComputerRackId = record.RackId
        WHERE record.OperateTime BETWEEN #{param.startDate} and #{param.endDate}
        <if test="param.position != null and param.position != ''">
            AND c.position LIKE concat('%',#{param.position},'%')
        </if>
        <if test="param.computerRackName != null and param.computerRackName != ''">
            AND c.computerRackName LIKE concat('%',#{param.computerRackName},'%')
        </if>
        <if test="param.iTDeviceName != null and param.iTDeviceName != ''">
            AND it.ITDeviceName LIKE concat('%',#{param.iTDeviceName},'%')
        </if>
        <if test="param.operateState != null and param.operateState > 0">
            AND record.operateState = #{param.operateState}
        </if>
        AND c.ResourceStructureId IN
        <foreach collection="param.resourceStructureIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        ORDER BY record.OperateTime DESC
    </select>

    <update id="batchUpdate">
        <foreach collection="records" item="item" separator=";">
            UPDATE tbl_rackmountrecord SET Expired = #{item.expired} WHERE Id = #{item.id}
        </foreach>
    </update>

</mapper>