<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.computerrack.mapper.UTagMapper">
    <select id="findAll" resultType="com.siteweb.computerrack.vo.UTagVo">
        SELECT tag.UTagId,
               tag.TagValue,
               itdevice.ITDeviceName,
               rack.ComputerRackName,
               rack.Position,
               model.ITDeviceModelName,
               tag.UpdateTime AS bindTime,
               tag.UPosition,
               equipment.EquipmentName
        FROM tbl_utag tag
                 LEFT JOIN itdevice itdevice ON tag.AsserId = itdevice.ITDeviceId
                 LEFT JOIN computerrack rack ON rack.ComputerRackId = itdevice.ComputerRackId
                 LEFT JOIN itdevicemodel model ON itdevice.ITDeviceModelId = model.ITDeviceModelId
                 LEFT JOIN tbl_udevice udevice ON udevice.UDeviceId = tag.UDeviceId
                 LEFT JOIN tbl_equipment equipment ON EquipmentId = udevice.uDeviceNumber
        ORDER BY tag.UpdateTime DESC
    </select>
    <select id="findTagById" resultType="com.siteweb.computerrack.dto.UTagDto">
        SELECT tag.UTagId,
               tag.TagValue,
               tag.AsserId,
               device.ITDeviceName
        FROM tbl_utag tag
                 LEFT JOIN itdevice device ON tag.AsserId = device.ITDeviceId
        WHERE tag.UTagId = #{id}
    </select>
    <select id="findTagRateByResourceStructureIds" resultType="com.siteweb.utility.vo.NameValueVO">
        SELECT
        '标签绑定率' as "name",
        CASE
        WHEN COUNT(i.ITDeviceId) = 0 THEN 0
        ELSE ROUND((100.0 * COUNT(tu.UTagId) / NULLIF(COUNT(i.ITDeviceId), 0)), 2)
        END as "value"
        FROM
        itdevice i
        LEFT JOIN computerrack c ON
        i.ComputerRackId = c.ComputerRackId
        LEFT JOIN resourcestructure r ON r.ResourceStructureId = c.ResourceStructureId
        LEFT JOIN tbl_utag tu ON tu.AsserId = i.ITDeviceId
        where r.ResourceStructureId IN
        <foreach collection="resourceStructureIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>