<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.computerrack.mapper.ComputerRackEquipmentMapper">
    <insert id="batchInsert">
        INSERT INTO computerrackequipmentemap(ComputerRackId, EquipmentId) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.computerRackId},#{item.equipmentId})
        </foreach>
    </insert>
    <select id="findComputerRackEquipmentDTOsByResourceStructureIds" resultType="com.siteweb.computerrack.dto.ComputerRackEquipmentDTO">
        SELECT r.ComputerRackId, r.ComputerRackName, r.ComputerRackNumber, r.position, m.EquipmentId FROM computerrack r
        left join computerrackequipmentemap m
        on r.ComputerRackId = m.ComputerRackId
        where r.ResourceStructureId in
        <foreach collection="resourceStructureIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="findComputerRackEquipmentDTOsById"
            resultType="com.siteweb.computerrack.dto.ComputerRackEquipmentDTO">
        SELECT r.ComputerRackId, r.ComputerRackName, r.ComputerRackNumber, r.position, m.EquipmentId FROM computerrack r
        left join computerrackequipmentemap m
        on r.ComputerRackId = m.ComputerRackId
        where r.ComputerRackId = #{id}
    </select>
</mapper>