<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.computerrack.mapper.ITDeviceModelMapper">


    <select id="findByiITDeviceModelId" resultType="com.siteweb.computerrack.entity.ITDeviceModel">
        SELECT model.ITDeviceModelId,
               model.ITDeviceModelName,
               model.UnitHeight,
               model.Manufactor,
               model.Model,
               model.Brand,
               model.Length,
               model.Width,
               model.Height,
               model.RatePower,
               model.RateCooling,
               model.RateWeight,
               model.ModelFile,
               model.categoryId,
               item.ItemValue as categoryName
        FROM ITDeviceModel model
                 LEFT JOIN tbl_dataitem item ON item.EntryId = 3003 AND model.categoryId = item.ItemId
        WHERE ITDeviceModelId = #{itDeviceModelId}
    </select>


    <select id="findByITDeviceModelName" resultType="com.siteweb.computerrack.entity.ITDeviceModel">
        SELECT
        ITDeviceModelId,
        ITDeviceModelName,
        UnitHeight,
        Manufactor,
        Model,
        Brand,
        Length,
        Width,
        Height,
        RatePower,
        RateCooling,
        RateWeight,
        ModelFile
        FROM ITDeviceModel WHERE ITDeviceModelName = #{itDeviceModelName}
    </select>

    <select id="findReferencesCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM ItDevice WHERE ITDeviceModelId = #{itDeviceModelId}
    </select>
    <select id="findAll" resultType="com.siteweb.computerrack.entity.ITDeviceModel">
        SELECT itdevicemodelid,
               itdevicemodelname,
               unitheight,
               manufactor,
               model,
               brand,
               length,
               width,
               height,
               ratepower,
               ratecooling,
               rateweight,
               modelfile,
               categoryId,
               item.itemvalue as categoryName
        FROM itdevicemodel model
                 LEFT JOIN tbl_dataitem item ON item.EntryId = 3003 AND model.categoryId = item.ItemId
    </select>
</mapper>