<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.computerrack.mapper.UDeviceMapper">
    <select id="findSelect" resultType="com.siteweb.computerrack.vo.UDeviceSelect">
        SELECT a.uDeviceNumber, b.ComputerRackName,if(a.RackId,'true','false') as bindState
        FROM tbl_udevice a
        LEFT JOIN computerrack b ON a.RackId = b.ComputerRackId
        ORDER BY a.RackId
    </select>
    <select id="findAll" resultType="com.siteweb.computerrack.entity.UDevice">
        SELECT a.udeviceid,
               a.udevicenumber,
               a.isonline,
               a.rackid,
               a.modulecnt,
               a.utagcnt,
               a.ipaddr,
               a.swequipmentid,
               a.createtime,
               a.updatetime,
               b.EquipmentName as uDeviceName
        FROM tbl_udevice a
                 INNER JOIN tbl_equipment b ON a.uDeviceNumber = b.EquipmentId
    </select>
</mapper>