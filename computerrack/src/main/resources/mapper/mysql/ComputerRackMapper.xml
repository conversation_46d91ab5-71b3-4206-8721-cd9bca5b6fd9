<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.computerrack.mapper.ComputerRackMapper">

    <resultMap id="defaultResultMap" type="com.siteweb.computerrack.entity.ComputerRack">
        <result property="computerRackId" column="computerRackId"/>
        <result property="computerRackName" column="computerRackName"/>
        <result property="computerRackNumber" column="computerRackNumber"/>
        <result property="resourceStructureId" column="resourceStructureId"/>
        <result property="position" column="position"/>
        <result property="customer" column="customer"/>
        <result property="business" column="business"/>
        <result property="startTime" column="startTime"/>
        <result property="remark" column="remark"/>
        <collection property="itDevices" ofType="com.siteweb.computerrack.entity.ITDevice" column="computerRackId"
                    javaType="ArrayList" select="com.siteweb.computerrack.mapper.ITDeviceMapper.findByComputerRackId"/>
    </resultMap>


    <select id="findSimpleComputerRacks"  resultType="com.siteweb.computerrack.dto.SimpleComputerRackDTO">
        SELECT ComputerRackId, ComputerRackName, ComputerRackNumber FROM ComputerRack WHERE ComputerRackId IN
        <foreach collection="computerRackIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>




    <select id="findByComputerRackNumber" resultMap="defaultResultMap">
        SELECT ComputerRackId, ComputerRackName, ComputerRackNumber,
        ResourceStructureId, Position, Customer, Business, StartTime, Remark
        FROM ComputerRack WHERE ComputerRackNumber = #{computerRackNumber} LIMIT 1
    </select>

    <select id="findByComputerRackName" resultMap="defaultResultMap">
        SELECT ComputerRackId, ComputerRackName, ComputerRackNumber,
        ResourceStructureId, Position, Customer, Business, StartTime, Remark
        FROM ComputerRack WHERE ComputerRackName = #{computerRackName} LIMIT 1
    </select>

    <select id="findByComputerRackId" resultMap="defaultResultMap">
        SELECT ComputerRackId, ComputerRackName, ComputerRackNumber,
        ResourceStructureId, Position, Customer, Business, StartTime, Remark
        FROM ComputerRack WHERE ComputerRackId = #{computerRackId}
    </select>

    <select id="findAllComputerRacks" resultMap="defaultResultMap">
        SELECT ComputerRackId, ComputerRackName, ComputerRackNumber,
        ResourceStructureId, Position, Customer, Business, StartTime, Remark
        FROM ComputerRack
    </select>


    <select id="findByResourceStructureId" resultType="com.siteweb.computerrack.entity.ComputerRack">
        SELECT ComputerRackId, ComputerRackName, ComputerRackNumber,
        ResourceStructureId, Position, Customer, Business, StartTime, Remark
        FROM ComputerRack WHERE ResourceStructureId = #{resourceStructureId}
    </select>
    <select id="getBusinessList" resultType="java.lang.String">
        SELECT DISTINCT business FROM ComputerRack
    </select>
    <select id="getCustomerList" resultType="java.lang.String">
        SELECT DISTINCT customer FROM ComputerRack
    </select>
    <select id="findByCustomer" resultMap="defaultResultMap">
        SELECT ComputerRackId, ComputerRackName, ComputerRackNumber,
        ResourceStructureId, Position, Customer, Business, StartTime, Remark
        FROM ComputerRack WHERE Customer = #{customer}
    </select>
    <select id="findByResourceStructureIdIn" resultType="com.siteweb.computerrack.entity.ComputerRack">
        SELECT ComputerRackId, ComputerRackName, ComputerRackNumber,
        ResourceStructureId, Position, Customer, Business, StartTime, Remark
        FROM ComputerRack WHERE ResourceStructureId IN
        <foreach item="item" collection="resourceStructureIdList" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="findByComputerRackIdIn" resultMap="defaultResultMap">
        SELECT ComputerRackId, ComputerRackName, ComputerRackNumber,
        ResourceStructureId, Position, Customer, Business, StartTime, Remark
        FROM ComputerRack WHERE ComputerRackId IN
        <foreach item="item" collection="computerrackIdList" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="findRackUIndexAttribute" resultType="com.siteweb.computerrack.vo.RackUIndexRateVo">
        SELECT rack.ComputerRackName as computerRackName,
        rack.Position as position,
        ifnull(capacity.`percent`,0) as uIndexPercent
        FROM computerrack rack
        INNER JOIN resourcestructure resource ON rack.ResourceStructureId = resource.ResourceStructureId
        LEFT JOIN capacityattribute capacity ON capacity.ObjectId = rack.ComputerRackId AND capacity.ObjectTypeId = 9 AND capacity.BaseAttributeId = 2000000
        WHERE resource.ResourceStructureId IN
        <foreach collection="resourceStructureIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        ORDER BY uIndexPercent desc
    </select>


    <update id="batchUpdate">
        <foreach collection="records" item="item" separator=";">
            UPDATE ComputerRack SET ComputerRackName = #{item.expired},
            ComputerRackNumber = #{item.computerRackNumber},
            ResourceStructureId = #{item.resourceStructureId},
            Position = #{item.position},
            Customer = #{item.customer},
            Business = #{item.business},
            StartTime = #{item.startTime},
            Remark = #{item.remark},
            WHERE ComputerRackId = #{item.computerRackId}
        </foreach>
    </update>

    <update id="batchUpdateCustomerByIds">
        UPDATE ComputerRack SET Customer = #{customer}
        WHERE ComputerRackId in
        <foreach collection="computerrackIdList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
    <update id="batchUpdatePositionByIds">
        UPDATE ComputerRack SET Position = #{fullPath}, ResourceStructureId = #{resourceStructureId}
        WHERE ComputerRackId in
        <foreach collection="computerrackIdList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <select id="findAllCustomersByResourceStructureIds" resultType="java.lang.String">
        SELECT DISTINCT Customer FROM ComputerRack
        WHERE ResourceStructureId IN
        <foreach collection="resourceStructureIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="findAllBusinessByResourceStructureIds" resultType="java.lang.String">
        SELECT DISTINCT Business FROM ComputerRack
        WHERE ResourceStructureId IN
        <foreach collection="resourceStructureIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="findComputerRackDTO" resultType="com.siteweb.computerrack.dto.ComputerRackDTO">
        SELECT a.ComputerRackId,
               a.ComputerRackName,
               a.ComputerRackNumber,
               a.ResourceStructureId,
               a.Customer,
               a.Business,
               a.StartTime,
               a.Remark,
               b.uDeviceNumber,
               c.EquipmentName as uDeviceName
        FROM computerrack a
                 LEFT JOIN tbl_udevice b ON a.ComputerRackId = b.RackId
                 LEFT JOIN tbl_equipment c ON b.uDeviceNumber = c.EquipmentId
    </select>
</mapper>