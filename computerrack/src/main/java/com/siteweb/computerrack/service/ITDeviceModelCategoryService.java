package com.siteweb.computerrack.service;

import com.siteweb.computerrack.dto.ITDeviceModelCategoryDTO;

import java.util.List;

public interface ITDeviceModelCategoryService {
    List<ITDeviceModelCategoryDTO> findAll();
    boolean existsName(String name);

    int create(ITDeviceModelCategoryDTO itDeviceModelCategoryDTO);

    int deleteById(Integer id);

    int updateById(ITDeviceModelCategoryDTO itDeviceModelCategoryDTO);

    boolean existsDependency(Integer id);
}
