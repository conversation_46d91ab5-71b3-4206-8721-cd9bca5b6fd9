package com.siteweb.computerrack.service;

import com.siteweb.computerrack.dto.BoolStringResponse;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.computerrack.dto.ComputerRackSignaImportDTO;
import com.siteweb.computerrack.entity.ComputerRackSignalMap;
import com.siteweb.computerrack.vo.ComputerRackSignalVO;
import com.siteweb.utility.dto.ImportErrorInfoDTO;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;

public interface ComputerRackSignalService {

    List<ComputerRackSignalVO> findComputerRackSignalByUserId(Integer userId);

    /**
     * 分页查询机架和信号映射列表
     * @param pageable 分页参数
     * @param keyword 关键字（可选，用于搜索机架名称、机架编号或位置）
     * @param userId 用户ID
     * @return 分页结果
     */
    Page<ComputerRackSignalVO> findComputerRackSignalByPage(Pageable pageable, String keyword, Integer userId);

    void editComputerRacksSignals(ComputerRackSignalMap computerRackSignalMap);

    void deleteByRackIds(String ids);

    List<ImportErrorInfoDTO> importComputerRacksSignals(List<ComputerRackSignaImportDTO> computerRackSignaImports);

    ComputerRackSignalMap findByRackId(Integer rackId);

    Double calculatePowerExpression(String powerExpression);

    Boolean calculateOpenExpression(String openExpression);

    List<ComputerRackSignalMap> findByComputerRackIds(Collection<Integer> computerRackIds);

    /**
     * 校验开通率表达式是否正常
     *
     * @param expression [设备id\信号id]
     * @return id替换成名称
     */
    BoolStringResponse checkOpenExpression(String expression);

    /**
     * 校验功率表达式是否正常
     */
    BoolStringResponse checkPowerExpression(String expression);
}