package com.siteweb.computerrack.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.StringUtils;
import com.siteweb.computerrack.dto.ITDeviceImportDTO;
import com.siteweb.computerrack.dto.ITDeviceListOperateResultDTO;
import com.siteweb.computerrack.dto.ITDeviceOperateDTO;
import com.siteweb.computerrack.dto.ITDeviceOperateResultDTO;
import com.siteweb.computerrack.entity.ITDevice;
import com.siteweb.computerrack.enumeration.ITDeviceOperatingStatus;
import com.siteweb.computerrack.service.ComputerRackService;
import com.siteweb.computerrack.service.ITDeviceOperateService;
import com.siteweb.computerrack.service.ITDeviceService;
import com.siteweb.computerrack.util.MsgUtil;
import com.siteweb.utility.dto.ImportErrorInfoDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;


@RestController
@RequestMapping("/api")
@Api(tags = {"IT设备导入管理器"})
public class ITDeviceOperateController {


    @Autowired
    private ITDeviceOperateService itDeviceOperateService;

    @Autowired
    private ITDeviceService iTDeviceService;

    @Autowired
    private ComputerRackService computerRackService;
    @Autowired
    private MsgUtil msgUtil;


    @ApiOperation("导入IT设备")
    @PostMapping(value = "/itdeviceoperate/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> importITDevices(@Valid @RequestBody List<ITDeviceImportDTO> itDeviceOperateTemplateList) {
        List<ImportErrorInfoDTO> errors = itDeviceOperateService.importList(itDeviceOperateTemplateList);
        return ResponseHelper.successful(errors);
    }


    // 上架
    @ApiOperation("IT设备上架")
    @PutMapping(value = "/itdeviceoperate/put")
    public ResponseEntity<ResponseResult> itDevicePutInShelf(@Valid @RequestBody ITDeviceOperateDTO operateInfo) {
        ITDeviceOperatingStatus result = itDeviceOperateService.putInShelf(operateInfo.itDeviceId, operateInfo.computerRackId, operateInfo.uIndex);
        ITDeviceOperateResultDTO resultDTO = new ITDeviceOperateResultDTO();
        resultDTO.setStatus(result);
        if (result.equals(ITDeviceOperatingStatus.SUCCESS)) {
            ITDevice itDevice = iTDeviceService.findITDevice(operateInfo.itDeviceId);
            resultDTO.setItDevice(itDevice);
            computerRackService.updateCapacityValueByComputerRackId(operateInfo.computerRackId);
        }
        resultDTO.setMsg(msgUtil.getMsgByStatus(resultDTO.getStatus()));
        return ResponseHelper.successful(resultDTO);
    }



    // 下架
    @ApiOperation("IT设备下架")
    @PutMapping(value = "/itdeviceoperate/take")
    public ResponseEntity<ResponseResult> itDeviceTakeFromShelf(@Valid @RequestBody ITDeviceOperateDTO operateInfo) {
        Integer computerRackId = null;
        ITDevice itDevice = iTDeviceService.findITDevice(operateInfo.itDeviceId);
        if (itDevice != null) computerRackId = itDevice.getComputerRackId();
        ITDeviceOperatingStatus result = itDeviceOperateService.takeFromShelf(operateInfo.itDeviceId);
        ITDeviceOperateResultDTO resultDTO = new ITDeviceOperateResultDTO();
        resultDTO.setStatus(result);
        if (result.equals(ITDeviceOperatingStatus.SUCCESS)) {
            if (computerRackId != null) {
                computerRackService.updateCapacityValueByComputerRackId(computerRackId);
            }
            itDevice = iTDeviceService.findITDevice(operateInfo.itDeviceId);
            resultDTO.setItDevice(itDevice);
        }
        resultDTO.setMsg(msgUtil.getMsgByStatus(resultDTO.getStatus()));
        return ResponseHelper.successful(resultDTO);
    }

    @ApiOperation("IT设备批量下架")
    @PutMapping(value = "/itdeviceoperate/batchtake/{ids}")
    public ResponseEntity<ResponseResult> itDeviceTakeFromShelf(@PathVariable String ids) {
        List<Integer> idList = StringUtils.splitToIntegerList(ids);
        List<ITDevice> itDeviceList = iTDeviceService.findITDeviceByIds(idList);
        ITDeviceOperatingStatus result = itDeviceOperateService.takeFromShelf(itDeviceList);
        ITDeviceListOperateResultDTO resultDTO = new ITDeviceListOperateResultDTO();
        resultDTO.setStatus(result);
        if (result.equals(ITDeviceOperatingStatus.SUCCESS)) {
            List<ITDevice> newItDeviceList = iTDeviceService.findITDeviceByIds(idList);
            resultDTO.setItDeviceList(newItDeviceList);
        }
        resultDTO.setMsg(msgUtil.getMsgByStatus(resultDTO.getStatus()));
        return ResponseHelper.successful(resultDTO);
    }
}
