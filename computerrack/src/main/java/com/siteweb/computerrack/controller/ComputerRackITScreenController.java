package com.siteweb.computerrack.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.computerrack.dto.ComputerRackITScreenParam;
import com.siteweb.computerrack.service.ComputerRackITScreenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/api")
@Api(tags = {"IT机柜展板相关接口"})
public class ComputerRackITScreenController {
    @Autowired
    ComputerRackITScreenService computerRackITScreenService;


    @ApiOperation("获取机架信号电力负载率")
    @GetMapping(value = "/computerrackspowerloadrate", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getComputerRacksPowerLoadRate(Integer rackId) {
        return ResponseHelper.successful(computerRackITScreenService.getComputerRacksPowerLoadRate(rackId));
    }

    @ApiOperation("获取机架功率")
    @GetMapping(value = "/computerrackpower", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getComputerRackPower(ComputerRackITScreenParam computerRackITScreenParam) {
        return ResponseHelper.successful(computerRackITScreenService.getComputerRackPower(computerRackITScreenParam));
    }

    @ApiOperation("获取机架功率变化趋势")
    @GetMapping(value = "/computerrackpowertrend", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getComputerRackPowerTrend(ComputerRackITScreenParam computerRackITScreenParam) {
        return ResponseHelper.successful(computerRackITScreenService.getComputerRackPowerTrend(computerRackITScreenParam));
    }

    @ApiOperation("获取机架功率负载率趋势")
    @GetMapping(value = "/computerrackpowerloadrate", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getComputerRackPowerLoadRate(ComputerRackITScreenParam computerRackITScreenParam) {
        return ResponseHelper.successful(computerRackITScreenService.getComputerRackPowerLoadRate(computerRackITScreenParam));
    }


}
