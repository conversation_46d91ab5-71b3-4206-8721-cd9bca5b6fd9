package com.siteweb.computerrack.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.StringUtils;
import com.siteweb.computerrack.service.ComputerRackCapacityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@Api(tags = {"机柜容量信息统计接口"})
@RequestMapping("/api")
public class ComputerRackCapacityController {

    @Autowired
    private ComputerRackCapacityService computerRackCapacityService;


    /**
     * GET  /computerracks : get the ComputerRacks.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of ComputerRacks in body
     */
    @ApiOperation("获取所有房间的机柜容量信息")
    @GetMapping("/computerrack/capacitybyroom")
    public ResponseEntity<ResponseResult> ByRoom() {
        return ResponseHelper.successful(computerRackCapacityService.ByRoom());
    }

    /**
     * GET  /computerracks : get the ComputerRacks.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of ComputerRacks in body
     */
    @ApiOperation("获取所有楼栋下机柜容量统计信息")
    @GetMapping("/computerrack/capacitybybuilding")
    public ResponseEntity<ResponseResult> ByBuilding() {
        return ResponseHelper.successful(computerRackCapacityService.ByBuilding());
    }


    /**
     * GET  /computerracks : get the ComputerRacks.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of ComputerRacks in body
     */
    @ApiOperation("获取给定层级下机柜容量统计信息")
    @GetMapping(value = "/computerrack/capacitybyrids", params = {"rids"})
    public ResponseEntity<ResponseResult> ByResourceStructures(@RequestParam("rids") String rids) {
        List<Integer> resourceStructureIds = StringUtils.getIntegerListByString(rids);
        return ResponseHelper.successful(computerRackCapacityService.ByResourceStructures(resourceStructureIds));
    }

}
