package com.siteweb.computerrack.controller;

import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.computerrack.dto.UTagDto;
import com.siteweb.computerrack.entity.UTag;
import com.siteweb.computerrack.service.UTagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api")
@Api(tags = {"U位标签管理"})
public class UTagController {
    @Autowired
    UTagService uTagService;
    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;

    @ApiOperation("查询标签列表")
    @GetMapping(value = "/utag", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAll() {
        return ResponseHelper.successful(uTagService.findAll());
    }

    @ApiOperation("查询标签根据id")
    @GetMapping(value = "/utag/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getTagById(@PathVariable("id") Integer id) {
        return ResponseHelper.successful(uTagService.findTagById(id));
    }

    @ApiOperation("添加标签")
    @PostMapping(value = "/utag", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createTag(@RequestBody UTagDto uTagDto) {
        if (uTagService.existsByTagValueNoTagId(uTagDto.getTagValue(), null)) {
            return ResponseHelper.failed("1001", localeMessageSourceUtil.getMessage("udevice.utag.valueexists"), HttpStatus.OK);
        }
        return ResponseHelper.successful(uTagService.createTag(uTagDto));
    }

    @ApiOperation("批量添加标签")
    @PostMapping(value = "/utag/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> importTag(@RequestBody List<UTagDto> uTagDtoList) {
        return ResponseHelper.successful(uTagService.createTagList(uTagDtoList));
    }

    @ApiOperation("删除标签")
    @DeleteMapping(value = "/utag/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteTag(@PathVariable("id") Integer id) {
        return ResponseHelper.successful(uTagService.deleteTag(id));
    }

    @ApiOperation("删除标签")
    @PostMapping(value = "/utag/remove/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteRemoveTag(@PathVariable("id") Integer id) {
        return this.deleteTag(id);
    }

    @ApiOperation("编辑标签")
    @PutMapping(value = "/utag", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateTag(@RequestBody UTagDto uTagDto) {
        if (uTagService.existsByTagValueNoTagId(uTagDto.getTagValue(), uTagDto.getuTagId())) {
            return ResponseHelper.failed("1001", localeMessageSourceUtil.getMessage("udevice.utag.valueexists"), HttpStatus.OK);
        }
        return ResponseHelper.successful(uTagService.updateTag(uTagDto));
    }

    @ApiOperation("标签绑定it设备")
    @PostMapping(value = "/utagbinditdevice", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createTagBindItDevice(@RequestBody UTagDto uTagDto) {
        try {
            return ResponseHelper.successful(uTagService.createTagBindItDevice(uTagDto));
        }catch (BusinessException e) {
            return ResponseHelper.failed(e, HttpStatus.OK);
        }
    }

    @ApiOperation("标签解除绑定")
    @RequestMapping(value = "/utagunbind/{id}", produces = MediaType.APPLICATION_JSON_VALUE, method = {RequestMethod.PUT, RequestMethod.POST})
    public ResponseEntity<ResponseResult> utagunbind(@PathVariable("id") Integer asserId) {
        uTagService.setAsserIdIsNull(asserId);
        return ResponseHelper.successful();
    }
}
