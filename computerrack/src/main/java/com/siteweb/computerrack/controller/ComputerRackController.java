package com.siteweb.computerrack.controller;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.computerrack.dto.*;
import com.siteweb.computerrack.service.ComputerRackService;
import com.siteweb.monitoring.entity.ResourceStructure;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


@RestController
@RequestMapping("/api")
@Api(tags = {"机架管理控制器"})
public class ComputerRackController {
    @Autowired
    private ComputerRackService computerRackService;
    private static final String USER_ID_IS_NULL = "userid is null";

    @ApiOperation("获取机架列表")
    @GetMapping(value = "/computerracks", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllComputerRack() {
        return ResponseHelper.successful(computerRackService.findComputerRackDTOs());
    }

    @ApiOperation("获取含有上架IT设备信息的机架列表")
    @GetMapping(value = "/computerracks/details", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findAllDetailedComputerRacks() {
        return ResponseHelper.successful(computerRackService.findAllDetailedComputerRacks());
    }

    @ApiOperation("获取简单的机架数据")
    @PostMapping(value = "/computerracks/simple", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findSimpleComputerRacks(@Valid @RequestBody List<Integer> computerRackIds) {
        return ResponseHelper.successful(computerRackService.findSimpleComputerRacks(computerRackIds));
    }


    @ApiOperation("获取机架列表，有权限")
    @GetMapping(value = "/computerracks", produces = MediaType.APPLICATION_JSON_VALUE, params = {"userId"})
    public ResponseEntity<ResponseResult> getAllComputerRackByUserId(Integer userId) {
        if (userId != null) {
            return ResponseHelper.successful(computerRackService.findComputerRackDTOsByUserId(userId));
        }
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()), USER_ID_IS_NULL, HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(computerRackService.findComputerRackDTOsByUserId(loginUserId));
    }

    @ApiOperation("根据id获取机架")
    @GetMapping(value = "/computerracks/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getComputerRackById(@PathVariable("id") Integer id) {
        return ResponseHelper.successful(computerRackService.findComputerRackDTO(id));
    }


    @ApiOperation("根据id列表获取机架")
    @GetMapping(value = "/computerracks", params = {"computerRackIdList"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getComputersRackByIds(@RequestParam("computerRackIdList") String computerRackIdList) {
        return ResponseHelper.successful(computerRackService.findDTOByComputerRackIdList(computerRackIdList));
    }


    @ApiOperation("获取层级下所有机架")
    @GetMapping(value = "/computerracks", params = {"resourceStructureId"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getComputersRackByStructure(@RequestParam(value = "resourceStructureId", required = true) Integer resourceStructureId) {
        return ResponseHelper.successful(computerRackService.findComputerRackDTOs(resourceStructureId, true));
    }


    @ApiOperation("创建机架")
    @PostMapping(value = "/computerracks", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createComputerRack(@Valid @RequestBody ComputerRackImportDTO computerRackDTO) {
        return ResponseHelper.successful(computerRackService.createComputerRack(computerRackDTO));
    }


    @ApiOperation("批量导入机架")
    @PostMapping(value = "/computerracks/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> importComputerRacks(@Valid @RequestBody List<ComputerRackImportDTO> computerRackDTOList) {
        return ResponseHelper.successful(computerRackService.createComputerRackList(computerRackDTOList));
    }


    @ApiOperation("修改机架")
    @PutMapping(value = "/computerracks", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateComputerRack(@Valid @RequestBody ComputerRackDTO computerRack) {
        return ResponseHelper.successful(computerRackService.updateComputerRack(computerRack));
    }


    @ApiOperation("删除机架")
    @DeleteMapping(value = "/computerracks/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteComputerRack(@PathVariable Integer id) {
        computerRackService.deleteById(id);
        return ResponseHelper.successful(HttpStatus.OK);
    }

    @ApiOperation("批量删除机架")
    @DeleteMapping(value = "/computerracks", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteBatch(@RequestParam List<Integer> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        computerRackService.deleteBatch(ids);
        return ResponseHelper.successful();
    }

    /**
     * GET  /computerrack rooms .
     *
     * @return the ResponseEntity with status 200 (OK) and the list of ComputerRacks in body
     */
    @ApiOperation("获取放置有机架的所有层级")
    @GetMapping("/computerracks/rooms")
    public List<ResourceStructure> getComputerRacks() {
        List<ResourceStructure> rooms = computerRackService.findAllResourceStructure();
        return rooms;
    }

    @ApiOperation("批量设置客户")
    @PutMapping(value = "/computerracks/customer", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchUpdateCustomer(@Valid @RequestBody ComputerRackCustomerRequestDTO computerRackCustomerRequestDTO) {
        return ResponseHelper.successful(computerRackService.batchUpdateCustomer(computerRackCustomerRequestDTO));
    }

    @ApiOperation("批量设置位置")
    @PutMapping(value = "/computerracks/position", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchUpdatePosition(@Valid @RequestBody ComputerRackPositionRequestDTO computerRackPositionRequestDTO) {
        return ResponseHelper.successful(computerRackService.batchUpdatePosition(computerRackPositionRequestDTO));
    }

    @ApiOperation("批量设置容量")
    @PutMapping(value = "/computerracks/capacity", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchUpdateCapacity(@RequestBody ComputerRackCapacityRequestDTO computerRackCapacityRequestDTO) {
        computerRackService.batchUpdateCapacity(computerRackCapacityRequestDTO);
        return ResponseHelper.successful();
    }

    @ApiOperation("通过层级ids获取机架容量统计")
    @GetMapping(value = "/capacity/attributes", params = "resourceStructureIds")
    public ResponseEntity<ResponseResult> getCapacityAttributeByResourceStructureIds(String resourceStructureIds) {
        return ResponseHelper.successful(computerRackService.findAttributesByResourceStructureIds(resourceStructureIds));
    }

    @ApiOperation("获取机架的所有客户")
    @GetMapping("/computerracks/customers")
    public ResponseEntity<ResponseResult> findAllCustomers() {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()), USER_ID_IS_NULL, HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(computerRackService.findAllCustomersByUserId(loginUserId));
    }

    @ApiOperation("获取机架的所有业务")
    @GetMapping("/computerracks/business")
    public ResponseEntity<ResponseResult> findAllBusiness() {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()), USER_ID_IS_NULL, HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(computerRackService.findAllBusinessByUserId(loginUserId));
    }
}
