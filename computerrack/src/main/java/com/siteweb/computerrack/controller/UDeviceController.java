package com.siteweb.computerrack.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.computerrack.entity.UDevice;
import com.siteweb.computerrack.service.UDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api")
@Api(tags = {"U位管理"})
public class UDeviceController {
    @Autowired
    UDeviceService uDeviceService;

    @ApiOperation("查询所有u位设备以及绑定状态")
    @GetMapping(value = "/udevice", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAll() {
        return ResponseHelper.successful(uDeviceService.findAll());
    }

    @ApiOperation("查询u位设备的下拉选项(用于绑定机架)")
    @GetMapping(value = "/udevice/select", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getSelect() {
        return ResponseHelper.successful(uDeviceService.findSelect());
    }

    @ApiOperation("解除u位管理器的绑定")
    @PutMapping(value = "/udevice/removerack", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> removeRackId(@RequestBody UDevice uDevice){
        return ResponseHelper.successful(uDeviceService.setUDeviceRackIdIsNull(uDevice.getRackId()));
    }

    @ApiOperation("u位设备绑定机架")
    @PutMapping(value = "/udevice/bindrack", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> bindRack(@RequestBody UDevice uDevice){
        return ResponseHelper.successful(uDeviceService.uDeviceBindRack(uDevice.getUDeviceNumber(), uDevice.getRackId()));
    }
}
