package com.siteweb.computerrack.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.computerrack.dto.ComputerRackEquipmentRequestDTO;
import com.siteweb.computerrack.service.ComputerRackEquipmentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;


@RestController
@RequestMapping("/api")
@Api(tags = {"机架和动环设备映射管理控制器"})
public class ComputerRackEquipmentController {
    @Autowired
    private ComputerRackEquipmentService computerRackEquipmentService;

    private static final String USER_ID_IS_NULL = "userid is null";

    @ApiOperation("获取机架和动环设备映射列表,有权限")
    @GetMapping(value = "/computerracksequipments", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllComputerRacksEquipments() {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()), USER_ID_IS_NULL, HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(computerRackEquipmentService.findComputerRackEquipmentVOsByUserId(loginUserId));
    }

    @ApiOperation("根据机架id获取机架绑定动环设备")
    @GetMapping(value = "/computerracksequipments/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getComputerRacksEquipmentsById(@PathVariable("id") Integer id) {
        return ResponseHelper.successful(computerRackEquipmentService.findComputerRackEquipmentVOsById(id));
    }

    @ApiOperation("批量创建机架绑定动环设备")
    @PostMapping(value = "/computerracksequipments", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createComputerRacksEquipments(@Valid @RequestBody ComputerRackEquipmentRequestDTO computerRackEquipmentRequestDTO) {
        return ResponseHelper.successful(computerRackEquipmentService.createComputerRacksEquipments(computerRackEquipmentRequestDTO));
    }

    @ApiOperation("批量更新机架绑定动环设备")
    @PutMapping(value = "/computerracksequipments", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateComputerRacksEquipments(@Valid @RequestBody ComputerRackEquipmentRequestDTO computerRackEquipmentRequestDTO) {
        return ResponseHelper.successful(computerRackEquipmentService.updateComputerRacksEquipments(computerRackEquipmentRequestDTO));
    }

    @ApiOperation("批量删除机架绑定动环设备")
    @DeleteMapping(value = "/computerracksequipments/{ids}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteComputerRacksEquipments(@PathVariable String ids) {
        computerRackEquipmentService.deleteByIds(ids);
        return ResponseHelper.successful(HttpStatus.OK);
    }
}
