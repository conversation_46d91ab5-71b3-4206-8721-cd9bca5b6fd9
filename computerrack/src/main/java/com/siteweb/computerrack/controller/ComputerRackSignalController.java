package com.siteweb.computerrack.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.computerrack.dto.ComputerRackSignaImportDTO;
import com.siteweb.computerrack.entity.ComputerRackSignalMap;
import com.siteweb.computerrack.service.ComputerRackSignalService;
import com.siteweb.computerrack.vo.ComputerRackSignalVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/api")
@Api(tags = {"机架和设备信号映射管理控制器"})
public class ComputerRackSignalController {
    @Autowired
    ComputerRackSignalService computerRackSignalService;


    /**
     * 获取机架和信号映射列表，支持分页和关键字搜索
     *
     * @param pageable 分页参数
     * @param keyword 关键字（可选，用于搜索机架名称、机架编号或位置）
     * @return 分页结果
     */
    @ApiOperation("获取机架和信号映射列表,有权限")
    @GetMapping(value = "/computerrackssignals", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllComputerRacksSignals(
            Pageable pageable,
            @ApiParam(value = "关键字（搜索机架名称、机架编号或位置）")
            @RequestParam(required = false) String keyword) {
        Integer loginUserId = TokenUserUtil.getLoginUserId();

        // 如果没有提供分页参数，则返回所有数据
        if (pageable == null || (pageable.getPageSize() == Integer.MAX_VALUE && pageable.getPageNumber() == 0)) {
            return ResponseHelper.successful(computerRackSignalService.findComputerRackSignalByUserId(loginUserId));
        }

        // 否则返回分页数据
        Page<ComputerRackSignalVO> page = computerRackSignalService.findComputerRackSignalByPage(pageable, keyword, loginUserId);
        return ResponseHelper.successful(page);
    }

    @ApiOperation("批量导入机架和信号映射")
    @PostMapping(value = "/computerrackssignals/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> importComputerRacksSignals(@RequestBody List<ComputerRackSignaImportDTO> computerRackSignaImports) {
        return ResponseHelper.successful(computerRackSignalService.importComputerRacksSignals(computerRackSignaImports));
    }

    @ApiOperation("编辑机架信号")
    @PostMapping(value = "/computerrackssignals", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> editComputerRacksSignals(@RequestBody ComputerRackSignalMap computerRackSignalMap) {
        computerRackSignalService.editComputerRacksSignals(computerRackSignalMap);
        return ResponseHelper.successful();
    }

    @ApiOperation("批量删除机架绑定信号")
    @DeleteMapping(value = "/computerrackssignals/{ids}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteComputerRacksSignals(@PathVariable String ids) {
        computerRackSignalService.deleteByRackIds(ids);
        return ResponseHelper.successful();
    }

    @ApiOperation("校验开通率表达式是否正常")
    @GetMapping(value = "/computerrackssignals/openexpression/validate", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> validateOpenExpression(String expression) {
        return ResponseHelper.successful(computerRackSignalService.checkOpenExpression(expression));
    }

    @ApiOperation("校验功率表达式是否正常")
    @GetMapping(value = "/computerrackssignals/powerexpression/validate", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> validatePowerExpression(String expression) {
        return ResponseHelper.successful(computerRackSignalService.checkPowerExpression(expression));
    }
}
