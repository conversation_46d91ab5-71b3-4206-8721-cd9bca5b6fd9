package com.siteweb.computerrack.controller;

import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.computerrack.dto.ITDeviceModelmportDTO;
import com.siteweb.computerrack.entity.ITDeviceModel;
import com.siteweb.computerrack.service.ITDeviceModelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


@RestController
@RequestMapping("/api")
@Api(tags = {"IT设备模型控制器"})
public class ITDeviceModelController {
    @Autowired
    private ITDeviceModelService itDeviceModelService;

    @ApiOperation("获取IT设备模型列表")
    @GetMapping(value = "/itdevicemodels", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllITDeviceModel() {
        return ResponseHelper.successful(itDeviceModelService.findITDeviceModels());
    }
    /**
     * 获取IT设备模型列表，支持分页和关键字搜索
     *
     * @param pageable 分页参数
     * @param keyword 关键字（可选，用于搜索模型名称、类型名称、厂家或品牌）
     * @return 分页结果
     */
    @ApiOperation("获取IT设备模型列表")
    @GetMapping(value = "/itdevicemodels/pages", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllITDeviceModelByPage(
            Pageable pageable,
            @ApiParam(value = "关键字（搜索模型名称、类型名称、厂家或品牌）")
            @RequestParam(required = false) String keyword) {
        // 如果没有提供分页参数，则返回所有数据
        if (pageable == null || (pageable.getPageSize() == Integer.MAX_VALUE && pageable.getPageNumber() == 0)) {
            return ResponseHelper.successful(itDeviceModelService.findITDeviceModels());
        }

        // 否则返回分页数据
        Page<ITDeviceModel> page = itDeviceModelService.findITDeviceModelsByPage(pageable, keyword);
        return ResponseHelper.successful(page);
    }


    @ApiOperation("根据id获取IT设备模型")
    @GetMapping(value = "/itdevicemodels/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getITDeviceModelById(@PathVariable("id") Integer id) {
        return ResponseHelper.successful(itDeviceModelService.findById(id));
    }


    @ApiOperation("IT设备模型是否被引用")
    @GetMapping(value = "/itdevicemodels/references/{iTDeviceModelId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getITDeviceModelByIds(@PathVariable("iTDeviceModelId") Integer iTDeviceModelId) {
        return ResponseHelper.successful(itDeviceModelService.findReferencesCount(iTDeviceModelId) > 0);
    }


    @ApiOperation("创建IT设备模型")
    @PostMapping(value = "/itdevicemodels", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createITDeviceModel(@Valid @RequestBody ITDeviceModel itDeviceModel) {
        if (itDeviceModelService.itDeviceModelNameExist(null, itDeviceModel.getITDeviceModelName())) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.NAME_EXIST.value()), ErrorCode.NAME_EXIST.getReasonPhrase(), HttpStatus.OK);
        }
        return ResponseHelper.successful(itDeviceModelService.createITDeviceModel(itDeviceModel));
    }

    @ApiOperation("批量导入IT设备模型")
    @PostMapping(value = "/itdevicemodels/import", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> importITDeviceModels(@RequestBody List<ITDeviceModelmportDTO> itDeviceModels) {
        return ResponseHelper.successful(itDeviceModelService.importITDeviceModels(itDeviceModels));
    }

    @ApiOperation("修改IT设备模型")
    @PutMapping(value = "/itdevicemodels", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateITDeviceModel(@Valid @RequestBody ITDeviceModel itDeviceModel) {
        if (itDeviceModelService.itDeviceModelNameExist(itDeviceModel.getITDeviceModelId(), itDeviceModel.getITDeviceModelName())) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.NAME_EXIST.value()), ErrorCode.NAME_EXIST.getReasonPhrase(), HttpStatus.OK);
        }
        return ResponseHelper.successful(itDeviceModelService.updateITDeviceModel(itDeviceModel));
    }


    @ApiOperation("删除IT设备模型")
    @DeleteMapping(value = "/itdevicemodels/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteITDeviceModel(@PathVariable Integer id) {
        itDeviceModelService.deleteById(id);
        return ResponseHelper.successful(HttpStatus.OK);
    }


}
