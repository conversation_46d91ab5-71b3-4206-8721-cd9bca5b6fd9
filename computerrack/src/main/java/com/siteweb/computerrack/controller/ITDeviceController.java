package com.siteweb.computerrack.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.computerrack.dto.ItdeviceQueryDTO;
import com.siteweb.computerrack.entity.ITDevice;
import com.siteweb.computerrack.entity.ITDeviceModel;
import com.siteweb.computerrack.enumeration.ITDeviceOperatingStatus;
import com.siteweb.computerrack.service.ComputerRackService;
import com.siteweb.computerrack.service.ITDeviceModelService;
import com.siteweb.computerrack.service.ITDeviceOperateService;
import com.siteweb.computerrack.service.ITDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;


@RestController
@RequestMapping("/api")
@Api(tags = {"IT设备控制器"})
public class ITDeviceController {
    @Autowired
    private ITDeviceService itDeviceService;

    @Autowired
    private ComputerRackService computerRackService;

    @Autowired
    private ITDeviceOperateService iTDeviceOperateService;

    @Autowired
    private ITDeviceModelService itDeviceModelService;

    @Autowired
    private LocaleMessageSourceUtil localeMessageSourceUtil;

    @ApiOperation("获取IT设备列表")
    @GetMapping(value = "/itdevices", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllITDevices() {
        return ResponseHelper.successful(itDeviceService.findITDevices());
    }
    /**
     * 获取IT设备列表，支持分页和关键字搜索
     *
     * @param pageable 分页参数
     * @param keyword 关键字（可选，用于搜索设备名称、模型名称、客户名或业务）
     * @return 分页结果
     */
    @ApiOperation("获取IT设备列表")
    @GetMapping(value = "/itdevices/pages", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllITDevicesByPage(
            Pageable pageable,
            @ApiParam(value = "关键字（搜索设备名称、模型名称、客户名或业务）")
            @RequestParam(required = false) String keyword) {
        // 如果没有提供分页参数，则返回所有数据
        if (pageable == null || (pageable.getPageSize() == Integer.MAX_VALUE && pageable.getPageNumber() == 0)) {
            return ResponseHelper.successful(itDeviceService.findITDevices());
        }

        // 否则返回分页数据
        Page<ITDevice> page = itDeviceService.findITDevicesByPage(pageable, keyword);
        return ResponseHelper.successful(page);
    }

    @ApiOperation("获取已上架的IT设备表")
    @PostMapping(value = "/itdevices/onshelf", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getOnShelfITDevices(@Valid @RequestBody List<Integer> computerRackIds) {
        return ResponseHelper.successful(itDeviceService.getOnShelfITDevices(computerRackIds));
    }

    @ApiOperation("获取已上架的IT设备表")
    @PostMapping(value = "/itdevices/onshelfmap", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getOnShelfITDeviceMap(@Valid @RequestBody List<Integer> computerRackIds) {
        return ResponseHelper.successful(itDeviceService.getOnShelfITDeviceMap(computerRackIds));
    }


    @ApiOperation("获取IT设备列表根据上下架状态")
    @GetMapping(value = "/itdevices", produces = MediaType.APPLICATION_JSON_VALUE, params = {"state"})
    public ResponseEntity<ResponseResult> getAllITDevices(Integer state) {
        return ResponseHelper.successful(itDeviceService.findITDevices(state));
    }

    @ApiOperation("根据id获取IT设备")
    @GetMapping(value = "/itdevices/{iTDeviceId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getITDeviceById(@PathVariable("iTDeviceId") Integer iTDeviceId) {
        return ResponseHelper.successful(itDeviceService.findITDevice(iTDeviceId));
    }


    @ApiOperation("获取机架所有IT设备")
    @GetMapping(value = "/itdevices/{computerRackId}", params = {"computerRackId"}, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getITDevicesByComputerRackId(@RequestParam("computerRackId") Integer computerRackId) {
        return ResponseHelper.successful(itDeviceService.findByComputerRackId(computerRackId));
    }


    @ApiOperation("创建IT设备")
    @PostMapping(value = "/itdevices", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createITDeviceModel(@Valid @RequestBody ITDevice itDevice) {
        if (itDeviceService.existsByName(null, itDevice.getITDeviceName())) {
            throw new BusinessException(localeMessageSourceUtil.getMessage("common.nameUnique"));
        }
        return ResponseHelper.successful(itDeviceService.createITDevice(itDevice));
    }

    @ApiOperation("获取IT设备列表根据层级id")
    @GetMapping(value = "/itdevices", produces = MediaType.APPLICATION_JSON_VALUE, params = {"resourceStructureId"})
    public ResponseEntity<ResponseResult> getITDeviceByResourceStructureId(Integer resourceStructureId) {
        return ResponseHelper.successful(itDeviceService.findITDeviceByResourceStructureIds(List.of(resourceStructureId)));
    }


    private boolean equalsInteger(Integer a, Integer b) {
        if (a == null && b == null) return true;
        if (a == null && b != null) return false;
        if (a != null && b == null) return false;
        return a.equals(b);
    }


    @ApiOperation("修改IT设备")
    @PutMapping(value = "/itdevices", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateITDeviceModel(@Valid @RequestBody ITDevice itDevice) {
        // ITDevice _itDevice = itDeviceService.findITDevice(itDevice.getITDeviceId());

        if (itDevice.getITDeviceId() == null) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        ITDevice cache = itDeviceService.findITDevice(itDevice.getITDeviceId());
        if (cache == null) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }
        if (itDeviceService.existsByName(itDevice.getITDeviceId(),itDevice.getITDeviceName())) {
            throw new BusinessException(localeMessageSourceUtil.getMessage("common.nameUnique"));
        }
        if (!this.equalsInteger(cache.getUIndex(), itDevice.getUIndex())) {
            // 禁止直接修改
            return null;
        }
        if (!this.equalsInteger(cache.getComputerRackId(), itDevice.getComputerRackId())) {
            // 禁止直接修改
            return null;
        }

        Integer _computerRackId = itDevice.getComputerRackId();
        Integer _uIndex = itDevice.getUIndex();
        boolean _putShelf = false;
        // IT设备模型改变
        if (!this.equalsInteger(itDevice.getITDeviceModelId(), cache.getITDeviceModelId())) {
            ITDeviceModel newITDeviceModel = itDeviceModelService.findById(itDevice.getITDeviceModelId());
            if (newITDeviceModel == null) {
                //
                return null;
            }
            // 先执行下架操作
            if (_computerRackId != null && _uIndex != null) {
                _putShelf = true;
                // 先下架
                iTDeviceOperateService.takeFromShelf(cache);
                // 是否可以上架新
                if (!iTDeviceOperateService.canPutShelf(_computerRackId, _uIndex, newITDeviceModel.getUnitHeight())) {
                    // 重新上架
                    iTDeviceOperateService.putInShelf(itDevice.getITDeviceId(), _computerRackId, _uIndex);
                    return null;
                }
            }
        }
        ITDevice result = itDeviceService.updateITDevice(itDevice);
        if (_putShelf) {
            ITDeviceOperatingStatus state = iTDeviceOperateService.putInShelf(result.getITDeviceId(), _computerRackId, _uIndex);
            computerRackService.updateCapacityValueByComputerRackId(_computerRackId);
        }
        return ResponseHelper.successful(result);
    }

    @ApiOperation("批量删除IT设备")
    @DeleteMapping(value = "/itdevices/{ids}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchDeleteITDevice(@PathVariable String ids) {
        List<Integer> idList = Arrays.stream(ids.split(",")).map(Integer::valueOf).toList();
        List<ITDevice> itDeviceList = itDeviceService.findITDeviceByIds(idList);
        if (!itDeviceList.isEmpty()) {
            if (itDeviceList.stream().allMatch(d -> d.getComputerRackId() == null)) {
                itDeviceService.batchDelete(idList);
            } else {
                // 上架的IT设备不能删除
                return ResponseHelper.failed(String.valueOf(ErrorCode.DELETE_OBJECT_ERROR), localeMessageSourceUtil.getMessage("common.field.inShelfItDeviceDelFail"), HttpStatus.BAD_REQUEST);
            }
        }
        return ResponseHelper.successful(HttpStatus.OK);
    }


    @ApiOperation("获取it设备与标签的绑定状态")
    @GetMapping("/utagdevice")
    public ResponseEntity<ResponseResult> getUTagDeviceSelect() {
        return ResponseHelper.successful(itDeviceService.findUTagDeviceSelect());
    }

    @ApiOperation("获取IT设备列表，筛选-分页")
    @GetMapping(value = "/itdevicesbyquery", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllITDevices(ItdeviceQueryDTO queryDTO) {
        return ResponseHelper.successful(itDeviceService.findITDevicesByQuery(queryDTO));
    }

    @ApiOperation("获取IT设备列表，根据标签编号")
    @GetMapping(value = "/itdevices", produces = MediaType.APPLICATION_JSON_VALUE, params = {"uTagValue"})
    public ResponseEntity<ResponseResult> getIdDeviceByuTagValue(String uTagValue) {
        return ResponseHelper.successful(itDeviceService.findIdDeviceByuTagValue(uTagValue));
    }

    @ApiOperation("发送控制命令 查找IT设备")
    @GetMapping("/itdevice/command")
    public ResponseEntity<ResponseResult> findItDeviceByCommand(String ids) {
        List<Integer> itDeviceIdList = StringUtils.splitToIntegerList(ids);
        return ResponseHelper.successful(itDeviceService.findItDevicesByCommand(TokenUserUtil.getLoginUserId(), itDeviceIdList));
    }
}
