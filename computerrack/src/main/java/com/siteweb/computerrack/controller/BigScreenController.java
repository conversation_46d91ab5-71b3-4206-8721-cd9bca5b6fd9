package com.siteweb.computerrack.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.computerrack.service.BigScreenService;
import com.siteweb.computerrack.service.impl.RackMountRecordServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
@Api(tags = {"u位管理大屏相关接口"})
public class BigScreenController {
    private static final String USER_ID_IS_NULL = "userid is null";
    @Autowired
    BigScreenService bigScreenService;
    @Autowired
    RackMountRecordServiceImpl rackMountRecordService;


    @ApiOperation("获取设备类型分类统计(饼图)")
    @GetMapping("/piechart/itdevicetype")
    public ResponseEntity<ResponseResult> getITDeviceTypePieChart(String resourceStructureId){
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()), USER_ID_IS_NULL, HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(bigScreenService.findITDeviceTypePieChart(loginUserId, resourceStructureId));
    }

    @ApiOperation("获取设备业务属性分类统计(饼图)")
    @GetMapping("/piechart/itdevicebusiness")
    public ResponseEntity<ResponseResult> getITDeviceBusinessPieChart(String resourceStructureId){
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()), USER_ID_IS_NULL, HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(bigScreenService.findITDeviceBusinessPieChart(loginUserId, resourceStructureId));
    }

    @ApiOperation("获取当前标签U位变化")
    @GetMapping("/currentuindexchange")
    public ResponseEntity<ResponseResult> getCurrentUIndexChange(String resourceStructureId){
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()), USER_ID_IS_NULL, HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(bigScreenService.findCurrentUIndexChange(loginUserId, resourceStructureId));
    }

    @ApiOperation("机房u位使用率")
    @GetMapping("/roomuindexrate")
    public ResponseEntity<ResponseResult> getRoomUIndexRate(String resourceStructureId){
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()), USER_ID_IS_NULL, HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(bigScreenService.findRoomUIndexRate(loginUserId, resourceStructureId));
    }
    @ApiOperation("机架u位使用率")
    @GetMapping("/rackuindexrate")
    public ResponseEntity<ResponseResult> getRackUIndexRate(String resourceStructureId){
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()), USER_ID_IS_NULL, HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(bigScreenService.findRackUIndexRate(loginUserId, resourceStructureId));
    }

    @ApiOperation("机架变更记录topN")
    @GetMapping(value = "/rackchangerecord")
    public ResponseEntity<ResponseResult> getRackChangeRecord(String resourceStructureId){
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()), USER_ID_IS_NULL, HttpStatus.BAD_REQUEST);
        }
        Integer top = bigScreenService.findTopN("bigscreen.rackchangerecord.top");
        return ResponseHelper.successful(rackMountRecordService.findTopN(loginUserId, top, resourceStructureId));
    }

    @ApiOperation("总u位变化率")
    @GetMapping(value = "/sumuindexrate")
    public ResponseEntity<ResponseResult> getSumUIndexRate(String resourceStructureId){
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()), USER_ID_IS_NULL, HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(bigScreenService.getSumUIndexRate(loginUserId, resourceStructureId));
    }

    @ApiOperation("某层级下，按客户统计机架数量")
    @GetMapping("/rackcountbycustomer")
    public ResponseEntity<ResponseResult> getRackCountByCustomer(String ids) {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()), USER_ID_IS_NULL, HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(bigScreenService.getRackCountByCustomer(loginUserId, ids));
    }

    @ApiOperation("某层级下，按业务统计机架数量")
    @GetMapping("/rackcountbybusiness")
    public ResponseEntity<ResponseResult> getRackCountByBusiness(String ids) {
        Integer loginUserId = TokenUserUtil.getLoginUserId();
        if (null == loginUserId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()), USER_ID_IS_NULL, HttpStatus.BAD_REQUEST);
        }
        return ResponseHelper.successful(bigScreenService.getRackCountByBusiness(loginUserId, ids));
    }
}
