package com.siteweb.computerrack.controller;

import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.computerrack.dto.CapacityScreenParam;
import com.siteweb.computerrack.service.CapacityScreenService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
@Api(tags = {"容量展板相关接口"})
public class CapacityScreenController {
    @Autowired
    CapacityScreenService capacityScreenService;

    @ApiOperation("获取总电力利用率")
    @GetMapping(value = "/capacitytotalpowerrate", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCapacityTotalPowerRate(Integer resourceStructureId) {
        return ResponseHelper.successful(capacityScreenService.getCapacityTotalPowerRate(resourceStructureId));
    }

    @ApiOperation("获取IT电力利用率")
    @GetMapping(value = "/capacityitpowerrate", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCapacityItPowerRate(Integer resourceStructureId) {
        return ResponseHelper.successful(capacityScreenService.getCapacityItPowerRate(resourceStructureId));
    }

    @ApiOperation("获取制冷负荷利用率")
    @GetMapping(value = "/capacityrefrigerationrate", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getCapacityRefrigerationPowerRate(Integer resourceStructureId) {
        return ResponseHelper.successful(capacityScreenService.getCapacityRefrigerationPowerRate(resourceStructureId));
    }

    @ApiOperation("获取机柜开通率")
    @GetMapping(value = "/computerrackopenrate", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getComputerRackOpenRate(Integer resourceStructureId) {
        return ResponseHelper.successful(capacityScreenService.getComputerRackOpenRate(resourceStructureId));
    }

    @ApiOperation("获取市电进线负载率")
    @GetMapping(value = "/mainspowerinletload", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getMainsPowerInletLoad(Integer resourceStructureId) {
        return ResponseHelper.successful(capacityScreenService.getMainsPowerInletLoad(resourceStructureId));
    }

    @ApiOperation("获取机房IT电力利用率排名TOP5")
    @GetMapping(value = "/roomitpowerrank", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getRoomItPowerRank(CapacityScreenParam param) {
        return ResponseHelper.successful(capacityScreenService.getRoomItPowerRank(param));
    }

    @ApiOperation("获取机房制冷负载率排名TOP5")
    @GetMapping(value = "/roomrefrigerationrank", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getRoomRefrigerationRank(CapacityScreenParam param) {
        return ResponseHelper.successful(capacityScreenService.getRoomRefrigerationRank(param));
    }

    @ApiOperation("获取机房机柜开通率排名TOP5")
    @GetMapping(value = "/roomrackopenrank", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getRoomRackOpenRank(CapacityScreenParam param) {
        return ResponseHelper.successful(capacityScreenService.getRoomRackOpenRank(param));
    }

}
