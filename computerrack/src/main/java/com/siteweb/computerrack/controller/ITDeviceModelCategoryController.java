package com.siteweb.computerrack.controller;

import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.computerrack.dto.ITDeviceModelCategoryDTO;
import com.siteweb.computerrack.service.ITDeviceModelCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api")
@Api(tags = {"IT设备模型类型操作"})
public class ITDeviceModelCategoryController {
    @Autowired
    ITDeviceModelCategoryService itDeviceModelCategoryService;
    @ApiOperation("获取所有的IT设备模型的类型")
    @GetMapping("/itdevicemodelcategory")
    public ResponseEntity<ResponseResult> getAll(){
         return ResponseHelper.successful(itDeviceModelCategoryService.findAll());
    }

    @ApiOperation("添加IT设备模型的类型")
    @PostMapping("/itdevicemodelcategory")
    public ResponseEntity<ResponseResult> create(@RequestBody ITDeviceModelCategoryDTO itDeviceModelCategoryDTO){
        if (itDeviceModelCategoryService.existsName(itDeviceModelCategoryDTO.getName())) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.NAME_EXIST.value()),ErrorCode.NAME_EXIST.getReasonPhrase(), HttpStatus.OK);
        }
        return ResponseHelper.successful(itDeviceModelCategoryService.create(itDeviceModelCategoryDTO));
    }

    @ApiOperation("删除IT设备模型的类型")
    @DeleteMapping("/itdevicemodelcategory/{id}")
    public ResponseEntity<ResponseResult> deleteById(@PathVariable Integer id){
        if (itDeviceModelCategoryService.existsDependency(id)) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.EXIST_DEPENDENCY.value()),ErrorCode.EXIST_DEPENDENCY.getReasonPhrase(), HttpStatus.OK);
        }
        return ResponseHelper.successful(itDeviceModelCategoryService.deleteById(id));
    }


    @ApiOperation("修改IT设备模型的类型")
    @PutMapping("/itdevicemodelcategory")
    public ResponseEntity<ResponseResult> update(@RequestBody ITDeviceModelCategoryDTO itDeviceModelCategoryDTO){
        return ResponseHelper.successful(itDeviceModelCategoryService.updateById(itDeviceModelCategoryDTO));
    }
}
