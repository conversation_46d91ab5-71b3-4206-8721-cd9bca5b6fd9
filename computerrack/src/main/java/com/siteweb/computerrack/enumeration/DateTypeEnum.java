package com.siteweb.computerrack.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 容量基类属性枚举
 */
@Getter
@AllArgsConstructor
public enum DateTypeEnum {
    /**
     * 年
     */
    YEAR(1),
    /**
     * 月
     */
    MONTH(2),
    /**
     * 季度
     */
    QUARTERLY(3),
    /**
     * 自定义
     */
    CUSTOMIZE(4),
    /**
     * 季度日
     */
    QUARTER_DAY(5)
    ;
    private final Integer value;

    public static DateTypeEnum getByValue(Integer value) {
        return Arrays.stream(values())
                     .filter(e -> e.getValue().equals(value))
                     .findFirst()
                     .orElse(null);
    }
}
