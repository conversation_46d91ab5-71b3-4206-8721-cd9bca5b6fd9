package com.siteweb.computerrack.enumeration;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * U位设备灯的状态定义 (数值 -> 状态 -> 对应灯光颜色):
 * 0: 检测标签 (绿色)
 * 1: 不可用   (蓝色)
 * 2: 维修     (浅黄色)
 * 3: 报警     (红色)
 * 4: 待上架   (紫色)
 * 5: 待下架   (深黄色)
 * 6: 生命周期 (浅蓝色)
 *
 * <AUTHOR>
 * @date 2025/04/28
 */
@Getter
@AllArgsConstructor
public enum UDeviceLightStatus {
    DETECT_TAG("0", "检测标签", "绿色"),
    UNAVAILABLE("1", "不可用", "蓝色"),
    MAINTENANCE("2", "维修", "浅黄色"),
    ALARM("3", "报警", "红色"),
    PENDING_INSTALL("4", "待上架", "紫色"),
    PENDING_REMOVE("5", "待下架", "深黄色"),
    LIFE_CYCLE("6", "生命周期", "浅蓝色");
    private final String code;
    private final String description;
    private final String color;
}
