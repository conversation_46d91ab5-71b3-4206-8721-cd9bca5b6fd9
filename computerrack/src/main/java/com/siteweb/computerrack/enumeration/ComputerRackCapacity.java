package com.siteweb.computerrack.enumeration;


import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 计算逻辑类型
 */
@JsonFormat(shape = JsonFormat.Shape.NUMBER)
public enum ComputerRackCapacity {

    /**
     * 机架U位容量
     */
    RACK_SPACE(2000000),
    /**
     * 机架重量容量
     */
    RACK_WEIGHT(2000001),
    /**
     * 机架制冷容量
     */
    RACK_COOLING(2000002),
    /**
     * 机架功率容量
     */
    RACK_POWER(2010000);


    private final Integer value;


    ComputerRackCapacity(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }
}