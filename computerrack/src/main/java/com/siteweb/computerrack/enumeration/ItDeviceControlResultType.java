package com.siteweb.computerrack.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * IT设备控制命令返回结果
 */
@Getter
@AllArgsConstructor
public enum ItDeviceControlResultType {
    /**
     * 未知错误:如数据库异常
     */
    UNKNOWN_ERROR(-1),
    /**
     * 执行成功
     */
    SUCCESS(0),
    /**
     * 参数错误
     */
    PARAM_ERROR(1),
    /**
     * 活动控制命令已存在
     */
    COMMAND_EXISTS(2),
    /**
     * 局站或设备处于工程状态
     */
    IN_PROJECT(3),
    /**
     * 没有执行控制命令的权限
     */
    NO_PERMISSION(4),
    /**
     * 设备不在线
     */
    EQUIPMENT_OFFLINE(5),
    /**
     * 找不到IT设备
     */
    IT_DEVICE_NOT_FOUND(6),
    /**
     * 找不到机架信息
     */
    RACK_INFO_NOT_FOUND(7),
    /**
     * U位设备不存在
     */
    U_POSITION_DEVICE_NOT_FOUND(8);


    private final int value;

    public int value() {
        return this.value;
    }

    public static ItDeviceControlResultType valueOf(int value) {
        return switch (value) {
            case -1 -> UNKNOWN_ERROR;
            case 0 -> SUCCESS;
            case 1 -> PARAM_ERROR;
            case 2 -> COMMAND_EXISTS;
            case 3 -> IN_PROJECT;
            case 4 -> NO_PERMISSION;
            case 5 -> EQUIPMENT_OFFLINE;
            case 6 -> IT_DEVICE_NOT_FOUND;
            case 7 -> RACK_INFO_NOT_FOUND;
            case 8 -> U_POSITION_DEVICE_NOT_FOUND;
            default -> null;
        };
    }
}
