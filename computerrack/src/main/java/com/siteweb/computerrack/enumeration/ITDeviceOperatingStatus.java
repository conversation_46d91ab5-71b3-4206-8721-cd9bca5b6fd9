package com.siteweb.computerrack.enumeration;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

@JsonFormat(shape = JsonFormat.Shape.NUMBER)
@Getter
@AllArgsConstructor
public enum ITDeviceOperatingStatus {

    /**
     * 成功
     */
    SUCCESS(0,"成功",""),
    /**
     * 未知的错误
     */
    UNKNOWN_ERROR(-1,"未知的错误","computerRackName"),
    /**
     * 没有找到IT设备
     */
    NOT_FOUND_ITDEVICE(-2,"没有找到IT设备","itDeviceName"),
    /**
     * 没有找到机架
     */
    NOT_FOUND_COMPUTERRACK(-3,"没有找到机架","computerRackName"),
    /**
     * IT设备没有在机架上
     */
    ITDEVICE_NOT_IN_RACK(-4,"IT设备没有在机架上","itDeviceName"),

    /**
     * U位必须大于0
     */
    U_INDEX_MUST_GREATER_0(-5,"U位必须大于0","uIndex"),

    /**
     * IT设备必须是闲置的
     */
    ITDEVICE_MUST_BE_IDLE(-6,"IT设备必须是闲置的","itDeviceName"),

    /**
     * IT设备位置超出范围
     */
    ITDEVICE_LOCATION_OUT_OF_RANGE(-7,"IT设备位置超出范围","itDeviceName"),

    /**
     * U位必须是空的
     */
    U_POSITION_MUST_EMPTY(-8,"U位必须是空的","uIndex"),

    /**
     * 没有找到机架
     */
    NOT_FOUND_RACK(-9,"没有找到机架","computerRackName");


    private final Integer value;
    private final String description;
    private final String field;
}
