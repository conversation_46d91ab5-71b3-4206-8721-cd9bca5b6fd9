package com.siteweb.computerrack.enumeration;

import com.siteweb.admin.enums.UserConfigEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;

/**
 * 容量基类属性枚举
 */
@Getter
@AllArgsConstructor
public enum CapacityBaseAttributeEnum {

    RACK_SPACE("rackSpace", "U位使用容量", "U"),
    RACK_WEIGHT("rackWeight", "结构承重容量", "kg"),
    RACK_COOLING("rackCooling", "制冷冷却容量", "kJ"),
    COM_POWER("comPower", "功率负载容量", "kVA"),
    COM_VOLTAGE("comVoltage", "电压负载容量", "V"),
    COM_CURRENT("comCurrent", "电流负载容量", "A");
    private final String value;
    private final String name;
    private final String unit;

    public static CapacityBaseAttributeEnum getByValue(String value) {
        return Arrays.stream(values())
                     .filter(e -> e.getValue().equals(value))
                     .findFirst()
                     .orElse(null);
    }
}
