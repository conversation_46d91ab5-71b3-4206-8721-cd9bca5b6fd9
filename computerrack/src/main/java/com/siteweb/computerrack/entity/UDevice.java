package com.siteweb.computerrack.entity;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("tbl_udevice")
public class UDevice {
    /**
     * U位管理设备主键id
     */
    @TableId(type = IdType.AUTO)
    private Integer uDeviceId;
    /**
     * U位管理设备唯一编码
     */
    private String uDeviceNumber;
    /**
     * 是否在线
     */
    private Integer isOnline;
    /**
     * 对应机架
     */
    private Integer rackId;
    /**
     * 额定U位
     */
    private Integer moduleCnt;
    /**
     * 标签数
     */
    private Integer uTagCnt;
    /**
     * ip地址
     */
    private String ipAddr;
    /**
     * SiteWeb设备Id(U位管理与SW采集设备关联关系)
     */
    private Integer swEquipmentId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 是否已绑定机架
     */
    @TableField(exist = false)
    private Boolean bindState;
    /**
     * 设备名称
     */
    @TableField(exist = false)
    private String uDeviceName;
    public Boolean getBindState(){
        return !ObjectUtil.isNull(this.rackId);
    }
}
