package com.siteweb.computerrack.exception;

import com.siteweb.common.exception.IExceptionCode;
import lombok.AllArgsConstructor;

@AllArgsConstructor
public class TagExceptionCode implements IExceptionCode {
    private String error;
    private String code;

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getError() {
        return error;
    }
}
