package com.siteweb.computerrack.util;

import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.computerrack.enumeration.ITDeviceOperatingStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class MsgUtil {
    @Autowired
    private LocaleMessageSourceUtil messageSourceUtil;


    public String getMsgByStatus(ITDeviceOperatingStatus status){
        if(ITDeviceOperatingStatus.SUCCESS.equals(status)){
            return messageSourceUtil.getMessage("common.field.success");
        }else if(ITDeviceOperatingStatus.UNKNOWN_ERROR.equals(status)){
            return messageSourceUtil.getMessage("common.field.unknownError");
        }else if(ITDeviceOperatingStatus.NOT_FOUND_ITDEVICE.equals(status)){
            return messageSourceUtil.getMessage("common.field.notFindDevice");
        }else if(ITDeviceOperatingStatus.NOT_FOUND_COMPUTERRACK.equals(status)){
            return messageSourceUtil.getMessage("common.field.notFindRack");
        }else if(ITDeviceOperatingStatus.ITDEVICE_NOT_IN_RACK.equals(status)){
            return messageSourceUtil.getMessage("common.field.itDeviceNotInRack");
        }else if(ITDeviceOperatingStatus.U_INDEX_MUST_GREATER_0.equals(status)){
            return messageSourceUtil.getMessage("common.field.uIndexMoreZero");
        }else if(ITDeviceOperatingStatus.ITDEVICE_MUST_BE_IDLE.equals(status)){
            return messageSourceUtil.getMessage("common.field.itDeviceFree");
        }else if(ITDeviceOperatingStatus.ITDEVICE_LOCATION_OUT_OF_RANGE.equals(status)){
            return messageSourceUtil.getMessage("common.field.itDeviceOutRange");
        }else if(ITDeviceOperatingStatus.U_POSITION_MUST_EMPTY.equals(status)){
            return messageSourceUtil.getMessage("common.field.uIndexEmpty");
        }else{
            return messageSourceUtil.getMessage("common.field.unknownError");
        }
    }
}
