此模板为项目提交Merge的示例教程，提交时不可使用此模板直接修改提交
可删除不涉及的标签,但标签名、格式、大小写 不可以修改，应始终保证以 `# feat` `# fix` `# schema` `# style` `# refactor`作为标签。
标签的每条记录为一行，应始终以`- ` 开始，请尽量将功能拆分成一个个小项 开始以适配markdown

feat标签      新功能的增加（此标签数据会打包至安装包内的更新日志内）
fix标签       对bug的修改 （此标签数据会打包至安装包内的更新日志内）
schema标签    结构的变更  （此标签数据会打包至安装包内的更新日志内）
style标签     不影响程序逻辑的代码修改 （此项不会加入更新日志）
refactor标签  代码的重构  （此项不会加入更新日志）


SQL数据库的变更以下方代码块为准
``` sql
    -- 此代码块是此次merge对MYSQL数据库的变更脚本包括表结构和初始化数据的更新
```

以下为正文开始
`======================================================`

# feat
- 3DCFD溫场功能
- API代理功能

# fix
- 修复bug1
- 修复bug2

# schema
- 接口变更1
- sql表变更2

# style
- 更新readme md
- 更新makefile

# refactor
- 重构功能1
- 重构功能2


``` sql
ALTER TABLE TBL_XXXX ADD COLUMN XXXX VARCHAR(64);
```