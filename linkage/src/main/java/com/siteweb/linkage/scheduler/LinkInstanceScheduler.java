package com.siteweb.linkage.scheduler;

import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.linkage.dto.LinkElementConfigDTO;
import com.siteweb.linkage.dto.LinkNodeDTO;
import com.siteweb.linkage.dto.LinkSegmentDTO;
import com.siteweb.linkage.entity.LinkConfig;
import com.siteweb.linkage.executor.LinkInstanceExecutor;
import com.siteweb.linkage.job.LinkConfigCronJobManager;
import com.siteweb.linkage.service.*;
import com.siteweb.monitoring.entity.AlarmChange;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.utility.service.HAStatusService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component("LinkInstanceScheduler")
public class LinkInstanceScheduler<T> implements ApplicationListener<BaseSpringEvent<T>> {

    private final Logger log = LoggerFactory.getLogger(LinkInstanceScheduler.class);

    @Autowired
    LinkConfigService linkConfigService;

    @Autowired
    LinkElementConfigService linkElementConfigService;

    @Autowired
    LinkSegmentService linkSegmentService;

    @Autowired
    LinkExecutorService linkExecutorService;

    @Autowired
    LinkConfigCronJobManager linkConfigCronJobManager;

    @Autowired
    ResourceStructureManager resourceStructureManager;

    @Autowired
    HAStatusService haStatusService;

    ScheduledThreadPoolExecutor threadPoolExecutor = new ScheduledThreadPoolExecutor(4);

    ConcurrentHashMap<Integer, String> linkConfigIdPointIdHashMap = new ConcurrentHashMap<>();

    ConcurrentHashMap<Integer, Integer> linkConfigIdEquipmentIdHashMap = new ConcurrentHashMap<>();

    ConcurrentHashMap<Integer, Set<Integer>> linkConfigIdResourceStructureIdHashMap = new ConcurrentHashMap<>();

    ConcurrentHashMap<Integer, String> linkConfigIdEventConditionHashMap = new ConcurrentHashMap<>();

    ConcurrentHashMap<Integer, Integer> linkConfigIdAlarmChangeOperationTypeHashMap = new ConcurrentHashMap<>();

    @Scheduled(fixedDelay = 10 * 1000)//every 10 seconds
    protected void schedule() {
        if (!haStatusService.isMasterHost()) {
            log.info("HAStatus is BACKUP:定时刷新后台联动配置退出");
            return;
        }
        List<LinkConfig> eventLinkConfigs = linkConfigService.findAll().stream().filter(o -> o.getLinkTriggerType().equals(2) && Boolean.TRUE.equals(o.getUsedStatus()) && checkTimeSpanValid(o)).toList();
        eventSchedule(eventLinkConfigs);
    }

    @PostConstruct
    public void cronSchedule() {
        List<LinkConfig> cronLinkConfigs = linkConfigService.findAll().stream().filter(o -> o.getLinkTriggerType().equals(1)).toList();
        for (LinkConfig linkConfig : cronLinkConfigs) {
            if (Boolean.TRUE.equals(linkConfig.getUsedStatus()) && checkTimeSpanValid(linkConfig)) {
                try {
                    linkConfigCronJobManager.addCronJob(linkConfig);
                } catch (Exception e) {
                    log.error("LinkConfigCronJobManager.addCronJob error: {}", e.getMessage(), e);
                }
            }
        }
    }

    private void eventSchedule(List<LinkConfig> linkConfigs) {
        HashMap<Integer, String> tmpLinkConfigIdPointIdHashMap = new HashMap<>();
        HashMap<Integer, Integer> tmpLinkConfigIdEquipmentIdHashMap = new HashMap<>();
        HashMap<Integer, Set<Integer>> tmpLinkConfigIdResourceStructureIdHashMap = new HashMap<>();
        HashMap<Integer, String> tmpLinkConfigIdEventConditionHashMap = new HashMap<>();
        HashMap<Integer, Integer> tmpLinkConfigIdAlarmChangeOperationTypeHashMap = new HashMap<>();
        for (LinkConfig linkconfig : linkConfigs) {
            linkConfigEventSchedule(tmpLinkConfigIdPointIdHashMap, tmpLinkConfigIdEquipmentIdHashMap, tmpLinkConfigIdResourceStructureIdHashMap, tmpLinkConfigIdEventConditionHashMap, tmpLinkConfigIdAlarmChangeOperationTypeHashMap, linkconfig);
        }
        linkConfigIdPointIdHashMap.clear();
        for (Map.Entry<Integer, String> entry : tmpLinkConfigIdPointIdHashMap.entrySet()) {
            linkConfigIdPointIdHashMap.put(entry.getKey(), entry.getValue());
        }
        linkConfigIdEquipmentIdHashMap.clear();
        for (Map.Entry<Integer, Integer> entry : tmpLinkConfigIdEquipmentIdHashMap.entrySet()) {
            linkConfigIdEquipmentIdHashMap.put(entry.getKey(), entry.getValue());
        }
        linkConfigIdResourceStructureIdHashMap.clear();
        for (Map.Entry<Integer, Set<Integer>> entry : tmpLinkConfigIdResourceStructureIdHashMap.entrySet()) {
            linkConfigIdResourceStructureIdHashMap.put(entry.getKey(), entry.getValue());
        }
        linkConfigIdEventConditionHashMap.clear();
        for (Map.Entry<Integer, String> entry : tmpLinkConfigIdEventConditionHashMap.entrySet()) {
            linkConfigIdEventConditionHashMap.put(entry.getKey(), entry.getValue());
        }
        linkConfigIdAlarmChangeOperationTypeHashMap.clear();
        for (Map.Entry<Integer, Integer> entry : tmpLinkConfigIdAlarmChangeOperationTypeHashMap.entrySet()) {
            linkConfigIdAlarmChangeOperationTypeHashMap.put(entry.getKey(), entry.getValue());
        }
    }

    private void linkConfigEventSchedule(HashMap<Integer, String> tmpLinkConfigIdPointIdHashMap, HashMap<Integer, Integer> tmpLinkConfigIdEquipmentIdHashMap, HashMap<Integer, Set<Integer>> tmpLinkConfigIdResourceStructureIdHashMap, HashMap<Integer, String> tmpLinkConfigIdEventConditionHashMap, HashMap<Integer, Integer> tmpLinkConfigIdAlarmChangeOperationTypeHashMap, LinkConfig linkconfig) {
        List<LinkElementConfigDTO> elementConfigDTOS = linkElementConfigService.findLinkElementConfigDTOsByLinkConfigId(linkconfig.getLinkConfigId());
        //找到所有事件控件，31为事件开始，32为事件结束，35为事件确认，36为事件存在
        Set<Integer> eventElementIdSet = Stream.of(31, 32, 35, 36).collect(Collectors.toSet());
        List<LinkElementConfigDTO> eventElementConfigDTOList = elementConfigDTOS.stream().filter(o -> eventElementIdSet.contains(o.getElementId())).toList();
        if (eventElementConfigDTOList.isEmpty()) {
            return;
        }
        List<LinkSegmentDTO> linkSegmentDTOList = linkSegmentService.findLinkSegmentsByLinkConfigId(linkconfig.getLinkConfigId());
        for (LinkElementConfigDTO elementConfigDTO : eventElementConfigDTOList) {
            for (LinkNodeDTO nodeDTO : elementConfigDTO.getNodeDTOs()) {
                if (!"left".equalsIgnoreCase(nodeDTO.getNodeDirection())) continue;
                Optional<LinkSegmentDTO> segmentDTO = linkSegmentDTOList.stream().filter(o -> nodeDTO.getNodeId().equals(o.getOutputNodeId())).findFirst();
                if (!segmentDTO.isPresent()) continue;
                Optional<LinkElementConfigDTO> leftElementConfig = elementConfigDTOS.stream().filter(o -> o.getLinkElementConfigId().equals(segmentDTO.get().getInputLinkElementConfigId())).findFirst();
                if (!leftElementConfig.isPresent()) continue;
                switch (leftElementConfig.get().getElementId()) {
                    case 61://测点
                        tmpLinkConfigIdPointIdHashMap.put(linkconfig.getLinkConfigId(), leftElementConfig.get().getExpression());
                        break;
                    case 62://设备
                        tmpLinkConfigIdEquipmentIdHashMap.put(linkconfig.getLinkConfigId(), Integer.parseInt(leftElementConfig.get().getExpression()));
                        break;
                    case 63://层级
                        int resourceStructureId = Integer.parseInt(leftElementConfig.get().getExpression());
                        List<ResourceStructure> allRelatedResourceStructureList = new ArrayList<>();
                        allRelatedResourceStructureList.add(resourceStructureManager.getResourceStructureById(resourceStructureId));
                        resourceStructureManager.getAllFlatChildren(resourceStructureId, allRelatedResourceStructureList);
                        tmpLinkConfigIdResourceStructureIdHashMap.put(linkconfig.getLinkConfigId(), allRelatedResourceStructureList.stream().map(ResourceStructure::getResourceStructureId).collect(Collectors.toSet()));
                        break;
                    default:
                        break;
                }
                tmpLinkConfigIdEventConditionHashMap.put(linkconfig.getLinkConfigId(), elementConfigDTO.getExpression());
                if (elementConfigDTO.getElementId().equals(31)) {
                    //AlarmChange的OperationType为1代表告警开始
                    tmpLinkConfigIdAlarmChangeOperationTypeHashMap.put(linkconfig.getLinkConfigId(), 1);
                } else if (elementConfigDTO.getElementId().equals(32)) {
                    //AlarmChange的OperationType为2代表告警结束
                    tmpLinkConfigIdAlarmChangeOperationTypeHashMap.put(linkconfig.getLinkConfigId(), 2);
                } else if (elementConfigDTO.getElementId().equals(35)) {
                    //AlarmChange的OperationType为3代表告警确认
                    tmpLinkConfigIdAlarmChangeOperationTypeHashMap.put(linkconfig.getLinkConfigId(), 3);
                }
            }
        }
    }

    private boolean checkTimeSpanValid(LinkConfig linkConfig) {
        if (linkConfig.getStartTime() == null && linkConfig.getEndTime() == null) {
            return true;
        } else {
            Date now = new Date();
            return linkConfig.getStartTime() != null && linkConfig.getEndTime() != null && linkConfig.getStartTime().before(now) && linkConfig.getEndTime().after(now);
        }
    }

    private boolean eventStartLinkConfigTrigger(AlarmChange alarmChange, String eventSeverityStr, Integer alarmChangeOperationType) {
        boolean trigger = false;
        if (!alarmChange.getOperationType().equals(alarmChangeOperationType)) {
            return false;
        }
        if (eventSeverityStr == null) {
            return true;
        }
        String[] tmpArray = eventSeverityStr.split(":");
        if (tmpArray.length != 2) {
            return false;
        }
        if ("severity".equalsIgnoreCase(tmpArray[0])) {
            String[] severityIdArray = tmpArray[1].split(",");
            if (severityIdArray.length == 0) {
                return false;
            }
            List<Integer> severityIdList = Arrays.asList(severityIdArray).stream().map(Integer::parseInt).toList();
            if (severityIdList.contains(alarmChange.getEventLevel())) {
                trigger = true;
            }
        }
        return trigger;
    }

    @Override
    public void onApplicationEvent(BaseSpringEvent<T> event) {
        if (!haStatusService.isMasterHost()) {
            log.info("HAStatus is BACKUP:后台联动事件监听退出");
            return;
        }
        if (event.getData() instanceof AlarmChange alarmChange) {
            if (linkConfigIdPointIdHashMap.isEmpty() && linkConfigIdEquipmentIdHashMap.isEmpty() && linkConfigIdResourceStructureIdHashMap.isEmpty()) {
                return;
            }
            String eventSeverityStr = null;
            Integer alarmChangeOperationType = null;
            String pointId = alarmChange.getStationId() + "," + alarmChange.getEquipmentId() + "," + alarmChange.getEventId();
            for (Map.Entry<Integer, String> entry : linkConfigIdPointIdHashMap.entrySet()) {
                if (pointId.equals(entry.getValue())) {
                    eventSeverityStr = linkConfigIdEventConditionHashMap.get(entry.getKey());
                    alarmChangeOperationType = linkConfigIdAlarmChangeOperationTypeHashMap.get(entry.getKey());
                    executeEventLinkInstance(alarmChange, eventSeverityStr, alarmChangeOperationType, entry.getKey());
                }
            }
            for (Map.Entry<Integer, Integer> entry : linkConfigIdEquipmentIdHashMap.entrySet()) {
                if (alarmChange.getEquipmentId().equals(entry.getValue())) {
                    eventSeverityStr = linkConfigIdEventConditionHashMap.get(entry.getKey());
                    alarmChangeOperationType = linkConfigIdAlarmChangeOperationTypeHashMap.get(entry.getKey());
                    executeEventLinkInstance(alarmChange, eventSeverityStr, alarmChangeOperationType, entry.getKey());
                }
            }
            for (Map.Entry<Integer, Set<Integer>> entry : linkConfigIdResourceStructureIdHashMap.entrySet()) {
                if (entry.getValue().contains(alarmChange.getResourceStructureId())) {
                    eventSeverityStr = linkConfigIdEventConditionHashMap.get(entry.getKey());
                    alarmChangeOperationType = linkConfigIdAlarmChangeOperationTypeHashMap.get(entry.getKey());
                    executeEventLinkInstance(alarmChange, eventSeverityStr, alarmChangeOperationType, entry.getKey());
                }
            }
        } else if (event.getData() instanceof LinkConfig linkConfig) {
            LinkInstanceExecutor expressionExecutor = new LinkInstanceExecutor(linkConfig.getLinkConfigId(), true, linkExecutorService, threadPoolExecutor);
            //如果是Cron表达式定时触发，则将联动控制加入线程池立即执行
            threadPoolExecutor.execute(expressionExecutor);
        }
    }

    private void executeEventLinkInstance(AlarmChange alarmChange, String eventSeverityStr, Integer alarmChangeOperationType, Integer linkConfigId) {
        if ((Integer.valueOf(1).equals(alarmChangeOperationType) && eventStartLinkConfigTrigger(alarmChange, eventSeverityStr, alarmChangeOperationType)) ||
                (Integer.valueOf(2).equals(alarmChangeOperationType) && alarmChange.getOperationType().equals(alarmChangeOperationType)) ||
                (Integer.valueOf(3).equals(alarmChangeOperationType) && alarmChange.getOperationType().equals(alarmChangeOperationType))) {
            LinkInstanceExecutor expressionExecutor = new LinkInstanceExecutor(linkConfigId, false, linkExecutorService, threadPoolExecutor);
            //如果是事件触发，则将联动控制加入线程池立即执行
            threadPoolExecutor.execute(expressionExecutor);
        }
    }
}
