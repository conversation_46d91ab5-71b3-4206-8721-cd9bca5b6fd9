package com.siteweb.admin.report;

import cn.hutool.core.text.CharSequenceUtil;
import com.siteweb.admin.entity.AuditReport;
import com.siteweb.admin.entity.SecurityReport;
import com.siteweb.admin.enums.AuditReportTypeEnum;
import com.siteweb.admin.enums.SecurityReportTypeEnum;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.AuditReportService;
import com.siteweb.admin.service.SecurityReportService;
import com.siteweb.common.event.BaseSpringEvent;
import com.siteweb.common.exception.UserValidException;
import com.siteweb.common.util.IpUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.utility.service.SystemConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.DependsOn;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.HashSet;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@DependsOn("liquibase")
public class SecurityAuditManager {
    public static final String ADMIN = "admin";
    @Autowired
    SystemConfigService systemConfigService;
    @Autowired
    AuditReportService auditReportService;
    @Autowired
    SecurityReportService securityReportService;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;

    @Autowired
    ApplicationEventPublisher applicationEventPublisher;
    /**
     * 是否开启审计报表
     */
    private Boolean enableAudit;
    /**
     * 是否开启安全日志报表
     */
    private Boolean enableSecurity;
    /**
     * 审计级别
     */
    private HashSet<Integer> auditLevel;
    /**
     * 审计报表当前的数量
     */
    private final AtomicInteger auditCurrentCount = new AtomicInteger();

    @PostConstruct
    public void init() {
        //设置当前审计报表的数量
        auditCurrentCount.set(auditReportService.findCurrentCount());
        loadSecuritySetting();
    }

    /**
     * 加载安全日志报表与审计报表的配置
     */
    private void loadSecuritySetting() {
        enableAudit = auditReportService.isEnableAudit();
        auditLevel = auditReportService.findAuditLevel();
        enableSecurity = securityReportService.isEnableSecurity();
    }

    /**
     * 同步审计、安全日志的配置项
     */
    @Scheduled(fixedDelay = 2 * 1000) // every 2 seconds
    protected void syncSetting() {
        this.loadSecuritySetting();
    }


    /**
     * 记录审计日志
     *
     * @param auditReportTypeEnum 审计日志类型
     * @param details             需要记录的描述信息
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NESTED)
    public void recordAuditReport(AuditReportTypeEnum auditReportTypeEnum, String details) {
        if (Boolean.FALSE.equals(enableAudit)) {
            return;
        }
        if (!auditLevel.contains(auditReportTypeEnum.getLevel())) {
            return;
        }
        AuditReport auditReport = new AuditReport(auditReportTypeEnum.getLevel(), details);
        auditReport.setResult(messageSourceUtil.getMessage("audit.report.operationSuccess"));
        auditReport.setType(auditReportTypeEnum.getDescribe());
        auditReport.setClientIp(IpUtil.getIpAddr());
        auditReport.setCreateTime(new Date());
        try {
            auditReport.setOperationAccount(TokenUserUtil.getLoginUserName());
        } catch (UserValidException e) {
            auditReport.setOperationAccount(ADMIN);
        }
        auditReportService.saveAuditReport(auditReport);
        this.auditReportCountIncrement();
    }

    /**
     * 记录审计日志
     *
     * @param auditReportTypeEnum 审计日志类型
     * @param details             需要记录的描述信息
     * @param isSuccess 操作是否成功
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NESTED)
    public void recordAuditReport(AuditReportTypeEnum auditReportTypeEnum, String details, Boolean isSuccess) {
        if (Boolean.FALSE.equals(enableAudit)) {
            return;
        }
        if (!auditLevel.contains(auditReportTypeEnum.getLevel())) {
            return;
        }
        AuditReport auditReport = new AuditReport(auditReportTypeEnum.getLevel(), details);
        auditReport.setResult(Boolean.TRUE.equals(isSuccess)?messageSourceUtil.getMessage("audit.report.operationSuccess"):messageSourceUtil.getMessage("audit.report.operationFail"));
        auditReport.setType(auditReportTypeEnum.getDescribe());
        auditReport.setClientIp(IpUtil.getIpAddr());
        auditReport.setCreateTime(new Date());
        try {
            auditReport.setOperationAccount(TokenUserUtil.getLoginUserName());
        } catch (UserValidException e) {
            auditReport.setOperationAccount(ADMIN);
        }
        auditReportService.saveAuditReport(auditReport);
        auditReportCountIncrement();
    }

    /**
     * 记录审计日志(主要用于已只账户的情况下使用如登录登出)
     *
     * @param auditReportTypeEnum 审计枚举
     * @param operationAccount    操作账户
     * @param details             详情
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NESTED)
    public void recordAuditReport(AuditReportTypeEnum auditReportTypeEnum, String operationAccount, String details) {
        if (Boolean.FALSE.equals(enableAudit)) {
            return;
        }
        if (!auditLevel.contains(auditReportTypeEnum.getLevel())) {
            return;
        }
        AuditReport auditReport = new AuditReport(auditReportTypeEnum.getLevel(), details);
        auditReport.setResult(messageSourceUtil.getMessage("audit.report.operationSuccess"));
        auditReport.setType(auditReportTypeEnum.getDescribe());
        auditReport.setClientIp(IpUtil.getIpAddr());
        auditReport.setCreateTime(new Date());
        auditReport.setOperationAccount(operationAccount);
        auditReportService.saveAuditReport(auditReport);
        auditReportCountIncrement();
    }

    /**
     * 记录安全日志(主要记录账户的登录登出黑名单限制等情况)
     *
     * @param operationAccount       操作账户
     * @param details                详情
     * @param securityReportTypeEnum 类别
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NESTED)
    public void recordSecurityReport(String operationAccount, String details, SecurityReportTypeEnum securityReportTypeEnum) {
        if (Boolean.FALSE.equals(enableSecurity)) {
            return;
        }
        SecurityReport securityReport = new SecurityReport();
        securityReport.setClientIp(IpUtil.getIpAddr());
        securityReport.setCreateTime(new Date());
        securityReport.setOperationAccount(operationAccount);
        securityReport.setDetails(details);
        securityReport.setType(securityReportTypeEnum.getType());
        securityReportService.saveSecurityReport(securityReport);
        BaseSpringEvent<SecurityReport> alarmChangeBaseSpringEvent = new BaseSpringEvent<>(securityReport);
        alarmChangeBaseSpringEvent.setData(securityReport);
        applicationEventPublisher.publishEvent(alarmChangeBaseSpringEvent);
    }

    public void recordAuditReport(String operationAccount, Integer level, String type, String details, String clientIp, String result) {
        if (Boolean.FALSE.equals(enableAudit)) {
            return;
        }
        if (!auditLevel.contains(level)) {
            return;
        }
        if (CharSequenceUtil.isBlank(operationAccount)) {
            operationAccount = ADMIN;
        }
        if (CharSequenceUtil.isBlank(clientIp)) {
            clientIp = IpUtil.LOCAL_IP;
        }
        if (CharSequenceUtil.isBlank(result)) {
            result = messageSourceUtil.getMessage("audit.report.operationSuccess");
        }
        AuditReport auditReport = new AuditReport(operationAccount, level, clientIp, details, new Date());
        auditReport.setResult(result);
        auditReport.setType(type);
        auditReportService.saveAuditReport(auditReport);
        auditReportCountIncrement();
    }

    /**
     * 审计报表计数增加一
     *
     */
    private synchronized void auditReportCountIncrement() {
        Integer currentCount = auditCurrentCount.incrementAndGet();
        Integer auditMaxCount = auditReportService.findAuditMaxCount();
        if (currentCount > auditMaxCount) {
            //需要删除的数量 = 最新的数量 - 最大的数量
            auditReportService.removeRecordCount(currentCount - auditMaxCount);
            auditCurrentCount.set(auditMaxCount);
        }
    }
}
