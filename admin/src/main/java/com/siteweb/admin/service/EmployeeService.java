package com.siteweb.admin.service;


import cn.hutool.poi.excel.ExcelWriter;
import com.siteweb.admin.entity.Employee;
import com.siteweb.admin.vo.EmployeeVO;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface EmployeeService {

    Employee findByEmployeeId(int employeeId);

    List<String> findEmployeeNameByIds(List<Integer> employeeIds);

    List<String> findEmployeePhoneByIds(List<Integer> employeeIds);

    List<String> findEmployeeEmailByIds(List<Integer> notifyPersonIds);

    List<Employee> findByEmployeeIds(List<Integer> employeeIdList);

    List<Employee> findEmployeeByDepartmentPermission(Integer userId);

    int createEmployee(Integer operatorId,EmployeeVO employeeVO);

    int batchCreateEmployee(Integer operatorId,List<Employee> employeeList);

    int updateEmployee(EmployeeVO employeeVO);
    int updateEmployeeByDescription(EmployeeVO employeeVO);

    int deleteEmployeeById(int employeeId);
    int deleteEmployeesByDescription(List<String> descriptionList);

    /**
     * 导出部门人员信息excel
     * @param departmentId 部门id
     * @param isHidden 是否隐藏敏感信息
     * @param title 标题
     * @return
     */
    ExcelWriter exportEmployee(int departmentId, boolean isHidden, String title);

    List<Employee> findEmployeesByDepartmentIds(Collection<Integer> departmentPermissionSet);

    Integer findDepartmentIdByEmployeeId(Integer employeeId);

    Set<Integer> findEmployeeIdsByDepartmentPermission(Integer employeeId);

    /**
     * 判断人员关联门卡总数
     * @param employeeId
     * @return
     */
    int findCardCountByEmployeeId(Integer employeeId);

    /**
     * 通过员工id获取员工的工号
     * @param employeeIds 员工ids
     * @return {@link List }<{@link String }>
     */
    List<String> findEmployeeJobNumberByIds(List<Integer> employeeIds);

    boolean updateNameById(Integer employeeId, String employeeName);
}
