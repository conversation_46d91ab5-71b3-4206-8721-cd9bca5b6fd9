package com.siteweb.admin.service;

import com.siteweb.admin.dto.OperationGroupMapCreateDTO;
import com.siteweb.admin.entity.OperationGroupMap;

import java.util.List;

public interface OperationGroupMapService {
    List<OperationGroupMap> findByGroupId(Integer groupId);

    boolean create(OperationGroupMapCreateDTO operationGroupMapCreateDTO);

    void deleteByGroupId(Integer groupId);

    List<Integer> findPermissionIdsByGroupIds(List<Integer> permissionGroupIds);
}
