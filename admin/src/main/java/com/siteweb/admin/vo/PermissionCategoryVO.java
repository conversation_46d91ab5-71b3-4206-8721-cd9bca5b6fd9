package com.siteweb.admin.vo;

import com.siteweb.admin.entity.PermissionCategory;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR> zhou
 * @Description PermissionCategoryVO
 * @createTime 2022-01-04 09:58:07
 */
@Data
@NoArgsConstructor
public class PermissionCategoryVO {

    private Integer permissionCategoryId;

    private String name;

    private String caption;

    private String description;

    public PermissionCategory build() {
        PermissionCategory permissionCategory = new PermissionCategory();
        BeanUtils.copyProperties(this, permissionCategory);
        return permissionCategory;
    }
}
