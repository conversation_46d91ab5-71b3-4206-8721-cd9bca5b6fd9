package com.siteweb.admin.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import com.siteweb.admin.entity.SpecialtyGroup;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.SpecialtyGroupService;
import com.siteweb.admin.vo.SpecialtyGroupVO;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> zhou @Description SpecialtyGroupController
 * @createTime 2022-01-17 08:48:23
 */
@RestController
@RequestMapping("/api")
@Api(value = "SpecialtyGroupController", tags = {"专业权限组操作接口"})
public class SpecialtyGroupController {

    @Autowired
    SpecialtyGroupService specialtyGroupService;


    /**
     * @param specialtyGroupVO: the specialtyGroupVO to create
     * @description post /specialtygroups : create a new specialtyGroup
     * <AUTHOR> zhou
     * @updateTime 2022/1/17 9:55
     * @return: ResponseEntity<ResponseResult>
     */
    @Operation(summary = "创建一个专业权限组")
    @ApiOperationSupport(ignoreParameters = {"specialtyGroupId"})
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "201",
                            description = "新的专业权限组已被创建",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "400",
                            description = "新的专业权限组其specialtyGroupId必须为null",
                            content = @Content)
            })
    @PostMapping(value = "/specialtygroups", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createSpecialtyGroup(
            @Valid @RequestBody SpecialtyGroupVO specialtyGroupVO) {
        if (null != specialtyGroupVO.getSpecialtyGroupId()) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "A new SpecialtyGroup cannot have an specialtyGroupId",
                    HttpStatus.BAD_REQUEST);
        }
        Integer result = specialtyGroupService.createSpecialtyGroup(specialtyGroupVO.build());
        if (result > 0) {
            specialtyGroupVO.setSpecialtyGroupId(result);
            return ResponseHelper.successful(specialtyGroupVO.build());
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.PRIMARY_KEY_ASSIGN_ERROR.value()),
                    "Can not get specialtyGroupId",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * PUT /specialtygroups : Updates an existing specialtyGroupVO.
     *
     * @param specialtyGroupVO the specialtyGroupVO to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated specialtyGroupVO, or
     * with status 400 (Bad Request) if the specialtyGroupVO is not valid, or with status 500
     * (Internal Server Error) if the specialtyGroupVO couldn't be updated
     */
    @Operation(summary = "修改一个专业权限组")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "专业权限组已被修改",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "400",
                            description = "要修改的专业权限组其specialtyGroupId不能为null",
                            content = @Content)
            })
    @PutMapping(value = "/specialtygroups", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateSpecialtyGroup(
            @Valid @RequestBody SpecialtyGroupVO specialtyGroupVO) {
        if (null == specialtyGroupVO.getSpecialtyGroupId()) {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "specialtyGroupId is null",
                    HttpStatus.BAD_REQUEST);
        }
        int result = specialtyGroupService.updateSpecialtyGroup(specialtyGroupVO.build());
        if (result > 0) {
            return ResponseHelper.successful(HttpStatus.OK);
        } else {
            return ResponseHelper.failed(
                    String.valueOf(ErrorCode.REQUEST_PARAMETER_INVALID.value()),
                    "SpecialtyGroupId input error",
                    HttpStatus.BAD_REQUEST);
        }
    }

    /**
     * GET /specialtygroups/ : get all specialtyGroups.
     *
     * @return the ResponseEntity with status 200 (OK) and with body of the specialtyGroup list
     */
    @Operation(summary = "查询所有专业权限组")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回所有专业权限组",
                            content = {@Content})
            })
    @GetMapping(value = "/specialtygroups", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllSpecialtyGroups() {
        List<SpecialtyGroup> specialtyGroups = specialtyGroupService.findAllSpecialtyGroups();
        return ResponseHelper.successful(specialtyGroups, HttpStatus.OK);
    }

    @Operation(summary = "根据角色查询所有专业权限组")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "返回所有专业权限组",
                            content = {@Content})
            })
    @GetMapping(value = "/specialtygroups/role", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllSpecialtyGroupsByRole() {
        List<SpecialtyGroup> specialtyGroups = new ArrayList<>();
        List<Integer> roleIds = TokenUserUtil.getRoles();
        if (CollectionUtil.isEmpty(roleIds)) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.ROLE_NOT_FOUND_ERROR.value()),
                    "specialtygroupsbyrole error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
        specialtyGroups = specialtyGroupService.findAllSpecialtyGroupsByRole(roleIds);
        return ResponseHelper.successful(specialtyGroups, HttpStatus.OK);
    }

    /**
     * GET /specialtygroups/:id : get the "id" specialtyGroup.
     *
     * @param specialtyGroupId the id of the specialtyGroup to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the specialtyGroup, or with
     * status 404 (Not Found)
     */
    @Operation(summary = "根据专业权限组ID查询单个专业权限组")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "根据专业权限组ID查询到的单个专业权限组",
                            content = {@Content}),
                    @ApiResponse(responseCode = "404", description = "根据专业权限组ID查询不到专业权限组", content = @Content)
            })
    @GetMapping(
            value = "/specialtygroups/{specialtyGroupId}",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getSpecialtyGroupById(
            @ApiParam(name = "specialtyGroupId", value = "专业权限组ID", required = true) @PathVariable
                    Integer specialtyGroupId) {
        SpecialtyGroup specialtyGroup = specialtyGroupService.findById(specialtyGroupId);
        return Optional.ofNullable(specialtyGroup)
                .map(result -> ResponseHelper.successful(result, HttpStatus.OK))
                .orElse(new ResponseEntity<>(HttpStatus.NOT_FOUND));
    }

    /**
     * DELETE /specialtygroups/:specialtyGroupId : delete the specialtyGroup by its specialtyGroupId.
     *
     * @param specialtyGroupId the id of the specialtyGroup to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @Operation(summary = "根据专业权限组ID删除单个专业权限组")
    @ApiResponses(
            value = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "专业权限组被删除",
                            content = {@Content}),
                    @ApiResponse(
                            responseCode = "404",
                            description = "根据SpecialtyGroupId查询不到SpecialtyGroup实体",
                            content = @Content)
            })
    @DeleteMapping(value = "/specialtygroups/{specialtyGroupId}")
    public ResponseEntity<ResponseResult> deleteSpecialtyGroupById(
            @ApiParam(name = "specialtyGroupId", value = "专业权限组ID", required = true) @PathVariable
                    Integer specialtyGroupId) {
        SpecialtyGroup specialtyGroup = specialtyGroupService.findById(specialtyGroupId);
        if (specialtyGroup == null) {
            return ResponseHelper.successful(HttpStatus.NOT_FOUND);
        }
        specialtyGroupService.deleteById(specialtyGroupId);
        return ResponseHelper.successful(HttpStatus.OK);
    }
}
