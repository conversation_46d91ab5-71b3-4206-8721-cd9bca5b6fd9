FROM openjdk:17-jdk-alpine
RUN apk update
RUN apk add -U tzdata
RUN cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN apk add --update font-adobe-100dpi ttf-dejavu fontconfig
RUN mkdir /home/<USER>
ENV LANG C.UTF-8
VOLUME /tmp
WORKDIR /home/<USER>
ADD ./core/target/siteweb6-core-1.0-SNAPSHOT.jar /home/<USER>/siteweb6-core-1.0-SNAPSHOT.jar
EXPOSE 8200
ENTRYPOINT ["java", "-Xms4096m", "-Xmx8192m","-Xmn2048m","-Xss1024K", "-jar", "-Dspring.profiles.active=prod", "-Dspring.config.location=/home/<USER>/config/application-prod.yml ","-Duser.timezone=Asia/Shanghai","siteweb6-core-1.0-SNAPSHOT.jar"]
