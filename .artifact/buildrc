#/bin/bash
# ==========================================================================================
# 制品编译前准备脚本
# ==========================================================================================
# 环境变量
# $ENV_ARTIFACTSYMBOL  制品符号、制品标识名、服务名
# $ENV_ARTIFACTID      制品ID
# $ENV_ARTIFACTNAME    制品名
# $ENV_PROJECT_ROOT    上一级目录绝对路径（源码根目录）
# ... 更多环境变量查阅 交付包管理台
# ==========================================================================================
# 目录
# $ENV_PROJECT_ROOT/.config 目录为制品的配置文件目录，当前脚本负责维护.config目录内的一切文件保持最新状态。
# .config 目录下的一切文件最终将部署至服务器的“/siteweb/”目录下
# dockerfile                此制品的docker打包脚本
# docker-compose.yml        此制品的 docker-compose 配置文件
# ==========================================================================================
/bin/cp -rf ../core/src/main/resources/application-prod.yml ../.config/backend/config
/bin/cp -rf ../core/src/main/resources/Banner.txt           ../.config/backend/config
/bin/cp -rf ../core/src/main/resources/logback-spring.xml   ../.config/backend/config
/bin/cp -rf ../core/src/main/resources/s6-server.p12        ../.config/backend/config
/bin/cp -rf ../core/src/main/resources/trustKeys.p12        ../.config/backend/config

# 空调节能模块  协议库
mkdir ../.config/backend/upload-dir/soFile/
/bin/cp -rf ../airconditioncontrol/src/main/resources/soFile/ACLogCtrl.so ../.config/backend/upload-dir/soFile/ACLogCtrl.so