version: "3.8"
services:
  backend:
    container_name: siteweb-server
    image: vertiv/siteweb_backend
    ports:
      - "8200:8200"
    environment:
      LANG: "en_US.UTF-8"
      TZ: "Asia/Shanghai"
    restart: always
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /siteweb/backend/logs:/home/<USER>/logs #日志文件
      - /siteweb/backend/upload-dir:/home/<USER>/upload-dir #日志文件
      - /siteweb/backend/config:/home/<USER>/config:ro #日志文件
      - /siteweb/nginx/assets/json:/home/<USER>/assets/json #前端app.json
    networks:
      monitoring:
        ipv4_address: ************
    extra_hosts:
      - siteweb.mysql:***********
      - siteweb.redis:***********
      - siteweb.influxdb:***********
      - siteweb.mongo:***********
    depends_on:
      mysql:
        condition: service_healthy
    tty: true

networks:
  monitoring:
    driver: bridge
    external: true
