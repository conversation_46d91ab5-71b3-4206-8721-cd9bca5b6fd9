package com.siteweb.as.controller.realtime;

import com.siteweb.as.dto.ActiveSignalAS;
import com.siteweb.as.manager.ActiveSignalASManager;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.hmi.service.GraphicHistoryDataPointService;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.mamager.EquipmentManager;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 活动告警表
 *
 */
@RestController
@Slf4j
@RequestMapping("/api")
@Api(value = "ActiveSignalASController", tags = {"ActiveSignal操作接口"})
public class ActiveSignalSampleController {
    @Autowired
    EquipmentManager equipmentManager;
    @Autowired
    ActiveSignalASManager activeSignalManagerAS;
    @Autowired
    GraphicHistoryDataPointService graphicHistoryDataPointService;
    @ApiOperation(value = "获取控制关联信号")
    @GetMapping(value = "realtime/ActiveSignalSample", params = {"stationId","equipmentid","objectids"})
    public ResponseEntity<ResponseResult> getActiveSignalByEquipId(Integer stationId, Integer equipmentid, Integer objectids) {
        Equipment equipment = equipmentManager.getEquipmentById(equipmentid);
        if (equipment == null) {
            return null;
        }
        ActiveSignalAS activeSignal = activeSignalManagerAS.getConfigSignalItemByEquipmentIdAndSignalId(equipmentid, objectids);
        List<Object> activeSignalList = new ArrayList<>();
        HashMap signalmap = new HashMap();
        signalmap.put("signalCategory", activeSignal.getSignalCategory());
        signalmap.put("name", activeSignal.getSignalName());
        signalmap.put("signalUnit", activeSignal.getUnit());
        signalmap.put("value", activeSignal.getCurrentValue());
        signalmap.put("sampleTime", activeSignal.getSampleTime());
        signalmap.put("signalMeaning", activeSignal.getSignalMeaning());
        activeSignal.getCurrentValue().setValueType(0);
        if (activeSignal.getCurrentValue().getStringValue() != null)
            activeSignal.getCurrentValue().setFloatValue(Float.valueOf(activeSignal.getCurrentValue().getStringValue()));

        activeSignalList.add(signalmap);
        return ResponseHelper.successful(activeSignalList);
    }
}
