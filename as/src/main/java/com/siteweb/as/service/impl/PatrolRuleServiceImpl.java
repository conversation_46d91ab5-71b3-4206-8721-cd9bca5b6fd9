package com.siteweb.as.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.as.dto.patrol.*;
import com.siteweb.as.entity.*;
import com.siteweb.as.mapper.*;
import com.siteweb.as.service.PatrolRuleService;
import com.siteweb.as.util.PatrolRuleUtil;
import com.siteweb.common.exception.BusinessException;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.monitoring.mamager.EquipmentBaseTypeManager;
import com.siteweb.utility.dto.ImportErrorInfoDTO;
import com.siteweb.utility.entity.EquipmentBaseType;
import com.siteweb.utility.entity.SignalBaseDic;
import com.siteweb.utility.mapper.SignalBaseDicMapper;
import com.siteweb.utility.service.StandardVerService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PatrolRuleServiceImpl implements PatrolRuleService {
    private static final String COMMON_FIELD_CANNOT_EMPTY = "common.field.cannotEmpty";
    public static final String NOT_FOUND = " 没找到";
    private final PatrolRuleMapper patrolRuleMapper;
    private final StandardVerService standardVerService;
    private final PatrolGroupRuleMapMapper patrolGroupRuleMapMapper;
    private final PatrolCalopMapper patrolCalopMapper;
    private final PatrolUnitMapper patrolUnitMapper;
    private final PatrolWarningLevelMapper patrolWarningLevelMapper;
    private final SignalBaseDicMapper signalBaseDicMapper;
    private final PatrolStandardRuleMapper patrolStandardRuleMapper;
    private final LocaleMessageSourceUtil messageSourceUtil;
    private final EquipmentBaseTypeManager equipmentBaseTypeManager;
    private final TransactionTemplate transactionTemplate;


    @Override
    public List<PatrolRuleDTO> getAllPatrolRule() {
        int standardVer = standardVerService.getStandardVer();
        List<PatrolRuleDTO> patrolRuleDTOList = patrolRuleMapper.getAllPatrolRule(standardVer);
        patrolRuleDTOList.forEach(patrolRuleDTO -> {
            List<PatrolRuleDTO.StandardInfo> standardInfos = patrolRuleDTO.getStandardInfos();
            patrolRuleDTO.setStandardInfo(standardInfos.stream()
                    .map(info -> info.getStandardDicId() + " : " + info.getSignalStandardName())
                    .collect(Collectors.joining(",")));
            patrolRuleDTO.setLimitMeaning(PatrolRuleUtil.calculateLimitMeaning(patrolRuleDTO));
        });
        return patrolRuleDTOList;
    }

    @Override
    public PatrolRuleDTO getPatrolRule(Integer ruleId) {
        int standardVer = standardVerService.getStandardVer();
        PatrolRuleDTO patrolRuleDTO = patrolRuleMapper.getPatrolRule(standardVer, ruleId);
        if (Objects.isNull(patrolRuleDTO)) {
            return null;
        }
        List<PatrolRuleDTO.StandardInfo> standardInfos = patrolRuleDTO.getStandardInfos();
        patrolRuleDTO.setStandardInfo(standardInfos.stream()
                .map(info -> info.getStandardDicId() + " : " + info.getSignalStandardName())
                .collect(Collectors.joining(",")));
        patrolRuleDTO.setLimitMeaning(PatrolRuleUtil.calculateLimitMeaning(patrolRuleDTO));
        return patrolRuleDTO;
    }

    @Override
    public List<PatrolRuleDTO> getPatrolRuleByGroup(Integer groupId) {
        int standardVer = standardVerService.getStandardVer();
        List<PatrolRuleDTO> patrolRuleDTOList = patrolRuleMapper.getPatrolRuleByGroup(standardVer, groupId);
        patrolRuleDTOList.forEach(patrolRuleDTO -> {
            List<PatrolRuleDTO.StandardInfo> standardInfos = patrolRuleDTO.getStandardInfos();
            patrolRuleDTO.setStandardInfo(standardInfos.stream()
                    .map(info -> info.getStandardDicId() + " : " + info.getSignalStandardName())
                    .collect(Collectors.joining(",")));
            patrolRuleDTO.setLimitMeaning(PatrolRuleUtil.calculateLimitMeaning(patrolRuleDTO));
        });
        return patrolRuleDTOList;
    }

    @Override
    public void savePatrolRule(PatrolRule patrolRule) {
        if (CharSequenceUtil.isBlank(patrolRule.getRuleName())) {
            throw new BusinessException("规则名称不能为空");
        }
        Long count = patrolRuleMapper.selectCount(Wrappers.lambdaQuery(PatrolRule.class).eq(PatrolRule::getRuleName, patrolRule.getRuleName()));
        if (count > 0) {
            throw new BusinessException("规则名称不能重复");
        }
        patrolRuleMapper.insert(patrolRule);
    }

    @Override
    public void updatePatrolRule(PatrolRule patrolRule) {
        if (CharSequenceUtil.isBlank(patrolRule.getRuleName())) {
            throw new BusinessException("规则名称不能为空");
        }
        Long count = patrolRuleMapper.selectCount(Wrappers.lambdaQuery(PatrolRule.class)
                .eq(PatrolRule::getRuleName, patrolRule.getRuleName())
                .ne(PatrolRule::getRuleId, patrolRule.getRuleId())
        );
        if (count > 0) {
            throw new BusinessException("规则名称不能重复");
        }
        patrolRuleMapper.updateById(patrolRule);
    }

    @Override
    public void deletePatrolRule(Integer ruleId) {
        Long count = patrolGroupRuleMapMapper.selectCount(Wrappers.lambdaQuery(PatrolGroupRuleMap.class).eq(PatrolGroupRuleMap::getRuleId, ruleId));
        if (count > 0) {
            throw new BusinessException("有关联的分组，未进行删除");
        }
        patrolRuleMapper.deleteById(ruleId);
    }

    @Override
    public List<PatrolCalop> listPatrolCalop() {
        return patrolCalopMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public List<PatrolUnit> listPatrolUnit() {
        return patrolUnitMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public List<PatrolWarningLevel> listPatrolWarningLevel() {
        return patrolWarningLevelMapper.selectList(Wrappers.emptyWrapper());
    }

    @Override
    public List<BaseEquipmentDTO> listBaseEquipment() {
        return patrolRuleMapper.listBaseEquipment();
    }

    @Override
    public List<SignalBaseDicDTO> listBaseSignal(Integer baseEquipmentId) {
        return patrolRuleMapper.listBaseSignal(baseEquipmentId);
    }

    @Override
    public String listStandardInfo(Long baseTypeId) {
        List<StandardInfoDTO> standardInfoDTOS = patrolRuleMapper.listStandardInfo(baseTypeId);
        if (CollUtil.isEmpty(standardInfoDTOS)) {
            return CharSequenceUtil.EMPTY;
        }
        return standardInfoDTOS.stream()
                .map(info -> info.getStandardDicId() + " : " + info.getSignalStandardName())
                .collect(Collectors.joining(","));
    }

    @Override
    public List<StandardEquipmentDTO> listStandardEquipment() {
        int standardVer = standardVerService.getStandardVer();
        return patrolStandardRuleMapper.selectAllStandardEquipment(standardVer);
    }

    @Override
    public List<StandardSignalDTO> listStandardSignal(Integer equipmentLogicClassId) {
        int standardVer = standardVerService.getStandardVer();
        return patrolStandardRuleMapper.listStandardSignalByEquipment(equipmentLogicClassId,standardVer);
    }

    @Override
    public List<PatrolStandardRuleDTO> getAllPatrolStandardRule() {
        int standardVer = standardVerService.getStandardVer();
        return patrolStandardRuleMapper.getAllPatrolStandardRule(standardVer);
    }

    @Override
    public PatrolStandardRuleDTO getPatrolStandardRule(Integer ruleId) {
        int standardVer = standardVerService.getStandardVer();
        PatrolStandardRuleDTO patrolStandardRule = patrolStandardRuleMapper.getPatrolStandardRule(standardVer, ruleId);
        if (Objects.isNull(patrolStandardRule)) {
            return null;
        }
        return patrolStandardRule;
    }

    @Override
    public List<PatrolStandardRuleDTO> getPatrolStandardRuleByGroup(Integer groupId) {
        int standardVer = standardVerService.getStandardVer();
        List<PatrolStandardRuleDTO> patrolRuleDTOList = patrolStandardRuleMapper.getPatrolStandardRuleByGroup(standardVer, groupId);

        return patrolRuleDTOList;
    }

    @Override
    public List<ImportErrorInfoDTO> importPatrolStandardRules(List<PatrolStandardRuleImportDTO> patrolRuleImportDTOS) {
        List<ImportErrorInfoDTO> importErrorInfoList = new ArrayList<>();
        List<PatrolRule> patrolRuleInsertList = new ArrayList<>();
        List<PatrolCalop> patrolCalops = patrolCalopMapper.selectList(Wrappers.emptyWrapper());
        List<PatrolUnit> patrolUnits = patrolUnitMapper.selectList(Wrappers.emptyWrapper());
        List<PatrolWarningLevel> patrolWarningLevels = patrolWarningLevelMapper.selectList(Wrappers.emptyWrapper());
        Map<String, Integer> patrolCalopMaps = patrolCalops.stream().collect(Collectors.toMap(PatrolCalop::getCalOperator, PatrolCalop::getCalOpId));
        Map<String, Integer> patrolUnitMaps = patrolUnits.stream().collect(Collectors.toMap(PatrolUnit::getUnitSymbol, PatrolUnit::getUnitId));
        Map<String, Integer> patrolWarningLevelMaps = patrolWarningLevels.stream().collect(Collectors.toMap(PatrolWarningLevel::getMeaning, PatrolWarningLevel::getLevelId));
        for (int i = 0; i < patrolRuleImportDTOS.size(); i++) {
            PatrolStandardRuleImportDTO patrolRuleImportDTO = patrolRuleImportDTOS.get(i);
            if (verifyStandardDataExist(patrolRuleImportDTO, importErrorInfoList, i)) {
                continue;
            }
            PatrolRule patrolRule = patrolRuleImportDTO.toPatrolRule();
            Integer limitDownCalOpId;
            limitDownCalOpId = patrolRuleImportDTO.getLimitDownCalOpId();
            if (Objects.isNull(limitDownCalOpId)) {
                limitDownCalOpId = patrolCalopMaps.get(patrolRuleImportDTO.getLimitDownCalOperator());
                if (Objects.isNull(limitDownCalOpId)) {
                    importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.limitDownCalOperator, patrolRuleImportDTO.getLimitDownCalOperator() + NOT_FOUND));
                    continue;
                }
            }
            patrolRule.setLimitDownCalOpId(limitDownCalOpId);
            Integer limitUpCalOpId;
            limitUpCalOpId = patrolRuleImportDTO.getLimitUpCalOpId();
            if (Objects.isNull(limitUpCalOpId)) {
                limitUpCalOpId = patrolCalopMaps.get(patrolRuleImportDTO.getLimitUpCalOperator());
                if (Objects.isNull(limitUpCalOpId)) {
                    importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.limitUpCalOperator, patrolRuleImportDTO.getLimitUpCalOperator() + NOT_FOUND));
                    continue;
                }
            }
            patrolRule.setLimitUpCalOpId(limitUpCalOpId);
            Integer unitId;
            unitId = patrolRuleImportDTO.getUnitId();
            if (Objects.isNull(unitId)) {
                unitId = patrolUnitMaps.get(patrolRuleImportDTO.getUnitSymbol());
                if (Objects.isNull(unitId)) {
                    importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.unitSymbol, patrolRuleImportDTO.getUnitSymbol() + NOT_FOUND));
                    continue;
                }
            }
            patrolRule.setUnitId(unitId);
            Integer warningLevelId;
            warningLevelId = patrolRuleImportDTO.getWarningLevelId();
            if (Objects.isNull(warningLevelId)) {
                warningLevelId = patrolWarningLevelMaps.get(patrolRuleImportDTO.getWarningMeaning());
                if (Objects.isNull(warningLevelId)) {
                    importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.warningMeaning, patrolRuleImportDTO.getWarningMeaning() + NOT_FOUND));
                    continue;
                }
            }
            patrolRule.setWarningLevelId(warningLevelId);
            Integer standardEquipmentId;
            standardEquipmentId = patrolRuleImportDTO.getEquipmentLogicClassId();
            if (Objects.isNull(standardEquipmentId)) {
                continue;
            }
            patrolRule.setEquipmentLogicClassId(standardEquipmentId);
            Integer standardDicId;
            standardDicId = patrolRuleImportDTO.getStandardDicId();
            if (Objects.isNull(standardDicId)) {
                continue;
            }
            patrolRule.setStandardDicId(standardDicId);
            patrolRuleInsertList.add(patrolRule);
        }
        transactionTemplate.execute(status -> {
            patrolRuleMapper.delete(null);
            patrolRuleMapper.insert(patrolRuleInsertList);
            return null; // 返回成功才会提交
        });
        return importErrorInfoList;
    }



    @Override
    public List<ImportErrorInfoDTO> importPatrolRules(List<PatrolRuleImportDTO> patrolRuleImportDTOS) {
        List<ImportErrorInfoDTO> importErrorInfoList = new ArrayList<>();
        List<PatrolRule> patrolRuleInsertList = new ArrayList<>();
        List<PatrolCalop> patrolCalops = patrolCalopMapper.selectList(Wrappers.emptyWrapper());
        List<PatrolUnit> patrolUnits = patrolUnitMapper.selectList(Wrappers.emptyWrapper());
        List<PatrolWarningLevel> patrolWarningLevels = patrolWarningLevelMapper.selectList(Wrappers.emptyWrapper());
        Map<String, Integer> patrolCalopMaps = patrolCalops.stream().collect(Collectors.toMap(PatrolCalop::getCalOperator, PatrolCalop::getCalOpId));
        Map<String, Integer> patrolUnitMaps = patrolUnits.stream().collect(Collectors.toMap(PatrolUnit::getUnitSymbol, PatrolUnit::getUnitId));
        Map<String, Integer> patrolWarningLevelMaps = patrolWarningLevels.stream().collect(Collectors.toMap(PatrolWarningLevel::getMeaning, PatrolWarningLevel::getLevelId));
        for (int i = 0; i < patrolRuleImportDTOS.size(); i++) {
            PatrolRuleImportDTO patrolRuleImportDTO = patrolRuleImportDTOS.get(i);
            if (verifyDataExist(patrolRuleImportDTO, importErrorInfoList, i)) {
                continue;
            }
            PatrolRule patrolRule = patrolRuleImportDTO.toPatrolRule();
            Integer limitDownCalOpId;
            limitDownCalOpId = patrolRuleImportDTO.getLimitDownCalOpId();
            if (Objects.isNull(limitDownCalOpId)) {
                limitDownCalOpId = patrolCalopMaps.get(patrolRuleImportDTO.getLimitDownCalOperator());
                if (Objects.isNull(limitDownCalOpId)) {
                    importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.limitDownCalOperator, patrolRuleImportDTO.getLimitDownCalOperator() + NOT_FOUND));
                    continue;
                }
            }
            patrolRule.setLimitDownCalOpId(limitDownCalOpId);
            Integer limitUpCalOpId;
            limitUpCalOpId = patrolRuleImportDTO.getLimitUpCalOpId();
            if (Objects.isNull(limitUpCalOpId)) {
                limitUpCalOpId = patrolCalopMaps.get(patrolRuleImportDTO.getLimitUpCalOperator());
                if (Objects.isNull(limitUpCalOpId)) {
                    importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.limitUpCalOperator, patrolRuleImportDTO.getLimitUpCalOperator() + NOT_FOUND));
                    continue;
                }
            }
            patrolRule.setLimitUpCalOpId(limitUpCalOpId);
            Integer unitId;
            unitId = patrolRuleImportDTO.getUnitId();
            if (Objects.isNull(unitId)) {
                unitId = patrolUnitMaps.get(patrolRuleImportDTO.getUnitSymbol());
                if (Objects.isNull(unitId)) {
                    importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.unitSymbol, patrolRuleImportDTO.getUnitSymbol() + NOT_FOUND));
                    continue;
                }
            }
            patrolRule.setUnitId(unitId);
            Integer warningLevelId;
            warningLevelId = patrolRuleImportDTO.getWarningLevelId();
            if (Objects.isNull(warningLevelId)) {
                warningLevelId = patrolWarningLevelMaps.get(patrolRuleImportDTO.getWarningMeaning());
                if (Objects.isNull(warningLevelId)) {
                    importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.warningMeaning, patrolRuleImportDTO.getWarningMeaning() + NOT_FOUND));
                    continue;
                }
            }
            patrolRule.setWarningLevelId(warningLevelId);
            Integer baseEquipmentId;
            baseEquipmentId = patrolRuleImportDTO.getBaseEquipmentId();
            if (Objects.isNull(baseEquipmentId)) {
                EquipmentBaseType equipmentBaseType = equipmentBaseTypeManager.getBaseEquipmentByName(patrolRuleImportDTO.getBaseEquipmentName());
                if (Objects.isNull(equipmentBaseType)) {
                    importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.baseEquipmentName, patrolRuleImportDTO.getBaseEquipmentName() + NOT_FOUND));
                    continue;
                }
                baseEquipmentId = equipmentBaseType.getBaseEquipmentId();
            }
            patrolRule.setBaseEquipmentId(baseEquipmentId);
            Long baseTypeId;
            baseTypeId = patrolRuleImportDTO.getBaseTypeId();
            if (Objects.isNull(baseTypeId)) {
                SignalBaseDic signalBaseDic = signalBaseDicMapper.selectOne(Wrappers.lambdaQuery(SignalBaseDic.class)
                        .eq(SignalBaseDic::getBaseTypeName, patrolRuleImportDTO.getBaseTypeName())
                        .eq(SignalBaseDic::getBaseEquipmentId, baseEquipmentId));
                if (Objects.isNull(signalBaseDic)) {
                    importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.baseTypeName, patrolRuleImportDTO.getBaseTypeName() + NOT_FOUND));
                    continue;
                }
                baseTypeId = signalBaseDic.getBaseTypeId();
            }
            patrolRule.setBaseTypeId(baseTypeId);
            patrolRuleInsertList.add(patrolRule);
        }
        transactionTemplate.execute(status -> {
            patrolRuleMapper.delete(null);
            patrolRuleMapper.insert(patrolRuleInsertList);
            return null; // 返回成功才会提交
        });
        return importErrorInfoList;
    }

    /**
     * 校验数据是否合法
     */
    private boolean verifyDataExist(PatrolRuleImportDTO patrolRuleImportDTO, List<ImportErrorInfoDTO> importErrorInfoList, int i) {
        boolean flag = false;
        if (CharSequenceUtil.isBlank(patrolRuleImportDTO.getRuleName())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.ruleName, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (Objects.isNull(patrolRuleImportDTO.getLimitDownCalOpId()) && CharSequenceUtil.isBlank(patrolRuleImportDTO.getLimitDownCalOperator())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.limitDownCalOperator, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (Objects.isNull(patrolRuleImportDTO.getLimitUpCalOpId()) && CharSequenceUtil.isBlank(patrolRuleImportDTO.getLimitUpCalOperator())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.limitUpCalOperator, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (Objects.isNull(patrolRuleImportDTO.getWarningLevelId()) && CharSequenceUtil.isBlank(patrolRuleImportDTO.getWarningMeaning())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.warningMeaning, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (Objects.isNull(patrolRuleImportDTO.getByPercentage()) && CharSequenceUtil.isBlank(patrolRuleImportDTO.getByPercentageMeaning())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.byPercentageMeaning, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (Objects.isNull(patrolRuleImportDTO.getBaseEquipmentId()) && CharSequenceUtil.isBlank(patrolRuleImportDTO.getBaseEquipmentName())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.baseEquipmentName, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (Objects.isNull(patrolRuleImportDTO.getBaseTypeId()) && CharSequenceUtil.isBlank(patrolRuleImportDTO.getBaseTypeName())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.baseTypeName, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }

        return flag;
    }

    private boolean verifyStandardDataExist(PatrolStandardRuleImportDTO patrolRuleImportDTO, List<ImportErrorInfoDTO> importErrorInfoList, int i) {
        boolean flag = false;
        if (CharSequenceUtil.isBlank(patrolRuleImportDTO.getRuleName())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.ruleName, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (Objects.isNull(patrolRuleImportDTO.getLimitDownCalOpId()) && CharSequenceUtil.isBlank(patrolRuleImportDTO.getLimitDownCalOperator())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.limitDownCalOperator, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (Objects.isNull(patrolRuleImportDTO.getLimitUpCalOpId()) && CharSequenceUtil.isBlank(patrolRuleImportDTO.getLimitUpCalOperator())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.limitUpCalOperator, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (Objects.isNull(patrolRuleImportDTO.getWarningLevelId()) && CharSequenceUtil.isBlank(patrolRuleImportDTO.getWarningMeaning())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.warningMeaning, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (Objects.isNull(patrolRuleImportDTO.getByPercentage()) && CharSequenceUtil.isBlank(patrolRuleImportDTO.getByPercentageMeaning())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.byPercentageMeaning, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (Objects.isNull(patrolRuleImportDTO.getEquipmentLogicClassId()) && CharSequenceUtil.isBlank(patrolRuleImportDTO.getStandardEquipmentName())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.baseEquipmentName, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }
        if (Objects.isNull(patrolRuleImportDTO.getStandardDicId()) && CharSequenceUtil.isBlank(patrolRuleImportDTO.getStandardSignalName())) {
            importErrorInfoList.add(ImportErrorInfoDTO.createImportErrorInfo(i, PatrolRuleImportDTO.Fields.baseTypeName, messageSourceUtil.getMessage(COMMON_FIELD_CANNOT_EMPTY)));
            flag = true;
        }

        return flag;
    }
}
