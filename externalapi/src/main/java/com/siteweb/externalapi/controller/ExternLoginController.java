package com.siteweb.externalapi.controller;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.admin.dto.AccountDTO;
import com.siteweb.admin.security.TokenUser;
import com.siteweb.admin.security.TokenUtil;
import com.siteweb.admin.service.AccountService;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.EncryptUtil;
import com.siteweb.externalapi.dto.LoginResult;
import com.siteweb.externalapi.vo.LoginVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.io.IOException;
import java.util.Base64;
import java.util.List;

@RestController
@RequestMapping("/api")
public class ExternLoginController {

    @Autowired
    TokenUtil tokenUtil;

    @Autowired
    AccountService accountService;

    @PostMapping("/external/login")
    public ResponseEntity<ResponseResult> externalUserLogin(@Valid @RequestBody LoginVO loginVO) throws IOException {

        if (ObjectUtil.isNull(loginVO)) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    "requestBody is null",
                    HttpStatus.BAD_REQUEST);
        }
        String userName = loginVO.getLoginId();
        try {
            byte[] decodedBytes = Base64.getDecoder().decode(loginVO.getPassword());
            String password = new String(decodedBytes);

            List<AccountDTO> accounts = accountService.findByLogonId(userName);
            if (accounts.isEmpty()) {
                return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                        "user not found",
                        HttpStatus.NOT_FOUND);
            }
            String loginType = loginVO.getLoginType();
            password = password + "[" + userName + "]";
            password = EncryptUtil.sha256(password);
            //password = EncryptUtil.changePasswordFormat(password);
            if (!password.equals(accounts.get(0).getPassword())) {
                return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                        "password is wrong",
                        HttpStatus.NOT_FOUND);
            }
            //根据传入的用户名，生成jwt token并返回给前端，从而实现登录功能
            TokenUser tokenUser = new TokenUser(accounts.get(0));
            String newToken = this.tokenUtil.createTokenForUser(tokenUser, loginType);

            return ResponseHelper.successful(new LoginResult( newToken));
        }  catch (Exception ex){
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    "password parse error",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}
