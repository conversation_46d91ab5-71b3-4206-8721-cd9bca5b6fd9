package com.siteweb.hmi.dto;
import com.siteweb.monitoring.entity.ActiveEvent;
import com.siteweb.monitoring.entity.HistoryEvent;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
/**
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GeneralAlarmStatisticsDTO {

    /**
     * 告警ID
     */
    private String sequenceId;

    /**
     * 设备ID
     */
    private Integer equipmentId;

    /**
     * 设备基类ID
     */
    private Integer equipmentCategory;

    /**
     * 局站ID
     */
    private Integer stationId;

    /**
     * 局站名
     */
    private String stationName;

    /**
     * 设备名
     */
    private String equipmentName;

    /**
     * 事件ID
     */
    private Integer eventId;

    /**
     * 事件名
     */
    private String eventName;

    /**
     * 告警等级ID
     */
    private Integer eventSeverityId;

    /**
     * 告警等级名
     */
    private String eventSeverity;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 层级ID
     */
    private Integer resourceStructureId;

    /**
     * 告警级别
     */
    private Integer eventLevel;

    public GeneralAlarmStatisticsDTO(HistoryEvent event) {
        this.sequenceId = event.getSequenceId();
        this.equipmentId = event.getEquipmentId();
        this.equipmentCategory = event.getEquipmentCategory();
        this.stationId = event.getStationId();
        this.stationName = event.getStationName();
        this.equipmentName = event.getEquipmentName();
        this.eventId = event.getEventId();
        this.eventName = event.getEventName();
        this.eventSeverityId = event.getEventSeverityId();
        this.eventSeverity = event.getEventSeverity();
        this.startTime = event.getStartTime();
        this.endTime = event.getEndTime();
        this.resourceStructureId = event.getResourceStructureId();
        this.eventLevel = event.getEventLevel();
    }
    public GeneralAlarmStatisticsDTO(ActiveEvent event) {
        this.sequenceId = event.getSequenceId();
        this.equipmentId = event.getEquipmentId();
        this.equipmentCategory = event.getEquipmentCategory();
        this.stationId = event.getStationId();
        this.stationName = event.getStationName();
        this.equipmentName = event.getEquipmentName();
        this.eventId = event.getEventId();
        this.eventName = event.getEventName();
        this.eventSeverityId = event.getEventSeverityId();
        this.eventSeverity = event.getEventSeverity();
        this.startTime = event.getStartTime();
        this.endTime = event.getEndTime();
        this.resourceStructureId = event.getResourceStructureId();
        this.eventLevel = event.getEventLevel();
    }



}
