package com.siteweb.hmi.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.hmi.dto.*;
import com.siteweb.hmi.mapper.ApiMapper;
import com.siteweb.hmi.service.ApiService;
import com.siteweb.hmi.service.IDCGraphicDataService;
import com.siteweb.hmi.util.GraphicDataUtil;
import com.siteweb.monitoring.dto.EquipmentDTO;
import com.siteweb.monitoring.dto.StationEquipmentDTO;
import com.siteweb.monitoring.entity.*;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.mamager.*;
import com.siteweb.monitoring.mapper.HistoryEventMapper;
import com.siteweb.monitoring.model.EquipmentState;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.HistoryEventService;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.monitoring.service.StationService;
import com.siteweb.monitoring.vo.ActiveEventFilterVO;
import com.siteweb.monitoring.vo.EquipmentFilterVo;
import com.siteweb.utility.dto.CoreEventSeverity;
import com.siteweb.utility.dto.EquipmentBaseTypeDTO;
import com.siteweb.utility.entity.DataItem;
import com.siteweb.utility.entity.EquipmentBaseType;
import com.siteweb.utility.enums.DataEntryEnum;
import com.siteweb.utility.manager.DataDictionaryManager;
import com.siteweb.utility.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collector;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: lzy
 * @Date: 2022/7/25 17:18
 */
@Service
public class ApiServiceImpl implements ApiService {

    @Autowired
    ApiMapper apiMapper;
    @Autowired
    LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    EquipmentManager equipmentManager;
    @Autowired
    EquipmentBaseTypeManager equipmentBaseTypeManager;
    @Autowired
    LocaleMessageSourceUtil localeMessageSourceUtil;
    @Autowired
    ResourceStructureManager resourceStructureManager;
    @Autowired
    EquipmentBaseTypeService equipmentBaseTypeService;
    @Autowired
    StationService stationService;
    @Autowired
    DataItemService dataItemService;
    @Autowired
    GraphicDataUtil graphicDataUtil;
    @Autowired
    EquipmentStateManager equipmentStateManager;
    @Autowired
    ActiveEventManager activeEventManager;
    @Autowired
    ResourceStructureService resourceStructureService;

    @Autowired
    DataDictionaryManager dataDictionaryManager;
    @Autowired
    StationManager stationManager;
    @Autowired
    SystemConfigService systemConfigService;

    @Autowired
    HistoryEventService historyEventService;
    @Autowired
    HistoryEventMapper historyEventMapper;
    @Autowired
    ResourceStructureTypeService resourceStructureTypeService;
    @Autowired
    IDCGraphicDataService idcGraphicDataService;
    @Autowired
    private CoreEventSeverityService coreEventSeverityService;
    @Value("${byteDance.eventLevelFilterEnable:#{null}}")
    private Boolean eventLevelFilterEnable;

    private Map<Integer, String> getEventLevelMap(){
        Map<Integer, String> eventLevelMap = new HashMap<>();
        List<CoreEventSeverity> eventSeverities = coreEventSeverityService.getCoreEventSeverities();
        for(CoreEventSeverity coreEventSeverity:eventSeverities){
            eventLevelMap.put(coreEventSeverity.getEventLevel(),coreEventSeverity.getSeverityName());
        }
        return  eventLevelMap;
    }
    @Override
    public List<StatisticsResult> countStationOnlineStatus() {
        List<Station> allStations = stationManager.findAll();
        Map<Integer, Integer> onlineStatusCountMap = new HashMap<>();
        for (Station station : allStations) {
            Integer connectState = station.getConnectState();
            onlineStatusCountMap.put(connectState, onlineStatusCountMap.getOrDefault(connectState, 0) + 1);
        }
        return onlineStatusCountMap.entrySet().stream().map(e -> {
            StatisticsResult statisticsResult = new StatisticsResult();
            statisticsResult.setId(e.getKey());
            statisticsResult.setName(messageSourceUtil.getMessage("api.stationStatus." + e.getKey()));
            statisticsResult.setValue(e.getValue());
            return statisticsResult;
        }).toList();
    }

    @Override
    public List<StatisticsResult> eventSeverityStatistics(Integer userId, Integer stationId) {
        List<Integer> stationIds = new ArrayList<>();
        //没传stationId则查询用户权限
        if (Objects.isNull(stationId)) {
            stationIds = stationService.findStationIdsByUserId(userId);
        } else {
            stationIds.add(stationId);
        }
        if (CollUtil.isEmpty(stationIds)) {
            return graphicDataUtil.constructCurrentStatistics();
        }
        boolean filterByAlarmNameId = standardAlarmNameIdIsNotNull();
        Set<Integer> stationIdSet = new HashSet<>(stationIds);
        Map<Integer, Long> eventSeverityCountMap = activeEventManager.queryAllActiveEvents()
                                                                     .stream()
                                                                     .filter(e -> Objects.isNull(e.getEndTime()) && stationIdSet.contains(e.getStationId()))
                                                                     .filter(e -> !filterByAlarmNameId || Objects.nonNull(e.getStandardAlarmNameId()))
                                                                     .collect(Collectors.groupingBy(ActiveEvent::getEventLevel, Collectors.counting()));
        //填充告警
        List<StatisticsResult> statisticsResults = graphicDataUtil.constructCurrentStatistics();
        for (StatisticsResult statisticsResult : statisticsResults) {
            eventSeverityCountMap.computeIfPresent(statisticsResult.getId(), (key, value) -> {
                statisticsResult.setValue(value.intValue());
                return value;
            });
        }
        return statisticsResults;
    }

    @Override
    public List<StatisticsResult> stationStatistics(Integer userId, String resourceStructureIds) {
        Set<Integer> stationIds;
        if (CharSequenceUtil.isNotEmpty(resourceStructureIds)) {
            List<Integer> selectResourceIds = Arrays.stream(resourceStructureIds.split(",")).map(Integer::valueOf).toList();
            List<Integer> userResourceIds = resourceStructureService.findResourceIdsByUserIdAndResourceStructureIds(userId, selectResourceIds);
            List<ResourceStructure> resourceStructureByIds = resourceStructureManager.getResourceStructureByIds(userResourceIds).stream().filter(e -> e.getStructureTypeId().equals(104)).toList();
            stationIds = resourceStructureByIds.stream().map(ResourceStructure::getOriginId).collect(Collectors.toSet());
        }else {
            stationIds = stationService.findByUserId(userId).stream().map(Station::getStationId).collect(Collectors.toSet());
        }
        if (CollUtil.isEmpty(stationIds)) {
            return new ArrayList<>();
        }
        return apiMapper.statoinStatistics(stationIds);
    }

    @Override
    public Map<String, List<AlarmtrendsDTO>> alarmtrends(Integer stationId) {
        Date now = new Date();
        Date firstDay = DateUtil.getFirstDayOfMonth(now);
        Date lastDay = DateUtil.getLastDayOfMonth(now);
        List<AlarmtrendsDTO> list = apiMapper.alarmtrends(stationId, firstDay, lastDay);
        Map<Integer, String> eventLevelMap = getEventLevelMap();
        Map<String, List<AlarmtrendsDTO>> result = eventLevelMap.values().stream().collect(Collectors.toMap(e -> e, e -> new ArrayList<>(list.size())));

        for (AlarmtrendsDTO alarmtrendsDTO : list) {
            Integer eventSeverityId = alarmtrendsDTO.getEventSeverityId();
            if (eventSeverityId != null) {
                String eventLevelName = eventLevelMap.get(eventSeverityId);
                if (eventLevelName != null) {
                    List<AlarmtrendsDTO> alarmtrendsDTOS = result.get(eventLevelName);
                    alarmtrendsDTO.setSeverityName(eventLevelName);
                    alarmtrendsDTOS.add(alarmtrendsDTO);
                }
            }
        }

        return result;
    }

    @Override
    public List<StatisticsResult> importantEventStatistics(String type, Integer userId, String resourceStructureIds) {
        /** 10-停电，37-水浸，34-高温，47-电池电压 **/
        Set<Integer> typeSet = StringUtils.splitToIntegerCollection(type, HashSet::new);
        Map<Integer, String> itemMap = dataItemService.findByEntryId(DataEntryEnum.EVENT_CATEGORY.getValue())
                                                      .stream()
                                                      .filter(e -> typeSet.contains(e.getItemId()))
                                                      .collect(Collectors.toMap(DataItem::getItemId, DataItem::getItemValue));
        if (CollUtil.isEmpty(itemMap)) {
            return Collections.emptyList();
        }
        Collection<Integer> stationIds = getStationsByUserIdAndResourceStructureIds(userId, resourceStructureIds);

        if (CollUtil.isEmpty(stationIds)) {
            return Collections.emptyList();
        }
        Set<Integer> stationIdSet = new HashSet<>(stationIds);
        boolean filterByAlarmNameId = standardAlarmNameIdIsNotNull();
        Map<Integer, Long> eventCategoryCountMap = activeEventManager.queryAllActiveEvents()
                                                                     .stream()
                                                                     .filter(e -> Objects.isNull(e.getEndTime()) && stationIdSet.contains(e.getStationId()) && itemMap.containsKey(e.getEventCategoryId()))
                                                                     .filter(e -> !filterByAlarmNameId || Objects.nonNull(e.getStandardAlarmNameId()))
                                                                     .collect(Collectors.groupingBy(ActiveEvent::getEventCategoryId, Collectors.counting()));
        List<StatisticsResult> statisticsResults = new ArrayList<>();
        itemMap.forEach((key,value)->{
            if (eventCategoryCountMap.containsKey(key)) {
                statisticsResults.add(new StatisticsResult(key, value, eventCategoryCountMap.get(key).intValue()));
                return;
            }
            statisticsResults.add(new StatisticsResult(key, value, 0));
        });
        return statisticsResults;
    }

    /**
     * 通过用户id与层级ids获取起局站ids
     * @param userId 用户id
     * @param resourceStructureIds 层级id 多个逗号隔开
     * @return {@link Collection}<{@link Integer}>
     */
    private Set<Integer> getStationsByUserIdAndResourceStructureIds(Integer userId, String resourceStructureIds) {
        Collection<Integer> stationIds = stationService.findStationIdsByUserId(userId);
        if (CollUtil.isEmpty(stationIds)) {
            return Collections.emptySet();
        }
        if (CharSequenceUtil.isBlank(resourceStructureIds)) {
            return new HashSet<>(stationIds);
        }
        List<Integer> selectResourceIds = StringUtils.splitToIntegerList(resourceStructureIds);
        List<Integer> userResourceIds = resourceStructureService.findResourceIdsByUserIdAndResourceStructureIds(userId, selectResourceIds);
        List<ResourceStructure> resourceStructureByIds = resourceStructureManager.getResourceStructureByIds(userResourceIds)
                                                                                 .stream()
                                                                                 .filter(e -> e.getStructureTypeId().equals(SourceType.STATION.value()))
                                                                                 .toList();
        return resourceStructureByIds.stream()
                                     .map(ResourceStructure::getOriginId)
                                     .collect(Collectors.toSet());
    }

    @Override
    public Map<String, List<StationAlarmStatisticsDTO>> stationalarmstatistics(Integer userId, String resourceStructureIds) {
        Set<Integer> stationIds = getStationsByUserIdAndResourceStructureIds(userId, resourceStructureIds);

        Map<Integer, String> eventLevelMap = getEventLevelMap();
        Map<String, List<StationAlarmStatisticsDTO>> result = eventLevelMap.values().stream().collect(Collectors.toMap(e -> e, e -> new ArrayList<>()));
        if (CollUtil.isEmpty(stationIds)) {
            return result;
        }
        boolean standardAlarmNameIdIsNotNull = standardAlarmNameIdIsNotNull();

        // 取前 10
        List<Map.Entry<AbstractMap.SimpleEntry<Integer, String>, List<ActiveEvent>>> top10StationsActiveEvents =
                activeEventManager.queryAllActiveEvents().stream()
                        .filter(event -> event.getEndTime() == null && stationIds.contains(event.getStationId()) &&
                                (!standardAlarmNameIdIsNotNull || Objects.nonNull(event.getStandardAlarmNameId())))
                        .collect(Collectors.groupingBy(event -> new AbstractMap.SimpleEntry<>(event.getStationId(), event.getStationName())))
                        .entrySet().stream()
                        .sorted(Comparator.comparingInt(e -> -e.getValue().size()))
                        .limit(10)
                        .toList();

        Map<Integer, String> top10StationsMap = new HashMap<>();
        Map<Integer, List<ActiveEvent>> activeEventLevelMap = new HashMap<>();

        top10StationsActiveEvents.forEach(entry -> {
            AbstractMap.SimpleEntry<Integer, String> key = entry.getKey();
            top10StationsMap.put(key.getKey(), key.getValue());

            entry.getValue().forEach(event ->
                    activeEventLevelMap.computeIfAbsent(event.getEventLevel(), k -> new ArrayList<>()).add(event)
            );
        });

        for (Map.Entry<Integer, List<ActiveEvent>> es : activeEventLevelMap.entrySet()) {
            Integer eventSeverityId = es.getKey();
            if (eventSeverityId != null) {
                String eventLevelName = eventLevelMap.get(eventSeverityId);
                if (eventLevelName != null) {
                    List<StationAlarmStatisticsDTO> dtos = result.get(eventLevelName);
                    if (dtos != null) {
                        Map<Integer, Long> stationCountMap = es.getValue().stream().collect(Collectors.groupingBy(ActiveEvent::getStationId, Collectors.counting()));
                        top10StationsMap.forEach((key, value) -> {
                            StationAlarmStatisticsDTO statisticsDTO = new StationAlarmStatisticsDTO();
                            statisticsDTO.setStationName(value);
                            statisticsDTO.setStationId(key);
                            statisticsDTO.setSeverityName(eventLevelName);
                            statisticsDTO.setEventSeverityId(eventSeverityId);
                            statisticsDTO.setAlarmCount(stationCountMap.get(key) != null ? stationCountMap.get(key).intValue() : 0);
                            dtos.add(statisticsDTO);
                        });
                    }
                }
            }
        }
        return result;
    }

    /**
     * 电信场景组态页中的告警相关接口是否只统计标准化告警
     * @return boolean true是 false 否
     */
    private boolean standardAlarmNameIdIsNotNull() {
        return systemConfigService.standardAlarmNameIdIsNotNull();
    }

    @Override
    public List<StationEquipmentDTO> findEuipmentlist(Integer stationId) {
        return equipmentService.findStationPageEquipmentDTOByStationId(stationId);
    }



    @Override
    public EquipmentStatistics devicecatogorystatistics(Integer type) {
        EquipmentBaseType equipmentBaseType = equipmentBaseTypeManager.getEquipmentBaseTypeFromCache(type);
        EquipmentStatistics result = new EquipmentStatistics();
        result.setTypename(equipmentBaseType.getBaseEquipmentName());
        result.setCount(CollUtil.size(equipmentManager.getEquipmentsByEquipmentBaseType(type)));
        return result;
    }

    @Override
    public List<StatisticsResult> alarmStatisticsByResourceType(Integer resourceType) {
        boolean filterByEventLevel = Boolean.TRUE.equals(eventLevelFilterEnable);
        List<StatisticsResult> statisticsResults = apiMapper.alarmStatisticsByResourceType(resourceType, filterByEventLevel);
        List<ResourceStructure> resourceStructureList = resourceStructureManager.getResourceStructureLstByTypeId(SourceType.BUILDING.value());
        Set<Integer> statisticsResultsIdSet = statisticsResults.stream()
                                                               .map(StatisticsResult::getId)
                                                               .collect(Collectors.toSet());
        for (ResourceStructure building : resourceStructureList) {
            if (statisticsResultsIdSet.contains(building.getResourceStructureId())) {
                continue;
            }
            statisticsResults.add(new StatisticsResult(building.getResourceStructureId(), building.getResourceStructureName(), 0));
        }
        return statisticsResults;
    }

    @Override
    public List<StatisticsResult> alarmStatisticsByResourceStructureId(Integer resourceStructureId) {
        boolean filterByEventLevel = Boolean.TRUE.equals(eventLevelFilterEnable);
        List<StatisticsResult> statisticsResults = apiMapper.alarmStatisticsByResourceStructureId(resourceStructureId, filterByEventLevel);
        List<EquipmentBaseTypeDTO> equipmentBaseTypeDtos = equipmentBaseTypeService.findUsedEquipmentBaseTypeDTOs();
        Set<Integer> statisticsResultsIdSet = statisticsResults.stream()
                                                               .map(StatisticsResult::getId)
                                                               .collect(Collectors.toSet());
        for (EquipmentBaseTypeDTO baseType : equipmentBaseTypeDtos) {
            if (statisticsResultsIdSet.contains(baseType.getBaseEquipmentId())) {
                continue;
            }
            statisticsResults.add(new StatisticsResult(baseType.getBaseEquipmentId(), baseType.getBaseEquipmentName(), 0));
        }
        return statisticsResults;
    }

    @Override
    public List<CommonObjectDTO> configcommonobject(Integer type) {
        return apiMapper.configcommonobject(type);
    }

    @Override
    public List<StatisticsCommonResult> structureavailability(Integer userId, String resourceStructureIds) {
        Collection<Integer> stationIds = stationService.findStationIdsByUserId(userId);

        if (CharSequenceUtil.isNotEmpty(resourceStructureIds)) {
            List<Integer> selectResourceIds = Arrays.stream(resourceStructureIds.split(",")).map(Integer::valueOf).toList();
            List<Integer> userResourceIds = resourceStructureService.findResourceIdsByUserIdAndResourceStructureIds(userId, selectResourceIds);
            List<ResourceStructure> resourceStructureByIds = resourceStructureManager.getResourceStructureByIds(userResourceIds).stream().filter(e -> e.getStructureTypeId().equals(104)).toList();
            stationIds = resourceStructureByIds.stream().map(ResourceStructure::getOriginId).collect(Collectors.toSet());
        }

        if (CollUtil.isEmpty(stationIds)) {
            return new ArrayList<>();
        }
        List<StructureAvailabilityDTO> list = apiMapper.structureavailability(stationIds);
        // 总数
        Map<Integer, List<StructureAvailabilityDTO>> collect = list.stream().collect(Collectors.groupingBy(StructureAvailabilityDTO::getId));
        // 在线数
        Map<Integer, Integer> onlineMap = collect.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> {
            int onlineState = 0;
            for (StructureAvailabilityDTO item : e.getValue()) {
                if (Objects.equals(OnlineState.ONLINE.value(), stationManager.findStationById(item.getStationId()).getConnectState())) {
                    ++onlineState;
                }
            }
            return onlineState;
        }));

        list = collect.entrySet().stream().map(e -> {
            StructureAvailabilityDTO structureAvailabilityDTO = e.getValue().get(0);
            StructureAvailabilityDTO dto = new StructureAvailabilityDTO();
            dto.setId(e.getKey());
            dto.setResourceStructureName(structureAvailabilityDTO.getResourceStructureName());
            dto.setTotalCount(e.getValue().size());
            dto.setOnlineCount(onlineMap.getOrDefault(e.getKey(), 0));
            return dto;
        }).toList();

        NumberFormat numberFormat = NumberFormat.getInstance();
        // 设置精确到小数点后2位
        numberFormat.setMaximumFractionDigits(2);
        return list.stream().map(e -> {
            String result = numberFormat.format((float) e.getOnlineCount() / (float) e.getTotalCount() * 100);
            StatisticsCommonResult statisticsResult = new StatisticsCommonResult();
            statisticsResult.setId(e.getId());
            statisticsResult.setName(e.getResourceStructureName());
            statisticsResult.setValue(result);
            return statisticsResult;
        }).toList();
    }

    @Override
    public List<StatisticsResult> alarmStatistics(Integer resourceTypeId, Integer resourceStructureId) {
        boolean filterByEventLevel = Boolean.TRUE.equals(eventLevelFilterEnable);
        List<StatisticsResult> statisticsResults = apiMapper.alarmStatistics(resourceTypeId, resourceStructureId, filterByEventLevel);
        int alarmSumCount = statisticsResults.stream()
                                             .mapToInt(StatisticsResult::getValue)
                                             .sum();
        statisticsResults.add(new StatisticsResult(resourceStructureId, localeMessageSourceUtil.getMessage("common.field.alarmCount"), alarmSumCount));
        return statisticsResults;
    }

    @Override
    public List<StatisticsCommonResult> deviceCategoryStatisticsState(EquipmentStateStatisticsVO equipmentStateStatisticsVO) {
        Set<Integer> baseTypeIdSet = Stream.of(equipmentStateStatisticsVO.getDeviceCategoryIds().split(","))
                                     .map(Integer::valueOf)
                                     .collect(Collectors.toSet());
        Set<Integer> resourceStructureSet = resourceStructureManager.findByTypeIdAndId(equipmentStateStatisticsVO.getPageCategory(), equipmentStateStatisticsVO.getObjectId());
        //在线\离线\告警数量
        List<Equipment> equipmentList = equipmentManager.findEquipmentsByBaseTypeIds(baseTypeIdSet)
                                                        .stream()
                                                        .filter(e -> resourceStructureSet.contains(e.getResourceStructureId()))
                                                        .toList();
        List<StatisticsCommonResult> statisticsResults = new ArrayList<>();
        int online = 0;
        int offline = 0;
        for (Equipment equipment : equipmentList) {
            EquipmentState equipmentState = equipmentStateManager.getEquipmentStateById(equipment.getEquipmentId());
            OnlineState onlineState = equipmentState.getOnlineState();
            if (onlineState.equals(OnlineState.ONLINE)) {
                online++;
            } else if (onlineState.equals(OnlineState.OFFLINE)) {
                offline++;
            }
        }
        statisticsResults.add(new StatisticsCommonResult(0,messageSourceUtil.getMessage("api.stationStatus.onlineCount"),online));
        statisticsResults.add(new StatisticsCommonResult(1,messageSourceUtil.getMessage("api.stationStatus.offlineCount"),offline));
        boolean filterByEventLevel = Boolean.TRUE.equals(eventLevelFilterEnable);
        List<Integer> eventLevels = coreEventSeverityService.getCoreEventSeverities().stream().map(CoreEventSeverity::getEventLevel).toList();
        List<ActiveEvent> activeEvents = activeEventManager.findActiveEventByCondition(
                event -> Objects.isNull(event.getEndTime()) && baseTypeIdSet.contains(event.getBaseEquipmentId()) && resourceStructureSet.contains(event.getResourceStructureId()));
        if (filterByEventLevel && !eventLevels.isEmpty()) {
            activeEvents = activeEvents.stream()
                    .filter(event -> eventLevels.contains(event.getEventLevel()))
                    .toList();
        }
        int activeEventCount = CollUtil.size(activeEvents);
        statisticsResults.add(new StatisticsCommonResult(2,messageSourceUtil.getMessage("api.stationStatus.activeEventCount"),activeEventCount));
        return statisticsResults;
    }

    @Override
    public List<StatisticsCommonResult> equipmentStateStatistics(Integer userId) {
        Set<Integer> equipmentIds = equipmentService.findEquipmentIdsByUserId(userId);
        HashMap<Integer, EquipmentState> equipmentStateByIds = equipmentStateManager.getEquipmentStateByIds(new ArrayList<>(equipmentIds));
        //在线、离线、屏蔽数
        int online = 0;
        int offline = 0;
        int mask =0;
        for (EquipmentState equipmentState : equipmentStateByIds.values()) {
            if (equipmentState.getOnlineState().equals(OnlineState.ONLINE)) {
                online++;
            }
            if (equipmentState.getOnlineState().equals(OnlineState.OFFLINE)) {
                offline++;
            }
            if (Boolean.TRUE.equals(equipmentState.getMasked())) {
                mask++;
            }
        }
        List<StatisticsCommonResult> statisticsResults = new ArrayList<>();
        statisticsResults.add(new StatisticsCommonResult(1,messageSourceUtil.getMessage("api.stationStatus.onlineCount"),online));
        statisticsResults.add(new StatisticsCommonResult(2,messageSourceUtil.getMessage("api.stationStatus.offlineCount"),offline));
        statisticsResults.add(new StatisticsCommonResult(3,messageSourceUtil.getMessage("api.stationStatus.mask"),mask));
        return statisticsResults;
    }

    @Override
    public StatisticsCommonResult equipmentSum(Integer userId) {
        Long sum = equipmentService.findEquipmentDTOsByUserId(userId).stream().count();
        return new StatisticsCommonResult(1, "totalEquipmentCount", sum);
    }

    @Override
    public StatisticsCommonResult getAlarmDeviceCountByDays(Integer userId, Integer days) {
        if (ObjectUtil.isNull(days)){
            days = 7;
        }
        ActiveEventFilterVO vo = new ActiveEventFilterVO();
        Date endDate = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, -days);
        Date startDate = calendar.getTime();
        vo.setStartTimeFrom(startDate);
        vo.setStartTimeTo(endDate);
        List<ActiveEvent> activeEvents = activeEventManager.queryActiveEvents(userId, vo);
        List<Integer> list = coreEventSeverityService.getCoreEventSeverities().stream().map(CoreEventSeverity::getEventLevel).toList();
        activeEvents = activeEvents.stream().filter(a -> list.contains(a.getEventLevel())).toList();

        List<Integer> activeEventEquipmentIds = activeEvents.stream().map(ActiveEvent::getEquipmentId).distinct().toList();

        List<Integer> historyEventEquipmentsIds = historyEventService.listAlertEquipmentsByTimeSpan(startDate, endDate);

        Long eventEquipmentCount = CollUtil.unionAll(activeEventEquipmentIds, historyEventEquipmentsIds).stream().distinct().count();
        return new StatisticsCommonResult(0, "getAlertDeviceCountByDays", eventEquipmentCount);
    }

    @Override
    public List<GeneralAlarmStatisticsDTO> getAlarmDeviceListByDays(Integer userId, Integer days) {
        ActiveEventFilterVO vo = new ActiveEventFilterVO();
        Date endDate = new Date();

        Calendar calendar = Calendar.getInstance();
        if (ObjectUtil.isNull(days)){
            days = 7;
        }
        calendar.add(Calendar.DAY_OF_YEAR, -days);
        Date startDate = calendar.getTime();
        vo.setStartTimeTo(endDate);
        List<GeneralAlarmStatisticsDTO> allAlarm = getAllAlarm(userId, startDate, endDate);
        return allAlarm.stream().sorted((a, b)->{return StringUtils.compare(a.getEquipmentName(),b.getEquipmentName());}).toList();
    }

    @Override
    public StatisticsCommonResult getDeviceAlarmRateByDays(Integer userId, Integer days) {
        if (ObjectUtil.isNull(days)){
            days = 7;
        }
        long totalCount = (long) equipmentSum(userId).getValue();
        long alarmCount = (long) getAlarmDeviceCountByDays(userId,days).getValue();
        double value = totalCount == 0L ? 0d : ((double) alarmCount / totalCount) * 100;
        DecimalFormat df = new DecimalFormat("#.##");
        return new StatisticsCommonResult(0, "getDeviceAlarmRateByDays",  df.format(value));
    }

    @Override
    public StatisticsCommonResult getAlarmCountByDaysAndType(Integer userId, Boolean unconfirmFlag, Integer days) {
        long count = getAlarmListByDaysAndType(userId, unconfirmFlag, days).stream().distinct().count();
//        long count = getAlarmListByDaysAndType(userId, unconfirmFlag, days).size();
        return new StatisticsCommonResult(0, "getAlarmCount",count);
    }

    public Long countAlarmByDaysAndType(Integer userId, Boolean unconfirmFlag, Integer days) {

        if (ObjectUtil.isNull(days)){
            days = 7;
        }

        ActiveEventFilterVO vo = new ActiveEventFilterVO();
        Date endDate = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, -days);
        Date startDate = calendar.getTime();
        vo.setStartTimeFrom(startDate);
        vo.setStartTimeTo(endDate);
        Long result = 0L;
        if (Boolean.TRUE.equals(unconfirmFlag)){
            vo.setEventConfirmed(false);
            List<ActiveEvent> activeEvents = activeEventManager.queryActiveEvents(userId, vo);
            result += activeEvents.size();
        }
        else{
            result = getAllAlarm(userId, startDate, endDate);
        }
        return result.stream().filter(a -> ObjectUtil.isNotNull(a.getStartTime())).distinct()
                .sorted(Comparator.comparing(GeneralAlarmStatisticsDTO::getStartTime).reversed())
                .toList();
    }

    @Override
    public List<GeneralAlarmStatisticsDTO> getAlarmListByDaysAndType(Integer userId, Boolean unconfirmFlag, Integer days) {

        if (ObjectUtil.isNull(days)){
            days = 7;
        }

        ActiveEventFilterVO vo = new ActiveEventFilterVO();
        Date endDate = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, -days);
        Date startDate = calendar.getTime();
        vo.setStartTimeFrom(startDate);
        vo.setStartTimeTo(endDate);
        List<GeneralAlarmStatisticsDTO> result;
        if (Boolean.TRUE.equals(unconfirmFlag)){
            result = new ArrayList<>();
            vo.setEventConfirmed(false);
            List<ActiveEvent> activeEvents = activeEventManager.queryActiveEvents(userId, vo);
            activeEvents.forEach(a -> {
                GeneralAlarmStatisticsDTO dto = new GeneralAlarmStatisticsDTO(a);
                result.add(dto);
            });
        }
        else{
            result = getAllAlarm(userId, startDate, endDate);
        }
        return result.stream().filter(a -> ObjectUtil.isNotNull(a.getStartTime())).distinct()
                .sorted(Comparator.comparing(GeneralAlarmStatisticsDTO::getStartTime).reversed())
                .toList();
    }

    @Override
    public List<StatisticsCommonResult> getEquipmentCategoryAlarmStatistics(Integer userId, Integer type) {
        List<GeneralAlarmStatisticsDTO> allAlarm = getAllAlarmByType(userId,type);
        List<StatisticsCommonResult> result = new ArrayList<>();
        Map<Integer, Long> collect = allAlarm.stream().filter(a->ObjectUtil.isNotNull(a.getEquipmentCategory()))
                .collect(Collectors.groupingBy(
                        GeneralAlarmStatisticsDTO::getEquipmentCategory,
                        Collectors.counting()
                ));
        Map<Integer, String> categories = dataItemService.findByEntryId(DataEntryEnum.EQUIPMENT_CATEGORY.getValue()).stream()
                .filter(dateitem -> ObjectUtil.isNotNull(dateitem.getItemId()) && ObjectUtil.isNotNull(dateitem.getItemValue()))
                .collect(Collectors.toMap(DataItem::getItemId, DataItem::getItemValue));
        collect.forEach((key, value) -> {
            if (categories.containsKey(key) && value != 0L) {
                result.add(new StatisticsCommonResult(key, categories.get(key), value));
            }
        });
        return result;
    }

    @Override
    public List<StatisticsCommonResult> getEquipmentAlarmRankStatistics(Integer userId, Integer type, Integer number) {
        List<GeneralAlarmStatisticsDTO> allAlarm = getAllAlarmByType(userId,type);
        List<StatisticsCommonResult> result = new ArrayList<>();
        Map<Integer, Long> collect = allAlarm.stream().filter(a->ObjectUtil.isNotNull(a.getEquipmentCategory()))
                .collect(Collectors.groupingBy(
                        GeneralAlarmStatisticsDTO::getEquipmentId,
                        Collectors.counting()
                ));
        EquipmentFilterVo equipmentFilterVo = new EquipmentFilterVo();
        equipmentFilterVo.setEquipmentIds(collect.keySet());
        Map<Integer, String> equipMap = equipmentService.findEquipmentDTOs(-1, equipmentFilterVo).stream().collect(Collectors.toMap(EquipmentDTO::getEqId, EquipmentDTO::getEqName));
        List<StatisticsCommonResult> finalResult = result;
        collect.forEach((key, value) -> {
            if (equipMap.containsKey(key) && value != 0L) {
                finalResult.add(new StatisticsCommonResult(key, equipMap.get(key), value));
            }
        });
        result = finalResult.stream()
                .sorted((r1, r2) -> {
                    Long value1 = (Long) r1.getValue();
                    Long value2 = (Long) r2.getValue();
                    return value2.compareTo(value1); // 降序排序
                })
                .limit(number)
                .collect(Collectors.toList());
        result = CollUtil.reverse(result);
        return result;
    }

    @Override
    public List<StatisticsCommonResult> getEquipmentAlarmLevelStatistics(Integer userId, Integer type, Integer equipmentId) {
        List<GeneralAlarmStatisticsDTO> allAlarmByType = getAllAlarmByType(userId, type);
        if (Boolean.TRUE.equals(eventLevelFilterEnable)) {
            // 后台告警等级过滤开关启动,对告警数据进行过滤
            List<CoreEventSeverity> coreEventSeverities = coreEventSeverityService.getCoreEventSeverities();
            List<Integer> filterEventLevel = coreEventSeverities.stream().map(CoreEventSeverity::getEventLevel).toList();
            if (CollUtil.isNotEmpty(allAlarmByType)) {
                allAlarmByType = allAlarmByType.stream().filter(f -> filterEventLevel.contains(f.getEventLevel())).toList();
            }
        }
        if (ObjectUtil.isNotNull(equipmentId)){
            allAlarmByType = allAlarmByType.stream().filter(a -> ObjectUtil.equals(a.getEquipmentId(),equipmentId)).toList();
        }
        Map<String, Long> collect = allAlarmByType.stream().filter(a->ObjectUtil.isNotNull(a.getEventSeverity()))
                .collect(Collectors.groupingBy(
                        GeneralAlarmStatisticsDTO::getEventSeverity,
                        Collectors.counting()
                ));
        List<StatisticsCommonResult> result = new ArrayList<>();
        collect.forEach((key, value) -> {
           result.add(new StatisticsCommonResult(0, key, value));
        });
        return result;
    }

    @Override
    public List<StatisticsCommonResult> getAreaAlarmRankStatistics(Integer userId, Integer type, Integer number, Integer resourceStructureType) {
        // 获取所有告警信息
        List<GeneralAlarmStatisticsDTO> allAlarms = getAllAlarmByType(userId, type);

        // 默认设置资源结构类型
        if (ObjectUtil.isNull(resourceStructureType) || ObjectUtil.isNull(SourceType.valueOf(resourceStructureType))) {
            resourceStructureType = SourceType.ROOM.value();
        }
        Integer finalResourceStructureType = resourceStructureType;

        // 过滤出有效的资源结构 ID
        List<Integer> resourceStructureIds = allAlarms.stream()
                .map(GeneralAlarmStatisticsDTO::getResourceStructureId)
                .filter(ObjectUtil::isNotNull)
                .distinct()
                .toList();

        // 获取资源结构映射表，并过滤出指定类型的资源
        Map<Integer, String> resourceStructureMap = resourceStructureManager
                .getResourceStructureByIds(resourceStructureIds).stream()
                .filter(structure -> Objects.equals(structure.getStructureTypeId(), finalResourceStructureType))
                .collect(Collectors.toMap(
                        ResourceStructure::getResourceStructureId,
                        ResourceStructure::getResourceStructureName
                ));

        // 过滤告警数据，计算每个资源结构的告警数量
        Map<Integer, Long> alarmCountMap = allAlarms.stream()
                .filter(alarm -> resourceStructureMap.containsKey(alarm.getResourceStructureId()))
                .collect(Collectors.groupingBy(
                        GeneralAlarmStatisticsDTO::getResourceStructureId,
                        Collectors.counting()
                ));

        // 构建结果列表
        List<StatisticsCommonResult> results = alarmCountMap.entrySet().stream()
                .map(entry -> new StatisticsCommonResult(
                        entry.getKey(),
                        resourceStructureMap.get(entry.getKey()),
                        entry.getValue()
                ))
                .sorted((r1,r2) ->{
                    Long a = (Long) r1.getValue();
                    Long b = (Long) r2.getValue();
                    return Long.compare(a,b);
                } )
                .limit(number)
                .toList();

        return results;
    }


    /**
     *
     * @param userId
     * @param dateType 1:近7天 2 近一月 3 近一年 4 所有时间
     * @return
     */
    List<GeneralAlarmStatisticsDTO> getAllAlarmByType(Integer userId ,Integer dateType){
        ActiveEventFilterVO vo = new ActiveEventFilterVO();
        Date startDate = null;
        Date endDate = new Date();

        // 计算日期范围
        Calendar calendar = Calendar.getInstance();
        switch (dateType) {
            case 1:
                //近7天
                calendar.add(Calendar.DAY_OF_YEAR, -7);
                startDate = calendar.getTime();
                break;
            case 2:
                //近一月
                calendar.add(Calendar.MONTH, -1);
                startDate = calendar.getTime();
                vo.setStartTimeFrom(startDate);
                break;
            case 3:
                //近一年
                calendar.add(Calendar.YEAR, -1);
                startDate = calendar.getTime();
                vo.setStartTimeFrom(startDate);
                break;
            case 4:
                //所有时间
                endDate = null;
                break;
            default:
                return Collections.emptyList();
        }

        vo.setStartTimeTo(endDate);

        // 获取告警数据
        return getAllAlarm(userId, startDate, endDate);
    }
    Long countAllAlarm(Integer userId, Date startDateFrom, Date startDateTo) {
        ActiveEventFilterVO vo = new ActiveEventFilterVO();
        Long activeCount = 0L;
        Long historyCount = 0L;
        List<Integer> enableLevelList = coreEventSeverityService.getCoreEventSeverities().stream().map(CoreEventSeverity::getEventLevel).toList();
        if (ObjectUtil.isNotNull(startDateFrom) && ObjectUtil.isNotNull(startDateTo)){
            if (startDateFrom.after(startDateTo)){
                return 0L;
            }
            else {
                vo.setStartTimeFrom(startDateFrom);
                vo.setStartTimeTo(startDateTo);
                historyCount = historyEventService.countByStartTimeSpan(startDateFrom, startDateTo);
            }
        }
        else {
            historyCount = historyEventMapper.selectCount(new QueryWrapper<HistoryEvent>());
        }
        List<ActiveEvent> activeEvents = activeEventManager.queryActiveEvents(userId, vo);
        activeCount = activeEvents.stream().filter(a -> enableLevelList.contains(a.getEventLevel())).count();
        return activeCount + historyCount;
    }
    /**
     * 查询所有告警
     */
    List<GeneralAlarmStatisticsDTO> getAllAlarm(Integer userId, Date startDateFrom, Date startDateTo) {
        ActiveEventFilterVO vo = new ActiveEventFilterVO();
        List<HistoryEvent> historyEvents ;
        List<GeneralAlarmStatisticsDTO> result = new ArrayList<>();
        List<Integer> enableLevelList = coreEventSeverityService.getCoreEventSeverities().stream().map(CoreEventSeverity::getEventLevel).toList();

        if (ObjectUtil.isNotNull(startDateFrom) && ObjectUtil.isNotNull(startDateTo)){
            if (startDateFrom.after(startDateTo)){
                return Collections.emptyList();
            }
            else {
                vo.setStartTimeFrom(startDateFrom);
                vo.setStartTimeTo(startDateTo);
                historyEvents = historyEventService.findByStartTimeSpan(startDateFrom, startDateTo);
            }
        }
        else {
            historyEvents = historyEventMapper.selectList(new QueryWrapper<HistoryEvent>());
        }
        //筛选区域
        historyEvents = historyEventService.findByUserId(historyEvents,TokenUserUtil.getLoginUserId());
        //筛选等级
        historyEvents = historyEvents.stream().filter(a -> enableLevelList.contains(a.getEventLevel())).toList();

        List<ActiveEvent> activeEvents = activeEventManager.queryActiveEvents(userId, vo);
        activeEvents = activeEvents.stream().filter(a -> enableLevelList.contains(a.getEventLevel())).toList();
        activeEvents.forEach(a -> {
            GeneralAlarmStatisticsDTO dto = new GeneralAlarmStatisticsDTO(a);
            result.add(dto);
        });
        historyEvents.forEach(a -> {
            GeneralAlarmStatisticsDTO dto = new GeneralAlarmStatisticsDTO(a);
            result.add(dto);
        });
        return result.stream().filter(a -> ObjectUtil.isNotNull(a.getStartTime())).distinct()
                .sorted(Comparator.comparing(GeneralAlarmStatisticsDTO::getStartTime).reversed())
                .toList();
    }

    /**
     *
     * @param periodType 1: 自然周 2.自然月
     * @return
     */
    @Override
    public List<AlarmTrendForByteDanceDTO> alarmTrendMonthOrWeek(Integer userId, Integer periodType) {
        Calendar calendar = Calendar.getInstance();
        Date firstDay;
        Date lastDay;
        // 今天是当前周或当前月的第几天
        int currentDay;
        List<CoreEventSeverity> coreEventSeverities = coreEventSeverityService.getCoreEventSeverities();
        if (periodType == 1) {
            // 获取当前周的第一天
            int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
            if (dayOfWeek == Calendar.SUNDAY) {
                // 如果今天是周日，则回退 6 天，获取上周一的日期
                calendar.add(Calendar.DAY_OF_MONTH, -6);
                currentDay = 7;
            } else {
                // 其余情况，回退到本周一
                calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                currentDay = dayOfWeek - 1;
            }
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            firstDay = calendar.getTime();

        } else { // 当前月
            currentDay = calendar.get(Calendar.DAY_OF_MONTH);
            // 获取当前月的第一天
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            firstDay = calendar.getTime();

        }
        lastDay = new Date();
        String dateType = currentDay <= 2 ? "H" : "D";
        List<String> dateRange = DateUtil.getDateRange(dateType, firstDay, lastDay, 1);
        List<GeneralAlarmStatisticsDTO> allAlarm = getAllAlarm(TokenUserUtil.getLoginUserId(), firstDay, lastDay);
        // 找到级别数量
        int arrSize = coreEventSeverities.size();
        SimpleDateFormat formatter = dateType.equals("H") ?
                new SimpleDateFormat("yyyy-MM-dd HH") :
                new SimpleDateFormat("yyyy-MM-dd");
        dateRange = dateRange.stream().map(a -> a.substring(0, "H".equals(dateType) ? 13 : 10)).toList();
        // 创建 eventLevel 到数组索引的映射
        Map<Integer, Integer> levelToIndexMap = new HashMap<>();
        for (int i = 0; i < coreEventSeverities.size(); i++) {
            levelToIndexMap.put(coreEventSeverities.get(i).getEventLevel(), i);
        }

        Map<String, int[]> alarmCountMap = new LinkedHashMap<>();
        dateRange.forEach(timePoint -> alarmCountMap.put(timePoint, new int[arrSize]));
        for (GeneralAlarmStatisticsDTO alarm : allAlarm) {
            if (alarm.getEventLevel() == null || ObjectUtil.isNull(alarm.getStartTime())){
                continue;
            }
            String alarmTime = formatter.format(alarm.getStartTime());
            int[] alarmCount = alarmCountMap.get(alarmTime);
            Integer index = levelToIndexMap.get(alarm.getEventLevel());

            if (index != null && index >= 0 && index < arrSize) {
                alarmCount[index]++;
            }
        }
        List<AlarmTrendForByteDanceDTO> result = new ArrayList<>();
        String eventAlarmName = coreEventSeverityService.getCoreEventSeverities()
                .stream()
                .filter(a -> Boolean.TRUE.equals(a.getEnable()))
                .sorted(Comparator.comparingInt(CoreEventSeverity::getEventLevel))
                .map(CoreEventSeverity::getSeverityName)
                .collect(Collectors.joining(","));

        alarmCountMap.forEach((key,value)->{
            result.add(new AlarmTrendForByteDanceDTO(key,Arrays.stream(value).mapToObj(String::valueOf).collect(Collectors.joining(",")),eventAlarmName));
        });

        // 确保结果按时间排序
        return result.stream()
                .sorted(Comparator.comparing(AlarmTrendForByteDanceDTO::getTime))
                .toList();

    }

    @Override
    public List<EventCategoryStatisticsByDuration> categoryStatisticsByDuration(Integer resourceSturctureId, Integer topN){
        return apiMapper.categoryStatisticsByDuration(resourceSturctureId)
                .stream()
                .limit(topN)
                .toList();
    }


}
