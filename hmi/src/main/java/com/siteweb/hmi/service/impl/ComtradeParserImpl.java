package com.siteweb.hmi.service.impl;

import com.siteweb.hmi.entity.*;
import com.siteweb.hmi.service.ComtradeParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.complex.Complex;
import org.apache.commons.math3.transform.DftNormalization;
import org.apache.commons.math3.transform.FastFourierTransformer;
import org.apache.commons.math3.transform.TransformType;
import org.springframework.stereotype.Service;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import com.siteweb.common.exception.BusinessException;

import java.io.*;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

/**
 * <AUTHOR> @date 2023-03-08 14:34
 */
@Slf4j
@Service
public class ComtradeParserImpl implements ComtradeParser {


    /**
     * 解析COMTRADE配置文件(.cfg)
     * COMTRADE配置文件格式说明：
     * 1. 第一行：变电站名称、记录装置标识、标准版本年份
     * 2. 第二行：通道总数、模拟通道数、状态通道数
     * 3. 模拟通道信息行：每行包含一个模拟通道的配置信息
     * 4. 状态通道信息行：每行包含一个状态通道的配置信息
     * 5. 线路频率
     * 6. 采样率信息：包含采样率段数和各段采样率
     * 7. 时间戳信息：数据记录开始的日期和时间
     * 8. 触发时间戳信息
     * 9. 数据文件类型：ASCII或BINARY
     * 10. 时间倍乘因子：可选字段，1999及之后版本支持
     *
     * @param cfgFile COMTRADE配置文件
     * @return ComtradeConfig 解析后的配置对象，解析失败返回null
     */
    @Override
    public ComtradeConfig parseConfigFile(File cfgFile) {
        ComtradeConfig config = new ComtradeConfig();
        List<AnalogChannelInfo> analogChannelInfos = new ArrayList<>();
        List<DigitalChannelInfo> digitalChannelInfos = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(new FileReader(cfgFile))) {
            String line;

            // 1. 解析第一行：变电站名称、记录装置标识、标准版本年份
            if ((line = reader.readLine()) != null) {
                String[] parts = line.split(",");
                if (parts.length >= 2) { // 版本年份可选
                    config.setStationName(parts[0].trim());
                    config.setRecordingDeviceId(parts[1].trim());
                    if (parts.length >= 3) {
                        config.setRevisionYear(parts[2].trim());
                    }
                }
            }

            // 2. 解析第二行：通道总数、模拟通道数、状态通道数
            if ((line = reader.readLine()) != null) {
                String[] parts = line.split(",");
                if (parts.length == 3) {
                    config.setTotalChannels(Integer.parseInt(parts[0].trim()));
                    // 处理可能包含字母的模拟通道数和状态通道数
                    String analogStr = parts[1].trim();
                    String digitalStr = parts[2].trim();

                    // 提取数字部分
                    String analogNum = analogStr.replaceAll("[^0-9]", "");
                    String digitalNum = digitalStr.replaceAll("[^0-9]", "");

                    config.setAnalogChannels(Integer.parseInt(analogNum));
                    config.setDigitalChannels(Integer.parseInt(digitalNum));
                }
            }

            // 3. 解析模拟通道信息行
            if (config.getAnalogChannels() != null && config.getAnalogChannels() > 0) {
                for (int i = 0; i < config.getAnalogChannels(); i++) {
                    if ((line = reader.readLine()) != null) {
                        String[] parts = line.split(",", -1); // 保留空字段
                        if (parts.length > 12) {
                            // 解析模拟通道各字段：通道号、通道ID、相位、CCBM、单位、系数a、系数b、偏移、最小值、最大值、一次侧额定值、二次侧额定值、主/次标识
                            Integer an = parseInt(parts[0]);
                            String chId = parseString(parts[1]);
                            String ph = parseString(parts[2]);
                            String ccbm = parseString(parts[3]);
                            String uu = parseString(parts[4]);
                            Double a = parseDouble(parts[5]);
                            Double b = parseDouble(parts[6]);
                            Double skew = parseDouble(parts[7]);
                            Integer min = parseInt(parts[8]);
                            Integer max = parseInt(parts[9]);
                            Double primaryFactor = parseDouble(parts[10]);
                            Double secondaryFactor = parseDouble(parts[11]);
                            String ps = parseString(parts[12]);

                            analogChannelInfos.add(new AnalogChannelInfo(
                                    an, chId, ph, ccbm, uu, a, b, skew, min, max, primaryFactor, secondaryFactor, ps.charAt(0), null, null, null
                            ));
                        } else {
                            log.error("模拟通道信息行格式错误: {}", line);
                        }
                    }
                }
                config.setAnalogChannelInfos(analogChannelInfos);
            }

            // 4. 解析状态通道信息行
            if (config.getDigitalChannels() != null && config.getDigitalChannels() > 0) {
                for (int i = 0; i < config.getDigitalChannels(); i++) {
                    if ((line = reader.readLine()) != null) {
                        String[] parts = line.split(",", -1); // 使用 -1 参数保留空字段
                        if (parts.length > 4) {
                            // 解析状态通道各字段：通道ID、相位、CCBM、正常状态值
                            String chId = parseString(parts[1]);
                            String phase = parseString(parts[2]);
                            String ccbm = parseString(parts[3]);
                            Integer normalStateY = parseInt(parts[4]);
                            digitalChannelInfos.add(new DigitalChannelInfo(
                                    chId,              // 通道ID
                                    i + 1,             // 通道序号(从1开始)
                                    normalStateY != null ? normalStateY : 0, // 正常状态值
                                    phase,             // 相位
                                    ccbm               // CCBM
                            ));
                        } else {
                            log.error("状态通道信息行格式错误: {}", line);
                        }
                    }
                }
                config.setDigitalChannelInfos(digitalChannelInfos);
            }

            // 5. 解析线路频率
            if ((line = reader.readLine()) != null) {
                config.setLineFrequency(Float.parseFloat(line.trim()));
            }

            // 6. 解析采样率信息
            if ((line = reader.readLine()) != null) {
                int nrates = Integer.parseInt(line.trim());
                config.setSamplingRateCount(nrates);
                if (nrates > 0) {
                    // 读取第一个采样率作为主采样率
                    if ((line = reader.readLine()) != null) {
                        String[] parts = line.split(",");
                        if (parts.length >= 1) {
                            config.setSamplingRate(Float.parseFloat(parts[0].trim()));
                        }
                        // 跳过其余的采样率段
                        for (int i = 0; i < nrates - 1; i++) {
                            reader.readLine();
                        }
                    }
                } else { // nrates == 0
                    if ((line = reader.readLine()) != null) {
                        String[] parts = line.split(",");
                        if (parts.length >= 1) {
                            config.setSamplingRate(Float.parseFloat(parts[0].trim()));
                        }
                    }
                }
            }

            // 计算录波频率(点/周波) = 采样率 / 额定频率
            config.setRecordingFrequency(config.getSamplingRate() / config.getLineFrequency());

            // 7. 解析时间戳信息(数据记录开始的日期和时间)
            if ((line = reader.readLine()) != null) {
                config.setStartTime(convertTimeFormat(line.trim()));
            }

            // 8. 解析触发时间戳信息
            if ((line = reader.readLine()) != null) {
                config.setTriggerTime(convertTimeFormat(line.trim()));
            }

            // 9. 解析数据文件类型(ASCII或BINARY)
            if ((line = reader.readLine()) != null) {
                config.setDataFormat(line.trim().toUpperCase());
            }

            // 10. 解析时间倍乘因子(可选字段，1999及之后版本)
            line = reader.readLine();
            if (line != null) {
                try {
                    float timeMult = Float.parseFloat(line.trim());
                    config.setTimeMult(timeMult);
                } catch (NumberFormatException e) {
                    log.debug("无法解析时间倍乘因子: {}", line);
                }
            }

        } catch (IOException e) {
            log.error("解析配置文件出现IO异常: {}", e.getMessage(), e);
            return null;
        } catch (NumberFormatException e) {
            log.error("解析配置文件出现数字格式化异常: {}", e.getMessage(), e);
            return null;
        }

        return config;
    }

    /**
     * 解析COMTRADE数据文件，支持ASCII和BINARY格式
     *
     * @param datFile COMTRADE数据文件
     * @param config  COMTRADE配置文件信息
     * @return 解析后的通道数据列表
     */
    @Override
    public List<ChannelData> parseDataFile(File datFile, ComtradeConfig config) {
        // 参数校验
        if (config == null || config.getDataFormat() == null) {
            log.error("配置信息不完整，无法解析数据文件。");
            return new ArrayList<>();
        }

        // 文件大小限制检查：最大1258291字节
        if (datFile != null && datFile.exists()) {
            long fileSize = datFile.length();
            final long MAX_FILE_SIZE = 1258291L; // 1258291
            
            if (fileSize > MAX_FILE_SIZE) {
                String errorMessage = String.format("COMTRADE数据文件过大，文件大小: %d 字节，超过最大限制: %d 字节，不允许解析", 
                        fileSize, MAX_FILE_SIZE);
                log.error(errorMessage);
                throw new BusinessException(errorMessage);
            }
            
            log.info("COMTRADE数据文件大小检查通过，文件大小: {} 字节", fileSize);
        }

        // 初始化返回结果列表
        List<ChannelData> channelDataList = new ArrayList<>();

        // 通道数据映射（通道ID -> 数据点列表）
        Map<String, List<Double>> allValues = new HashMap<>();      // 存储所有通道的数值
        Map<String, List<String>> allTimestamps = new HashMap<>();  // 存储所有通道的时间戳
        Map<String, Double> minValues = new HashMap<>();            // 存储所有通道的最小值
        Map<String, Double> maxValues = new HashMap<>();            // 存储所有通道的最大值
        Map<String, List<String>> allZeroCrossings = new HashMap<>(); // 存储所有通道的过零点时间戳

        if ("ASCII".equalsIgnoreCase(config.getDataFormat())) {
            // ASCII 格式处理代码（尚未实现）
            log.warn("ASCII格式解析尚未实现");
        } else if ("BINARY".equalsIgnoreCase(config.getDataFormat())) {
            try {
                // 获取通道配置信息
                List<AnalogChannelInfo> analogChannelInfos = config.getAnalogChannelInfos();
                List<DigitalChannelInfo> digitalChannelInfos = config.getDigitalChannelInfos();

                // 计算数字通道的字数（每16个数字通道打包成一个2字节字）
                int digitalWordCount = (config.getDigitalChannels() + 15) / 16; // 向上取整

                // 计算每条记录的字节长度：
                // 4字节(采样序号) + 4字节(时间戳) + 2字节/模拟通道 + 2字节/数字通道字
                int recordSize = 4 + 4 + (config.getAnalogChannels() * 2) + (digitalWordCount * 2);

                // 设置时间倍乘因子（用于时间戳计算）
                float timeMult = (config.getTimeMult() != null) ? config.getTimeMult() : 1.0f;

                // 缓存常用配置值以提高性能
                int analogChannels = config.getAnalogChannels();
                int digitalChannels = config.getDigitalChannels();
                String startTime = config.getStartTime();

                // 预加载模拟通道信息
                String[] analogChannelIds = new String[analogChannels];
                double[] analogChannelMultipliersA = new double[analogChannels];    // 比例因子A
                double[] analogChannelOffsetsB = new double[analogChannels];        // 偏移量B
                double[] analogChannelPrimaryFactors = new double[analogChannels];  // 一次侧额定值
                double[] analogChannelSecondaryFactors = new double[analogChannels];// 二次侧额定值
                String[] analogChannelUnits = new String[analogChannels];           // 通道单位

                // 初始化模拟通道信息
                if (analogChannelInfos != null) {
                    for (int i = 0; i < analogChannels; i++) {
                        if (i < analogChannelInfos.size()) {
                            AnalogChannelInfo info = analogChannelInfos.get(i);
                            analogChannelIds[i] = info.getChId();
                            analogChannelMultipliersA[i] = info.getA() != null ? info.getA() : 1.0;
                            analogChannelOffsetsB[i] = info.getB() != null ? info.getB() : 0.0;
                            analogChannelPrimaryFactors[i] = info.getPrimaryFactor() != null ? info.getPrimaryFactor() : 1.0;
                            analogChannelSecondaryFactors[i] = info.getSecondaryFactor() != null ? info.getSecondaryFactor() : 1.0;
                            analogChannelUnits[i] = info.getUu();
                        } else {
                            // 使用默认值填充缺失的通道信息
                            analogChannelIds[i] = "A" + (i + 1);
                            analogChannelMultipliersA[i] = 1.0;
                            analogChannelOffsetsB[i] = 0.0;
                            analogChannelPrimaryFactors[i] = 1.0;
                            analogChannelSecondaryFactors[i] = 1.0;
                            analogChannelUnits[i] = null;
                        }
                    }
                }

                // 预加载数字通道信息
                String[] digitalChannelIds = new String[digitalChannels];
                if (digitalChannelInfos != null) {
                    for (int i = 0; i < digitalChannels; i++) {
                        if (i < digitalChannelInfos.size()) {
                            DigitalChannelInfo info = digitalChannelInfos.get(i);
                            digitalChannelIds[i] = info.getId();
                        } else {
                            digitalChannelIds[i] = "D" + (i + 1);
                        }
                    }
                }

                // 优化缓冲区大小：8KB或64条记录，取较小值
                int bufferSize = Math.min(8192, recordSize * 64);
                bufferSize = bufferSize - (bufferSize % recordSize); // 确保是recordSize的整数倍

                // 创建ByteBuffer用于解析二进制数据
                ByteBuffer bb = ByteBuffer.allocate(recordSize).order(ByteOrder.LITTLE_ENDIAN);

                // 初始化通道数据收集器
                for (int i = 0; i < analogChannels; i++) {
                    String channelId = analogChannelIds[i] != null ? analogChannelIds[i] : "A" + (i + 1);
                    allValues.put(channelId, new ArrayList<>());
                    allTimestamps.put(channelId, new ArrayList<>());
                    allZeroCrossings.put(channelId, new ArrayList<>());
                }

                for (int i = 0; i < digitalChannels; i++) {
                    String channelId = digitalChannelIds[i] != null ? digitalChannelIds[i] : "D" + (i + 1);
                    allValues.put(channelId, new ArrayList<>());
                    allTimestamps.put(channelId, new ArrayList<>());
                    allZeroCrossings.put(channelId, new ArrayList<>());
                }

                // 计算总记录数用于进度显示
                long fileSize = datFile.length();
                long totalRecords = fileSize / recordSize;
                long processedRecords = 0;
                int lastProgressPercent = 0;

                // 使用缓冲流读取文件
                try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream(datFile), 32768)) {
                    byte[] buffer = new byte[bufferSize];
                    int bytesRead;

                    // 循环读取文件内容
                    while ((bytesRead = bis.read(buffer)) > 0) {
                        int completeRecords = bytesRead / recordSize;

                        // 处理完整的记录
                        for (int r = 0; r < completeRecords; r++) {
                            int offset = r * recordSize;

                            // 重用ByteBuffer解析数据
                            bb.clear();
                            bb.put(buffer, offset, recordSize);
                            bb.flip();

                            // 解析采样点序号和时间戳
                            int sampleNumber = bb.getInt();
                            long rawTimestamp = bb.getInt() & 0xFFFFFFFFL; // 转换为无符号长整型

                            // 计算采样时间（微秒级精度）
                            String timeStr = null;
                            if (startTime != null) {
                                // 解析起始时间
                                DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS");
                                LocalDateTime baseTime = LocalDateTime.parse(startTime, inputFormatter);

                                // 计算时间偏移
                                long offsetMicros = (long) (rawTimestamp * timeMult);
                                LocalDateTime sampleTime = baseTime.plus(offsetMicros, ChronoUnit.MICROS);

                                // 格式化时间戳
                                DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("HH:mm:ss.SSSSSS");
                                timeStr = sampleTime.format(outputFormatter);
                            } else {
                                timeStr = String.valueOf(rawTimestamp);
                            }

                            // 解析模拟通道数据
                            for (int i = 0; i < analogChannels; i++) {
                                short rawValue = bb.getShort();
                                String channelId = analogChannelIds[i];

                                // 计算物理值：物理值 = (A * 原始值 + B) * (一次侧额定值 / 二次侧额定值)
                                double baseValue = rawValue * analogChannelMultipliersA[i] + analogChannelOffsetsB[i];
                                double transformRatio = analogChannelPrimaryFactors[i] / analogChannelSecondaryFactors[i];
                                double physicalValue = baseValue * transformRatio;

                                // 收集通道数据
                                List<Double> values = allValues.get(channelId);
                                List<String> timestamps = allTimestamps.get(channelId);
                                
                                values.add(physicalValue);
                                timestamps.add(timeStr);

                                // 更新最小值和最大值
                                if (!minValues.containsKey(channelId) || physicalValue < minValues.get(channelId)) {
                                    minValues.put(channelId, physicalValue);
                                }
                                if (!maxValues.containsKey(channelId) || physicalValue > maxValues.get(channelId)) {
                                    maxValues.put(channelId, physicalValue);
                                }
                            }

                            // 解析数字通道数据
                            // 解析状态通道数据
                            for (int word = 0; word < digitalWordCount; word++) {
                                short digitalWord = bb.getShort();

                                // 一次处理一个字（16位）的状态通道
                                for (int bit = 0; bit < 16; bit++) {
                                    int channelIndex = word * 16 + bit;
                                    if (channelIndex < digitalChannels) {
                                        // 从高位到低位提取数字量状态位
                                        int digitalValue = (digitalWord >> (15 - bit)) & 0x1;

                                        String channelId = digitalChannelIds[channelIndex];

                                        // 收集通道数据
                                        List<Double> values = allValues.get(channelId);
                                        values.add((double) digitalValue);

                                        List<String> timestamps = allTimestamps.get(channelId);
                                        timestamps.add(timeStr);

                                        // 更新最小值和最大值
                                        if (!minValues.containsKey(channelId) || digitalValue < minValues.get(channelId)) {
                                            minValues.put(channelId, (double) digitalValue);
                                        }
                                        if (!maxValues.containsKey(channelId) || digitalValue > maxValues.get(channelId)) {
                                            maxValues.put(channelId, (double) digitalValue);
                                        }
                                    }
                                }
                            }

                            // 更新进度
                            processedRecords++;
                            int progressPercent = (int) ((processedRecords * 100) / totalRecords);
                            if (progressPercent > lastProgressPercent && progressPercent % 10 == 0) {
                                lastProgressPercent = progressPercent;
                                log.debug("COMTRADE数据解析进度: {}%", progressPercent);
                            }
                        }

                        // 处理不完整记录的逻辑（如果有）
                        int remainingBytes = bytesRead % recordSize;
                        if (remainingBytes > 0) {
                            // 移动剩余字节到缓冲区开始位置
                            System.arraycopy(buffer, bytesRead - remainingBytes, buffer, 0, remainingBytes);

                            // 尝试读取补充字节以完成一条记录
                            int additionalBytesNeeded = recordSize - remainingBytes;
                            int additionalBytesRead = bis.read(buffer, remainingBytes, additionalBytesNeeded);

                            if (additionalBytesRead == additionalBytesNeeded) {
                                // 处理这条完整记录...（代码与上面相同，但为简化不再重复）
                            }
                        }
                    }
                }

                // 转换收集的数据为ChannelData对象
                for (String channelId : allValues.keySet()) {
                    List<Double> values = allValues.get(channelId);
                    List<String> timestamps = allTimestamps.get(channelId);

                    if (values.isEmpty()) {
                        continue; // 跳过空通道
                    }

                    // 重新计算过零点（基于完整数据集进行更准确的检测）
                    List<String> zeroCrossingTimestamps = detectZeroCrossings(values, timestamps);
                    allZeroCrossings.put(channelId, zeroCrossingTimestamps);

                    // 按照三个过零点为一个完整周期计算RMS值
                    List<Double> cyclicRmsValues = calculateCyclicRmsValues(values, timestamps, zeroCrossingTimestamps);

                    // 构建完整数据点列表（时间戳+值）
                    List<List<String>> dataWithTimestamp = new ArrayList<>(values.size());
                    for (int i = 0; i < values.size(); i++) {
                        List<String> pointData = Arrays.asList(
                                timestamps.get(i),
                                String.valueOf(values.get(i))
                        );
                        dataWithTimestamp.add(pointData);
                    }

                    // 计算有效值(RMS)、最小值、最大值和相位角
                    Double rmsValue = calculateRMS(values);
                    Double minValue = minValues.getOrDefault(channelId, 0.0);
                    Double maxValue = maxValues.getOrDefault(channelId, 0.0);
                    Double phaseAngle = calculatePhaseAngle(values, config.getLineFrequency(), config.getSamplingRate());

                    // 添加日期信息
                    String date = "";
                    if (startTime != null) {
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSSSSS");
                        try {
                            Date parsedDate = sdf.parse(startTime.toString());
                            date = new SimpleDateFormat("yyyy-MM-dd").format(parsedDate);
                        } catch (ParseException e) {
                            log.error("日期格式转换失败: {}", e.getMessage());
                        }
                    }

                    // 获取单位
                    String unit = null;
                    boolean isAnalog = false;
                    for (int i = 0; i < analogChannels; i++) {
                        if (analogChannelIds[i] != null && analogChannelIds[i].equals(channelId)) {
                            unit = analogChannelUnits[i];
                            isAnalog = true;
                            break;
                        }
                    }
                    // 如果不是模拟通道，则可能是数字通道，数字通道单位通常为null或空
                    if (!isAnalog) {
                        // 对于数字通道，可以设置默认单位，例如 null 或 ""
                        unit = null; // 或者 ""
                    }

                    // 创建通道数据对象并添加到结果列表
                    ChannelData cd = new ChannelData(channelId, dataWithTimestamp, rmsValue, minValue, maxValue, phaseAngle, date, unit, allZeroCrossings.getOrDefault(channelId, new ArrayList<>()), cyclicRmsValues);
                    channelDataList.add(cd);
                }

                log.info("成功解析BINARY格式DAT文件，共解析{}个通道", channelDataList.size());

            } catch (IOException e) {
                log.error("解析BINARY格式DAT文件出现IO异常: {}", e.getMessage(), e);
            } catch (Exception e) {
                log.error("解析BINARY格式DAT文件出现未知异常: {}", e.getMessage(), e);
            }
        } else {
            log.error("未知的数据文件格式: {}", config.getDataFormat());
        }

        return channelDataList;
    }

    @Override
    public ComtradeAnalysis getComtradeAnalysis(ComtradeConfig config, String calculationWindowStartTime, String calculationWindowEndTime) {
        ComtradeAnalysis comtradeAnalysis = new ComtradeAnalysis();
        List<ChannelData> channelDataList = config.getChannelDataList();
        SymmetricalComponentData symmetricalComponentData = new SymmetricalComponentData();
        List<Phasor> phasorList = new ArrayList<>();

        // 设置分析数据
        comtradeAnalysis.setStartTime(config.getStartTime());
        comtradeAnalysis.setTriggerTime(config.getTriggerTime());
        comtradeAnalysis.setCalculationWindowStartTime(calculationWindowStartTime);
        comtradeAnalysis.setCalculationWindowEndTime(calculationWindowEndTime);
        comtradeAnalysis.setNominalFrequency(config.getLineFrequency());
        comtradeAnalysis.setSampleRate(config.getSamplingRate());
        if (calculationWindowStartTime != null && calculationWindowEndTime != null) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS");
                LocalDateTime startDateTime = LocalDateTime.parse(calculationWindowStartTime, formatter);
                LocalDateTime endDateTime = LocalDateTime.parse(calculationWindowEndTime, formatter);
                long millisDiff = ChronoUnit.MILLIS.between(startDateTime, endDateTime);
                comtradeAnalysis.setCalculationWindowTimeLength(millisDiff);
            } catch (Exception e) {
                log.error("计算时间窗口长度时出错: {}", e.getMessage());
                comtradeAnalysis.setCalculationWindowTimeLength(0L);
            }
        } else {
            comtradeAnalysis.setCalculationWindowTimeLength(0L);
        }


        // 查找电压和电流通道
        ChannelData vaChannel = findChannelData(channelDataList, "Ua");
        ChannelData vbChannel = findChannelData(channelDataList, "Ub");
        ChannelData vcChannel = findChannelData(channelDataList, "Uc");
        ChannelData u4Channel = findChannelData(channelDataList, "U4"); // 查找U4通道，但不用于对称分量计算

        ChannelData iaChannel = findChannelData(channelDataList, "Ia");
        ChannelData ibChannel = findChannelData(channelDataList, "Ib");
        ChannelData icChannel = findChannelData(channelDataList, "Ic");
        ChannelData i4Channel = findChannelData(channelDataList, "I4"); // 查找I4通道，但不用于对称分量计算

        // 检查是否找到了所有必要的三相通道
        boolean canCalculateVoltage = vaChannel != null && vbChannel != null && vcChannel != null;
        boolean canCalculateCurrent = iaChannel != null && ibChannel != null && icChannel != null;

        if (!canCalculateVoltage) {
            log.warn("找不到完整的三相电压通道 (Ua, Ub, Uc)，无法计算电压对称分量和不平衡度。");
        }
        if (!canCalculateCurrent) {
            log.warn("找不到完整的三相电流通道 (Ia, Ib, Ic)，无法计算电流对称分量和不平衡度。");
        }

        // 计算电压对称分量和不平衡度
        if (canCalculateVoltage) {
            try {
                // 获取基波幅值 (RMS * sqrt(2)) 和相位角 (弧度)
                Complex va = new Complex(vaChannel.getEffective() * Math.sqrt(2) * Math.cos(Math.toRadians(vaChannel.getPhaseAngle())),
                        vaChannel.getEffective() * Math.sqrt(2) * Math.sin(Math.toRadians(vaChannel.getPhaseAngle())));
                Complex vb = new Complex(vbChannel.getEffective() * Math.sqrt(2) * Math.cos(Math.toRadians(vbChannel.getPhaseAngle())),
                        vbChannel.getEffective() * Math.sqrt(2) * Math.sin(Math.toRadians(vbChannel.getPhaseAngle())));
                Complex vc = new Complex(vcChannel.getEffective() * Math.sqrt(2) * Math.cos(Math.toRadians(vcChannel.getPhaseAngle())),
                        vcChannel.getEffective() * Math.sqrt(2) * Math.sin(Math.toRadians(vcChannel.getPhaseAngle())));

                // 定义对称分量算子 a 和 a^2
                Complex a = new Complex(Math.cos(Math.toRadians(120)), Math.sin(Math.toRadians(120)));
                Complex a2 = new Complex(Math.cos(Math.toRadians(240)), Math.sin(Math.toRadians(240)));

                // 计算对称分量电压 (复数)
                Complex v0 = va.add(vb).add(vc).divide(3.0);
                Complex v1 = va.add(a.multiply(vb)).add(a2.multiply(vc)).divide(3.0);
                Complex v2 = va.add(a2.multiply(vb)).add(a.multiply(vc)).divide(3.0);

                // 设置对称分量电压幅值
                symmetricalComponentData.setZeroSequenceVoltage(v0.abs());
                symmetricalComponentData.setPositiveSequenceVoltage(v1.abs());
                symmetricalComponentData.setNegativeSequenceVoltage(v2.abs());

                // 计算电压不平衡度
                if (v1.abs() > 1E-9) { // 避免除以零
                    symmetricalComponentData.setVoltageNegativeSequenceImbalance((v2.abs() / v1.abs()) * 100.0);
                    symmetricalComponentData.setVoltageZeroSequenceImbalance((v0.abs() / v1.abs()) * 100.0);
                } else {
                    symmetricalComponentData.setVoltageNegativeSequenceImbalance(0.0);
                    symmetricalComponentData.setVoltageZeroSequenceImbalance(0.0);
                    log.warn("正序电压幅值接近零，无法计算电压不平衡度。");
                }

                // 为相量图添加电压相量数据
                Phasor vaPhasor = new Phasor();
                vaPhasor.setPhase(vaChannel.getName());
                vaPhasor.setType("voltage");
                vaPhasor.setMagnitude(vaChannel.getEffective() * Math.sqrt(2));
                vaPhasor.setAngle(vaChannel.getPhaseAngle());
                phasorList.add(vaPhasor);

                Phasor vbPhasor = new Phasor();
                vbPhasor.setPhase(vbChannel.getName());
                vbPhasor.setType("voltage");
                vbPhasor.setMagnitude(vbChannel.getEffective() * Math.sqrt(2));
                vbPhasor.setAngle(vbChannel.getPhaseAngle());
                phasorList.add(vbPhasor);

                Phasor vcPhasor = new Phasor();
                vcPhasor.setPhase(vcChannel.getName());
                vcPhasor.setType("voltage");
                vcPhasor.setMagnitude(vcChannel.getEffective() * Math.sqrt(2));
                vcPhasor.setAngle(vcChannel.getPhaseAngle());
                phasorList.add(vcPhasor);

                // 如果U4存在，也添加到相量数据中
                if (u4Channel != null) {
                    Phasor u4Phasor = new Phasor();
                    u4Phasor.setPhase(u4Channel.getName());
                    u4Phasor.setType("voltage");
                    u4Phasor.setMagnitude(u4Channel.getEffective() * Math.sqrt(2));
                    u4Phasor.setAngle(u4Channel.getPhaseAngle());
                    phasorList.add(u4Phasor);
                }

            } catch (Exception e) {
                log.error("计算电压对称分量和不平衡度时发生错误: {}", e.getMessage(), e);
            }
        }

        // 计算电流对称分量和不平衡度
        if (canCalculateCurrent) {
            try {
                // 获取基波幅值 (RMS * sqrt(2)) 和相位角 (弧度)
                Complex ia = new Complex(iaChannel.getEffective() * Math.sqrt(2) * Math.cos(Math.toRadians(iaChannel.getPhaseAngle())),
                        iaChannel.getEffective() * Math.sqrt(2) * Math.sin(Math.toRadians(iaChannel.getPhaseAngle())));
                Complex ib = new Complex(ibChannel.getEffective() * Math.sqrt(2) * Math.cos(Math.toRadians(ibChannel.getPhaseAngle())),
                        ibChannel.getEffective() * Math.sqrt(2) * Math.sin(Math.toRadians(ibChannel.getPhaseAngle())));
                Complex ic = new Complex(icChannel.getEffective() * Math.sqrt(2) * Math.cos(Math.toRadians(icChannel.getPhaseAngle())),
                        icChannel.getEffective() * Math.sqrt(2) * Math.sin(Math.toRadians(icChannel.getPhaseAngle())));

                // 定义对称分量算子 a 和 a^2 (与电压计算相同)
                Complex a = new Complex(Math.cos(Math.toRadians(120)), Math.sin(Math.toRadians(120)));
                Complex a2 = new Complex(Math.cos(Math.toRadians(240)), Math.sin(Math.toRadians(240)));

                // 计算对称分量电流 (复数)
                Complex i0 = ia.add(ib).add(ic).divide(3.0);
                Complex i1 = ia.add(a.multiply(ib)).add(a2.multiply(ic)).divide(3.0);
                Complex i2 = ia.add(a2.multiply(ib)).add(a.multiply(ic)).divide(3.0);

                // 设置对称分量电流幅值
                symmetricalComponentData.setZeroSequenceCurrent(i0.abs());
                symmetricalComponentData.setPositiveSequenceCurrent(i1.abs());
                symmetricalComponentData.setNegativeSequenceCurrent(i2.abs());
                // 计算电流不平衡度
                if (i1.abs() > 1E-9) { // 避免除以零
                    symmetricalComponentData.setCurrentNegativeSequenceImbalance((i2.abs() / i1.abs()) * 100.0);
                    symmetricalComponentData.setCurrentZeroSequenceImbalance((i0.abs() / i1.abs()) * 100.0);
                } else {
                    symmetricalComponentData.setCurrentNegativeSequenceImbalance(0.0);
                    symmetricalComponentData.setCurrentZeroSequenceImbalance(0.0);
                    log.warn("正序电流幅值接近零，无法计算电流不平衡度。");
                }

                // 为相量图添加电流相量数据
                Phasor iaPhasor = new Phasor();
                iaPhasor.setPhase(iaChannel.getName());
                iaPhasor.setType("current");
                iaPhasor.setMagnitude(iaChannel.getEffective() * Math.sqrt(2));
                iaPhasor.setAngle(iaChannel.getPhaseAngle());
                phasorList.add(iaPhasor);

                Phasor ibPhasor = new Phasor();
                ibPhasor.setPhase(ibChannel.getName());
                ibPhasor.setType("current");
                ibPhasor.setMagnitude(ibChannel.getEffective() * Math.sqrt(2));
                ibPhasor.setAngle(ibChannel.getPhaseAngle());
                phasorList.add(ibPhasor);

                Phasor icPhasor = new Phasor();
                icPhasor.setPhase(icChannel.getName());
                icPhasor.setType("current");
                icPhasor.setMagnitude(icChannel.getEffective() * Math.sqrt(2));
                icPhasor.setAngle(icChannel.getPhaseAngle());
                phasorList.add(icPhasor);

                // 如果I4存在，也添加到相量数据中
                if (i4Channel != null) {
                    Phasor i4Phasor = new Phasor();
                    i4Phasor.setPhase(i4Channel.getName());
                    i4Phasor.setType("current");
                    i4Phasor.setMagnitude(i4Channel.getEffective() * Math.sqrt(2));
                    i4Phasor.setAngle(i4Channel.getPhaseAngle());
                    phasorList.add(i4Phasor);
                }

            } catch (Exception e) {
                log.error("计算电流对称分量和不平衡度时发生错误: {}", e.getMessage(), e);
            }
        }

        log.info("对称分量、不平衡度和相量数据计算完成");
        comtradeAnalysis.setSymmetricalComponentData(symmetricalComponentData);
        comtradeAnalysis.setPhasors(phasorList);
        return comtradeAnalysis;
    }

    /**
     * 辅助方法：根据通道名称查找通道数据
     */
    private ChannelData findChannelData(List<ChannelData> channelDataList, String channelName) {
        if (channelDataList == null || channelName == null) {
            return null;
        }
        for (ChannelData channelData : channelDataList) {
            if (channelData.getName() != null && channelData.getName().equalsIgnoreCase(channelName)) {
                return channelData;
            }
        }
        return null;
    }

    // 辅助方法
    private String parseString(String s) {
        if (s == null || s.trim().isEmpty() || s.trim().equals("-1")) return null;
        return s.trim();
    }

    private Double parseDouble(String s) {
        if (s == null || s.trim().isEmpty() || s.trim().equals("-1")) return null;
        try {
            return Double.valueOf(s.trim());
        } catch (NumberFormatException e) {
            log.warn("无法将字符串 '{}' 解析为 Double，返回 null", s);
            return null;
        }
    }

    private Integer parseInt(String s) {
        if (s == null || s.trim().isEmpty() || s.trim().equals("-1")) return null;
        try {
            return Integer.valueOf(s.trim());
        } catch (NumberFormatException e) {
            log.warn("无法将字符串 '{}' 解析为 Integer，返回 null", s);
            return null;
        }
    }

    /**
     * 计算有效值（RMS - Root Mean Square）
     *
     * @param values 数据值列表
     * @return 有效值
     */
    private Double calculateRMS(List<Double> values) {
        if (values == null || values.isEmpty()) {
            return 0.0;
        }

        double sumOfSquares = 0.0;
        for (Double value : values) {
            if (value != null) {
                sumOfSquares += value * value;
            }
        }

        return Math.sqrt(sumOfSquares / values.size());
    }

    /**
     * 计算相位角（Phase Angle）
     * 使用傅里叶变换提取信号的相位信息
     *
     * @param values        数据值列表
     * @param lineFrequency 电网频率 (Hz)
     * @param samplingRate  采样率 (Hz)
     * @return 相位角 (度)
     */
    private Double calculatePhaseAngle(List<Double> values, Float lineFrequency, Float samplingRate) {
        if (values == null || values.isEmpty() || lineFrequency == null || samplingRate == null) {
            return 0.0;
        }

        try {
            // 确定窗口大小 - 应该是采样率/频率的整数倍，以包含完整的周期
            int cyclePoints = Math.round(samplingRate / lineFrequency);
            int windowSize = cyclePoints;

            // 确保窗口大小是2的幂，这是FFT算法的要求
            int powerOf2 = 1;
            while (powerOf2 < windowSize) {
                powerOf2 *= 2;
            }
            windowSize = powerOf2;

            // 如果数据点不够，则返回0
            if (values.size() < windowSize) {
                log.warn("数据点数量不足以计算相位角，需要{}点，实际{}点", windowSize, values.size());
                return 0.0;
            }

            // 准备FFT输入数据
            double[] inputData = new double[windowSize];
            for (int i = 0; i < windowSize; i++) {
                if (i < values.size()) {
                    inputData[i] = values.get(i);
                } else {
                    inputData[i] = 0.0; // 不足的部分补0
                }
            }

            // 应用窗函数（汉宁窗）以减少频谱泄漏
            applyHannWindow(inputData);

            // 执行FFT
            FastFourierTransformer transformer = new FastFourierTransformer(DftNormalization.STANDARD);
            Complex[] fftResult = transformer.transform(inputData, TransformType.FORWARD);

            // 确定基波频率对应的FFT结果索引
            int fundamentalIndex = Math.round(lineFrequency * windowSize / samplingRate);
            if (fundamentalIndex >= fftResult.length) {
                log.error("计算基波索引错误: {} >= {}", fundamentalIndex, fftResult.length);
                return 0.0;
            }

            // 获取基波的复数值
            Complex fundamental = fftResult[fundamentalIndex];

            // 计算相位角（弧度）
            double phaseRadians = Math.atan2(fundamental.getImaginary(), fundamental.getReal());

            // 转换为度数并规范化到0-360度范围
            double phaseDegrees = Math.toDegrees(phaseRadians);
            if (phaseDegrees < 0) {
                phaseDegrees += 360.0;
            }

            return phaseDegrees;
        } catch (Exception e) {
            log.error("计算相位角时发生错误: {}", e.getMessage(), e);
            return 0.0;
        }
    }

    /**
     * 应用汉宁窗函数减少频谱泄漏
     *
     * @param data 输入数据
     */
    private void applyHannWindow(double[] data) {
        int n = data.length;
        for (int i = 0; i < n; i++) {
            // 汉宁窗公式: 0.5 * (1 - cos(2πi/(n-1)))
            double multiplier = 0.5 * (1 - Math.cos(2 * Math.PI * i / (n - 1)));
            data[i] *= multiplier;
        }
    }


    /**
     * 将 '09/05/2025,18:47:23.950000' 格式的时间字符串
     * 转换为 'yyyy-MM-dd HH:mm:ss.SSSSSS' 格式
     * 使用Java 17的java.time包处理
     *
     * @param timeStr 格式为 'dd/MM/yyyy,HH:mm:ss.SSSSSS' 的时间字符串
     * @return 格式为 'yyyy-MM-dd HH:mm:ss.SSSSSS' 的时间字符串
     */
    public static String convertTimeFormat(String timeStr) {
        try {
            // 定义输入格式
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy,HH:mm:ss.SSSSSS");

            // 解析输入字符串
            LocalDateTime dateTime = LocalDateTime.parse(timeStr, inputFormatter);

            // 定义输出格式
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS");

            // 格式化并返回结果
            return dateTime.format(outputFormatter);

        } catch (Exception e) {
            return "转换出错: " + e.getMessage();
        }
    }

    /**
     * 根据设备ID和不含后缀的文件名解析COMTRADE数据文件。
     *
     * @param equipmentId              设备ID (当前未使用，但为接口一致性保留)
     * @param fileNameWithoutExtension 不含后缀的文件名
     * @return 解析后的通道数据列表
     */
    public List<ChannelData> parseDataFileByName(Integer equipmentId, String fileNameWithoutExtension) {
        log.info("开始解析COMTRADE数据，设备ID: {}, 文件名: {}", equipmentId, fileNameWithoutExtension);

        String basePath = "upload-dir/comtrade/" + equipmentId + "/";
        File cfgFile = new File(basePath + fileNameWithoutExtension + ".cfg");
        File datFile = new File(basePath + fileNameWithoutExtension + ".dat");

        if (!cfgFile.exists()) {
            log.error("COMTRADE CFG 文件未找到: {}", cfgFile.getAbsolutePath());
            return new ArrayList<>();
        }
        if (!datFile.exists()) {
            log.error("COMTRADE DAT 文件未找到: {}", datFile.getAbsolutePath());
            return new ArrayList<>();
        }

        // 1. 解析 .cfg 文件获取配置信息
        ComtradeConfig config = parseConfigFile(cfgFile);
        if (config == null) {
            log.error("解析 COMTRADE 配置文件失败: {}", cfgFile.getAbsolutePath());
            return new ArrayList<>();
        }

        // 确保配置中的关键信息存在 (与 getComtradeData 方法中的校验类似)
        if (config.getStartTime() == null || config.getSamplingRate() == null || config.getAnalogChannels() == null || config.getDigitalChannels() == null || config.getDataFormat() == null) {
            log.error("COMTRADE 配置信息不完整。");
            return new ArrayList<>();
        }
        if (!"BINARY".equalsIgnoreCase(config.getDataFormat()) && !"ASCII".equalsIgnoreCase(config.getDataFormat())) {
            log.error("COMTRADE 数据格式不支持 (仅支持 BINARY 或 ASCII): {}", config.getDataFormat());
            return new ArrayList<>();
        }


        // 2. 解析 .dat 文件获取通道数据
        List<ChannelData> channelDataList = parseDataFile(datFile, config);
        if (channelDataList.isEmpty() && "BINARY".equalsIgnoreCase(config.getDataFormat())) { // ASCII可能返回空列表是正常的，如果文件本身为空
            log.warn("解析 COMTRADE 数据文件 {} 可能未产生数据或文件为空。", datFile.getAbsolutePath());
        } else if (channelDataList.isEmpty() && "ASCII".equalsIgnoreCase(config.getDataFormat())) {
            log.info("解析的 ASCII COMTRADE 数据文件 {} 为空或不包含数据点。", datFile.getAbsolutePath());
        }


        log.info("成功通过文件名解析COMTRADE数据，设备ID: {}, 文件: {}，共解析{}个通道", equipmentId, fileNameWithoutExtension, channelDataList.size());
        return channelDataList;
    }

    @Override
    public ComtradeConfig getFilteredComtradeConfig(Integer equipmentId, String fileName, String calculationWindowStartTime, String calculationWindowEndTime) {
        log.info("开始获取并过滤COMTRADE数据，设备ID: {}, 文件名: {}, 窗口开始时间: {}, 窗口结束时间: {}",
                equipmentId, fileName, calculationWindowStartTime, calculationWindowEndTime);

        // 构建文件路径
        String basePath = "upload-dir/comtrade/" + equipmentId + "/";
        File cfgFile = new File(basePath + fileName + ".cfg");
        File datFile = new File(basePath + fileName + ".dat");

        // 检查文件是否存在
        if (!cfgFile.exists()) {
            log.error("COMTRADE配置文件不存在: {}", cfgFile.getAbsolutePath());
            return null;
        }
        if (!datFile.exists()) {
            log.error("COMTRADE数据文件不存在: {}", datFile.getAbsolutePath());
            return null;
        }

        // 1. 解析配置文件
        ComtradeConfig config = parseConfigFile(cfgFile);
        if (config == null) {
            log.error("解析COMTRADE配置文件失败: {}", cfgFile.getAbsolutePath());
            return null;
        }

        // 2. 解析数据文件
        List<ChannelData> channelDataList = parseDataFile(datFile, config);
        if (channelDataList == null || channelDataList.isEmpty()) {
            log.warn("解析COMTRADE数据文件未获取到通道数据: {}", datFile.getAbsolutePath());
            // 仍然返回配置信息，只是没有通道数据
            return config;
        }

        // 3. 根据时间窗口过滤数据
        if (calculationWindowStartTime != null && calculationWindowEndTime != null) {
            List<ChannelData> filteredChannelDataList = new ArrayList<>();

            try {
                DateTimeFormatter windowFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS");
                LocalDateTime startDateTime = LocalDateTime.parse(calculationWindowStartTime, windowFormatter);
                LocalDateTime endDateTime = LocalDateTime.parse(calculationWindowEndTime, windowFormatter);

                // 对每个通道应用过滤
                for (ChannelData channelData : channelDataList) {
                    List<List<String>> filteredData = new ArrayList<>();
                    if (channelData.getData() != null) {
                        for (List<String> point : channelData.getData()) {
                            // 预期点数据格式: [时间戳, 值]
                            if (point.size() >= 2) {
                                String timeStr = point.get(0);

                                // 需要将时间戳转换为可比较的格式（假设时间戳是"HH:mm:ss.SSSSSS"格式）
                                // 获取日期部分
                                String dateStr = "";
                                if (config.getStartTime() != null && config.getStartTime().length() >= 10) {
                                    dateStr = config.getStartTime().substring(0, 10) + " "; // 提取 "yyyy-MM-dd "
                                } else {
                                    // 如果无法从config获取日期，使用当前日期
                                    dateStr = java.time.LocalDate.now().toString() + " ";
                                }

                                try {
                                    // 拼接日期和时间
                                    String fullTimeStr = dateStr + timeStr;
                                    LocalDateTime pointTime = LocalDateTime.parse(fullTimeStr,
                                            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS"));

                                    // 过滤在时间窗口内的数据点
                                    if ((pointTime.isEqual(startDateTime) || pointTime.isAfter(startDateTime)) &&
                                            (pointTime.isEqual(endDateTime) || pointTime.isBefore(endDateTime))) {
                                        filteredData.add(point);
                                    }
                                } catch (Exception e) {
                                    log.warn("解析时间戳失败: {}, {}", timeStr, e.getMessage());
                                    // 如果无法解析时间戳，保留该点（可选行为）
                                    filteredData.add(point);
                                }
                            } else {
                                // 数据格式不正确，记录警告但仍保留数据点
                                log.warn("数据点格式不正确: {}", point);
                                filteredData.add(point);
                            }
                        }
                    }

                    // 为过滤后的数据创建新的ChannelData对象
                    ChannelData filteredChannelData = new ChannelData(
                            channelData.getName(),
                            filteredData,
                            channelData.getEffective(),
                            channelData.getMin(),
                            channelData.getMax(),
                            channelData.getPhaseAngle(),
                            channelData.getDate(),
                            channelData.getUnit(),
                            channelData.getZeroCrossingTimestamps(), // 保持原有的过零点时间戳
                            channelData.getCyclicRmsValues() != null ? channelData.getCyclicRmsValues() : new ArrayList<>() // 保持原有的周期RMS值
                    );

                    // 重新计算过滤后数据的统计值（如有需要）
                    if (!filteredData.isEmpty()) {
                        List<Double> values = new ArrayList<>();
                        for (List<String> point : filteredData) {
                            try {
                                values.add(Double.parseDouble(point.get(1)));
                            } catch (NumberFormatException e) {
                                log.warn("无法解析数值: {}", point.get(1));
                            }
                        }
                    }

                    filteredChannelDataList.add(filteredChannelData);
                }

                // 更新通道数据列表
                channelDataList = filteredChannelDataList;

            } catch (Exception e) {
                log.error("过滤数据时发生错误: {}", e.getMessage(), e);
                // 发生错误时继续使用原始数据
            }
        }

        // 4. 设置通道数据到配置对象
        config.setChannelDataList(channelDataList);


        log.info("已成功获取并过滤COMTRADE数据，设备ID: {}, 文件名: {}, 通道数: {}",
                equipmentId, fileName, channelDataList.size());

        return config;
    }

    @Override
    public long exportComtradeDataToCsv(Integer equipmentId, String fileNameWithoutExtension, String outputFilePath) {
        log.info("开始导出COMTRADE数据为CSV，设备ID: {}, 文件名: {}, 输出路径: {}",
                equipmentId, fileNameWithoutExtension, outputFilePath);

        try {
            // 1. 解析COMTRADE数据
            List<ChannelData> channelDataList = parseDataFileByName(equipmentId, fileNameWithoutExtension);
            if (channelDataList == null || channelDataList.isEmpty()) {
                log.warn("未找到COMTRADE数据，设备ID: {}, 文件名: {}", equipmentId, fileNameWithoutExtension);
                return 0;
            }

            // 2. 创建通道映射，按照图片中的列顺序
            Map<String, ChannelData> channelMap = new HashMap<>();
            for (ChannelData channelData : channelDataList) {
                channelMap.put(channelData.getName(), channelData);
            }

            // 3. 定义CSV列顺序（根据图片内容）
            String[] columnNames = {"Microseconds", "Ua", "Ub", "Uc", "U4", "Ia", "Ib", "Ic", "I4"};

            // 4. 确定数据行数（以第一个有效通道的数据长度为准）
            int dataRowCount = 0;
            ChannelData firstChannel = null;
            for (String columnName : columnNames) {
                if (!"Microseconds".equals(columnName) && channelMap.containsKey(columnName)) {
                    firstChannel = channelMap.get(columnName);
                    if (firstChannel.getData() != null) {
                        dataRowCount = firstChannel.getData().size();
                        break;
                    }
                }
            }

            if (dataRowCount == 0) {
                log.warn("所有通道数据为空，无法导出CSV");
                return 0;
            }

            // 5. 使用hutool的CsvWriter写入CSV文件
            try (CsvWriter writer = CsvUtil.getWriter(
                    java.nio.file.Paths.get(outputFilePath).toUri().getPath(),
                    java.nio.charset.StandardCharsets.UTF_8, false)) {

                // 写入表头
                writer.writeHeaderLine(columnNames);

                // 写入数据行
                for (int i = 0; i < dataRowCount; i++) {
                    List<String> row = new ArrayList<>();

                    for (String columnName : columnNames) {
                        if ("Microseconds".equals(columnName)) {
                            // 微秒时间戳列：从第一个有效通道获取时间戳并转换为微秒
                            if (firstChannel != null && firstChannel.getData() != null &&
                                    i < firstChannel.getData().size() &&
                                    firstChannel.getData().get(i).size() > 0) {
                                String timeStr = firstChannel.getData().get(i).get(0);
                                // 将时间戳转换为微秒（假设时间戳格式为 HH:mm:ss.SSSSSS）
                                try {
                                    String[] timeParts = timeStr.split(":");
                                    if (timeParts.length == 3) {
                                        int hours = Integer.parseInt(timeParts[0]);
                                        int minutes = Integer.parseInt(timeParts[1]);
                                        String[] secParts = timeParts[2].split("\\.");
                                        int seconds = Integer.parseInt(secParts[0]);
                                        int microseconds = 0;
                                        if (secParts.length > 1) {
                                            // 补齐到6位微秒
                                            String microStr = secParts[1];
                                            while (microStr.length() < 6) {
                                                microStr += "0";
                                            }
                                            if (microStr.length() > 6) {
                                                microStr = microStr.substring(0, 6);
                                            }
                                            microseconds = Integer.parseInt(microStr);
                                        }

                                        // 计算总微秒数
                                        long totalMicroseconds = (long) hours * 3600 * 1000000L +
                                                (long) minutes * 60 * 1000000L +
                                                (long) seconds * 1000000L +
                                                microseconds;
                                        row.add(String.valueOf(totalMicroseconds));
                                    } else {
                                        row.add(String.valueOf(i)); // 如果解析失败，使用索引
                                    }
                                } catch (Exception e) {
                                    log.warn("解析时间戳失败: {}, 使用索引: {}", timeStr, i);
                                    row.add(String.valueOf(i));
                                }
                            } else {
                                row.add(String.valueOf(i));
                            }
                        } else {
                            // 其他通道数据列
                            ChannelData channelData = channelMap.get(columnName);
                            if (channelData != null && channelData.getData() != null &&
                                    i < channelData.getData().size() &&
                                    channelData.getData().get(i).size() > 1) {
                                // 获取数值（第二列是数值）
                                row.add(channelData.getData().get(i).get(1));
                            } else {
                                row.add("0"); // 如果通道不存在或数据不足，填充0
                            }
                        }
                    }

                    // 写入行数据
                    writer.writeLine(row.toArray(new String[0]));
                }

                log.info("成功导出COMTRADE数据为CSV，设备ID: {}, 文件名: {}, 数据行数: {}",
                        equipmentId, fileNameWithoutExtension, dataRowCount);
                return dataRowCount;

            }

        } catch (Exception e) {
            log.error("导出COMTRADE数据为CSV时发生错误: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 对指定通道进行谐波分析
     *
     * @param channelData      通道数据
     * @param nominalFrequency 额定频率
     * @param sampleRate       采样率
     * @param maxHarmonicOrder 最大谐波次数
     * @return 通道谐波分析结果
     */
    public ChannelHarmonic analyzeChannelHarmonics(ChannelData channelData, Double nominalFrequency,
                                                   Double sampleRate, Integer maxHarmonicOrder) {
        if (channelData == null || channelData.getData() == null || channelData.getData().isEmpty()) {
            log.warn("通道数据为空，无法进行谐波分析: {}", channelData != null ? channelData.getName() : "unknown");
            return null;
        }

        // 验证输入参数的合理性
        if (nominalFrequency == null || nominalFrequency <= 0) {
            log.error("额定频率无效: {}", nominalFrequency);
            return null;
        }

        if (sampleRate == null || sampleRate <= 0) {
            log.error("采样率无效: {}", sampleRate);
            return null;
        }

        // 检查采样率是否满足奈奎斯特定理
        if (sampleRate < 2 * nominalFrequency * maxHarmonicOrder) {
            log.warn("采样率可能不足以准确分析{}次谐波，建议采样率至少为{}Hz",
                    maxHarmonicOrder, 2 * nominalFrequency * maxHarmonicOrder);
        }

        try {
            // 提取通道数值数据
            List<Double> values = new ArrayList<>();
            for (List<String> point : channelData.getData()) {
                if (point.size() >= 2) {
                    try {
                        values.add(Double.parseDouble(point.get(1)));
                    } catch (NumberFormatException e) {
                        log.warn("无法解析数值: {}", point.get(1));
                    }
                }
            }

            if (values.isEmpty()) {
                log.warn("通道 {} 没有有效的数值数据", channelData.getName());
                return null;
            }

            // 确定FFT窗口大小（应该是2的幂，并包含整数个周期）
            int cyclePoints = (int) Math.round(sampleRate / nominalFrequency);
            int windowSize = calculateOptimalFFTWindowSize(cyclePoints, values.size());

            if (windowSize > values.size()) {
                log.warn("数据点数量不足以进行谐波分析，需要{}点，实际{}点", windowSize, values.size());
                return null;
            }

            // 检查是否有足够的周期数进行准确分析
            double actualCycles = (double) windowSize / cyclePoints;
            if (actualCycles < 2.0) {
                log.warn("数据长度可能不足以进行准确的谐波分析，实际周期数: {}", actualCycles);
            }

            // 准备FFT输入数据并进行预处理
            double[] inputData = new double[windowSize];
            for (int i = 0; i < windowSize; i++) {
                inputData[i] = values.get(i);
            }

            // 去除直流分量
            double dcComponent = 0.0;
            for (double value : inputData) {
                dcComponent += value;
            }
            dcComponent /= inputData.length;

            for (int i = 0; i < inputData.length; i++) {
                inputData[i] -= dcComponent;
            }

            log.debug("通道 {} 去除直流分量: {}", channelData.getName(), dcComponent);

            // 应用汉宁窗以减少频谱泄漏
            applyHannWindow(inputData);

            // 执行FFT
            FastFourierTransformer transformer = new FastFourierTransformer(DftNormalization.STANDARD);
            Complex[] fftResult = transformer.transform(inputData, TransformType.FORWARD);

            // 分析谐波分量
            List<HarmonicComponent> harmonicComponents = extractHarmonicComponents(
                    fftResult, nominalFrequency, sampleRate, windowSize, maxHarmonicOrder);

            // 计算谐波统计参数
            ChannelHarmonic channelHarmonic = calculateHarmonicStatistics(
                    channelData, harmonicComponents, nominalFrequency);

            return channelHarmonic;

        } catch (Exception e) {
            log.error("通道 {} 谐波分析时发生错误: {}", channelData.getName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 计算最优的FFT窗口大小
     */
    private int calculateOptimalFFTWindowSize(int cyclePoints, int dataSize) {
        // 确保至少包含4个完整周期以获得足够的频率分辨率
        int minSize = cyclePoints * 4;

        // 找到一个2的幂，使其大于或等于最小尺寸
        int powerOf2 = 1;
        while (powerOf2 < minSize) {
            powerOf2 *= 2;
        }

        // 确保不超过可用数据大小
        while (powerOf2 > dataSize && powerOf2 > cyclePoints) {
            powerOf2 /= 2;
        }

        // 如果计算出的窗口大小太小，至少保证一个周期
        if (powerOf2 < cyclePoints) {
            // 找到大于等于一个周期的最小2的幂
            powerOf2 = 1;
            while (powerOf2 < cyclePoints) {
                powerOf2 *= 2;
            }
            // 如果还是超过数据大小，则使用数据大小
            if (powerOf2 > dataSize) {
                // 找到小于等于数据大小的最大2的幂
                powerOf2 = 1;
                while (powerOf2 * 2 <= dataSize) {
                    powerOf2 *= 2;
                }
            }
        }

        log.debug("FFT窗口大小计算 - 周期点数: {}, 数据大小: {}, 选择窗口: {}, 包含周期数: {:.2f}",
                cyclePoints, dataSize, powerOf2, (double) powerOf2 / cyclePoints);

        return powerOf2;
    }

    /**
     * 从FFT结果中提取谐波分量
     */
    private List<HarmonicComponent> extractHarmonicComponents(Complex[] fftResult, Double nominalFrequency,
                                                              Double sampleRate, int windowSize, Integer maxHarmonicOrder) {
        List<HarmonicComponent> components = new ArrayList<>();

        // 频率分辨率
        double frequencyResolution = sampleRate / windowSize;
        log.debug("频率分辨率: {:.3f} Hz, 窗口大小: {}, 最大谐波次数: {}",
                frequencyResolution, windowSize, maxHarmonicOrder);

        for (int order = 1; order <= maxHarmonicOrder; order++) {
            double targetFrequency = nominalFrequency * order;
            int targetIndex = (int) Math.round(targetFrequency / frequencyResolution);

            if (targetIndex < fftResult.length) {
                Complex harmonicComplex = fftResult[targetIndex];

                // 计算幅值（考虑FFT的归一化）
                // 对于实信号的FFT，除了直流分量(index=0)和奈奎斯特频率外，
                // 其他所有频率分量都需要乘以2来补偿负频率部分
                double magnitude;
                if (targetIndex == 0) {
                    // 直流分量不需要乘以2
                    magnitude = harmonicComplex.abs() / windowSize;
                } else if (targetIndex == windowSize / 2) {
                    // 奈奎斯特频率（如果存在）不需要乘以2
                    magnitude = harmonicComplex.abs() / windowSize;
                } else {
                    // 其他频率分量（包括基波和各次谐波）需要乘以2
                    magnitude = harmonicComplex.abs() * 2.0 / windowSize;
                }

                // 计算相位角
                double phase = Math.toDegrees(Math.atan2(harmonicComplex.getImaginary(), harmonicComplex.getReal()));
                if (phase < 0) phase += 360.0;

                // 确定谐波类型
                String type = "FUNDAMENTAL";
                if (order > 1) {
                    type = (order % 2 == 0) ? "EVEN" : "ODD";
                }

                HarmonicComponent component = new HarmonicComponent(
                        order, targetFrequency, magnitude, phase, 0.0, type);
                components.add(component);

                log.debug("{}次谐波 - 频率: {:.1f}Hz, 索引: {}, 幅值: {:.6f}, 相位: {:.1f}°",
                        order, targetFrequency, targetIndex, magnitude, phase);
            } else {
                log.warn("{}次谐波频率 {:.1f}Hz 超出FFT分析范围，跳过", order, targetFrequency);
            }
        }

        // 计算谐波含有率
        if (!components.isEmpty() && components.get(0).getOrder() == 1) {
            double fundamentalMagnitude = components.get(0).getMagnitude();
            if (fundamentalMagnitude > 1E-9) {
                for (HarmonicComponent component : components) {
                    if (component.getOrder() > 1) {
                        double percentage = (component.getMagnitude() / fundamentalMagnitude) * 100.0;
                        component.setPercentage(percentage);
                    } else {
                        component.setPercentage(100.0); // 基波为100%
                    }
                }
                log.debug("基波幅值: {:.6f}, 已计算各次谐波含有率", fundamentalMagnitude);
            } else {
                log.warn("基波幅值过小 ({:.9f})，无法计算谐波含有率", fundamentalMagnitude);
            }
        }

        return components;
    }

    /**
     * 计算谐波统计参数
     */
    private ChannelHarmonic calculateHarmonicStatistics(ChannelData channelData,
                                                        List<HarmonicComponent> harmonicComponents,
                                                        Double nominalFrequency) {
        ChannelHarmonic channelHarmonic = new ChannelHarmonic();

        // 基本信息
        channelHarmonic.setChannelName(channelData.getName());
        channelHarmonic.setChannelType(determineChannelType(channelData.getName()));
        channelHarmonic.setUnit(channelData.getUnit());
        channelHarmonic.setEffectiveValue(channelData.getEffective());
        channelHarmonic.setNominalFrequency(nominalFrequency);

        if (!harmonicComponents.isEmpty()) {
            // 基波有效值
            HarmonicComponent fundamental = harmonicComponents.get(0);
            if (fundamental.getOrder() == 1) {
                channelHarmonic.setFundamentalValue(fundamental.getMagnitude() / Math.sqrt(2)); // 转换为RMS
            }

            // 计算THD（总谐波失真）
            double fundamentalMagnitude = fundamental.getMagnitude();
            double harmonicSumSquares = 0.0;
            double oddHarmonicSumSquares = 0.0;
            double evenHarmonicSumSquares = 0.0;
            double kFactorSum = 0.0;

            // 修正的奇偶谐波分类逻辑：排除基波，只对高次谐波进行分类
            for (HarmonicComponent component : harmonicComponents) {
                if (component.getOrder() > 1) { // 排除基波，只计算高次谐波
                    double harmonicMagnitude = component.getMagnitude();
                    harmonicSumSquares += harmonicMagnitude * harmonicMagnitude;

                    // 正确的奇偶谐波分类：
                    // 奇谐波：3, 5, 7, 9, 11, 13, ...
                    // 偶谐波：2, 4, 6, 8, 10, 12, ...
                    if (component.getOrder() % 2 == 1) { // 奇次谐波（3,5,7...）
                        oddHarmonicSumSquares += harmonicMagnitude * harmonicMagnitude;
                    } else { // 偶次谐波（2,4,6...）
                        evenHarmonicSumSquares += harmonicMagnitude * harmonicMagnitude;
                    }

                    // K因数计算：用于评估谐波对变压器等设备的影响
                    kFactorSum += Math.pow(component.getOrder(), 2) * Math.pow(harmonicMagnitude, 2);
                }
            }

            if (fundamentalMagnitude > 1E-9) {
                // THD计算：THD = sqrt(sum(Hi^2)) / H1 * 100%
                double thd = (Math.sqrt(harmonicSumSquares) / fundamentalMagnitude) * 100.0;
                channelHarmonic.setTotalHarmonicDistortion(thd);

                // 奇谐波含量：sqrt(sum(奇次谐波^2)) / 基波 * 100%
                double oddHarmonics = (Math.sqrt(oddHarmonicSumSquares) / fundamentalMagnitude) * 100.0;
                channelHarmonic.setOddHarmonics(oddHarmonics);

                // 偶谐波含量：sqrt(sum(偶次谐波^2)) / 基波 * 100%
                double evenHarmonics = (Math.sqrt(evenHarmonicSumSquares) / fundamentalMagnitude) * 100.0;
                channelHarmonic.setEvenHarmonics(evenHarmonics);

                // K因数：K = sqrt(sum(n^2 * Hn^2)) / H1
                double kFactor = Math.sqrt(kFactorSum) / fundamentalMagnitude;
                channelHarmonic.setKFactor(kFactor);

                // 生成427次谐波的详细统计信息
                List<List<String>> harmonicStats = generateDetailedHarmonicStats(harmonicComponents, fundamentalMagnitude);
                channelHarmonic.setHarmonicComponents(harmonicStats);

                log.debug("通道 {} 谐波统计 - THD: {:.2f}%, 奇谐波: {:.2f}%, 偶谐波: {:.2f}%, K因数: {:.2f}",
                        channelData.getName(), thd, oddHarmonics, evenHarmonics, kFactor);
            } else {
                log.warn("通道 {} 基波幅值过小，无法计算谐波统计参数", channelData.getName());
                channelHarmonic.setTotalHarmonicDistortion(0.0);
                channelHarmonic.setOddHarmonics(0.0);
                channelHarmonic.setEvenHarmonics(0.0);
                channelHarmonic.setKFactor(0.0);
                
                // 设置默认的427次谐波统计信息
                List<List<String>> harmonicStats = generateDefaultHarmonicStats();
                channelHarmonic.setHarmonicComponents(harmonicStats);
            }

            // 计算波峰因数和波形因子
            calculateWaveformFactors(channelData, channelHarmonic);
        } else {
            // 如果没有谐波分量数据，设置默认值
            List<List<String>> harmonicStats = generateDefaultHarmonicStats();
            channelHarmonic.setHarmonicComponents(harmonicStats);
        }

        return channelHarmonic;
    }

    /**
     * 生成427次谐波的详细统计信息
     * @param harmonicComponents 谐波分量列表
     * @param fundamentalMagnitude 基波幅值
     * @return 427次谐波的统计信息，每个内层List包含[总谐波失真%, 偶谐波%, 奇谐波%]
     */
    private List<List<String>> generateDetailedHarmonicStats(List<HarmonicComponent> harmonicComponents, double fundamentalMagnitude) {
        List<List<String>> harmonicStats = new ArrayList<>();
        
        // 创建谐波分量映射，便于快速查找
        Map<Integer, HarmonicComponent> harmonicMap = new HashMap<>();
        for (HarmonicComponent component : harmonicComponents) {
            harmonicMap.put(component.getOrder(), component);
        }
        
        // 为每次谐波（1-427次）生成统计信息
        for (int order = 1; order <= 427; order++) {
            List<String> harmonicData = new ArrayList<>();
            
            HarmonicComponent component = harmonicMap.get(order);
            if (component != null && fundamentalMagnitude > 1E-9) {
                // 计算该次谐波的含有率
                double harmonicPercentage = (component.getMagnitude() / fundamentalMagnitude) * 100.0;
                
                // 计算到该次谐波为止的累积THD
                double cumulativeTHD = calculateCumulativeTHD(harmonicMap, fundamentalMagnitude, order);
                
                // 计算到该次谐波为止的累积奇谐波含量
                double cumulativeOddHarmonics = calculateCumulativeOddHarmonics(harmonicMap, fundamentalMagnitude, order);
                
                // 计算到该次谐波为止的累积偶谐波含量
                double cumulativeEvenHarmonics = calculateCumulativeEvenHarmonics(harmonicMap, fundamentalMagnitude, order);
                
                harmonicData.add(String.format("%.2f", cumulativeTHD));           // 累积总谐波失真%
                harmonicData.add(String.format("%.2f", cumulativeEvenHarmonics)); // 累积偶谐波含量%
                harmonicData.add(String.format("%.2f", cumulativeOddHarmonics));  // 累积奇谐波含量%
            } else {
                // 如果该次谐波不存在或基波幅值过小，设置为0
                harmonicData.add("0.00"); // 总谐波失真%
                harmonicData.add("0.00"); // 偶谐波含量%
                harmonicData.add("0.00"); // 奇谐波含量%
            }
            
            harmonicStats.add(harmonicData);
        }
        
        return harmonicStats;
    }
    
    /**
     * 计算累积总谐波失真（到指定次数为止）
     */
    private double calculateCumulativeTHD(Map<Integer, HarmonicComponent> harmonicMap, double fundamentalMagnitude, int maxOrder) {
        double harmonicSumSquares = 0.0;
        
        for (int order = 2; order <= maxOrder; order++) { // 从2次谐波开始
            HarmonicComponent component = harmonicMap.get(order);
            if (component != null) {
                double harmonicMagnitude = component.getMagnitude();
                harmonicSumSquares += harmonicMagnitude * harmonicMagnitude;
            }
        }
        
        return (Math.sqrt(harmonicSumSquares) / fundamentalMagnitude) * 100.0;
    }
    
    /**
     * 计算累积奇谐波含量（到指定次数为止）
     */
    private double calculateCumulativeOddHarmonics(Map<Integer, HarmonicComponent> harmonicMap, double fundamentalMagnitude, int maxOrder) {
        double oddHarmonicSumSquares = 0.0;
        
        for (int order = 3; order <= maxOrder; order += 2) { // 奇次谐波：3, 5, 7, 9, ...
            HarmonicComponent component = harmonicMap.get(order);
            if (component != null) {
                double harmonicMagnitude = component.getMagnitude();
                oddHarmonicSumSquares += harmonicMagnitude * harmonicMagnitude;
            }
        }
        
        return (Math.sqrt(oddHarmonicSumSquares) / fundamentalMagnitude) * 100.0;
    }
    
    /**
     * 计算累积偶谐波含量（到指定次数为止）
     */
    private double calculateCumulativeEvenHarmonics(Map<Integer, HarmonicComponent> harmonicMap, double fundamentalMagnitude, int maxOrder) {
        double evenHarmonicSumSquares = 0.0;
        
        for (int order = 2; order <= maxOrder; order += 2) { // 偶次谐波：2, 4, 6, 8, ...
            HarmonicComponent component = harmonicMap.get(order);
            if (component != null) {
                double harmonicMagnitude = component.getMagnitude();
                evenHarmonicSumSquares += harmonicMagnitude * harmonicMagnitude;
            }
        }
        
        return (Math.sqrt(evenHarmonicSumSquares) / fundamentalMagnitude) * 100.0;
    }
    
    /**
     * 生成默认的427次谐波统计信息（全部为0）
     */
    private List<List<String>> generateDefaultHarmonicStats() {
        List<List<String>> harmonicStats = new ArrayList<>();
        
        for (int order = 1; order <= 427; order++) {
            List<String> harmonicData = new ArrayList<>();
            harmonicData.add("0.00"); // 总谐波失真%
            harmonicData.add("0.00"); // 偶谐波含量%
            harmonicData.add("0.00"); // 奇谐波含量%
            harmonicStats.add(harmonicData);
        }
        
        return harmonicStats;
    }

    /**
     * 确定通道类型
     */
    private String determineChannelType(String channelName) {
        if (channelName == null) return "UNKNOWN";
        String upperName = channelName.toUpperCase();
        if (upperName.startsWith("I") || upperName.contains("CURRENT")) {
            return "CURRENT";
        } else if (upperName.startsWith("U") || upperName.startsWith("V") || upperName.contains("VOLTAGE")) {
            return "VOLTAGE";
        } else {
            return "UNKNOWN";
        }
    }

    /**
     * 计算波形因子相关参数
     */
    private void calculateWaveformFactors(ChannelData channelData, ChannelHarmonic channelHarmonic) {
        if (channelData.getData() == null || channelData.getData().isEmpty()) {
            return;
        }

        List<Double> values = new ArrayList<>();
        for (List<String> point : channelData.getData()) {
            if (point.size() >= 2) {
                try {
                    values.add(Double.parseDouble(point.get(1)));
                } catch (NumberFormatException e) {
                    // 忽略无效数据点
                }
            }
        }

        if (values.isEmpty()) return;

        // 计算峰值
        double maxValue = values.stream().mapToDouble(Math::abs).max().orElse(0.0);

        // 计算平均值（绝对值）
        double averageValue = values.stream().mapToDouble(Math::abs).average().orElse(0.0);

        // 计算有效值（已有）
        double rmsValue = channelData.getEffective();

        if (rmsValue > 1E-9) {
            // 波峰因数 = 峰值 / 有效值
            channelHarmonic.setCrestFactor(maxValue / rmsValue);
        }

        if (averageValue > 1E-9) {
            // 波形因子 = 有效值 / 平均值
            channelHarmonic.setFormFactor(rmsValue / averageValue);
        }
    }


    /**
     * 获取完整的COMTRADE配置和数据（不进行时间过滤）
     *
     * @param equipmentId 设备ID
     * @param fileName    COMTRADE文件名（不含扩展名）
     * @return COMTRADE配置对象，包含完整的通道数据
     */
    public ComtradeConfig getComtradeConfig(Integer equipmentId, String fileName) {
        log.info("开始获取完整COMTRADE数据，设备ID: {}, 文件名: {}", equipmentId, fileName);

        // 构建文件路径
        String basePath = "upload-dir/comtrade/" + equipmentId + "/";
        File cfgFile = new File(basePath + fileName + ".cfg");
        File datFile = new File(basePath + fileName + ".dat");

        // 检查文件是否存在
        if (!cfgFile.exists()) {
            log.error("COMTRADE配置文件不存在: {}", cfgFile.getAbsolutePath());
            return null;
        }
        if (!datFile.exists()) {
            log.error("COMTRADE数据文件不存在: {}", datFile.getAbsolutePath());
            return null;
        }

        // 1. 解析配置文件
        ComtradeConfig config = parseConfigFile(cfgFile);
        if (config == null) {
            log.error("解析COMTRADE配置文件失败: {}", cfgFile.getAbsolutePath());
            return null;
        }

        // 2. 解析数据文件
        List<ChannelData> channelDataList = parseDataFile(datFile, config);
        if (channelDataList == null || channelDataList.isEmpty()) {
            log.warn("解析COMTRADE数据文件未获取到通道数据: {}", datFile.getAbsolutePath());
            // 仍然返回配置信息，只是没有通道数据
            return config;
        }

        // 3. 设置通道数据到配置对象
        config.setChannelDataList(channelDataList);

        log.info("已成功获取完整COMTRADE数据，设备ID: {}, 文件名: {}, 通道数: {}",
                equipmentId, fileName, channelDataList.size());

        return config;
    }

    /**
     * 计算工频周期 20ms RMS值
     * 将原始数据按20ms窗口分割，计算每个窗口的RMS值
     * 
     * @param equipmentId 设备ID
     * @param fileNameWithoutExtension 不含后缀的文件名
     * @return 包含20ms RMS值的通道数据列表
     */
    public List<ChannelData> calculate20msRmsValues(Integer equipmentId, String fileNameWithoutExtension) {
        log.info("开始计算工频周期20ms RMS值，设备ID: {}, 文件名: {}", equipmentId, fileNameWithoutExtension);

        // 1. 首先获取原始数据
        List<ChannelData> originalChannelDataList = parseDataFileByName(equipmentId, fileNameWithoutExtension);
        if (originalChannelDataList == null || originalChannelDataList.isEmpty()) {
            log.warn("未找到原始COMTRADE数据，无法计算20ms RMS值");
            return new ArrayList<>();
        }

        // 2. 获取配置信息以确定采样率
        String basePath = "upload-dir/comtrade/" + equipmentId + "/";
        File cfgFile = new File(basePath + fileNameWithoutExtension + ".cfg");
        ComtradeConfig config = parseConfigFile(cfgFile);
        if (config == null || config.getSamplingRate() == null) {
            log.error("无法获取采样率信息，无法计算20ms RMS值");
            return new ArrayList<>();
        }

        float samplingRate = config.getSamplingRate();
        float lineFrequency = config.getLineFrequency() != null ? config.getLineFrequency() : 50.0f; // 默认50Hz
        
        // 3. 计算一个完整周期对应的采样点数，而不是固定20ms
        // 对于50Hz: 1周期 = 20ms
        // 对于60Hz: 1周期 = 16.67ms
        int cyclePoints = Math.round(samplingRate / lineFrequency);
        double cycleDurationMs = 1000.0 / lineFrequency;
        
        // 4. 计算滑动步长（通常为窗口大小的1/4到1/2，以获得平滑的RMS曲线）
        int stepSize = Math.max(1, cyclePoints / 4); // 使用1/4周期作为步长
        
        log.info("采样率: {}Hz, 线路频率: {}Hz, 周期时长: {:.2f}ms, 周期采样点数: {}, 滑动步长: {}", 
                samplingRate, lineFrequency, cycleDurationMs, cyclePoints, stepSize);

        List<ChannelData> rmsChannelDataList = new ArrayList<>();

        // 5. 对每个通道计算RMS值
        for (ChannelData originalChannel : originalChannelDataList) {
            if (originalChannel.getData() == null || originalChannel.getData().isEmpty()) {
                continue;
            }

            List<List<String>> rmsData = new ArrayList<>();
            List<Double> allRmsValues = new ArrayList<>();
            
            // 提取原始数值数据
            List<Double> originalValues = new ArrayList<>();
            List<String> originalTimestamps = new ArrayList<>();
            
            for (List<String> point : originalChannel.getData()) {
                if (point.size() >= 2) {
                    try {
                        originalValues.add(Double.parseDouble(point.get(1)));
                        originalTimestamps.add(point.get(0));
                    } catch (NumberFormatException e) {
                        log.warn("无法解析数值: {}", point.get(1));
                    }
                }
            }

            if (originalValues.size() < cyclePoints) {
                log.warn("通道 {} 数据点数量不足一个完整周期({:.2f}ms)，跳过", originalChannel.getName(), cycleDurationMs);
                continue;
            }

            // 6. 使用重叠滑动窗口计算RMS值
            for (int windowStart = 0; windowStart <= originalValues.size() - cyclePoints; windowStart += stepSize) {
                int windowEnd = windowStart + cyclePoints;
                
                // 提取当前窗口的数据
                List<Double> windowValues = originalValues.subList(windowStart, windowEnd);
                
                // 计算当前窗口的RMS值
                double rmsValue = calculateRMS(windowValues);
                allRmsValues.add(rmsValue);
                
                // 使用窗口中间点的时间戳作为RMS值的时间戳
                int middleIndex = windowStart + cyclePoints / 2;
                if (middleIndex >= originalTimestamps.size()) {
                    middleIndex = originalTimestamps.size() - 1;
                }
                String timestamp = originalTimestamps.get(middleIndex);
                
                // 添加到RMS数据列表
                List<String> rmsPoint = Arrays.asList(timestamp, String.valueOf(rmsValue));
                rmsData.add(rmsPoint);
            }

            // 7. 计算RMS数据的统计值
            Double minRms = allRmsValues.stream().mapToDouble(Double::doubleValue).min().orElse(0.0);
            Double maxRms = allRmsValues.stream().mapToDouble(Double::doubleValue).max().orElse(0.0);
            Double avgRms = allRmsValues.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            
            // 使用原始通道的相位角
            Double phaseAngle = originalChannel.getPhaseAngle();

            // 8. 创建RMS通道数据对象
            ChannelData rmsChannelData = new ChannelData(
                    originalChannel.getName() + "_" + String.format("%.1f", cycleDurationMs) + "ms_RMS", // 通道名称加上实际周期时长
                    rmsData,
                    avgRms,  // 有效值使用平均RMS值
                    minRms,  // 最小值
                    maxRms,  // 最大值
                    phaseAngle, // 相位角
                    originalChannel.getDate(), // 日期
                    originalChannel.getUnit(),  // 单位
                    new ArrayList<>(), // RMS数据通常不需要过零点检测，使用空列表
                    new ArrayList<>() // RMS数据不需要周期RMS值，使用空列表
            );

            rmsChannelDataList.add(rmsChannelData);
            
            log.debug("通道 {} 计算完成，原始数据点: {}, RMS数据点: {}, 平均RMS: {:.6f}", 
                    originalChannel.getName(), originalValues.size(), rmsData.size(), avgRms);
        }

        log.info("工频周期RMS值计算完成，设备ID: {}, 文件: {}, 处理通道数: {}", 
                equipmentId, fileNameWithoutExtension, rmsChannelDataList.size());

        return rmsChannelDataList;
    }

    /**
     * 计算工频周期 RMS值（支持自定义窗口大小）
     * 
     * @param equipmentId 设备ID
     * @param fileNameWithoutExtension 不含后缀的文件名
     * @param windowSizeMs 窗口大小（毫秒）
     * @return 包含RMS值的通道数据列表
     */
    public List<ChannelData> calculateRmsValues(Integer equipmentId, String fileNameWithoutExtension, double windowSizeMs) {
        log.info("开始计算{}ms RMS值，设备ID: {}, 文件名: {}", windowSizeMs, equipmentId, fileNameWithoutExtension);

        // 1. 首先获取原始数据
        List<ChannelData> originalChannelDataList = parseDataFileByName(equipmentId, fileNameWithoutExtension);
        if (originalChannelDataList == null || originalChannelDataList.isEmpty()) {
            log.warn("未找到原始COMTRADE数据，无法计算{}ms RMS值", windowSizeMs);
            return new ArrayList<>();
        }

        // 2. 获取配置信息以确定采样率
        String basePath = "upload-dir/comtrade/" + equipmentId + "/";
        File cfgFile = new File(basePath + fileNameWithoutExtension + ".cfg");
        ComtradeConfig config = parseConfigFile(cfgFile);
        if (config == null || config.getSamplingRate() == null) {
            log.error("无法获取采样率信息，无法计算{}ms RMS值", windowSizeMs);
            return new ArrayList<>();
        }

        float samplingRate = config.getSamplingRate();
        float lineFrequency = config.getLineFrequency() != null ? config.getLineFrequency() : 50.0f;
        
        // 3. 计算窗口对应的采样点数
        int windowSizePoints = Math.round(samplingRate * (float)(windowSizeMs / 1000.0));
        
        // 4. 计算滑动步长（使用窗口大小的1/4作为步长，以获得平滑的RMS曲线）
        int stepSize = Math.max(1, windowSizePoints / 4);
        
        // 5. 验证窗口大小的合理性
        double cycleDurationMs = 1000.0 / lineFrequency;
        int cyclePoints = Math.round(samplingRate / lineFrequency);
        
        if (Math.abs(windowSizeMs - cycleDurationMs) > 1.0) {
            log.warn("警告：窗口大小{}ms与系统周期{:.2f}ms不匹配，可能影响RMS计算精度", 
                    windowSizeMs, cycleDurationMs);
        }
        
        log.info("采样率: {}Hz, 线路频率: {}Hz, 窗口: {}ms({}点), 系统周期: {:.2f}ms({}点), 滑动步长: {}", 
                samplingRate, lineFrequency, windowSizeMs, windowSizePoints, cycleDurationMs, cyclePoints, stepSize);

        List<ChannelData> rmsChannelDataList = new ArrayList<>();

        // 6. 对每个通道计算RMS值
        for (ChannelData originalChannel : originalChannelDataList) {
            if (originalChannel.getData() == null || originalChannel.getData().isEmpty()) {
                continue;
            }

            List<List<String>> rmsData = new ArrayList<>();
            List<Double> allRmsValues = new ArrayList<>();
            
            // 提取原始数值数据
            List<Double> originalValues = new ArrayList<>();
            List<String> originalTimestamps = new ArrayList<>();
            
            for (List<String> point : originalChannel.getData()) {
                if (point.size() >= 2) {
                    try {
                        originalValues.add(Double.parseDouble(point.get(1)));
                        originalTimestamps.add(point.get(0));
                    } catch (NumberFormatException e) {
                        log.warn("无法解析数值: {}", point.get(1));
                    }
                }
            }

            if (originalValues.size() < windowSizePoints) {
                log.warn("通道 {} 数据点数量不足一个{}ms窗口，跳过", originalChannel.getName(), windowSizeMs);
                continue;
            }

            // 7. 使用重叠滑动窗口计算RMS值
            for (int windowStart = 0; windowStart <= originalValues.size() - windowSizePoints; windowStart += stepSize) {
                int windowEnd = windowStart + windowSizePoints;
                
                // 提取当前窗口的数据
                List<Double> windowValues = originalValues.subList(windowStart, windowEnd);
                
                // 计算当前窗口的RMS值
                double rmsValue = calculateRMS(windowValues);
                allRmsValues.add(rmsValue);
                
                // 使用窗口中间点的时间戳作为RMS值的时间戳
                int middleIndex = windowStart + windowSizePoints / 2;
                if (middleIndex >= originalTimestamps.size()) {
                    middleIndex = originalTimestamps.size() - 1;
                }
                String timestamp = originalTimestamps.get(middleIndex);
                
                // 添加到RMS数据列表
                List<String> rmsPoint = Arrays.asList(timestamp, String.valueOf(rmsValue));
                rmsData.add(rmsPoint);
            }

            // 8. 计算RMS数据的统计值
            Double minRms = allRmsValues.stream().mapToDouble(Double::doubleValue).min().orElse(0.0);
            Double maxRms = allRmsValues.stream().mapToDouble(Double::doubleValue).max().orElse(0.0);
            Double avgRms = allRmsValues.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
            
            // 使用原始通道的相位角
            Double phaseAngle = originalChannel.getPhaseAngle();

            // 9. 创建RMS通道数据对象
            ChannelData rmsChannelData = new ChannelData(
                    originalChannel.getName() + "_" + (int)windowSizeMs + "ms_RMS", // 通道名称加上RMS后缀
                    rmsData,
                    avgRms,  // 有效值使用平均RMS值
                    minRms,  // 最小值
                    maxRms,  // 最大值
                    phaseAngle, // 相位角
                    originalChannel.getDate(), // 日期
                    originalChannel.getUnit(),  // 单位
                    new ArrayList<>(), // RMS数据通常不需要过零点检测，使用空列表
                    new ArrayList<>() // RMS数据不需要周期RMS值，使用空列表
            );

            rmsChannelDataList.add(rmsChannelData);
            
            log.debug("通道 {} 计算完成，原始数据点: {}, RMS数据点: {}, 平均RMS: {:.6f}", 
                    originalChannel.getName(), originalValues.size(), rmsData.size(), avgRms);
        }

        log.info("{}ms RMS值计算完成，设备ID: {}, 文件: {}, 处理通道数: {}", 
                windowSizeMs, equipmentId, fileNameWithoutExtension, rmsChannelDataList.size());

        return rmsChannelDataList;
    }

    /**
     * 检测信号的过零点
     * 过零点定义为：相邻两个采样点之间信号从正值变为负值或从负值变为正值的时刻
     * 使用线性插值估算精确的过零时刻
     * 
     * @param values 信号数值列表
     * @param timestamps 对应的时间戳列表
     * @return 过零点时间戳列表
     */
    private List<String> detectZeroCrossings(List<Double> values, List<String> timestamps) {
        List<String> zeroCrossings = new ArrayList<>();
        
        if (values == null || timestamps == null || values.size() != timestamps.size() || values.size() < 2) {
            return zeroCrossings;
        }
        
        for (int i = 0; i < values.size() - 1; i++) {
            double value1 = values.get(i);
            double value2 = values.get(i + 1);
            String time1 = timestamps.get(i);
            String time2 = timestamps.get(i + 1);
            
            // 检测过零点：从负到正 或 从正到负
            // 考虑零值的情况：如果一个点正好是零，也算作过零点
            if ((value1 < 0 && value2 >= 0) || (value1 > 0 && value2 <= 0)) {
                // 如果 value1 已经是0，直接记录 time1
                if (value1 == 0) {
                    zeroCrossings.add(time1);
                } else if (value2 == 0) {
                    // 如果 value2 是0，直接记录 time2
                    zeroCrossings.add(time2);
                } else {
                    // 线性插值计算更精确的过零点时间
                    String interpolatedTime = interpolateZeroCrossingTime(value1, value2, time1, time2);
                    if (interpolatedTime != null) {
                        zeroCrossings.add(interpolatedTime);
                    }
                }
            }
        }
        
        log.debug("检测到{}个过零点", zeroCrossings.size());
        return zeroCrossings;
    }
    
    /**
     * 使用线性插值估算精确的过零时刻
     * 公式：t0 = t1 + (t2 - t1) * (0 - v1) / (v2 - v1)
     * 
     * @param value1 前一个采样点的值
     * @param value2 当前采样点的值
     * @param time1 前一个采样点的时间戳
     * @param time2 当前采样点的时间戳
     * @return 插值得到的过零时刻，如果无法计算则返回null
     */
    private String interpolateZeroCrossingTime(double value1, double value2, String time1, String time2) {
        try {
            // 转换时间戳为微秒
            long micros1 = parseTimeStringToMicros(time1);
            long micros2 = parseTimeStringToMicros(time2);
            
            // 线性插值计算过零时刻
            // t0 = t1 + (t2 - t1) * (0 - v1) / (v2 - v1)
            double ratio = (0 - value1) / (value2 - value1);
            long zeroCrossingMicros = micros1 + Math.round((micros2 - micros1) * ratio);
            
            // 转换回时间戳格式
            return formatMicrosToTimeString(zeroCrossingMicros);
            
        } catch (Exception e) {
            log.warn("插值计算过零时刻失败: {}", e.getMessage());
            return time2; // 如果计算失败，返回当前时间戳
        }
    }
    
    /**
     * 将时间戳字符串转换为微秒
     * 支持格式：HH:mm:ss.SSSSSS
     */
    private long parseTimeStringToMicros(String timeStr) {
        String[] parts = timeStr.split(":");
        if (parts.length != 3) {
            throw new IllegalArgumentException("时间戳格式错误: " + timeStr);
        }
        
        int hours = Integer.parseInt(parts[0]);
        int minutes = Integer.parseInt(parts[1]);
        
        String[] secParts = parts[2].split("\\.");
        int seconds = Integer.parseInt(secParts[0]);
        int microseconds = 0;
        
        if (secParts.length > 1) {
            String microStr = secParts[1];
            // 补齐到6位微秒
            while (microStr.length() < 6) {
                microStr += "0";
            }
            if (microStr.length() > 6) {
                microStr = microStr.substring(0, 6);
            }
            microseconds = Integer.parseInt(microStr);
        }
        
        return (long) hours * 3600 * 1000000L +
               (long) minutes * 60 * 1000000L +
               (long) seconds * 1000000L +
               microseconds;
    }
    
    /**
     * 将微秒转换为时间戳字符串格式
     * 格式：HH:mm:ss.SSSSSS
     */
    private String formatMicrosToTimeString(long totalMicros) {
        // 处理可能的负值或超出24小时的情况
        totalMicros = totalMicros % (24 * 3600 * 1000000L);
        if (totalMicros < 0) {
            totalMicros += 24 * 3600 * 1000000L;
        }
        
        long hours = totalMicros / (3600 * 1000000L);
        totalMicros %= (3600 * 1000000L);
        
        long minutes = totalMicros / (60 * 1000000L);
        totalMicros %= (60 * 1000000L);
        
        long seconds = totalMicros / 1000000L;
        long microseconds = totalMicros % 1000000L;
        
        return String.format("%02d:%02d:%02d.%06d", hours, minutes, seconds, microseconds);
    }

    /**
     * 按照三个过零点为一个完整周期计算RMS值（优化版本）
     * 对于正弦波信号，三个连续的过零点构成一个完整的周期：
     * 第一个过零点 -> 第二个过零点（半周期）
     * 第二个过零点 -> 第三个过零点（另一个半周期）
     * 
     * @param values 信号数值列表
     * @param timestamps 对应的时间戳列表
     * @param zeroCrossingTimestamps 过零点时间戳列表
     * @return 每个周期的RMS值列表
     */
    private List<Double> calculateCyclicRmsValues(List<Double> values, List<String> timestamps, List<String> zeroCrossingTimestamps) {
        List<Double> cyclicRmsValues = new ArrayList<>();
        
        if (values == null || timestamps == null || zeroCrossingTimestamps == null || 
            values.size() != timestamps.size() || zeroCrossingTimestamps.size() < 3) {
            log.debug("数据不足以计算周期RMS值，需要至少3个过零点");
            return cyclicRmsValues;
        }
        
        try {
            // 预先转换所有时间戳为微秒数组，避免重复解析
            long[] timestampMicros = new long[timestamps.size()];
            for (int i = 0; i < timestamps.size(); i++) {
                try {
                    timestampMicros[i] = parseTimeStringToMicros(timestamps.get(i));
                } catch (Exception e) {
                    timestampMicros[i] = Long.MAX_VALUE; // 标记无效时间戳
                }
            }
            
            // 预先转换过零点时间戳
            long[] zeroCrossingMicros = new long[zeroCrossingTimestamps.size()];
            for (int i = 0; i < zeroCrossingTimestamps.size(); i++) {
                try {
                    zeroCrossingMicros[i] = parseTimeStringToMicros(zeroCrossingTimestamps.get(i));
                } catch (Exception e) {
                    zeroCrossingMicros[i] = Long.MAX_VALUE; // 标记无效时间戳
                }
            }
            
            // 为每三个连续的过零点计算一个周期的RMS值
            for (int i = 0; i <= zeroCrossingTimestamps.size() - 3; i++) {
                long startZeroCrossingMicros = zeroCrossingMicros[i];
                long endZeroCrossingMicros = zeroCrossingMicros[i + 2]; // 第三个过零点
                
                // 使用二分查找找到对应的数据索引范围
                int startIndex = findClosestTimestampIndex(timestampMicros, startZeroCrossingMicros);
                int endIndex = findClosestTimestampIndex(timestampMicros, endZeroCrossingMicros);
                
                if (startIndex >= 0 && endIndex >= 0 && startIndex < endIndex && endIndex < values.size()) {
                    // 直接计算RMS值，避免创建临时List
                    double cycleRms = calculateRMSDirectly(values, startIndex, endIndex);
                    cyclicRmsValues.add(cycleRms);
                    
                    if (log.isDebugEnabled()) {
                        log.debug("周期 {} RMS值: {:.6f}, 数据点数: {}, 索引范围: {} -> {}", 
                                i + 1, cycleRms, endIndex - startIndex + 1, startIndex, endIndex);
                    }
                } else {
                    log.warn("无法找到过零点对应的数据索引，跳过周期 {}: startIndex={}, endIndex={}", 
                            i + 1, startIndex, endIndex);
                }
            }
            
            log.debug("计算完成，共计算了{}个周期的RMS值", cyclicRmsValues.size());
            
        } catch (Exception e) {
            log.error("计算周期RMS值时发生错误: {}", e.getMessage(), e);
        }
        
        return cyclicRmsValues;
    }
    
    /**
     * 使用二分查找在时间戳数组中查找最接近指定时间戳的索引（优化版本）
     * 
     * @param timestampMicros 时间戳微秒数组（已排序）
     * @param targetMicros 目标时间戳微秒
     * @return 最接近的时间戳索引，如果未找到返回-1
     */
    private int findClosestTimestampIndex(long[] timestampMicros, long targetMicros) {
        if (timestampMicros == null || timestampMicros.length == 0 || targetMicros == Long.MAX_VALUE) {
            return -1;
        }
        
        // 使用二分查找找到插入位置
        int left = 0;
        int right = timestampMicros.length - 1;
        int closestIndex = 0;
        long minDifference = Math.abs(timestampMicros[0] - targetMicros);
        
        while (left <= right) {
            int mid = left + (right - left) / 2;
            long currentMicros = timestampMicros[mid];
            
            if (currentMicros == Long.MAX_VALUE) {
                // 跳过无效时间戳
                right = mid - 1;
                continue;
            }
            
            long difference = Math.abs(currentMicros - targetMicros);
            if (difference < minDifference) {
                minDifference = difference;
                closestIndex = mid;
            }
            
            if (currentMicros < targetMicros) {
                left = mid + 1;
            } else if (currentMicros > targetMicros) {
                right = mid - 1;
            } else {
                return mid; // 精确匹配
            }
        }
        
        // 检查相邻元素
        if (closestIndex > 0) {
            long prevDiff = Math.abs(timestampMicros[closestIndex - 1] - targetMicros);
            if (prevDiff < minDifference && timestampMicros[closestIndex - 1] != Long.MAX_VALUE) {
                closestIndex = closestIndex - 1;
                minDifference = prevDiff;
            }
        }
        
        if (closestIndex < timestampMicros.length - 1) {
            long nextDiff = Math.abs(timestampMicros[closestIndex + 1] - targetMicros);
            if (nextDiff < minDifference && timestampMicros[closestIndex + 1] != Long.MAX_VALUE) {
                closestIndex = closestIndex + 1;
            }
        }
        
        return closestIndex;
    }
    
    /**
     * 直接计算指定范围内数据的RMS值，避免创建临时List（优化版本）
     * 
     * @param values 数据值列表
     * @param startIndex 开始索引（包含）
     * @param endIndex 结束索引（包含）
     * @return RMS值
     */
    private double calculateRMSDirectly(List<Double> values, int startIndex, int endIndex) {
        if (values == null || startIndex < 0 || endIndex >= values.size() || startIndex > endIndex) {
            return 0.0;
        }
        
        double sumOfSquares = 0.0;
        int count = 0;
        
        for (int i = startIndex; i <= endIndex; i++) {
            Double value = values.get(i);
            if (value != null) {
                sumOfSquares += value * value;
                count++;
            }
        }
        
        return count > 0 ? Math.sqrt(sumOfSquares / count) : 0.0;
    }
    
    /**
     * 在时间戳列表中查找最接近指定时间戳的索引（原版本，保持兼容性）
     * 由于过零点时间戳可能是插值计算得出的，需要找到最接近的实际采样点
     * 
     * @param timestamps 时间戳列表
     * @param targetTimestamp 目标时间戳
     * @return 最接近的时间戳索引，如果未找到返回-1
     */
    private int findTimestampIndex(List<String> timestamps, String targetTimestamp) {
        if (timestamps == null || targetTimestamp == null) {
            return -1;
        }
        
        try {
            long targetMicros = parseTimeStringToMicros(targetTimestamp);
            int closestIndex = -1;
            long minDifference = Long.MAX_VALUE;
            
            for (int i = 0; i < timestamps.size(); i++) {
                try {
                    long currentMicros = parseTimeStringToMicros(timestamps.get(i));
                    long difference = Math.abs(currentMicros - targetMicros);
                    
                    if (difference < minDifference) {
                        minDifference = difference;
                        closestIndex = i;
                    }
                } catch (Exception e) {
                    // 忽略无法解析的时间戳
                    continue;
                }
            }
            
            return closestIndex;
            
        } catch (Exception e) {
            log.warn("查找时间戳索引时发生错误: {}", e.getMessage());
            return -1;
        }
    }
}