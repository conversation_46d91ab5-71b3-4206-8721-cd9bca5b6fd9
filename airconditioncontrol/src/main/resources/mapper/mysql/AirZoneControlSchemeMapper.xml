<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.airconditioncontrol.mapper.AirZoneControlSchemeMapper">


    <resultMap id="AirZoneSchemeInfoMap" type="com.siteweb.airconditioncontrol.model.AirZoneSchemeInfo">
        <result column="StationId" property="stationId"/>
        <result column="VirtualEquipmentId" property="virtualEquipmentId"/>
        <collection property="schemeLists" ofType="com.siteweb.airconditioncontrol.entity.AirZoneControlScheme"
                    column="StationId, VirtualEquipmentId">
            <result column="SchemeStationId" property="stationId"/>
            <result column="SchemeVirtualEquipmentId" property="virtualEquipmentId"/>

            <result column="SchemeId" property="schemeId"/>
            <result column="SchemeName" property="schemeName"/>
            <result column="StartDate" property="startDate"/>
            <result column="EndDate" property="endDate"/>
            <result column="UpdateTime" property="updateTime"/>
            <result column="UpdateId" property="updateId"/>
            <result column="ExtendField" property="extendField"/>
        </collection>
    </resultMap>

    <update id="updateBySchemeId">
        UPDATE Aircon_ZoneControlScheme
            SET SchemeName = #{schemeName}, StartDate = #{startDate}, EndDate = #{endDate}, UpdateId = #{updateId}, UpdateTime = #{updateTime}
        WHERE SchemeId = #{schemeId}
    </update>
    <delete id="delById">
        DELETE FROM Aircon_ZoneControlScheme
        WHERE SchemeId = #{schemeId} AND VirtualEquipmentId = #{virtualEquipmentId}
    </delete>
    <delete id="delByVirtualEquipmentId">
        DELETE FROM Aircon_ZoneControlScheme
        WHERE VirtualEquipmentId = #{virtualEquipmentId}
    </delete>

    <select id="findByVid" resultType="com.siteweb.airconditioncontrol.dto.ZoneScheme">
        SELECT * FROM Aircon_ZoneControlScheme WHERE StationId = #{stationId} AND VirtualEquipmentId = #{virtualEquipmentId} ORDER BY UpdateTime DESC
    </select>

    <select id="findZoneSchemeInfo" resultMap="AirZoneSchemeInfoMap">
        SELECT t.*, t.StationId SchemeStationId, t.VirtualEquipmentId SchemeVirtualEquipmentId
        FROM Aircon_ZoneControlScheme t <if test="virtualEquipmentId != null"> WHERE t.VirtualEquipmentId = #{virtualEquipmentId}</if>
    </select>
    <select id="countWithTheSchemeName" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM Aircon_ZoneControlScheme
        WHERE SchemeName = #{schemeName} AND StationId = #{stationId} AND VirtualEquipmentId = #{virtualEquipmentId} <if test="excludeSchemeId != null"> AND SchemeId != #{excludeSchemeId}</if>
    </select>
    <select id="countOfExistScheme" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM Aircon_ZoneControlScheme
        WHERE VirtualEquipmentId = #{virtualEquipmentId} AND StationId = #{stationId}
    </select>
</mapper>