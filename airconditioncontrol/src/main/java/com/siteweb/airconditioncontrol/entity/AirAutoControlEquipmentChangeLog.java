package com.siteweb.airconditioncontrol.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.airconditioncontrol.dto.AirComplexindex;
import com.siteweb.airconditioncontrol.dto.AirGroupParams;
import com.siteweb.airconditioncontrol.dto.ZoneOperation;
import com.siteweb.airconditioncontrol.dto.ZoneScheme;
import com.siteweb.airconditioncontrol.enumeration.AutoControlGroupModule;
import com.siteweb.airconditioncontrol.enumeration.ChangeState;
import com.siteweb.airconditioncontrol.enumeration.ZoneStrategyEntityType;
import com.siteweb.airconditioncontrol.function.CommonUtils;
import com.siteweb.airconditioncontrol.function.IniFileUtils;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.StringUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.javatuples.Triplet;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 自动群控设备分组变更日志表
 */
@Data
@NoArgsConstructor
@TableName("Aircon_AutoControlEquipmentChangeLog")
public class AirAutoControlEquipmentChangeLog {

    @TableId(value="LogId", type = IdType.AUTO)
    private Integer logId;
    private Integer stationId;
    private String stationName;
    private Integer monitorUnitId;
    private String monitorUnitName;
    private Integer virtualEquipmentId;
    private String groupName;
    private String groupNameNew;
    /** 标注变更类型，有效值：1-增；2-改；3-删 */
    private Integer operateType;
    /** 变更类型operateType的文字描述 */
    private String operateTypeLabel;
    /** 操作的所属模块，取枚举 AutoControlGroupModule 的值 */
    private Integer operateModule;
    /** 操作的所属模块，文字描述 */
    private String operateModuleLabel;
    /** 当操作的所属模块是定时策略时，用来标注定时策略或操作序列的变更类型，有效值：1-增；2-改；3-删 */
    private Integer subOperateType;
    /** 子变更类型 subOperateType 的文字描述 */
    private String subOperateTypeLabel;
    /** 变更子对象的类型，现只运用于变更定时策略模块时，有效值：1-定时策略记录；2-操作序列记录 */
    private Integer subObjectType;
    /** 变更子对象的类型 subObjectType 的文字描述 */
    private String subObjectTypeLabel;

    private String operator;
    private Integer operatorId;
    private Date insertTime;
    /** 变更的详细内容 */
    private String changeContent;
    private String extendField1;

    //非数据库字段
    // ---- GROUP -----//
    @JsonIgnore
    @TableField(exist = false)
    private String airconEquipIds;//群控设备分组所管理的空调设备id串，以逗号分隔
    /** null表示未变更，但空串表示是选择为空，注意二者的区别 */
    @JsonIgnore
    @TableField(exist = false)
    private String airconEquipIdsNew;//群控设备分组所管理的空调设备id串，以逗号分隔
    // ---- BASIC_PARAM -----//
    @JsonIgnore
    @TableField(exist = false)
    private AirGroupParams groupParams;//基础参数数据库值
    @JsonIgnore
    @TableField(exist = false)
    private AirGroupParams groupParamsNew;//基础参数待修改新值
    // ---- MAP_TEMPERATURE -----//
    @JsonIgnore
    @TableField(exist = false)
    private String tempEquipIds;//群控设备分组所管理的温度设备id串，以逗号分隔
    @JsonIgnore
    @TableField(exist = false)
    private String tempEquipIdsNew;//群控设备分组所管理的温度设备id串，以逗号分隔
    @JsonIgnore
    @TableField(exist = false)
    private Integer tempType;//群控设备分组所管理的温度设备类型，1为室外温度采集点，非1为室内温度采集点
    // ---- MAP_FAN -----//
    @JsonIgnore
    @TableField(exist = false)
    private String fanEquipIds;//群控设备分组所管理的风机设备id串，以逗号分隔
    @JsonIgnore
    @TableField(exist = false)
    private String fanEquipIdsNew;//群控设备分组所管理的风机设备id串，以逗号分隔
    // ---- MAP_ZONE_STRATEGY -----//
    @JsonIgnore
    @TableField(exist = false)
    private ZoneScheme mapZoneScheme;
    @JsonIgnore
    @TableField(exist = false)
    private ZoneScheme mapZoneSchemeNew;
    @JsonIgnore
    @TableField(exist = false)
    private ZoneOperation mapZoneOperation;
    @JsonIgnore
    @TableField(exist = false)
    private ZoneOperation mapZoneOperationNew;
    // ---- MAP_ENERGY_COMPLEXINDEX -----//
    @JsonIgnore
    @TableField(exist = false)
    private AirComplexindex complexIndexInfo;//群控设备分组所映射的能耗指标信息
    @JsonIgnore
    @TableField(exist = false)
    private AirComplexindex complexIndexInfoNew;//群控设备分组所映射的能耗指标信息


    /** 设置 操作类型 信息 */
    public void setOperateTypeInfo(ChangeState operateType, LocaleMessageSourceUtil messageSourceUtil) {
        if(operateType == null) return;
        this.operateType = operateType.value();
        if(operateType == ChangeState.UPDATE) {
            this.operateTypeLabel = messageSourceUtil.getMessage("airconditioncontrol.common.update");
        } else if(operateType == ChangeState.NEW) {
            this.operateTypeLabel = messageSourceUtil.getMessage("airconditioncontrol.common.new");
        } else if(operateType == ChangeState.DELETE) {
            this.operateTypeLabel = messageSourceUtil.getMessage("airconditioncontrol.common.delete");
        } else {
            this.operateTypeLabel = "UNKNOWN";
        }
    }
    /** 设置 操作模块 信息 */
    public void setOperateModuleInfo(AutoControlGroupModule operateModule, LocaleMessageSourceUtil messageSourceUtil) {
        if(operateModule == null) return;
        this.operateModule = operateModule.value();
        if(operateModule == AutoControlGroupModule.GROUP) {
            this.operateModuleLabel = messageSourceUtil.getMessage("airConditionControl.autoControl.group");
        } else if(operateModule == AutoControlGroupModule.BASIC_PARAM) {
            this.operateModuleLabel = messageSourceUtil.getMessage("airConditionControl.autoControl.basicParam");
        } else if(operateModule == AutoControlGroupModule.MAP_TEMPERATURE) {
            this.operateModuleLabel = messageSourceUtil.getMessage("airConditionControl.autoControl.mapTemperature");
        } else if(operateModule == AutoControlGroupModule.MAP_FAN) {
            this.operateModuleLabel = messageSourceUtil.getMessage("airConditionControl.autoControl.mapFan");
        } else if(operateModule == AutoControlGroupModule.MAP_ZONE_STRATEGY) {
            this.operateModuleLabel = messageSourceUtil.getMessage("airConditionControl.autoControl.mapZoneStrategy");
        } else if(operateModule == AutoControlGroupModule.MAP_ENERGY_COMPLEXINDEX) {
            this.operateModuleLabel = messageSourceUtil.getMessage("airConditionControl.autoControl.mapEnergyComplexIndex");
        } else {
            this.operateModuleLabel = "UNKNOW";
        }
    }
    /** 设置 子操作类型 信息 */
    public void setSubOperateTypeInfo(ChangeState subOperateType, LocaleMessageSourceUtil messageSourceUtil) {
        if(subOperateType == null) return;
        this.subOperateType = subOperateType.value();
        if(subOperateType == ChangeState.UPDATE) {
            this.subOperateTypeLabel = messageSourceUtil.getMessage("airconditioncontrol.common.update");
        } else if(subOperateType == ChangeState.NEW) {
            this.subOperateTypeLabel = messageSourceUtil.getMessage("airconditioncontrol.common.new");
        } else if(subOperateType == ChangeState.DELETE) {
            this.subOperateTypeLabel = messageSourceUtil.getMessage("airconditioncontrol.common.delete");
        } else {
            this.subOperateTypeLabel = "UNKNOWN";
        }
    }
    /** 设置 子操作对象类型 信息 */
    public void setSubObjectTypeInfo(ZoneStrategyEntityType subObjectType, LocaleMessageSourceUtil messageSourceUtil) {
        if(subObjectType == null) return;
        this.subObjectType = subObjectType.value();
        if(subObjectType == ZoneStrategyEntityType.SCHEME) {
            this.subObjectTypeLabel = messageSourceUtil.getMessage("airConditionControl.autoControl.mapZoneStrategy.scheme");
        } else if(subObjectType == ZoneStrategyEntityType.OPERATION) {
            this.subObjectTypeLabel = messageSourceUtil.getMessage("airConditionControl.autoControl.mapZoneStrategy.operation");
        } else {
            this.subObjectTypeLabel = "UNKNOW";
        }
    }

    /**
     * 拼接并赋值 changeContent 字段
     * @return 1-相关数据有变更且赋值changeContent成功； 0-相关数据无变更； -1-数据有误，无法正确生成日志记录
     */
    public int spliceAndSetChangeContent() {

        if(operateType == null || operateModule == null) return -1;
        StringBuffer tmp = new StringBuffer("[");
        tmp.append(ChangeState.valueOf(operateType)).append("] ")
                .append(StringUtils.isNotBlank(groupNameNew) ? groupNameNew : groupName)
                .append(" | ").append(AutoControlGroupModule.valueOf(operateModule))
                .append("  \r\nDetail：  \r\n");
        if(operateType.equals(ChangeState.UPDATE.value())) {
            return dealUpdate();
        } else if(operateType.equals(ChangeState.NEW.value())) {
            if(Objects.equals(operateModule, AutoControlGroupModule.GROUP.value())) {//只有分组本身有创建，其他包括定时策略都属于修改
                tmp.append("    Name:").append(groupNameNew).append("\r\n").append("    airconEquipIds:").append(airconEquipIdsNew);
                changeContent = tmp.toString();
                return 1;
            }
        } else if(operateType.equals(ChangeState.DELETE.value())) {
            if(Objects.equals(operateModule, AutoControlGroupModule.GROUP.value())) {//只有分组本身有删除，其他包括定时策略都属于修改
                tmp.append("    Name:").append(groupName).append("\r\n").append("    airconEquipIds:").append(airconEquipIds);
                changeContent = tmp.toString();
                return 1;
            }
        }
        return -1;
    }

    /**
     * 统一处理更新的操作记录
     * @return 与spliceAndSetChangeContent()返回值意义相同
     */
    private int dealUpdate() {
        StringBuffer tmp = new StringBuffer("[");
        tmp.append(ChangeState.valueOf(operateType)).append("] ")
                .append(StringUtils.isNotBlank(groupNameNew) ? groupNameNew : groupName)
                .append(" | ").append(AutoControlGroupModule.valueOf(operateModule))
                .append("  \r\nDetail：  \r\n");
        if(operateModule.equals(AutoControlGroupModule.GROUP.value())) {

            String changeFieldStr = "";
            if(groupNameNew != null && !Objects.equals(groupName, groupNameNew)) {
                changeFieldStr += spliceOneChangeStr("GroupName", groupName, groupNameNew, true);
            }
            if(airconEquipIdsNew != null && !Objects.equals(airconEquipIds, airconEquipIdsNew)) {
                changeFieldStr += spliceOneChangeStr("AirconEquipIds", airconEquipIds, airconEquipIdsNew, false);
            }
            if(StringUtils.isBlank(changeFieldStr)) {
                return 0;
            } else {
                tmp.append(changeFieldStr);
                changeContent = tmp.toString();
                return 1;
            }
        } else if(operateModule.equals(AutoControlGroupModule.BASIC_PARAM.value())) {

            if(groupParams == null || groupParamsNew == null) return -1;
            List<Triplet<String, String, String>> diffFields = diffAirGroupParams(groupParams, groupParamsNew);
            return setChangeFieldInfo(diffFields, tmp);
        } else if(operateModule.equals(AutoControlGroupModule.MAP_TEMPERATURE.value())) {
            if(Objects.equals(tempEquipIds, tempEquipIdsNew)) {
                return 0;
            } else {
                String innerOutFlag = Objects.equals(CommonUtils.OUT_TEMP_FLAG, tempType) ? "OutTemp" : "InnerTemp";
                tmp.append(spliceOneChangeStr("TemperatureEquipIds("+innerOutFlag+")", tempEquipIds, tempEquipIdsNew, false));
                changeContent = tmp.toString();
                return 1;
            }
        } else if(operateModule.equals(AutoControlGroupModule.MAP_FAN.value())) {
            if(Objects.equals(fanEquipIds, fanEquipIdsNew)) {
                return 0;
            } else {
                tmp.append(spliceOneChangeStr("FanEquipIds", fanEquipIds, fanEquipIdsNew, false));
                changeContent = tmp.toString();
                return 1;
            }
        } else if(operateModule.equals(AutoControlGroupModule.MAP_ZONE_STRATEGY.value())) {
            if(subObjectType == null || subOperateType == null)  return -1;
            tmp = new StringBuffer("["); //定时策略的信息头与其他不一致，需要重新new一个
            tmp.append(ChangeState.valueOf(operateType)).append("] ")
                    .append(StringUtils.isNotBlank(groupNameNew) ? groupNameNew : groupName)
                    .append(" | ").append(AutoControlGroupModule.MAP_ZONE_STRATEGY)
                    .append(" | [").append(ChangeState.valueOf(subOperateType)).append(" ")
                    .append(ZoneStrategyEntityType.valueOf(subObjectType)).append("] ");
            tmp.append(spliceSchemeInfo());
            spliceOperationInfo(tmp);
            tmp.append("  \r\nDetail：  \r\n");
            if(Objects.equals(subOperateType, ChangeState.UPDATE.value())) {
                if(Objects.equals(subObjectType, ZoneStrategyEntityType.SCHEME.value())) {
                    List<Triplet<String, String, String>> diffFields = diffScheme(mapZoneScheme, mapZoneSchemeNew);
                    return setChangeFieldInfo(diffFields, tmp);
                } else if(Objects.equals(subObjectType, ZoneStrategyEntityType.OPERATION.value())) {
                    List<Triplet<String, String, String>> diffFields = diffOperation(mapZoneOperation, mapZoneOperationNew);
                    return setChangeFieldInfo(diffFields, tmp);
                } else {
                    return -1;
                }
            } else if(Objects.equals(subOperateType, ChangeState.NEW.value())) {//新增时，mapZoneScheme, mapZoneSchemeNew也都不能为null，以id是否为null作判断吧，Operation同理
                if(Objects.equals(subObjectType, ZoneStrategyEntityType.SCHEME.value())) {
                    List<Triplet<String, String, String>> diffFields = diffScheme(mapZoneScheme, mapZoneSchemeNew);
                    return setChangeFieldInfo(diffFields, tmp);
                } else if(Objects.equals(subObjectType, ZoneStrategyEntityType.OPERATION.value())) {
                    List<Triplet<String, String, String>> diffFields = diffOperation(mapZoneOperation, mapZoneOperationNew);
                    return setChangeFieldInfo(diffFields, tmp);
                } else {
                    return -1;
                }
            } else if(Objects.equals(subOperateType, ChangeState.DELETE.value())) {
                if(Objects.equals(subObjectType, ZoneStrategyEntityType.SCHEME.value())) {
                    if(mapZoneScheme == null || mapZoneScheme.getSchemeId() == null) {
                        return -1;
                    } else {
                        tmp.append("    SchemeInfo：").append(mapZoneScheme.toText()); //此策略下的所有操作序列也会被一并删除，日志中暂不记录
                        changeContent = tmp.toString();
                        return 1;
                    }
                } else if(Objects.equals(subObjectType, ZoneStrategyEntityType.OPERATION.value())) {
                    if(mapZoneOperation == null || mapZoneOperation.getOperationId() == null) {
                        return -1;
                    } else {
                        tmp.append("    OperationInfo：").append(mapZoneOperation.toText());
                        changeContent = tmp.toString();
                        return 1;
                    }
                } else {
                    return -1;
                }
            } else {
                return -1;
            }
        } else if(operateModule.equals(AutoControlGroupModule.MAP_ENERGY_COMPLEXINDEX.value())) {

            if(complexIndexInfo == null || complexIndexInfoNew == null) return -1;
            String oldStr = complexIndexInfo.toText();
            String newStr = complexIndexInfoNew.toText();
            if(Objects.equals(oldStr, newStr)) {
                return 0;
            } else {
                tmp.append(spliceOneChangeStr("MapComplexIndex", oldStr, newStr, false));
                changeContent = tmp.toString();
                return 1;
            }
        } else {
            return -1;
        }
    }

    /** 在操作模块是 定时策略 时，获取定时策略的信息 */
    private String spliceSchemeInfo() {
        String result = "Exception";
        if(operateModule.equals(AutoControlGroupModule.MAP_ZONE_STRATEGY.value())) {
            if(subObjectType != null && subOperateType != null) {
                if(Objects.equals(subObjectType, ZoneStrategyEntityType.SCHEME.value())) {
                    if(Objects.equals(subOperateType, ChangeState.UPDATE.value()) || Objects.equals(subOperateType, ChangeState.NEW.value())) {
                        result = mapZoneSchemeNew.getSchemeName() + "(" + mapZoneSchemeNew.getSchemeId() + ")";
                    } else if(Objects.equals(subOperateType, ChangeState.DELETE.value())) {
                        result = mapZoneScheme.getSchemeName() + "(" + mapZoneScheme.getSchemeId() + ")";
                    }
                } else if(Objects.equals(subObjectType, ZoneStrategyEntityType.OPERATION.value())) {
                    if(Objects.equals(subOperateType, ChangeState.UPDATE.value()) || Objects.equals(subOperateType, ChangeState.NEW.value())) {
                        result = mapZoneOperationNew.getSchemeName() + "(" + mapZoneOperationNew.getSchemeId() + ")";
                    } else if(Objects.equals(subOperateType, ChangeState.DELETE.value())) {
                        result = mapZoneOperation.getSchemeName() + "(" + mapZoneOperation.getSchemeId() + ")";
                    }
                }
            }
        }
        return result;
    }
    /** 在操作模块是 定时策略 时，尝试获取操作序列的信息 */
    private void spliceOperationInfo(StringBuffer sb) {
        if(operateModule.equals(AutoControlGroupModule.MAP_ZONE_STRATEGY.value())) {
            if(subObjectType != null && subOperateType != null) {
                if(Objects.equals(subObjectType, ZoneStrategyEntityType.OPERATION.value())) {
                    if(Objects.equals(subOperateType, ChangeState.UPDATE.value()) || Objects.equals(subOperateType, ChangeState.NEW.value())) {
                        sb.append(" | ").append(mapZoneOperationNew.getOperationCmdName()).append("(")
                                .append(mapZoneOperationNew.getOperationId() == null ? "\"\"" : mapZoneOperationNew.getOperationId()).append(")");
                    } else if(Objects.equals(subOperateType, ChangeState.DELETE.value())) {
                        sb.append(" | ").append(mapZoneOperation.getOperationCmdName()).append("(")
                                .append(mapZoneOperation.getOperationId() == null ? "\"\"" : mapZoneOperation.getOperationId()).append(")");
                    }
                }
            }
        }
    }

    /** 通过字段差异信息集合为 changeContent 字段赋值 */
    private int setChangeFieldInfo(List<Triplet<String, String, String>> diffFields, StringBuffer infoSb) {

        if(diffFields.size() < 1) {
            return 0;
        } else {
            for (Triplet<String, String, String> item : diffFields) {
                infoSb.append(spliceOneChangeStr(item.getValue0(), item.getValue1(), item.getValue2(), true));
            }
            changeContent = infoSb.toString();
            return 1;
        }
    }

    /** 比较新旧基础参数的差异字段 */
    private List<Triplet<String, String, String>> diffAirGroupParams(AirGroupParams groupParams, AirGroupParams groupParamsNew) {
        ArrayList<Triplet<String, String, String>> diffFields = new ArrayList<>();
        if(!Objects.equals(groupParams.getParaEnable(), groupParamsNew.getParaEnable())) {
            diffFields.add(new Triplet<>("ParaEnable", groupParams.getParaEnable() == null ? null : groupParams.getParaEnable().toString()
                                , groupParamsNew.getParaEnable() == null ? null : groupParamsNew.getParaEnable().toString()));
        }
        if(!Objects.equals(groupParams.getTempCoolAll(), groupParamsNew.getTempCoolAll())) {
            diffFields.add(new Triplet<>("TempCoolAll", IniFileUtils.convertDouble(groupParams.getTempCoolAll())
                    , IniFileUtils.convertDouble(groupParamsNew.getTempCoolAll())));
        }
        if(!Objects.equals(groupParams.getTempCoolStart(), groupParamsNew.getTempCoolStart())) {
            diffFields.add(new Triplet<>("TempCoolStart", IniFileUtils.convertDouble(groupParams.getTempCoolStart())
                    , IniFileUtils.convertDouble(groupParamsNew.getTempCoolStart())));
        }
        if(!Objects.equals(groupParams.getTempDiff(), groupParamsNew.getTempDiff())) {
            diffFields.add(new Triplet<>("TempDiff", IniFileUtils.convertDouble(groupParams.getTempDiff())
                    , IniFileUtils.convertDouble(groupParamsNew.getTempDiff())));
        }
        if(!Objects.equals(groupParams.getRollingCount(), groupParamsNew.getRollingCount())) {
            diffFields.add(new Triplet<>("RollingCount", groupParams.getRollingCount() == null ? null : groupParams.getRollingCount().toString()
                    , groupParamsNew.getRollingCount() == null ? null : groupParamsNew.getRollingCount().toString()));
        }
        if(!Objects.equals(groupParams.getRunPeriod(), groupParamsNew.getRunPeriod())) {
            diffFields.add(new Triplet<>("RunPeriod", groupParams.getRunPeriod() == null ? null : groupParams.getRunPeriod().toString()
                    , groupParamsNew.getRunPeriod() == null ? null : groupParamsNew.getRunPeriod().toString()));
        }
        if(!Objects.equals(groupParams.getOperationInterval(), groupParamsNew.getOperationInterval())) {
            diffFields.add(new Triplet<>("OperationInterval", groupParams.getOperationInterval() == null ? null : groupParams.getOperationInterval().toString()
                    , groupParamsNew.getOperationInterval() == null ? null : groupParamsNew.getOperationInterval().toString()));
        }
        if(!Objects.equals(groupParams.getFanInstall(), groupParamsNew.getFanInstall())) {
            diffFields.add(new Triplet<>("FanInstall", groupParams.getFanInstall() == null ? null : groupParams.getFanInstall().toString()
                    , groupParamsNew.getFanInstall() == null ? null : groupParamsNew.getFanInstall().toString()));
        }
        if(!Objects.equals(groupParams.getOutInTempDiff(), groupParamsNew.getOutInTempDiff())) {
            diffFields.add(new Triplet<>("OutInTempDiff", IniFileUtils.convertDouble(groupParams.getOutInTempDiff())
                    , IniFileUtils.convertDouble(groupParamsNew.getOutInTempDiff())));
        }
        if(!Objects.equals(groupParams.getTempFanStart(), groupParamsNew.getTempFanStart())) {
            diffFields.add(new Triplet<>("TempFanStart", IniFileUtils.convertDouble(groupParams.getTempFanStart())
                    , IniFileUtils.convertDouble(groupParamsNew.getTempFanStart())));
        }
        if(!Objects.equals(groupParams.getTempInFanStop(), groupParamsNew.getTempInFanStop())) {
            diffFields.add(new Triplet<>("TempInFanStop", IniFileUtils.convertDouble(groupParams.getTempInFanStop())
                    , IniFileUtils.convertDouble(groupParamsNew.getTempInFanStop())));
        }
        if(!Objects.equals(groupParams.getTempOutFanStop(), groupParamsNew.getTempOutFanStop())) {
            diffFields.add(new Triplet<>("TempOutFanStop", IniFileUtils.convertDouble(groupParams.getTempOutFanStop())
                    , IniFileUtils.convertDouble(groupParamsNew.getTempOutFanStop())));
        }
        if(!Objects.equals(groupParams.getEnableWarm(), groupParamsNew.getEnableWarm())) {
            diffFields.add(new Triplet<>("EnableWarm", groupParams.getEnableWarm() == null ? null : groupParams.getEnableWarm().toString()
                    , groupParamsNew.getEnableWarm() == null ? null : groupParamsNew.getEnableWarm().toString()));
        }
        if(!Objects.equals(groupParams.getTempHotAll(), groupParamsNew.getTempHotAll())) {
            diffFields.add(new Triplet<>("TempHotAll", IniFileUtils.convertDouble(groupParams.getTempHotAll())
                    , IniFileUtils.convertDouble(groupParamsNew.getTempHotAll())));
        }
        if(!Objects.equals(groupParams.getTempHotStart(), groupParamsNew.getTempHotStart())) {
            diffFields.add(new Triplet<>("TempHotStart", IniFileUtils.convertDouble(groupParams.getTempHotStart())
                    , IniFileUtils.convertDouble(groupParamsNew.getTempHotStart())));
        }
        return diffFields;
    }

    /** 比较操作序列对象的差异字段 */
    private List<Triplet<String, String, String>> diffOperation(ZoneOperation mapZoneOperation, ZoneOperation mapZoneOperationNew) {
        ArrayList<Triplet<String, String, String>> diffFields = new ArrayList<>();
        if(!Objects.equals(mapZoneOperation.getOperationCmdId(), mapZoneOperationNew.getOperationCmdId())) {
            diffFields.add(new Triplet<>("OperationCmdId", mapZoneOperation.getOperationCmdId() == null ? null : mapZoneOperation.getOperationCmdId().toString()
                    , mapZoneOperationNew.getOperationCmdId() == null ? null : mapZoneOperationNew.getOperationCmdId().toString()));
        }
        if(!Objects.equals(mapZoneOperation.getOperationCmdName(), mapZoneOperationNew.getOperationCmdName())) {
            diffFields.add(new Triplet<>("OperationCmdName", mapZoneOperation.getOperationCmdName(), mapZoneOperationNew.getOperationCmdName()));
        }
        if(!Objects.equals(mapZoneOperation.getParams(), mapZoneOperationNew.getParams())) {
            diffFields.add(new Triplet<>("Params", mapZoneOperation.getParams(), mapZoneOperationNew.getParams()));
        }
        if(!Objects.equals(mapZoneOperation.getOperationTime(), mapZoneOperationNew.getOperationTime())) {
            diffFields.add(new Triplet<>("OperationTime", mapZoneOperation.getOperationTime(), mapZoneOperationNew.getOperationTime()));
        }
        return diffFields;
    }
    /** 比较定时策略对象的差异字段 */
    private List<Triplet<String, String, String>> diffScheme(ZoneScheme mapZoneScheme, ZoneScheme mapZoneSchemeNew) {
        ArrayList<Triplet<String, String, String>> diffFields = new ArrayList<>();
        if(!Objects.equals(mapZoneScheme.getSchemeName(), mapZoneSchemeNew.getSchemeName())) {
            diffFields.add(new Triplet<>("SchemeName", mapZoneScheme.getSchemeName(), mapZoneSchemeNew.getSchemeName()));
        }
        if(!Objects.equals(mapZoneScheme.getStartDate(), mapZoneSchemeNew.getStartDate())) {
            diffFields.add(new Triplet<>("StartDate", mapZoneScheme.getStartDate(), mapZoneSchemeNew.getStartDate()));
        }
        if(!Objects.equals(mapZoneScheme.getEndDate(), mapZoneSchemeNew.getEndDate())) {
            diffFields.add(new Triplet<>("EndDate", mapZoneScheme.getEndDate(), mapZoneSchemeNew.getEndDate()));
        }
        return diffFields;
    }
    /**
     * 拼接字段变化描述字符串
     * @param fieldKey 更新的字段名
     * @param oldValue 旧值
     * @param newValue 更改后的新值
     * @param isSingleLine 是否单行显示
     * @return 拼接完成后的字符串
     */
    private String spliceOneChangeStr(String fieldKey, String oldValue, String newValue, boolean isSingleLine) {
        StringBuilder tmp = new StringBuilder("");
        String indent1 = "  ";
        String indent2 = "    ";
        String indent3 = "      ";
        if(isSingleLine) {
            tmp.append(indent1).append(fieldKey).append(": change from ").append(CommonUtils.addDoubleQuotes(oldValue)).append(" TO ")
                    .append(CommonUtils.addDoubleQuotes(newValue)).append(" \r\n");
        } else {
            tmp.append(indent1).append(fieldKey).append(": change from \r\n")
                    .append(indent3).append(CommonUtils.addDoubleQuotes(oldValue)).append("\r\n")
                    .append(indent2).append("TO\r\n")
                    .append(indent3).append(CommonUtils.addDoubleQuotes(newValue)).append(" \r\n");
        }
        return tmp.toString();
    }

}
