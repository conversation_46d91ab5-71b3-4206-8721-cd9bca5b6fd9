package com.siteweb.airconditioncontrol.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.siteweb.admin.entity.RegionMap;
import com.siteweb.admin.mapper.AccountMapper;
import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.admin.service.RegionMapService;
import com.siteweb.admin.service.RegionService;
import com.siteweb.airconditioncontrol.dto.*;
import com.siteweb.airconditioncontrol.entity.AirBatchControlEquipmentMap;
import com.siteweb.airconditioncontrol.entity.AirBatchControlGroup;
import com.siteweb.airconditioncontrol.entity.AirBatchControlGroupChangeLog;
import com.siteweb.airconditioncontrol.entity.AirBatchControlRecord;
import com.siteweb.airconditioncontrol.enumeration.AirStdType;
import com.siteweb.airconditioncontrol.enumeration.BatchControlType;
import com.siteweb.airconditioncontrol.enumeration.ChangeState;
import com.siteweb.airconditioncontrol.function.CommonUtils;
import com.siteweb.airconditioncontrol.function.IniFileUtils;
import com.siteweb.airconditioncontrol.mapper.AirBatchControlGroupChangeLogMapper;
import com.siteweb.airconditioncontrol.mapper.AirBatchControlMapper;
import com.siteweb.airconditioncontrol.mapper.AirBatchControlRecordMapper;
import com.siteweb.airconditioncontrol.mapper.AirGroupViewMapper;
import com.siteweb.airconditioncontrol.model.AirConditionBase;
import com.siteweb.airconditioncontrol.service.AirBatchControlService;
import com.siteweb.airconditioncontrol.service.AirEquipTemplateService;
import com.siteweb.airconditioncontrol.service.AirVirtualEquipManageService;
import com.siteweb.airconditioncontrol.service.BasicDataService;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.common.util.LocaleMessageSourceUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.monitoring.dto.EquipmentDTO;
import com.siteweb.monitoring.dto.ResourceStructureEquipmentTreeDTO;
import com.siteweb.monitoring.entity.*;
import com.siteweb.monitoring.enumeration.ControlResultType;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.EquipmentStateManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.mamager.StationManager;
import com.siteweb.monitoring.service.ActiveControlService;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.monitoring.vo.ControlCommandVO;
import lombok.extern.slf4j.Slf4j;
import org.javatuples.Quintet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Service
@Transactional
@Slf4j
public class AirBatchControlServiceImpl implements AirBatchControlService {

    /** 缓存空调类设备的基类id */
    private static List<Integer> airconEquipmentBaseTypes = new ArrayList<>();
    private static List<Integer> tempEquipmentBaseTypes = List.of(1004,1006,1009);
    private static List<Integer> fanEquipmentBaseTypes = List.of(801);

    /** 缓存手动群控分组及其关联空调设备集合 */
    private static ConcurrentHashMap<String, AirBatchControlGroupInfo> allAirBatchControlGroupMap = new ConcurrentHashMap<>();

    @Autowired
    private BasicDataService basicDataService;
    @Autowired
    private EquipmentService equipmentService;
    @Autowired
    private EquipmentManager equipmentManager;
    @Autowired
    private AirVirtualEquipManageService airVirtualEquipManageService;
    @Autowired
    private AirEquipTemplateService airEquipTemplateService;
    @Autowired
    private ActiveControlService activeControlService;

    @Autowired
    private AirBatchControlMapper airBatchControlMapper;
    @Autowired
    private AirBatchControlRecordMapper airBatchControlRecordMapper;
    @Autowired
    private AirBatchControlGroupChangeLogMapper airBatchControlGroupChangeLogMapper;
    @Autowired
    private AccountMapper accountMapper;

    @Autowired
    private EquipmentStateManager equipmentStateManager;
    @Autowired
    private LocaleMessageSourceUtil messageSourceUtil;
    @Autowired
    private RegionService regionService;
    @Autowired
    private RegionMapService regionMapService;
    @Autowired
    ResourceStructureManager resourceStructureManager;
    @Autowired
    private ResourceStructureService resourceStructureService;
    @Autowired
    private StationManager stationManager;
    @Autowired
    private AirGroupViewMapper airGroupViewMapper;

    @PostConstruct
    public void init() {
        log.info("Begin load AirconEquipmentBaseType To Cache...");
        try {
            airconEquipmentBaseTypes = airBatchControlMapper.findAirconEquipmentBaseIds();
        } catch (Exception ex) {
            log.error("Load AirconEquipmentBaseType throw Exception: ", ex);
            airconEquipmentBaseTypes = new ArrayList<>();
        }
        log.info("Load AirconEquipmentBaseType To Cache Completed：maps count=" + (airconEquipmentBaseTypes == null ? -1 : airconEquipmentBaseTypes.size()));

        log.info("Begin load AirBatchControlGroupInfo To Cache...");
        cacheLoadAllAirBatchControlGroupInfo("init");
        log.info("Load AirBatchControlGroupInfo To Cache Completed：maps count=" + (allAirBatchControlGroupMap == null ? -1 : allAirBatchControlGroupMap.size()));
    }

    @Override
    public void reloadCacheFanEquipInfo() {
        log.info("Begin reload airBatchControlGroupInfo To Cache...");
        cacheLoadAllAirBatchControlGroupInfo("reload");
        log.info("Reload airBatchControlGroupInfo To Cache Completed：maps count=" + (allAirBatchControlGroupMap == null ? -1 : allAirBatchControlGroupMap.size()));
    }

    @Override
    public ConcurrentHashMap<String, AirBatchControlGroupInfo> getAllAirBatchControlGroupInfo() {
        return allAirBatchControlGroupMap;
    }

    private void cacheLoadAllAirBatchControlGroupInfo(String flag) {
        try {
            log.info("call cacheLoadAllAirBatchControlGroupInfo, flag:" + flag);
            cacheClearAll();
            List<AirBatchControlGroupInfo> list = airBatchControlMapper.findBatchControlGroupInfo(null);
            if(list != null && list.size() > 0) {
                for (AirBatchControlGroupInfo oneGroupInfo : list) {
                    allAirBatchControlGroupMap.put(oneGroupInfo.getGroupId(), oneGroupInfo);
                }
            }
        } catch(Exception ex) {
            cacheClearAll();
            log.error("Load airBatchControlGroupInfo To Cache [cacheLoadAllAirBatchControlGroupInfo()] Exception：", ex);
        }
    }

    @Override
    public void cacheUpdate(String groupId) {
        if(groupId == null) {
            log.warn("cacheUpdate: groupId is null");
            return;
        }
        List<AirBatchControlGroupInfo> list = airBatchControlMapper.findBatchControlGroupInfo(groupId);
        if(list == null || list.size() < 1) {
            allAirBatchControlGroupMap.remove(groupId);
        } else {
            if(list.size() > 1) {
                log.error("groupId=" + groupId + " 的手动分组 size>1 (" + list.size() + "), use first.");
            }
            AirBatchControlGroupInfo item0 = list.get(0);
            allAirBatchControlGroupMap.put(item0.getGroupId(), item0);
        }
    }

    /** 清空某一虚拟设备的缓存信息 */
    @Override
    public void cacheClear(String groupId) {
        if(groupId == null) {
            log.warn("cacheClear(BatchControlGroup): groupId is null");
            return;
        }
        allAirBatchControlGroupMap.remove(groupId);
    }

    @Override
    public StructureTreeNode getStationAirconEquipTree(Integer loginUserId) {
        return getStationEquipmentTree(loginUserId, airconEquipmentBaseTypes);
    }
    @Override
    public ResourceStructureEquipmentTreeDTO getStationAirConEquipResTree(Integer loginUserId) {
        return getResourceEquipTree(loginUserId, new HashSet<>(airconEquipmentBaseTypes));
    }

    @Override
    public StructureTreeNode getStationTempEquipTree(Integer loginUserId) {
        return getStationEquipmentTree(loginUserId, tempEquipmentBaseTypes);
    }
    @Override
    public ResourceStructureEquipmentTreeDTO getStationTempEquipResTree(Integer loginUserId) {
        return getResourceEquipTree(loginUserId, new HashSet<>(tempEquipmentBaseTypes));
    }

    @Override
    public StructureTreeNode getStationFanEquipTree(Integer loginUserId) {
        return getStationEquipmentTree(loginUserId, fanEquipmentBaseTypes);
    }
    @Override
    public ResourceStructureEquipmentTreeDTO getStationFanEquipResTree(Integer loginUserId) {
        return getResourceEquipTree(loginUserId,  new HashSet<>(fanEquipmentBaseTypes));
    }

    private void cacheClearAll() {
        allAirBatchControlGroupMap = new ConcurrentHashMap<>();
    }

    public StructureTreeNode getStationEquipmentTree(Integer loginUserId, List<Integer> equipmentBaseTypes) {

        HashMap<String, StructureTreeNode> allMonitorUnitMap = new HashMap<>();
        List<StructureTreeNode> allStationMonitorUnitNodeList = new ArrayList<>();
        StructureTreeNode rootNode = basicDataService.getStructureTree(allMonitorUnitMap, allStationMonitorUnitNodeList);
        if(allStationMonitorUnitNodeList.size() < 1) {
            log.info("stationList.size() < 1");
            return rootNode;
        }
        if(allMonitorUnitMap.size() < 1) {
            log.info("allMonitorUnitMap.size() < 1");
            return rootNode;
        }
        List<EquipmentDTO> equipmentDTOs = equipmentService.findEquipmentDTOsByUserId(loginUserId);
        if(equipmentDTOs != null && equipmentDTOs.size() > 0) {
            List<Integer> airMngEquipTemplateIdList = null; //虚拟设备模板id
            for (EquipmentDTO equipmentDTO : equipmentDTOs) {
                if(equipmentDTO != null && equipmentDTO.getEqCategory() != null && equipmentBaseTypes.contains(equipmentDTO.getEqCategory())) {//根据传入设备类型进行筛选
                    if(airMngEquipTemplateIdList == null) {
                        airMngEquipTemplateIdList = new ArrayList<>();
                        List<EquipmentTemplate> airMngEquipTemplateList = airVirtualEquipManageService.getAirMngEquipTemplateList();
                        if(airMngEquipTemplateList == null || airMngEquipTemplateList.size() < 1) {
                            log.warn("do not find airMngEquipTemplate");
                        } else {
                            for (EquipmentTemplate equipmentTemplate : airMngEquipTemplateList) {
                                airMngEquipTemplateIdList.add(equipmentTemplate.getEquipmentTemplateId());
                            }
                        }
                    }
                    Equipment equipment = equipmentManager.getEquipmentById(equipmentDTO.getEqId());
                    if(equipment == null) {
                        log.warn("Equipment not find from cache, equipmentId = " + equipmentDTO.getEqId());
                    } else {
                        Integer monitorUnitId = equipment.getMonitorUnitId();
                        Integer stationId = equipment.getStationId();
                        if(stationId == null || monitorUnitId == null) {
                            log.warn("stationId or monitorUnitId is null, equipmentId = " + equipmentDTO.getEqId());
                        } else {
                            Integer equipmentTemplateId = equipment.getEquipmentTemplateId();
                            if(equipmentTemplateId == null) {
                                log.warn("do not find equipmentTemplateId");
                                continue;
                            }
                            if(airMngEquipTemplateIdList.contains(equipmentTemplateId)) {//空调虚拟设备不显示
                                continue;
                            }
                            String monitorKey = stationId + "." + monitorUnitId;
                            StructureTreeNode parentMonitorUnitNode = allMonitorUnitMap.get(monitorKey);
                            if(parentMonitorUnitNode != null) {//只有智能采集器下的设备才需要挂
                                List<StructureTreeNode> children = parentMonitorUnitNode.getChildren();
                                if(children == null) {
                                    parentMonitorUnitNode.setChildren(new ArrayList<>());
                                    children = parentMonitorUnitNode.getChildren();
                                }
                                //创建一个设备节点对象
                                StructureTreeNode eqNode = new StructureTreeNode();
                                eqNode.setNodeId(equipment.getEquipmentId());
                                eqNode.setStationId(equipment.getStationId());
                                eqNode.setNodeType(5);
                                eqNode.setParentNodeId(parentMonitorUnitNode.getNodeId());
                                eqNode.setParentNodeType(parentMonitorUnitNode.getNodeType());
                                eqNode.setNodeName(equipment.getEquipmentName());
                                eqNode.setOnlineState(equipmentDTO.getOnlineState().value());
                                eqNode.setEquipmentTemplateId(equipmentTemplateId);
                                children.add(eqNode);
                            }
                        }
                    }
                }
            }
        }
        //去除没有空调设备的监控单元节点
        for (StructureTreeNode oneStation : allStationMonitorUnitNodeList) {
            List<StructureTreeNode> monitorUnitChildrenOld = oneStation.getChildren();
            List<StructureTreeNode> monitorUnitChildrenNew = new ArrayList<>();
            for (StructureTreeNode oneMonitorUnit : monitorUnitChildrenOld) {
                if(oneMonitorUnit.getChildren().size() > 0) {
                    monitorUnitChildrenNew.add(oneMonitorUnit);
                }
            }
            if(monitorUnitChildrenOld.size() > monitorUnitChildrenNew.size()) {
                if(monitorUnitChildrenNew.size() > 0) {
                    oneStation.setChildren(monitorUnitChildrenNew);
                } else {
                    oneStation.setChildren(new ArrayList<>());
                }
            }
        }
        return rootNode;
    }

    @Override
    public ResponseEntity<ResponseResult> addOneAirBatchControlGroup(AirBatchControlGroupInfo batchControlGroupInfo) {

        if(batchControlGroupInfo == null) {
            return ResponseHelper.failed("-10", "Params is null", HttpStatus.OK);
        }
        if(batchControlGroupInfo.getGroupId() != null) {
            return ResponseHelper.failed("-10", "Id is not null", HttpStatus.OK);
        }
        if(StringUtils.isBlank(batchControlGroupInfo.getGroupName())) {
            return ResponseHelper.failed("-10", "Name is blank", HttpStatus.OK);
        } else {
            batchControlGroupInfo.setGroupName(batchControlGroupInfo.getGroupName().trim());
        }
        if(batchControlGroupInfo.getUpdaterId() == null) {
            return ResponseHelper.failed("-10", "UpdaterId is null", HttpStatus.OK);
        }
        if(batchControlGroupInfo.getAirEquipIdList() == null) {
            batchControlGroupInfo.setAirEquipIdList(new ArrayList<>());
        }
        //判断分组名是否重复
        boolean hasSameGroupName = isSameGroupName(batchControlGroupInfo.getGroupName(), null);
        if(hasSameGroupName) {
            return ResponseHelper.failed("-11", "Duplicate Name", HttpStatus.OK);
        }
        //分组所管理的设备的id串，以逗号分隔
        String equipmentIds = null;
        //插入一条分组记录
        Date curDate = CommonUtils.getCurDate();
        AirBatchControlGroup newGroup = new AirBatchControlGroup();
        newGroup.setGroupId(StringUtils.getUUId());
        newGroup.setGroupName(batchControlGroupInfo.getGroupName());
        newGroup.setCreatorId(batchControlGroupInfo.getUpdaterId());
        newGroup.setUpdateId(batchControlGroupInfo.getUpdaterId());
        newGroup.setCreateTime(curDate);
        newGroup.setUpdateTime(curDate);
        newGroup.setDescription(null);
        newGroup.setExtendField(null);
        airBatchControlMapper.insert(newGroup);
        //批量保存分组与空调设备映射关系记录
        if(batchControlGroupInfo.getAirEquipIdList().size() > 0) {
            batchInsertBatchControlEquipmentMap(newGroup.getGroupId(), newGroup.getUpdateId(), curDate, batchControlGroupInfo.getAirEquipIdList());
            equipmentIds = batchControlGroupInfo.getAirEquipIdList().stream().sorted().collect(Collectors.joining(","));
        }
        //记录日志
        AirBatchControlGroupChangeLog changeLog = new AirBatchControlGroupChangeLog();
        changeLog.setOperateTypeInfo(ChangeState.NEW.value(), messageSourceUtil);
        changeLog.setGroupId(newGroup.getGroupId());
        changeLog.setGroupNameNew(newGroup.getGroupName());
        changeLog.setEquipmentIdsNew(equipmentIds);
        changeLog.setOperatorId(TokenUserUtil.getLoginUserId());
        changeLog.setOperator(CommonUtils.getLoginUserName(accountMapper, TokenUserUtil.getLoginUserId()));
        changeLog.setInsertTime(curDate);
        changeLog.spliceAndSetChangeContent();
        airBatchControlGroupChangeLogMapper.insert(changeLog);

        //更新缓存
        cacheUpdate(newGroup.getGroupId());
        return ResponseHelper.successful("SUCCESS");
    }

    @Override
    public ResponseEntity<ResponseResult> deleteOneAirBatchControlGroup(String groupId) {
        AirBatchControlGroupInfo groupDb = airBatchControlMapper.findGroupInfoById(groupId);
        if(groupDb == null) {
            return ResponseHelper.failed("-10", "Not exists.", HttpStatus.OK);
        }
        //构建日志对象
        AirBatchControlGroupChangeLog changeLog = new AirBatchControlGroupChangeLog();
        changeLog.setOperateTypeInfo(ChangeState.DELETE.value(), messageSourceUtil);
        changeLog.setOperator(CommonUtils.getLoginUserName(accountMapper, TokenUserUtil.getLoginUserId()));
        changeLog.setOperatorId(TokenUserUtil.getLoginUserId());
        changeLog.setGroupId(groupDb.getGroupId());
        changeLog.setGroupName(groupDb.getGroupName());
        if(groupDb.getAirEquipIdList() != null && groupDb.getAirEquipIdList().size() > 0) {
            String ids = groupDb.getAirEquipIdList().stream().sorted().collect(Collectors.joining(","));
            changeLog.setEquipmentIds(ids);
        }
        changeLog.spliceAndSetChangeContent();

        //删除数据库记录
        airBatchControlMapper.deleteById(groupId);
        airBatchControlMapper.delMapByGroupId(groupId);
        //记录日志
        changeLog.setInsertTime(CommonUtils.getCurDate());
        airBatchControlGroupChangeLogMapper.insert(changeLog);

        //更新缓存
        cacheUpdate(groupId);
        return ResponseHelper.successful("SUCCESS");
    }

    @Override
    public ResponseEntity<ResponseResult> updateOneAirBatchControlGroup(AirBatchControlGroupInfo batchControlGroupInfo) {

        if(batchControlGroupInfo == null) {
            return ResponseHelper.failed("-10", "Params is null", HttpStatus.OK);
        }
        if(batchControlGroupInfo.getGroupId() == null) {
            return ResponseHelper.failed("-10", "Id is null", HttpStatus.OK);
        }
        if(StringUtils.isBlank(batchControlGroupInfo.getGroupName())) {
            return ResponseHelper.failed("-10", "Name is blank", HttpStatus.OK);
        } else {
            batchControlGroupInfo.setGroupName(batchControlGroupInfo.getGroupName().trim());
        }
        if(batchControlGroupInfo.getUpdaterId() == null) {
            return ResponseHelper.failed("-10", "UpdaterId is null", HttpStatus.OK);
        }
        if(batchControlGroupInfo.getAirEquipIdList() == null) {
            batchControlGroupInfo.setAirEquipIdList(new ArrayList<>());
        }
        //校验分组是否存在
        AirBatchControlGroupInfo groupDb = airBatchControlMapper.findGroupInfoById(batchControlGroupInfo.getGroupId());
        if(groupDb == null) {
            return ResponseHelper.failed("-10", "Group not exists", HttpStatus.OK);
        }
        AirBatchControlGroupChangeLog changeLog = new AirBatchControlGroupChangeLog();//变更日志记录对象
        changeLog.setGroupId(batchControlGroupInfo.getGroupId());
        changeLog.setGroupName(groupDb.getGroupName());
        Date curDate = CommonUtils.getCurDate();
        if(!batchControlGroupInfo.getGroupName().equals(groupDb.getGroupName())) {//分组名变化后，需更新
            //判断分组名是否重复
            boolean hasSameGroupName = isSameGroupName(batchControlGroupInfo.getGroupName(), batchControlGroupInfo.getGroupId());
            if(hasSameGroupName) {
                return ResponseHelper.failed("-11", "Duplicate Name", HttpStatus.OK);
            }
            //更新分组名
            airBatchControlMapper.updateGroupNameById(batchControlGroupInfo.getGroupName(), batchControlGroupInfo.getGroupId(), batchControlGroupInfo.getUpdaterId(), curDate);
            changeLog.setOperateTypeInfo(ChangeState.UPDATE.value(), messageSourceUtil);
            changeLog.setGroupNameNew(batchControlGroupInfo.getGroupName());//保存变化后的新分组名
        }
        //更新关联空调映射信息
        String oldIds = "";
        String newIds = "";
        List<String> oldAirEquipList = groupDb.getAirEquipIdList();
        if(oldAirEquipList != null && oldAirEquipList.size() > 0) {
            oldIds = oldAirEquipList.stream().sorted().collect(Collectors.joining(","));
        }
        List<String> newAirEquipList = batchControlGroupInfo.getAirEquipIdList();
        if(newAirEquipList != null && newAirEquipList.size() > 0) {
            newIds = newAirEquipList.stream().sorted().collect(Collectors.joining(","));
        }
        if(!newIds.equals(oldIds)) {
            airBatchControlMapper.delMapByGroupId(batchControlGroupInfo.getGroupId());
            batchInsertBatchControlEquipmentMap(batchControlGroupInfo.getGroupId(), batchControlGroupInfo.getUpdaterId(), curDate, batchControlGroupInfo.getAirEquipIdList());
            changeLog.setOperateTypeInfo(ChangeState.UPDATE.value(), messageSourceUtil);
            changeLog.setEquipmentIds(oldIds);
            changeLog.setEquipmentIdsNew(newIds);
        }
        //insert changelog
        if(changeLog.getOperateType() != null) {
            changeLog.setOperator(CommonUtils.getLoginUserName(accountMapper, TokenUserUtil.getLoginUserId()));
            changeLog.setOperatorId(TokenUserUtil.getLoginUserId());
            changeLog.setInsertTime(curDate);
            changeLog.spliceAndSetChangeContent();
            airBatchControlGroupChangeLogMapper.insert(changeLog);
        }
        //Update Cache
        cacheUpdate(batchControlGroupInfo.getGroupId());
        return ResponseHelper.successful("SUCCESS");
    }

    @Override
    public List<EquipSampleTblRow> getAirconListByGroupId(String groupId) {

        List<EquipSampleTblRow> result = new ArrayList<>();
        AirBatchControlGroupInfo oneBatchControlGroupInfo = allAirBatchControlGroupMap.get(groupId);
        if(oneBatchControlGroupInfo != null) {
            result = oneBatchControlGroupInfo.getAirEquipList();
            if(result == null) {
                result = new ArrayList<>();
            } else {
                ConcurrentHashMap<Integer, AirStdTypeMap> allTemplateMapSignalsCacheInfo = airEquipTemplateService.getAllTemplateMapSignalsCacheInfo();
                for (EquipSampleTblRow oneEquipRow : result) {
                    //迭代填充在线状态
                    OnlineState equipmentOnlineState = equipmentStateManager.getEquipmentOnlineStateById(oneEquipRow.getEquipmentId());
                    oneEquipRow.setOnlineState(equipmentOnlineState == null ? -1 : equipmentOnlineState.value());
                    //填充设备所映射的标准类型
                    AirStdTypeMap airStdTypeMap = allTemplateMapSignalsCacheInfo.get(oneEquipRow.getEquipmentTemplateId());
                    if(airStdTypeMap != null && airStdTypeMap.getTypeId() != null) {
                        oneEquipRow.setStdTypeId(airStdTypeMap.getTypeId());
                    }
                }
            }
        }
        return result;
    }

    @Override
    public List<AirBatchControlGroup> getAllBatchControlGroups() {
        return airBatchControlMapper.findAllBatchControlGroups();
    }

    @Override
    public AirBatchControlGroupInfo getGroupInfoByGroupId(String groupId) {
        return airBatchControlMapper.findGroupInfoById(groupId);
    }

    @Override
    public ResponseEntity<ResponseResult> batchControlSend(BatchControlCommand batchControlCommand, Integer userId) {
        if(batchControlCommand == null) {
            return ResponseHelper.failed("-10", "Params is null", HttpStatus.OK);
        }
        if(batchControlCommand.getType() == null) {
            return ResponseHelper.failed("-10", "Type is null", HttpStatus.OK);
        }
        if(batchControlCommand.getEquipmentIdList() == null || batchControlCommand.getEquipmentIdList().size() < 1) {
            log.info("batch control equipment count == 0");
            return ResponseHelper.successful("SUCCESS");
        }

        BatchControlType batchControlType = batchControlCommand.gainEnumType();
        if(batchControlType == BatchControlType.UN_SUPPORT || batchControlType == null) {
            return ResponseHelper.failed("-10", "Unsupported Type: " + batchControlCommand.getType(), HttpStatus.OK);
        }
        if(batchControlType == BatchControlType.SET_TEMPERATURE || batchControlType == BatchControlType.SET_WORK_MODE) {//设置运行温度 或 设置运行模式 需要有参数
            if(batchControlCommand.getParamValue() == null) {
                return ResponseHelper.failed("-10", "paramValue is null", HttpStatus.OK);
            }
        }

        //拿到各类型空调的各种标准化信号的id集合
        HashMap<Integer, HashMap<String, Integer>> stdIdsDic = AirConditionBase.getStdIdsDic();
        HashMap<String, Integer> airCommonStdIdsDic = stdIdsDic.get(AirStdType.AIR_COMMON.value());
        HashMap<String, Integer> airSpecialStdIdsDic = stdIdsDic.get(AirStdType.AIR_SPECIAL.value());
        HashMap<String, Integer> airCommon2StdIdsDic = stdIdsDic.get(AirStdType.AIR_COMMON2.value());
        //所有支持的空调标准类型
        List<Integer> supportEquipTypeList = List.of(AirStdType.AIR_COMMON.value(), AirStdType.AIR_SPECIAL.value(), AirStdType.AIR_COMMON2.value());

        int workModeFlag = -1;//工作模式
        int countTotal = batchControlCommand.getEquipmentIdList().size(); //待下发控制命令总数，一般与设备数相同，但如果有一拖二空调，一个一拖二空调可能会下发两条控制命令
        int countSuccess = 0; //成功下发数
        int countFail = 0; //通过条件校验，真实下发了，且下发失败数
        int countUnMapped = 0; //校验未通过：因未映射标准类型或标准信号未映射
        int countUnSupport = 0; //校验未通过：不支持下发此命令
        List<Quintet<String, String, String, String, String>> resultListSuccess = new ArrayList<>(); //Triplet.get0: 局站id；Triplet.get1: 设备id；Triplet.get2: 空调标准类型id；Triplet.get3: 如果是一拖二，此处保存是主机的序号，只会是1或者2，如果非一拖二，此处保存-1；Triplet.get4: 实际保存到TBL_ActiveControl中记录uuid唯一标识串
        List<String> resultListFail = new ArrayList<>();
        List<String> resultListUnMapped = new ArrayList<>();
        List<String> resultListUnSupport = new ArrayList<>();
        ConcurrentHashMap<Integer, AirStdTypeMap> allTemplateMapSignalsCacheInfo = airEquipTemplateService.getAllTemplateMapSignalsCacheInfo();
        //开始循环下发
        for (String stationEquipmentId : batchControlCommand.getEquipmentIdList()) {
            String[] split = stationEquipmentId.split("\\.");
            Integer stationId = Integer.valueOf(split[0]);
            Integer equipmentId = Integer.valueOf(split[1]);
            Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
            if(equipment == null) {
                log.warn("equipment can not find: " + equipmentId);
                countFail++;
                resultListFail.add(stationEquipmentId);
                continue;
            }
            AirStdTypeMap airStdTypeMap = allTemplateMapSignalsCacheInfo.get(equipment.getEquipmentTemplateId());
            if(airStdTypeMap == null) {//无映射信息
                countUnMapped++;
                resultListUnMapped.add(stationEquipmentId);
                continue;
            }
            AirStdType equipType = AirStdType.valueOf(airStdTypeMap.getTypeId());
            if(equipType == null || !supportEquipTypeList.contains(equipType.value())) {
                log.warn("unsupported equipment type: " + airStdTypeMap.getTypeId());
                countUnSupport++;
                resultListUnSupport.add(stationEquipmentId);
                continue;
            }

            ConcurrentHashMap<Integer, AirStdSignalMap> signalMaps = airStdTypeMap.getSignalMaps();
            signalMaps = signalMaps == null ? new ConcurrentHashMap<>() : signalMaps; //此设备模板的所有已映射标准信号

            //映射的信号
            AirStdSignalMap signalMap = null;//映射的实际信号信息
            AirStdSignalMap signalMap2 = null;//一拖二空调第二个主机映射的实际信号信息
            //构建命令对象，填充共有字段信息
            Date curDate = CommonUtils.getCurDate();
            ControlCommandVO controlCommandVO = new ControlCommandVO();
            controlCommandVO.setStationId(stationId);
            controlCommandVO.setEquipmentId(equipmentId);
            controlCommandVO.setStartTime(curDate);
            if(batchControlType == BatchControlType.TURN_ON) {//远程开机

                if(equipType ==  AirStdType.AIR_COMMON) {
                    signalMap = signalMaps.get(airCommonStdIdsDic.get("SignalCMDTurnOn_StdId"));
                } else if(equipType ==  AirStdType.AIR_COMMON2) {
                    signalMap = signalMaps.get(airCommon2StdIdsDic.get("SignalCMDTurnOn_StdId"));
                    signalMap2 = signalMaps.get(airCommon2StdIdsDic.get("SignalCMDTurnOn2_StdId"));
                } else if(equipType ==  AirStdType.AIR_SPECIAL) {
                    signalMap = signalMaps.get(airSpecialStdIdsDic.get("SignalCMDTurnOn_StdId"));
                }
            }
            if(batchControlType == BatchControlType.TURN_OFF) {//远程关机

                if(equipType ==  AirStdType.AIR_COMMON) {
                    signalMap = signalMaps.get(airCommonStdIdsDic.get("SignalCMDTurnOff_StdId"));
                } else if(equipType ==  AirStdType.AIR_COMMON2) {
                    signalMap = signalMaps.get(airCommon2StdIdsDic.get("SignalCMDTurnOff_StdId"));
                    signalMap2 = signalMaps.get(airCommon2StdIdsDic.get("SignalCMDTurnOff2_StdId"));
                } else if(equipType ==  AirStdType.AIR_SPECIAL) {
                    signalMap = signalMaps.get(airSpecialStdIdsDic.get("SignalCMDTurnOff_StdId"));
                }
            }
            if(batchControlType == BatchControlType.SET_TEMPERATURE) {//设置运行温度

                if(equipType ==  AirStdType.AIR_COMMON) {
                    signalMap = signalMaps.get(airCommonStdIdsDic.get("SignalCMDSetTemperature_StdId"));
                } else if(equipType ==  AirStdType.AIR_COMMON2) {
                    signalMap = signalMaps.get(airCommon2StdIdsDic.get("SignalCMDSetTemperature_StdId"));
                    signalMap2 = signalMaps.get(airCommon2StdIdsDic.get("SignalCMDSetTemperature2_StdId"));
                } else if(equipType ==  AirStdType.AIR_SPECIAL) {
                    signalMap = signalMaps.get(airSpecialStdIdsDic.get("SignalCMDSetTemperature_StdId"));
                }
            }
            if(batchControlType == BatchControlType.SET_WORK_MODE) {//设置运行模式

                String stdIdKey = "";
                String stdIdKey2 = "";
                if(batchControlCommand.getParamValue() == 1d) {//制热模式
                    stdIdKey = "SignalCMDSetHeating_StdId";
                    stdIdKey2 = "SignalCMDSetHeating2_StdId";
                    workModeFlag = 1;
                }
                if(batchControlCommand.getParamValue() == 0d) {//制冷模式
                    stdIdKey = "SignalCMDSetCooling_StdId";
                    stdIdKey2 = "SignalCMDSetCooling2_StdId";
                    workModeFlag = 0;
                }
                if(StringUtils.isBlank(stdIdKey) || StringUtils.isBlank(stdIdKey2)) {
                    log.warn("UnSupport work mode: " + batchControlCommand.getParamValue());
                    countUnSupport++;
                    resultListUnSupport.add(stationEquipmentId);
                    continue;
                }
                if(equipType ==  AirStdType.AIR_COMMON) {
                    signalMap = signalMaps.get(airCommonStdIdsDic.get(stdIdKey));
                } else if(equipType ==  AirStdType.AIR_COMMON2) {
                    signalMap = signalMaps.get(airCommon2StdIdsDic.get(stdIdKey));
                    signalMap2 = signalMaps.get(airCommon2StdIdsDic.get(stdIdKey2));
                } else if(equipType ==  AirStdType.AIR_SPECIAL) {//专用空调不支持设置运行模式
                    countUnSupport++;
                    resultListUnSupport.add(stationEquipmentId);
                    continue;
                }
            }
            if(signalMap == null) {
                countUnMapped++;
                resultListUnMapped.add(stationEquipmentId);
                continue;
            }
            //controlCommandVO.setDescription(CommonUtils.getAirconBatchControlSendDescription(equipType.name(),batchControlType.value(),workModeFlag));
            controlCommandVO.setDescription(StringUtils.getUUId());
            controlCommandVO.setControlId(signalMap.getSwSignalId());
            if(batchControlType == BatchControlType.SET_TEMPERATURE) {
                controlCommandVO.setSetValue(IniFileUtils.convertDouble(batchControlCommand.getParamValue()));
            } else {
                controlCommandVO.setSetValue(signalMap.getSwParam());
            }
            ControlResultType resultType = activeControlService.sendControlCommand(controlCommandVO, userId);
            if(resultType == ControlResultType.SUCCESS) {
                countSuccess++;
                String airCommon2SerialNo = equipType ==  AirStdType.AIR_COMMON2 ? "1" : "-1"; //如果是一拖二空调，这就是它的第一个主机
                resultListSuccess.add(new Quintet<>(stationId.toString(), equipmentId.toString(), equipType.value().toString(), airCommon2SerialNo, controlCommandVO.getDescription()));
            } else {
                countFail++;
                resultListFail.add(stationEquipmentId);
            }
            if(equipType ==  AirStdType.AIR_COMMON2) {
                countTotal++; //一拖二，多出一个空调主机，相当于总下发命令多出一个
                if(signalMap2 == null) {
                    countUnMapped++;
                    resultListUnMapped.add(stationEquipmentId);
                    continue;
                }
                ControlCommandVO controlCommandVO2 = new ControlCommandVO();
                controlCommandVO2.setStationId(stationId);
                controlCommandVO2.setEquipmentId(equipmentId);
                controlCommandVO2.setStartTime(curDate);
                //controlCommandVO2.setDescription(CommonUtils.getAirconBatchControlSendDescription(equipType.name() + "_2",batchControlType.value(),workModeFlag));
                controlCommandVO2.setDescription(StringUtils.getUUId());
                controlCommandVO2.setControlId(signalMap2.getSwSignalId());
                if(batchControlType == BatchControlType.SET_TEMPERATURE) {
                    controlCommandVO2.setSetValue(IniFileUtils.convertDouble(batchControlCommand.getParamValue()));
                } else {
                    controlCommandVO2.setSetValue(signalMap2.getSwParam());
                }
                ControlResultType resultType2 = activeControlService.sendControlCommand(controlCommandVO2, userId);
                if(resultType2 == ControlResultType.SUCCESS) {
                    countSuccess++;
                    String airCommon2SerialNo = "2"; //如果是一拖二空调，这就是它的第二个主机
                    resultListSuccess.add(new Quintet<>(stationId.toString(), equipmentId.toString(), equipType.value().toString(), airCommon2SerialNo, controlCommandVO2.getDescription()));
                } else {
                    countFail++;
                    resultListFail.add(stationEquipmentId);
                }
            }
        }

        if(countTotal != (countSuccess + countFail + countUnMapped + countUnSupport)) {
            log.error("unknow exception: countTotal != countSuccess + countFail + countUnMapped + countUnSupport" +
                    ": countTotal=" + countTotal + "; countSuccess=" + countSuccess + "; countFail=" + countFail +
                    "; countUnMapped=" + countUnMapped + "; countUnSupport=" + countUnSupport);
        }

        if(countSuccess < 1) {//全部失败
            return ResponseHelper.failed("-201", "send fail", HttpStatus.OK);
        }
        List<Object> sendResult = new ArrayList<>();
        if(countFail > 0) {//有下发失败的
            sendResult.add(new Object(){ public Integer getFlag() { return -1;} public String getMsg() { return "Fail";} public List<String> getIds() { return resultListFail; } });
        }
        if(countUnMapped > 0) {//有未映射导致下发失败的情况
            sendResult.add(new Object(){ public Integer getFlag() { return -2;} public String getMsg() { return "UnMapped";} public List<String> getIds() { return resultListUnMapped; } });
        }
        if(countUnSupport > 0) {//不支持的设备类型或不支持的控制命令
            sendResult.add(new Object(){ public Integer getFlag() { return -3;} public String getMsg() { return "UnSupport";} public List<String> getIds() { return resultListUnSupport; } });
        }
        //需要保存此次下发命令信息到空调系统的独立记录表中，主要是保存映射关系
        if(resultListSuccess.size() < 1) {
            log.error("Unknow ERROR: countSuccess >= 1, but resultListSuccess.size() < 1");
        }
        String uuidInStr = resultListSuccess.stream().map(Quintet::getValue4).collect(Collectors.joining("','"));
        uuidInStr = "'" + uuidInStr + "'";
        List<ActiveControl> activeControlList = airBatchControlMapper.findActiveControlsByUUIDs(uuidInStr);
        if(activeControlList == null || activeControlList.size() < 1) {
            log.error("Unknow ERROR: countSuccess >= 1 and resultListSuccess.size() >= 1; but activeControlList is empty, uuidInStr = " + uuidInStr);
            activeControlList = new ArrayList<>();
        }
        List<AirBatchControlRecord> needInserRecordList = new ArrayList<>();
        Date curDate = CommonUtils.getCurDate();
        for (Quintet<String, String, String, String, String> oneSuccess : resultListSuccess) {
            String uuid = oneSuccess.getValue4();
            ActiveControl activeControl = activeControlList.stream().filter(one -> uuid.equals(one.getDescription())).findFirst().orElse(null);
            Integer serialNo = null;
            Integer controlId = null;
            if(activeControl == null) {
                log.error("Unknow ERROR: uuid(" + uuid + ") not find a ActiveControl");
            } else {
                serialNo = activeControl.getSerialNo();
                if(serialNo == null) {
                    log.error("Unknow ERROR: uuid(" + uuid + ") find SerialNo == null");
                }
                controlId = activeControl.getControlId();
            }
            //构造空调批量控制记录对象
            AirBatchControlRecord batchControlRecord = new AirBatchControlRecord();
            batchControlRecord.setRecordId(null);
            batchControlRecord.setStationId(Integer.valueOf(oneSuccess.getValue0()));
            batchControlRecord.setEquipmentId(Integer.valueOf(oneSuccess.getValue1()));
            batchControlRecord.setSerialNo(serialNo);
            batchControlRecord.setControlId(controlId);
            batchControlRecord.setStdControlId(batchControlCommand.getType());
            batchControlRecord.setStdWorkModeFlag(workModeFlag);
            batchControlRecord.setAirStdTypeId(Integer.valueOf(oneSuccess.getValue2()));
            batchControlRecord.setAirCommon2No(Integer.valueOf(oneSuccess.getValue3()));
            batchControlRecord.setUuid(uuid);
            batchControlRecord.setInsertTime(curDate);
            needInserRecordList.add(batchControlRecord);
        }
        if(needInserRecordList.size() > 0) {
            airBatchControlRecordMapper.batchInsert(needInserRecordList);
        }
        return ResponseHelper.successful(sendResult);
    }

    @Override
    public List<AirActiveControlDTO> getControlResultsByGroupId(String groupId) {
        return airBatchControlMapper.findControlResultsByGroupId(groupId);
    }

    @Override
    public BatchControlResultSum batchControlExecResult(String groupId) {
        BatchControlResultSum batchControlResultSum = airBatchControlRecordMapper.calchControlExecResultByGroupId(groupId);
        if(batchControlResultSum != null) {
            if(batchControlResultSum.getTotalSum() != (batchControlResultSum.getSuccessSum() + batchControlResultSum.getFailSum() + batchControlResultSum.getSendingSum())) {
                log.warn("totalSum(" + batchControlResultSum.getTotalSum() + ") != successSum(" + batchControlResultSum.getSuccessSum()
                        + ") + failSum(" + batchControlResultSum.getFailSum() + ") + sendingSum(" + batchControlResultSum.getSendingSum() + ")");
            }
        }
        return batchControlResultSum;
    }

    /** 批量插入手动群控分组与受控空调设备的关联关系记录 */
    private void batchInsertBatchControlEquipmentMap(String groupId, Integer updateId, Date operateTime, List<String> airEquipIdList) {
        ArrayList<AirBatchControlEquipmentMap> maps = new ArrayList<>();
        if(airEquipIdList == null) return;
        for (String stationIdEquipmentIdStr : airEquipIdList) {
            String[] split = stationIdEquipmentIdStr.split("\\."); //格式应该是 stationId.equipmentId
            AirBatchControlEquipmentMap oneMap = new AirBatchControlEquipmentMap();
            oneMap.setGroupId(groupId);
            oneMap.setStationId(Integer.valueOf(split[0]));
            oneMap.setEquipmentId(Integer.valueOf(split[1]));
            oneMap.setUpdateId(updateId);
            oneMap.setUpdateTime(operateTime);
            maps.add(oneMap);
        }
        if(maps.size() > 0) {
            airBatchControlMapper.batchInsert(maps);
        }
    }

    /** 判断当前分组名是否已存在 */
    private boolean isSameGroupName(String groupName, String excludeId) {
        Integer count = airBatchControlMapper.getCountByGroupName(groupName, excludeId);
        return count > 0;
    }

    //新层级设备树
    public ResourceStructureTreeDTO getResourceEquipTree(Integer userId , Set<Integer> equipBaseType) {
        List<ResourceStructureTreeDTO> resourceStructureTreeDtos = this.findResourceStructureTreeDtoByUserId(userId);
        List<AirEquipmentDTO> equipmentDTOs = findEquipmentDTOsByUserId(userId, equipBaseType);
        return  this.buildTree(resourceStructureTreeDtos,equipmentDTOs,true,false);
    }
    @Override
    public ResourceStructureTreeDTO getResourceMonitorOrStationTree(Integer userId,Boolean haveMonitor,Boolean isOverView){
        List<ResourceStructureTreeDTO> resourceStructureTreeDtos = this.findResourceStructureTreeDtoByUserId(userId);
        List<AirEquipmentDTO> monitorUnitEquDto = new ArrayList<>();
        if(haveMonitor){
            List<Equipment> monitorUnits = airBatchControlMapper.findMonitorUnitInfoByEquipment();
            monitorUnitEquDto = monitorUnits.stream()
                    .map(this::convertEquipmentDTO)
                    .toList();
        }

        return  this.buildTree(resourceStructureTreeDtos,monitorUnitEquDto,haveMonitor,isOverView);
    }



    public List<AirEquipmentDTO> findEquipmentDTOsByUserId(Integer userId, Set<Integer> equipBaseType) {
        //拥有所有区域的权限 获取筛选种类的设备
        if (Boolean.TRUE.equals(regionService.isAllRegion(userId))) {
            return equipmentManager.findEquipsByBaseTypeIds(equipBaseType)
                    .stream()
                    .map(this::convertEquipmentDTO)
                    .toList();
        }
        List<RegionMap> regionMapList = regionMapService.findByUserId(userId);
        List<AirEquipmentDTO> equipmentList = new ArrayList<>();
        for (RegionMap regionMap : regionMapList) {
            //层级下所有设备权限
            if (regionMap.getEquipmentId().equals(-1)) {
                List<AirEquipmentDTO> equipmentsByResourceStructureId = equipmentManager.findEquipmentsByResourceStructureId(regionMap.getResourceStructureId())
                        .stream()
                        .filter(eq -> equipBaseType.contains(eq.getEquipmentBaseType()))
                        .map(this::convertEquipmentDTO)
                        .toList();
                equipmentList.addAll(equipmentsByResourceStructureId);
                continue;
            }
            Equipment equipment = equipmentManager.getEquipmentById(regionMap.getEquipmentId());
            if(equipBaseType.contains(equipment.getEquipmentBaseType())) {
                equipmentList.add(this.convertEquipmentDTO(equipment));
            }
        }
        return equipmentList.stream()
                .sorted(Comparator.comparing(EquipmentDTO::getDisplayIndex).thenComparing(EquipmentDTO::getEqName))
                .toList();
    }
    private AirEquipmentDTO convertEquipmentDTO(Equipment equipment) {
        AirEquipmentDTO dto = new AirEquipmentDTO();
        dto.setSId(equipment.getStationId());
        dto.setEqId(equipment.getEquipmentId());
        dto.setEqName(equipment.getEquipmentName());
        dto.setRId(equipment.getResourceStructureId());
        dto.setEquipmentTemplateId(equipment.getEquipmentTemplateId());
        dto.setDisplayIndex(equipment.getDisplayIndex());
        return dto;
    }
    private ResourceStructureTreeDTO convertResourceStructureTreeDTO(ResourceStructure resourceStructure) {
        ResourceStructureTreeDTO resDto = new ResourceStructureTreeDTO();
        resDto.setRId(resourceStructure.getResourceStructureId());
        resDto.setRName(resourceStructure.getResourceStructureName());
        resDto.setTypeId(resourceStructure.getStructureTypeId());
        resDto.setParentId(resourceStructure.getParentResourceStructureId());
        resDto.setChildren(new ArrayList<>());
        resDto.setEqChildren(new ArrayList<>());
        resDto.setSortValue(resourceStructure.getSortValue());
        resDto.setOriginId(resourceStructure.getOriginId());
        return resDto;
    }
    private List<ResourceStructureTreeDTO> findResourceStructureTreeDtoByUserId(Integer userId) {
        //拥有所有区域的权限 获取所有层级资源
        if (regionService.isAllRegion(userId)) {
            return resourceStructureManager.getAll()
                    .stream()
                    .map(this::convertResourceStructureTreeDTO)
                    .toList();
        }
        List<RegionMap> regionMapList = regionMapService.findByUserId(userId);
        Set<Integer> resourceStructureIdSet = regionMapList.stream().map(RegionMap::getResourceStructureId).collect(Collectors.toSet());
        Set<ResourceStructureTreeDTO> resourceStructureList = new HashSet<>();
        for (Integer resourceStructureId : resourceStructureIdSet) {
            //获取所有父级节点
            List<ResourceStructureTreeDTO> resourceStructureEquipmentTreeDTOS = resourceStructureManager.findAllParentStructureListById(resourceStructureId)
                    .stream()
                    .map(this::convertResourceStructureTreeDTO)
                    .toList();
            resourceStructureList.addAll(resourceStructureEquipmentTreeDTOS);
        }
        return resourceStructureList.stream()
                .sorted(Comparator.comparing(ResourceStructureEquipmentTreeDTO::getSortValue).thenComparing(ResourceStructureEquipmentTreeDTO::getRName))
                .toList();
    }

    public ResourceStructureTreeDTO buildTree(List<ResourceStructureTreeDTO> resourceStructureList, List<AirEquipmentDTO> equipmentList, Boolean monitorTree,Boolean isOverView) {
        if (CollUtil.isEmpty(resourceStructureList)) {
            return null;
        }
        int rootId = 0;
        LinkedHashMap<Integer, ResourceStructureTreeDTO> resourceStructureDTOLinkedHashMap = new LinkedHashMap<>();
        for (ResourceStructureTreeDTO resourceStructure : resourceStructureList) {
            resourceStructureDTOLinkedHashMap.put(resourceStructure.getRId(), resourceStructure);
            if (resourceStructure.getParentId() == 0) {
                rootId = resourceStructure.getRId();
            }
        }
        if (monitorTree) {
            //如果是概要视图，则监控单元下没有群控分组的监控单元不显示
            if(isOverView){
                HashMap<Integer, List<StructureTreeNode>> monitorUnitMaps = new HashMap<>();
                List<StructureTreeNode> groupList = airGroupViewMapper.findAllGroups();
                if (groupList != null && groupList.size() > 0) {
                    for (StructureTreeNode monitorUnitGroupNode : groupList) {
                        monitorUnitMaps.put(monitorUnitGroupNode.getNodeId(), monitorUnitGroupNode.getChildren()); //NodeId 实为 monitorUnitId
                    }
                }
                if (CollUtil.isNotEmpty(equipmentList)) {
                    for (EquipmentDTO equipment : equipmentList) {
                        if(monitorUnitMaps.get(equipment.getEqId()) != null && monitorUnitMaps.get(equipment.getEqId()).size()  >0){
                            if (resourceStructureDTOLinkedHashMap.containsKey(equipment.getRId())) {
                                ResourceStructureTreeDTO resourceStructureEquipmentDTO = resourceStructureDTOLinkedHashMap.get(equipment.getRId());
                                resourceStructureEquipmentDTO.getEqChildren().add(equipment);
                            }
                        }
                    }
                }
            }
            else {
                // 设备挂载
                if (CollUtil.isNotEmpty(equipmentList)) {
                    for (EquipmentDTO equipment : equipmentList) {
                        if (resourceStructureDTOLinkedHashMap.containsKey(equipment.getRId())) {
                            ResourceStructureTreeDTO resourceStructureEquipmentDTO = resourceStructureDTOLinkedHashMap.get(equipment.getRId());
                            resourceStructureEquipmentDTO.getEqChildren().add(equipment);
                        }
                    }
                }
            }
            //层级挂载
            for (ResourceStructureTreeDTO resourceStructureEquipmentDTO : resourceStructureDTOLinkedHashMap.values()) {
                if (resourceStructureEquipmentDTO.getParentId() != 0 && resourceStructureDTOLinkedHashMap.containsKey(resourceStructureEquipmentDTO.getParentId())) {
                    ResourceStructureTreeDTO pResourceStructureEquipmentDTO = resourceStructureDTOLinkedHashMap.get(resourceStructureEquipmentDTO.getParentId());
                    if (resourceStructureEquipmentDTO.getTypeId() == 5 || resourceStructureEquipmentDTO.getTypeId() == 6 || resourceStructureEquipmentDTO.getTypeId() == 105) {
                        ResourceStructureTreeDTO haveSon = resourceStructureDTOLinkedHashMap.get(resourceStructureEquipmentDTO.getRId());
                        if (haveSon != null || resourceStructureEquipmentDTO.getEqChildren().size() > 0 ) {
                            pResourceStructureEquipmentDTO.getChildren().add(resourceStructureEquipmentDTO);
                        }
                    } else {
                        pResourceStructureEquipmentDTO.getChildren().add(resourceStructureEquipmentDTO);
                    }
                }
            }
        } else {
            for (ResourceStructureTreeDTO resourceStructureEquipmentDTO : resourceStructureDTOLinkedHashMap.values()) {
                if (resourceStructureEquipmentDTO.getOriginId() != null) {
                    Station station = stationManager.findStationById(resourceStructureEquipmentDTO.getOriginId());
                    if (station != null) {
                        resourceStructureEquipmentDTO.setLatitude(station.getLatitude());
                        resourceStructureEquipmentDTO.setLongitude(station.getLongitude());
                    }
                }
                if (resourceStructureEquipmentDTO.getParentId() != 0 && resourceStructureDTOLinkedHashMap.containsKey(resourceStructureEquipmentDTO.getParentId())) {
                    ResourceStructureTreeDTO pResourceStructureEquipmentDTO = resourceStructureDTOLinkedHashMap.get(resourceStructureEquipmentDTO.getParentId());
                    pResourceStructureEquipmentDTO.getChildren().add(resourceStructureEquipmentDTO);
                }
            }
        }

        return resourceStructureDTOLinkedHashMap.get(rootId);
    }
}
