<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.HistoryEventMapper">

    <sql id="Base_Column_List">
        SequenceId, StationId, StationName, EquipmentId, EquipmentName, EventId, EventName, EventConditionId,
        EventLevel, EventSeverity, StartTime, EndTime, CancelTime, CancelUserId, CancelUserName, ConfirmTime,
        ConfirmerId, ConfirmerName, EventValue, ReversalNum, Meanings, EventFilePath, Description, SourceHostId,
        InstructionId, InstructionStatus, StandardAlarmNameId, StandardAlarmName, BaseTypeId, BaseTypeName,
        EquipmentCategory, EquipmentCategoryName, MaintainState, SignalId, RelateSequenceId, EventCategoryId,
        EventStateId, CenterId, CenterName, StructureName, MonitorUnitName, StructureId, StationCategoryId,
        EquipmentVendor, EndValue, ResourceStructureId, BaseEquipmentId
    </sql>
    <insert id="batchInsert">
        <choose>
            <when test="_databaseId == 'opengauss'">
                INSERT INTO tbl_historyevent (
                sequenceId, stationId, stationName, equipmentId, equipmentName, eventId, eventName,
                eventConditionId, eventSeverityId, eventSeverity, eventLevel, startTime, endTime,
                cancelTime, cancelUserId, cancelUserName, confirmTime, confirmerId, confirmerName,
                eventValue, endValue, reversalNum, meanings, eventFilePath, description, sourceHostId,
                instructionId, instructionStatus, standardAlarmNameId, standardAlarmName, baseTypeId,
                baseTypeName, equipmentCategory, equipmentCategoryName, maintainState, signalId,
                relateSequenceId, eventCategoryId, eventStateId, centerId, centerName, structureName,
                monitorUnitName, structureId, stationCategoryId, equipmentVendor, convergenceEventId,
                resourceStructureId, baseEquipmentId
                )
                VALUES
                <foreach collection="list" item="item" index="index" separator=",">
                    (
                    #{item.sequenceId}, #{item.stationId}, #{item.stationName}, #{item.equipmentId},
                    #{item.equipmentName}, #{item.eventId}, #{item.eventName}, #{item.eventConditionId},
                    #{item.eventSeverityId}, #{item.eventSeverity}, #{item.eventLevel}, #{item.startTime},
                    #{item.endTime}, #{item.cancelTime}, #{item.cancelUserId}, #{item.cancelUserName},
                    #{item.confirmTime}, #{item.confirmerId}, #{item.confirmerName}, #{item.eventValue},
                    #{item.endValue}, #{item.reversalNum}, #{item.meanings}, #{item.eventFilePath},
                    #{item.description}, #{item.sourceHostId}, #{item.instructionId}, #{item.instructionStatus},
                    #{item.standardAlarmNameId}, #{item.standardAlarmName}, #{item.baseTypeId},
                    #{item.baseTypeName}, #{item.equipmentCategory}, #{item.equipmentCategoryName},
                    #{item.maintainState}, #{item.signalId}, #{item.relateSequenceId}, #{item.eventCategoryId},
                    #{item.eventStateId}, #{item.centerId}, #{item.centerName}, #{item.structureName},
                    #{item.monitorUnitName}, #{item.structureId}, #{item.stationCategoryId},
                    #{item.equipmentVendor}, #{item.convergenceEventId}, #{item.resourceStructureId},
                    #{item.baseEquipmentId}
                    )
                </foreach>
                ON DUPLICATE key  update nothing;
            </when>
            <otherwise>
                INSERT INTO tbl_historyevent (
                sequenceId, stationId, stationName, equipmentId, equipmentName, eventId, eventName,
                eventConditionId, eventSeverityId, eventSeverity, eventLevel, startTime, endTime,
                cancelTime, cancelUserId, cancelUserName, confirmTime, confirmerId, confirmerName,
                eventValue, endValue, reversalNum, meanings, eventFilePath, description, sourceHostId,
                instructionId, instructionStatus, standardAlarmNameId, standardAlarmName, baseTypeId,
                baseTypeName, equipmentCategory, equipmentCategoryName, maintainState, signalId,
                relateSequenceId, eventCategoryId, eventStateId, centerId, centerName, structureName,
                monitorUnitName, structureId, stationCategoryId, equipmentVendor, convergenceEventId,
                resourceStructureId, baseEquipmentId
                )
                VALUES
                <foreach collection="list" item="item" index="index" separator=",">
                    (
                    #{item.sequenceId}, #{item.stationId}, #{item.stationName}, #{item.equipmentId},
                    #{item.equipmentName}, #{item.eventId}, #{item.eventName}, #{item.eventConditionId},
                    #{item.eventSeverityId}, #{item.eventSeverity}, #{item.eventLevel}, #{item.startTime},
                    #{item.endTime}, #{item.cancelTime}, #{item.cancelUserId}, #{item.cancelUserName},
                    #{item.confirmTime}, #{item.confirmerId}, #{item.confirmerName}, #{item.eventValue},
                    #{item.endValue}, #{item.reversalNum}, #{item.meanings}, #{item.eventFilePath},
                    #{item.description}, #{item.sourceHostId}, #{item.instructionId}, #{item.instructionStatus},
                    #{item.standardAlarmNameId}, #{item.standardAlarmName}, #{item.baseTypeId},
                    #{item.baseTypeName}, #{item.equipmentCategory}, #{item.equipmentCategoryName},
                    #{item.maintainState}, #{item.signalId}, #{item.relateSequenceId}, #{item.eventCategoryId},
                    #{item.eventStateId}, #{item.centerId}, #{item.centerName}, #{item.structureName},
                    #{item.monitorUnitName}, #{item.structureId}, #{item.stationCategoryId},
                    #{item.equipmentVendor}, #{item.convergenceEventId}, #{item.resourceStructureId},
                    #{item.baseEquipmentId}
                    )
                </foreach>
                ON CONFLICT (StartTime,StationId,EquipmentId,EventId,EventConditionId) DO NOTHING;
            </otherwise>
        </choose>
    </insert>

    <select id="findByBaseEquipmentIdAndStartTimeSpan" resultType="com.siteweb.monitoring.entity.HistoryEvent">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tbl_historyevent WHERE BaseEquipmentId=#{baseEquipmentId} AND StartTime &gt;  #{startDate} AND StartTime &lt; #{endDate}
        ORDER BY StartTime DESC
    </select>

    <select id="findByEquipmentIdAndStartTimeSpan" resultType="com.siteweb.monitoring.entity.HistoryEvent">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tbl_historyevent WHERE EquipmentId=#{equipmentId} AND StartTime &gt;  #{startDate} AND StartTime &lt; #{endDate}
        ORDER BY StartTime DESC
    </select>

    <select id="findByEventIdAndStartTimeSpan" resultType="com.siteweb.monitoring.entity.HistoryEvent">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tbl_historyevent WHERE EventId=#{eventId} AND StartTime &gt;  #{startDate} AND StartTime &lt; #{endDate}
        ORDER BY StartTime DESC
    </select>

    <select id="findByStartTimeSpan" resultType="com.siteweb.monitoring.entity.HistoryEvent">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tbl_historyevent WHERE StartTime &gt;  #{startDate} AND StartTime &lt; #{endDate}
        ORDER BY StartTime DESC
    </select>

    <select id="countByStartTimeSpan" resultType="java.lang.Long">
        SELECT
        count(*)
        FROM tbl_historyevent WHERE StartTime &gt;  #{startDate} AND StartTime &lt; #{endDate}
    </select>

    <select id="listAlertEquipmentsByTimeSpan" resultType="java.lang.Integer">
        SELECT
        distinct EquipmentId
        FROM tbl_historyevent WHERE StartTime &gt;  #{startDate} AND StartTime &lt; #{endDate}
        GROUP BY EquipmentId
    </select>

    <select id="findByConvergenceEventId" resultType="com.siteweb.monitoring.entity.HistoryEvent">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tbl_historyevent WHERE ConvergenceEventId  =  #{convergenceEventId}
        ORDER BY StartTime DESC
    </select>

    <select id="findByStartTimeAndEquipmentIdAndEventIdAndConditionId" resultType="com.siteweb.monitoring.entity.HistoryEvent">
        select th.* from ( select t.* from activeeventoperationlog t ) t
                                     left join tbl_historyevent th on t.StartTime = th.StartTime
            and t.StationId = th.StationId
            and t.EquipmentId = th.EquipmentId
            and t.EventId = th.EventId
            and t.EventConditionId = th.EventConditionId
        where t.StartTime = #{startDate}
          and t.EquipmentId = #{equipmentId,jdbcType=INTEGER}
          and t.EventId =  #{eventId,jdbcType=INTEGER}
          and t.StationId = #{stationId,jdbcType=INTEGER}
          and t.EventConditionId = #{eventConditionId,jdbcType=INTEGER}
        limit 1
    </select>
    <select id="findAlarmDurationByEquipmentIdAndEventId" resultType="com.siteweb.monitoring.entity.HistoryEvent">
        SELECT <include refid="Base_Column_List"/>
        FROM tbl_historyevent
        WHERE EquipmentId = #{equipmentId}
          AND EventId = #{eventId}
          AND StartTime &gt;= #{startTime}
          AND StartTime &lt;= #{endTime}
    </select>
    <select id="findDurationByStationIdsAndEventCategoryId" resultType="com.siteweb.monitoring.entity.HistoryEvent">
        SELECT <include refid="Base_Column_List"/>
        FROM tbl_historyevent a
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON a.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        WHERE a.EventCategoryId = #{eventCategoryId}
        AND a.StartTime &gt;= #{startTime}
        AND a.StartTime &lt;= #{endTime}
        AND a.stationId IN
        <foreach collection="stationIds" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
    </select>
    <select id="findDurationByResourceStructureIdsAndEventCategoryId" resultType="com.siteweb.monitoring.entity.HistoryEvent">
        SELECT <include refid="Base_Column_List"/>
        FROM tbl_historyevent a
        <if test="filterByEventLevel">
            INNER JOIN tbl_dataitem tdi
            ON a.EventLevel = tdi.ExtendField4
            AND tdi.EntryId = 23
            AND tdi.Enable = 1
        </if>
        WHERE a.EventCategoryId = #{eventCategoryId}
        AND a.StartTime &gt;= #{startTime}
        AND a.StartTime &lt;= #{endTime}
        AND a.ResourceStructureId IN
        <foreach collection="resourceStructureIds" item="stationId" open="(" close=")" separator=",">
            #{stationId}
        </foreach>
    </select>
    <select id="getPowerOffCountByStationIds" resultType="com.siteweb.monitoring.vo.HistoryPowerOffCountVO">
        SELECT StationId AS stationId, COUNT(*) AS count
        FROM tbl_historyevent
        WHERE EventCategoryId = #{eventCategoryId}
        AND StartTime &gt;= #{startTime}
        AND StartTime &lt;= #{endTime}
        AND StationId IN
        <foreach item="stationId"  collection="stationIds" open="(" separator="," close=")">
            #{stationId}
        </foreach>
        GROUP BY StationId
    </select>

    <select id="isExistAlarmByEquipmentIdAndTime" resultType="com.siteweb.monitoring.entity.HistoryEvent">
        SELECT *
        FROM tbl_historyevent
        WHERE EquipmentId = #{equipmentId}
        AND StartTime &lt;= #{time}
        AND endTime &gt;= #{time}
    </select>
    <select id="findAlarmCountByEquipmentIdAndEventId" resultType="java.lang.Integer">
        SELECT count(*)
        FROM tbl_historyevent
        WHERE EquipmentId = #{equipmentId}
        <if test="eventId != null">
            AND EventId = #{eventId}
        </if>
        <if test="startTime != null">
            AND StartTime &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND StartTime &lt;= #{endTime}
        </if>
    </select>
    <select id="groupHistoryEventsBySeverity" resultType="java.util.Map">
        SELECT
        EventLevel AS eventLevel,
        COUNT(*) AS count
        FROM tbl_historyevent
        WHERE StartTime &gt; #{startDate}
        AND StartTime &lt; #{endDate}
        AND EquipmentId IN
        <foreach item="equipmentId" collection="equipmentIds" open="(" separator="," close=")">
            #{equipmentId}
        </foreach>
        AND EventLevel IS NOT NULL
        GROUP BY EventLevel
    </select>
</mapper>