package com.siteweb.monitoring.controller;

import com.siteweb.common.result.Result;
import com.siteweb.common.util.UserUtil;
import com.siteweb.monitoring.service.HistoryEventService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 历史事件统计控制器
 * 提供基于权限过滤的历史事件统计功能
 */
@Api(tags = "历史事件统计")
@RestController
@RequestMapping("/api/history-event-statistics")
@Slf4j
public class HistoryEventStatisticsController {

    @Autowired
    private HistoryEventService historyEventService;

    @ApiOperation("统计历史事件总数量")
    @GetMapping("/count")
    public Result<Long> countHistoryEvents(
            @ApiParam(value = "开始时间", required = true)
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @ApiParam(value = "结束时间", required = true)
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        
        Integer userId = UserUtil.getCurrentUserId();
        Long count = historyEventService.countHistoryEventsByUserId(startTime, endTime, userId);
        return Result.success(count);
    }

    @ApiOperation("按设备统计历史事件数量")
    @GetMapping("/count-by-equipment")
    public Result<List<Map<String, Object>>> countHistoryEventsByEquipment(
            @ApiParam(value = "开始时间", required = true)
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @ApiParam(value = "结束时间", required = true)
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        
        Integer userId = UserUtil.getCurrentUserId();
        List<Map<String, Object>> result = historyEventService.countHistoryEventsByEquipmentAndUserId(startTime, endTime, userId);
        return Result.success(result);
    }

    @ApiOperation("按事件等级统计历史事件数量")
    @GetMapping("/count-by-event-level")
    public Result<List<Map<String, Object>>> countHistoryEventsByEventLevel(
            @ApiParam(value = "开始时间", required = true)
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @ApiParam(value = "结束时间", required = true)
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        
        Integer userId = UserUtil.getCurrentUserId();
        List<Map<String, Object>> result = historyEventService.countHistoryEventsByEventLevelAndUserId(startTime, endTime, userId);
        return Result.success(result);
    }

    @ApiOperation("按设备类型统计历史事件数量")
    @GetMapping("/count-by-equipment-category")
    public Result<List<Map<String, Object>>> countHistoryEventsByEquipmentCategory(
            @ApiParam(value = "开始时间", required = true)
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @ApiParam(value = "结束时间", required = true)
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        
        Integer userId = UserUtil.getCurrentUserId();
        List<Map<String, Object>> result = historyEventService.countHistoryEventsByEquipmentCategoryAndUserId(startTime, endTime, userId);
        return Result.success(result);
    }

    @ApiOperation("按资源结构ID统计历史事件数量")
    @GetMapping("/count-by-resource-structure")
    public Result<List<Map<String, Object>>> countHistoryEventsByResourceStructureId(
            @ApiParam(value = "开始时间", required = true)
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
            @ApiParam(value = "结束时间", required = true)
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
        
        Integer userId = UserUtil.getCurrentUserId();
        List<Map<String, Object>> result = historyEventService.countHistoryEventsByResourceStructureIdAndUserId(startTime, endTime, userId);
        return Result.success(result);
    }
}
