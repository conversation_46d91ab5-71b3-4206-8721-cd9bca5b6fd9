package com.siteweb.monitoring.controller;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.common.response.ErrorCode;
import com.siteweb.common.response.ResponseHelper;
import com.siteweb.common.response.ResponseResult;
import com.siteweb.monitoring.dto.ConfigSignalDTO;
import com.siteweb.monitoring.dto.ConfigSignalItem;
import com.siteweb.monitoring.service.SignalService;
import com.siteweb.monitoring.vo.SignalBatchApplyVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * tblSignal info table
 *
 * <AUTHOR>
 * @email
 * @date 2022-01-18 13:38:34
 */
@RestController
@RequestMapping("/api")
@Api(value = "SignalController", tags = {"Signal操作接口"})
@RequiredArgsConstructor
public class SignalController {

    private final SignalService signalService;

    /**
     * GET  /signals?equipmentId=1  get the List<Signal> by equipmentId.
     *
     * @param equipmentId the equipmentId
     * @return the ResponseEntity with status 200 (OK) and with body the Equipment, or with status 404 (Not Found)
     */
    @ApiOperation(value = "根据equipmentId获取信号")
    @GetMapping(value = "/cfgsignals", params = {"equipmentId"})
    public ResponseEntity<ResponseResult> getEquipmentId(@RequestParam("equipmentId") Integer equipmentId) {
        List<ConfigSignalItem> equipment = signalService.findEquipmentId(equipmentId);
        return Optional.ofNullable(equipment)
                .map(result -> ResponseHelper.successful(equipment, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @ApiOperation(value = "根据equipmentIds获取信号")
    @GetMapping(value = "/cfgsignals", params = {"equipmentIds"})
    public ResponseEntity<ResponseResult> getEquipmentIds(@RequestParam("equipmentIds") String equipmentIds) {
        List<Integer> equipmentIdList = Arrays.stream(equipmentIds.split(",")).map(Integer::valueOf).toList();
        Map<Integer, List<ConfigSignalItem>> configSignalItemMap = signalService.findConfigSignalMap(equipmentIdList);
        return Optional.ofNullable(configSignalItemMap)
                .map(result -> ResponseHelper.successful(configSignalItemMap, HttpStatus.OK))
                .orElse(ResponseHelper.successful(HttpStatus.NOT_FOUND));
    }

    @ApiOperation(value = "根据EquipmentId查找信号配置，只返回少量字段")
    @GetMapping(value = "/simplesignaldtos",
            produces = MediaType.APPLICATION_JSON_VALUE,
            params = {"equipmentId"})
    public ResponseEntity<ResponseResult> getSimpleSignalDTOS(@RequestParam(name = "equipmentId") int equipmentId) {
        return ResponseHelper.successful(signalService.findSimpleSignalDTOsByEquipmentId(equipmentId));
    }

    @ApiOperation(value = "根据实时信号key查找信号配置，返回信号相关联的设备信息")
    @GetMapping(value = "/simplesignaldtos", produces = MediaType.APPLICATION_JSON_VALUE, params = {"realSignalKeys"})
    public ResponseEntity<ResponseResult> getSimpleSignalDTOS(@RequestParam("realSignalKeys") List<String> realSignalKeys){
        return ResponseHelper.successful(signalService.findSimpleSignalDTOsByRealSignalKeys(realSignalKeys));
    }

    @ApiOperation(value = "根据equipmentId获取信号与事件")
    @GetMapping(value = "/cfgsignalevent", produces = MediaType.APPLICATION_JSON_VALUE, params = {"equipmentId"})
    public ResponseEntity<ResponseResult> getSignalEvent(@RequestParam("equipmentId") Integer equipmentId){
        return ResponseHelper.successful(signalService.findSignalEvent(equipmentId));
    }

    @ApiOperation(value = "根据EquipmentId和SignalId查找信号配置，组装了信号属性和信号含义")
    @GetMapping(value = "/cfgsignaldtos",
            produces = MediaType.APPLICATION_JSON_VALUE,
            params = {"equipmentId", "signalId"})
    public ResponseEntity<ResponseResult> getConfigSignalBySignalId(@RequestParam(name = "equipmentId") int equipmentId, @RequestParam(name = "signalId") int signalId) {
        return ResponseHelper.successful(signalService.findConfigSignalDTOBySignalId(equipmentId, signalId));
    }

    @ApiOperation(value = "信号动态配置")
    @PutMapping(value = "/cfgsignaldtos",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateConfigSignal(@Valid @RequestBody ConfigSignalDTO configSignalDTO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    "userId is null",
                    HttpStatus.BAD_REQUEST);
        }
        int result = signalService.updateConfigSignalDTO(userId, configSignalDTO);
        if (result > 0) {
            return ResponseHelper.successful();
        }
        return ResponseHelper.failed(String.valueOf(ErrorCode.DYNAMIC_UPDATE_CONFIG_SIGNAL_ERROR.value()),
                "dynamic update config signal error",
                HttpStatus.INTERNAL_SERVER_ERROR);
    }

    @ApiOperation(value = "信号批量应用动态配置")
    @PutMapping(value = "/batchconfigsignaldtos",
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> batchApplyConfigSignalDTOToEquipment(@Valid @RequestBody SignalBatchApplyVO signalBatchApplyVO) {
        Integer userId = TokenUserUtil.getLoginUserId();
        if (null == userId) {
            return ResponseHelper.failed(String.valueOf(ErrorCode.USER_NOT_FOUND_ERROR.value()),
                    "userId is null",
                    HttpStatus.BAD_REQUEST);
        }
        int result = signalService.batchApplyConfigSignalDTOToEquipment(userId, signalBatchApplyVO);
        if (result > 0) {
            return ResponseHelper.successful();
        }
        return ResponseHelper.failed(String.valueOf(ErrorCode.DYNAMIC_UPDATE_CONFIG_SIGNAL_ERROR.value()),
                "batch apply config signal to equipment error",
                HttpStatus.INTERNAL_SERVER_ERROR);
    }

}
