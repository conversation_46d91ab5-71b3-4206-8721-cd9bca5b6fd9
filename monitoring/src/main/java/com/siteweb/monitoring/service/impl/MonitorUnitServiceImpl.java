package com.siteweb.monitoring.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.monitoring.dto.MonitorUnitDTO;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.MonitorUnit;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.MonitorUnitManager;
import com.siteweb.monitoring.mapper.MonitorUnitMapper;
import com.siteweb.monitoring.service.MonitorUnitService;
import com.siteweb.monitoring.vo.MonitorUnitRegisterVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service("monitorUnitService")
public class MonitorUnitServiceImpl implements MonitorUnitService {

    @Autowired
    private MonitorUnitMapper monitorUnitMapper;

    @Autowired
    private MonitorUnitManager monitorUnitManager;

    @Autowired
    private EquipmentManager equipmentManager;

    @Override
    public List<MonitorUnit> findMonitorUnits() {
        return monitorUnitMapper.selectList(null);
    }

    @Override
    public int createMonitorUnit(MonitorUnit monitorUnit) {
        return monitorUnitMapper.insert(monitorUnit);
    }

    @Override
    public int deleteById(Integer monitorUnitId) {
        return    monitorUnitMapper.deleteById(monitorUnitId);
    }

    @Override
    public int updateMonitorUnit(MonitorUnit monitorUnit) {
        return monitorUnitMapper.updateById(monitorUnit);
    }

    @Override
    public MonitorUnit findById(Integer monitorUnitId) {
        return monitorUnitMapper.selectById(monitorUnitId);
    }

    @Override
    public List<MonitorUnitDTO> findMonitorUnitDTOs() {
        return monitorUnitManager.getALL();
    }

    @Override
    public List<MonitorUnitDTO> findMonitorUnitDTOsByIds(List<Integer> muIds) {
        return monitorUnitManager.getByIds(muIds);
    }

    @Override
    public int registerMonitorUnit(MonitorUnitRegisterVO monitorUnitRegisterVO) {
        return monitorUnitManager.registerMonitorUnit(monitorUnitRegisterVO);
    }

    @Override
    public int syncMonitorUnit(Integer monitorUnitId, Integer isSync) {
        return monitorUnitMapper.update(null, Wrappers.lambdaUpdate(MonitorUnit.class)
                                                      .set(MonitorUnit::getIsSync, isSync)
                                                      .eq(MonitorUnit::getMonitorUnitId, monitorUnitId));
    }

    @Override
    public MonitorUnitDTO getMonitorUnitByEquipmentId(Integer equipmentId) {
        Equipment equipment = equipmentManager.getEquipmentById(equipmentId);
        if (Objects.isNull(equipment)) {
            return null;
        }
        return monitorUnitManager.getById(equipment.getMonitorUnitId());
    }
}
