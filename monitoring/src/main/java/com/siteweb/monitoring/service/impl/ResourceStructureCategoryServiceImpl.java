package com.siteweb.monitoring.service.impl;

import com.siteweb.monitoring.dto.EquipmentDTO;
import com.siteweb.monitoring.dto.tree.EquipmentCategoryTree;
import com.siteweb.monitoring.dto.tree.ResourceStructureEquipmentCategoryTreeDTO;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.enumeration.AlarmState;
import com.siteweb.monitoring.mamager.ActiveEventManager;
import com.siteweb.monitoring.mamager.EquipmentStateManager;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.ResourceStructureCategoryService;
import com.siteweb.monitoring.service.ResourceStructureService;
import com.siteweb.utility.enums.DataEntryEnum;
import com.siteweb.utility.service.DataItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ResourceStructureCategoryServiceImpl implements ResourceStructureCategoryService {
    @Autowired
    ResourceStructureService resourceStructureService;
    @Autowired
    EquipmentService equipmentService;
    @Autowired
    DataItemService dataItemService;
    @Autowired
    EquipmentStateManager equipmentStateManager;
    @Autowired
    ActiveEventManager activeEventManager;

    @Override
    public List<ResourceStructureEquipmentCategoryTreeDTO> getParkAlarmPositionOverviewInfo(Integer userId, Boolean existAlarm) {
        List<EquipmentDTO> equipmentList = equipmentService.findEquipmentDTOsByUserId(userId);
        if (Boolean.TRUE.equals(existAlarm)) {
            //设置设备告警状态
            this.setEquipmentExtendState(equipmentList);
        }
        List<ResourceStructure> resourceStructureList = resourceStructureService.findResourceStructureByUserId(userId);
        List<EquipmentCategoryTree> allCategoryItems = dataItemService.findByEntryId(DataEntryEnum.EQUIPMENT_CATEGORY.getValue())
                                                                      .stream()
                                                                      .map(EquipmentCategoryTree::new)
                                                                      .toList();

        // 构建 categoryItemMap，但只存储必要的父子关系
        Map<Integer, List<Integer>> parentChildMap = new HashMap<>();
        for (EquipmentCategoryTree category : allCategoryItems) {
            parentChildMap.computeIfAbsent(category.getParentItemId(), k -> new ArrayList<>()).add(category.getItemId());
        }
        // itemId和EquipmentCategoryTree的map
        Map<Integer, EquipmentCategoryTree> categoryTreeMap = allCategoryItems.stream().collect(Collectors.toMap(EquipmentCategoryTree::getItemId, Function.identity()));

        return buildResourceStructureTree(resourceStructureList, equipmentList, categoryTreeMap, parentChildMap);
    }
    /**
     * 设置拓展状态
     *
     * @param equipmentDTOList 需要设置的设备集合
     */
    public void setEquipmentExtendState(List<EquipmentDTO> equipmentDTOList) {
        Set<Integer> equipmentEventSet = equipmentStateManager.getAllEquipmentAlarmState();
        Map<Integer, Integer> equipmentAlarmState = activeEventManager.getEquipmentAlarmState();
        for (EquipmentDTO equipmentDTO : equipmentDTOList) {
            //是否被屏蔽
            equipmentDTO.setMasked(equipmentStateManager.getEquipmentMaskStatesById(equipmentDTO.getEqId()));
            //是否有告警
            equipmentDTO.setAlarmState(equipmentEventSet.contains(equipmentDTO.getEqId()) ? AlarmState.ALARM : AlarmState.NORMAL);
            //最大设备告警等级
            equipmentDTO.setMaxEventSeverity(equipmentAlarmState.get(equipmentDTO.getEqId()));
        }
    }

    private List<ResourceStructureEquipmentCategoryTreeDTO> buildResourceStructureTree(
            List<ResourceStructure> resourceStructures,
            List<EquipmentDTO> equipments,
            Map<Integer, EquipmentCategoryTree> categoryTreeMap,
            Map<Integer, List<Integer>> parentChildMap) {

        List<ResourceStructureEquipmentCategoryTreeDTO> dtoList = new ArrayList<>();
        for (ResourceStructure rs : resourceStructures) {
            ResourceStructureEquipmentCategoryTreeDTO dto = new ResourceStructureEquipmentCategoryTreeDTO();
            dto.setRId(rs.getResourceStructureId());
            dto.setTypeId(rs.getStructureTypeId());
            dto.setRName(rs.getResourceStructureName());
            dto.setParentId(rs.getParentResourceStructureId());
            dto.setSortValue(rs.getSortValue());
            dto.setChildren(new ArrayList<>());
            dto.setEquipmentCategoryChildren(new ArrayList<>());
            dtoList.add(dto);
        }

        Map<Integer, ResourceStructureEquipmentCategoryTreeDTO> dtoMap = new HashMap<>();
        for (ResourceStructureEquipmentCategoryTreeDTO dto : dtoList) {
            dtoMap.put(dto.getRId(), dto);
        }

        List<ResourceStructureEquipmentCategoryTreeDTO> rootNodes = new ArrayList<>();
        for (ResourceStructureEquipmentCategoryTreeDTO dto : dtoList) {
            if (dto.getParentId() == null || dto.getParentId() == 0) {
                rootNodes.add(dto);
            } else {
                ResourceStructureEquipmentCategoryTreeDTO parent = dtoMap.get(dto.getParentId());
                if (parent != null) {
                    parent.getChildren().add(dto);
                }
            }
        }

        for (ResourceStructureEquipmentCategoryTreeDTO dto : dtoList) {
            List<EquipmentDTO> matchingEquipment = new ArrayList<>();
            for (EquipmentDTO eq : equipments) {
                if (Objects.equals(eq.getRId(), dto.getRId())) {
                    matchingEquipment.add(eq);
                }
            }
            if (!matchingEquipment.isEmpty()) {
                dto.setEquipmentCategoryChildren(createEquipmentCategoryTree(matchingEquipment, categoryTreeMap, parentChildMap));
            }
        }

        return rootNodes;
    }

    private List<EquipmentCategoryTree> createEquipmentCategoryTree(
            List<EquipmentDTO> equipmentDTOList,
            Map<Integer, EquipmentCategoryTree> categoryTreeMap,
            Map<Integer, List<Integer>> parentChildMap) {

        List<EquipmentCategoryTree> rootCategories = new ArrayList<>();
        List<Integer> rootCategoryIds = parentChildMap.getOrDefault(0, Collections.emptyList());
        for (Integer rootCategoryId : rootCategoryIds) {
            EquipmentCategoryTree rootCategory = categoryTreeMap.get(rootCategoryId);
            if (rootCategory != null) {
                rootCategories.add(filterAndCloneEquipmentCategoryTree(rootCategory, categoryTreeMap, parentChildMap, equipmentDTOList));
            }
        }
        return rootCategories.stream().filter(Objects::nonNull).toList();
    }

    private EquipmentCategoryTree filterAndCloneEquipmentCategoryTree(
            EquipmentCategoryTree currentCategory,
            Map<Integer, EquipmentCategoryTree> categoryTreeMap,
            Map<Integer, List<Integer>> parentChildMap,
            List<EquipmentDTO> equipmentDTOList) {

        EquipmentCategoryTree clonedCategory = new EquipmentCategoryTree();
        clonedCategory.setItemId(currentCategory.getItemId());
        clonedCategory.setParentItemId(currentCategory.getParentItemId());
        clonedCategory.setItemValue(currentCategory.getItemValue());
        clonedCategory.setCategoryChildren(new ArrayList<>());
        clonedCategory.setEqChildren(new ArrayList<>());

        List<EquipmentDTO> matchingEquipment = new ArrayList<>();
        for (EquipmentDTO eq : equipmentDTOList) {
            if (Objects.equals(eq.getEquipmentCategory(), clonedCategory.getItemId())) {
                matchingEquipment.add(eq);
            }
        }
        clonedCategory.setEqChildren(matchingEquipment);

        List<Integer> childIds = parentChildMap.getOrDefault(currentCategory.getItemId(), Collections.emptyList());
        for (Integer childId : childIds) {
            EquipmentCategoryTree childCategory = categoryTreeMap.get(childId);
            if (childCategory != null) {
                EquipmentCategoryTree clonedChild = filterAndCloneEquipmentCategoryTree(childCategory, categoryTreeMap, parentChildMap, equipmentDTOList);
                if (clonedChild != null) {
                    clonedCategory.getCategoryChildren().add(clonedChild);
                }
            }
        }

        if (matchingEquipment.isEmpty() && clonedCategory.getCategoryChildren().isEmpty()) {
            return null;
        }
        return clonedCategory;
    }
}
