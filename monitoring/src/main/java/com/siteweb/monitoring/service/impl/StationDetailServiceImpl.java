package com.siteweb.monitoring.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.common.util.DateUtil;
import com.siteweb.common.util.StringUtils;
import com.siteweb.monitoring.dto.*;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.entity.Station;
import com.siteweb.monitoring.entity.StationStructure;
import com.siteweb.monitoring.enumeration.OnlineState;
import com.siteweb.monitoring.enumeration.SourceType;
import com.siteweb.monitoring.mamager.MonitorUnitManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.mamager.StationManager;
import com.siteweb.monitoring.mapper.SamplerUnitMapper;
import com.siteweb.monitoring.mapper.StationMapper;
import com.siteweb.monitoring.service.*;
import com.siteweb.monitoring.vo.StationFilterVO;
import com.siteweb.utility.dto.DataItemDTO;
import com.siteweb.utility.enums.DataEntryEnum;
import com.siteweb.utility.manager.DataDictionaryManager;
import com.siteweb.utility.service.impl.DataItemServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service("StationDetailService")
public class StationDetailServiceImpl implements StationDetailService {
    @Autowired
    MonitorUnitManager monitorUnitManager;

    @Autowired
    SamplerUnitMapper samplerUnitMapper;

    @Autowired
    StationManager stationManager;

    @Autowired
    DataDictionaryManager dataDictionaryManager;

    @Autowired
    ResourceStructureManager resourceStructureManager;

    @Autowired
    ResourceStructureService resourceStructureService;

    @Autowired
    StationMapper stationMapper;
    @Autowired
    ActiveEventService activeEventService;
    @Autowired
    StationStructureService stationStructureService;
    @Autowired
    DataItemServiceImpl dataItemService;
    @Autowired
    StationStructureMapService stationStructureMapService;


    public List<MonitorUnitDTO> getMonitorUnitByStationId(Integer resourceStructureId) {
        ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(resourceStructureId);
        if(ObjectUtil.isNull(resourceStructure)){
            return  new ArrayList<>();
        }
        return  monitorUnitManager.getByStationId(resourceStructure.getOriginId());
    }

    @Override
    public List<SamplerUnitDTO> getSamplerUnitDTOByStationId(Integer resourceStructureId) {
        ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(resourceStructureId);
        if(ObjectUtil.isNull(resourceStructure)){
            return  new ArrayList<>();
        }
        return samplerUnitMapper.getSamplerUnitDTOByStationId(resourceStructure.getOriginId());
    }

    public StationDetail getStationDetailByResourceStructureId(Integer resourceStructureId) {
        ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureById(resourceStructureId);
        if(ObjectUtil.isNull(resourceStructure)){
            return  null;
        }
        Station station =  stationManager.findStationById(resourceStructure.getOriginId());
        if(ObjectUtil.isNull(station)){
            return  null;
        }
        DataItemDTO stationCategory =dataDictionaryManager.getDataItemDTO(71, station.getStationCategory());
        DataItemDTO stationGrade = dataDictionaryManager.getDataItemDTO(2, station.getStationGrade());
        DataItemDTO stationState = dataDictionaryManager.getDataItemDTO(5, station.getStationState());
        StationDetail result =new StationDetail(station, stationCategory.getItemValue(), stationGrade.getItemValue(),stationState.getItemValue());
        result.setResourceStructureId(resourceStructureId);
        Map<Integer, ResourceStructure> resourceStructureMap = resourceStructureManager.getAllParentStructureById(resourceStructureId);
        ResourceStructure resourceStructure1 =  resourceStructureMap.get(102);
        if (resourceStructure1 != null && CharSequenceUtil.isNotBlank(resourceStructure1.getResourceStructureName())) {
            result.setCenterName(resourceStructure1.getResourceStructureName());
        } else {
            result.setCenterName(resourceStructureManager.getRoot().getResourceStructureName());
        }
        ResourceStructure resourceStructure2=  resourceStructureMap.get(103);
        if(resourceStructure2 !=null){
            result.setGroupName(resourceStructure2.getResourceStructureName());
        }
        return  result;
    }
    public StationDetail getStationDetailByStationId(Integer stationId){
        ResourceStructure resourceStructure = resourceStructureManager.getResourceStructureByOriginId(stationId,104);
        if(ObjectUtil.isNotNull(resourceStructure))
            return  getStationDetailByResourceStructureId(resourceStructure.getResourceStructureId());
        return  null;
    }

    @Override
    public Page<StationDetail> queryPageableStationDetails(int userId, Pageable pageable, StationFilterVO stationFilterVO) {
        List<StationDetail> dtoList = new ArrayList<>();
        List<StationDetail> listDetail = findStationByFilter(userId, stationFilterVO);

        List<StationDetail> slice = listDetail.stream().skip((long) pageable.getPageNumber() * pageable.getPageSize())
                .limit(pageable.getPageSize())
                .toList();
        for (StationDetail focusSignalDTO : slice) {

            dtoList.add(focusSignalDTO);
        }
        return new PageImpl<>(dtoList, pageable, listDetail.size());
    }

    @Override
    public StationDetailDTO  queryStationDetails(int userId, StationFilterVO stationFilterVO) {
        StationDetailDTO result = new StationDetailDTO();
        List<StationDetail>  stationDetails =  findStationByFilter(userId,stationFilterVO);
        int onlineCount = (int)stationDetails.stream().filter(e -> e.getConnectState() == OnlineState.ONLINE).count();
        int offLineCount = (int)stationDetails.stream().filter(e -> e.getConnectState() != OnlineState.ONLINE).count();
        result.setStationDetailList(stationDetails);
        result.setOffLineCount(offLineCount);
        result.setOnlineCount(onlineCount);
        return  result;
    }

    @Override
    public StationGroupStateDTO getStationGroupState(String stationIds) {
        List<Integer> ids = StringUtils.getIntegerListByString(stationIds);
        return stationMapper.getStationGroupStateDTO(ids);
    }


    @Override
    public List<PowerOffStationDetail> queryPowerOffStationDetails(int userId) {
        List<PowerOffStationDetail> result = new ArrayList<>();
        List<ActiveEventDTO> activeEventDTOS = activeEventService.findActiveEventDTOsByUserIdAndEventCategoryAndEndTimeIsNull(userId, 10);

        for (ActiveEventDTO activeEventDTO:activeEventDTOS){
            PowerOffStationDetail powerOffStationDetail = new PowerOffStationDetail(activeEventDTO);
            Integer duration = DateUtil.differentSecondsByMillisecond(powerOffStationDetail.getStartTime(),new Date())/60;
            powerOffStationDetail.setDuration(duration);
            Map<Integer, ResourceStructure> resourceStructureMap = resourceStructureManager.getAllParentStructureById(activeEventDTO.getResourceStructureId());
            ResourceStructure resourceStructure1 = resourceStructureMap.get(102);
            if (resourceStructure1 != null) {
                powerOffStationDetail.setCenterName(resourceStructure1.getResourceStructureName());
            }
            ResourceStructure resourceStructure2 = resourceStructureMap.get(103);
            if (resourceStructure2 != null) {
                powerOffStationDetail.setGroupName(resourceStructure2.getResourceStructureName());
            }
            ResourceStructure resourceStructure3 = resourceStructureMap.get(104);
            if (resourceStructure3 != null) {
                powerOffStationDetail.setStationName(resourceStructure3.getResourceStructureName());
            }
            result.add(powerOffStationDetail);
        }
        return  result;
    }

    /**
     * 根据过滤条件查找站点详细信息
     *
     * @param userId 用户ID
     * @param filter 站点过滤条件
     * @return 符合条件的站点详情列表
     */
    private List<StationDetail> findStationByFilter(int userId, StationFilterVO filter) {
        // 1. 获取资源结构ID列表
        List<Integer> resourceStructureIds = getResourceStructureIds(userId, filter);

        // 2. 预加载相关数据
        StationStructure centerStructure = stationStructureService.findRoot();
        Map<Integer, String> categoryNameMap = dataItemService.findIdValueMapByEntryId(DataEntryEnum.STATION_CATEGORY.getValue());
        Map<Integer, String> gradeNameMap = dataItemService.findIdValueMapByEntryId(DataEntryEnum.STATION_GRADE.getValue());
        Map<Integer, String> stateNameMap = dataItemService.findIdValueMapByEntryId(DataEntryEnum.STATION_STATE.getValue());
        Map<Integer, String> structureNameMap = stationStructureMapService.stationStructureNameMap();

        // 3. 转换为站点详情列表
        List<StationDetail> stationDetails = resourceStructureIds.stream()
                                                                 .map(resourceStructureId -> {
                                                                     ResourceStructure structure = resourceStructureManager.getResourceStructureById(resourceStructureId);
                                                                     return stationManager.findStationById(structure.getOriginId());
                                                                 })
                                                                 .filter(Objects::nonNull)
                                                                 .map(station -> {
                                                                     StationDetail detail = new StationDetail(station, categoryNameMap.get(station.getStationCategory()), gradeNameMap.get(station.getStationGrade()), stateNameMap.get(station.getStationState()));
                                                                     detail.setCenterName(centerStructure.getStructureName());
                                                                     detail.setGroupName(structureNameMap.get(station.getStationId()));
                                                                     return detail;
                                                                 })
                                                                 .collect(Collectors.toList());

        // 4. 根据在线状态筛选
        return filterByOnlineStatus(stationDetails, filter.getOnLineStatus());
    }


    /**
     * 获取资源结构ID列表
     */
    private List<Integer> getResourceStructureIds(int userId, StationFilterVO filter) {
        return Optional.ofNullable(filter.getResourceStructureIds())
                       .filter(ids -> !ids.isEmpty())
                       .orElseGet(() -> resourceStructureService.findByObjectTypeIdAndUserId(List.of(SourceType.STATION.value()), userId)
                                                                .stream()
                                                                .map(ResourceStructure::getResourceStructureId)
                                                                .toList());
    }

    /**
     * 根据在线状态筛选站点
     */
    private List<StationDetail> filterByOnlineStatus(List<StationDetail> stations, Integer onlineStatus) {
        if (onlineStatus == null) {
            return stations;
        }

        if (onlineStatus == OnlineState.ONLINE.value()) {
            return stations.stream()
                           .filter(station -> station.getConnectState() == OnlineState.ONLINE)
                           .toList();
        }

        if (onlineStatus == OnlineState.UNREGISTER.value()) {
            return stations.stream()
                           .filter(station -> station.getConnectState() == OnlineState.OFFLINE)
                           .toList();
        }

        return stations;
    }
}
