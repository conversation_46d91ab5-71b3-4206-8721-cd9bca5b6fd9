package com.siteweb.monitoring.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.monitoring.entity.TSLActiveEvent;
import com.siteweb.monitoring.mapper.TSLActiveEventMapper;
import com.siteweb.monitoring.model.ConfigEventItem;
import com.siteweb.monitoring.service.SARAlarmActiveRecordService;
import com.siteweb.monitoring.service.TSLActiveEventService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class TSLActiveEventServiceImpl implements TSLActiveEventService {
    @Autowired
    TSLActiveEventMapper tslActiveEventMapper;
    @Autowired
    SARAlarmActiveRecordService sarAlarmActiveRecordService;

    @Override
    public int savePreActiveEvent(ConfigEventItem configEventVO) {
        //没有结束时间且告警已存在，直接返回-1
        if (Objects.isNull(configEventVO.getEndTime())) {
            if (this.existsBySequenceId(configEventVO.getSequenceId())) {
                return -1;
            }
            return this.createActiveEvent(new TSLActiveEvent(configEventVO));
        }
        //有结束时间 不存在 返回 -2
        if (!this.existsBySequenceIdOrResetSequenceId(configEventVO.getSequenceId(),configEventVO.getSequenceId())) {
            return -2;
        }
        //获取重启前的SequenceId
        TSLActiveEvent tslActiveEvent = tslActiveEventMapper.selectOne(Wrappers.lambdaQuery(TSLActiveEvent.class)
                                                                               .eq(TSLActiveEvent::getResetSequenceId, configEventVO.getSequenceId()));
        String deleteSequenceId = configEventVO.getSequenceId();
        if (Objects.nonNull(tslActiveEvent)) {
            deleteSequenceId = tslActiveEvent.getSequenceId();
        }
        deleteBySequenceId(deleteSequenceId);
        return 0;
    }

    private int createActiveEvent(TSLActiveEvent tslActiveEvent) {
        return tslActiveEventMapper.insert(tslActiveEvent);
    }

    private boolean existsBySequenceId(String sequenceId) {
        return tslActiveEventMapper.exists(Wrappers.lambdaQuery(TSLActiveEvent.class)
                                                   .eq(TSLActiveEvent::getSequenceId, sequenceId));
    }

    private boolean existsBySequenceIdOrResetSequenceId(String sequenceId, String resetSequenceId) {
        return tslActiveEventMapper.exists(Wrappers.lambdaQuery(TSLActiveEvent.class)
                                                   .eq(TSLActiveEvent::getSequenceId, sequenceId)
                                                   .or()
                                                   .eq(TSLActiveEvent::getResetSequenceId, resetSequenceId));
    }

    private void deleteBySequenceId(String sequenceId) {
        tslActiveEventMapper.delete(Wrappers.lambdaQuery(TSLActiveEvent.class)
                                            .eq(TSLActiveEvent::getSequenceId, sequenceId));
    }
}
