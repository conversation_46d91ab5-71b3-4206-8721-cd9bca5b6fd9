package com.siteweb.monitoring.service.impl;

import com.siteweb.admin.security.TokenUserUtil;
import com.siteweb.monitoring.dto.DeviceDetail;
import com.siteweb.monitoring.dto.DeviceStructureOverview;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.ResourceStructure;
import com.siteweb.monitoring.mamager.EquipmentBaseTypeManager;
import com.siteweb.monitoring.mamager.EquipmentManager;
import com.siteweb.monitoring.mamager.ResourceStructureManager;
import com.siteweb.monitoring.service.DeviceStructureOverViewService;
import com.siteweb.monitoring.service.EquipmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@Service("DeviceStructureOverViewService")
public class DeviceStructureOverViewServiceImpl implements DeviceStructureOverViewService {
    @Autowired
    ResourceStructureManager resourceStructureManager;

    @Autowired
    EquipmentManager equipmentManager;

    @Autowired
    EquipmentBaseTypeManager equipmentBaseTypeManager;

    @Autowired
    EquipmentService equipmentService;

    public List<DeviceStructureOverview> getDeviceStructureByCategory(int categoryId) {
        int rootId = 0;
        //获取所有的元数据
        ConcurrentHashMap<Integer, DeviceStructureOverview> resourceStructureOverViewConcurrentHashMap = new ConcurrentHashMap<>();
        for(ResourceStructure resourceStructure:resourceStructureManager.getAll()){
            resourceStructureOverViewConcurrentHashMap.put(resourceStructure.getResourceStructureId(), new DeviceStructureOverview(resourceStructure));
            if (resourceStructure.getParentResourceStructureId() == 0) {
                rootId = resourceStructure.getResourceStructureId();
            }
        }
        Set<Integer> equipmentIdsByUserId = equipmentService.findEquipmentIdsByUserId(TokenUserUtil.getLoginUserId());
        List<Equipment> deviceDetailList = equipmentManager.getEquipmentsByCategoryId(categoryId);
        //将设备挂到节点下
        for (Equipment equipment: deviceDetailList){
            if(resourceStructureOverViewConcurrentHashMap.containsKey(equipment.getResourceStructureId()) && equipmentIdsByUserId.contains(equipment.getEquipmentId())) {
                DeviceStructureOverview resourceStructureOverView = resourceStructureOverViewConcurrentHashMap.get(equipment.getResourceStructureId());
                DeviceDetail deviceDetail = new DeviceDetail(equipment);
                resourceStructureOverView.getDeviceChildren().add(deviceDetail);
            }
        }

        //將节点挂到节点下
        for (DeviceStructureOverview deviceStructureOverview: resourceStructureOverViewConcurrentHashMap.values()){
            if(deviceStructureOverview.getParentId() != 0){
                if(resourceStructureOverViewConcurrentHashMap.containsKey(deviceStructureOverview.getParentId())) {
                    DeviceStructureOverview presourceStructureOverView = resourceStructureOverViewConcurrentHashMap.get(deviceStructureOverview.getParentId());
                    presourceStructureOverView.getChildren().add(deviceStructureOverview);
                }
            }
        }

        List<DeviceStructureOverview> result = new ArrayList<>();
        result.add(resourceStructureOverViewConcurrentHashMap.get(rootId));
        return result;
    }

}
