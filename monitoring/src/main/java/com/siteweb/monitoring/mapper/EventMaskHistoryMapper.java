package com.siteweb.monitoring.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.monitoring.entity.EventMaskHistory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface EventMaskHistoryMapper extends BaseMapper<EventMaskHistory> {
    void batchInsert(@Param("eventMaskHistoryInsertList") List<EventMaskHistory> eventMaskHistoryInsertList);
}
