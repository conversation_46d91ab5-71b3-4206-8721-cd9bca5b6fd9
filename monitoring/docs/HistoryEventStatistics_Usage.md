# 历史事件统计功能使用说明

## 概述

本功能将原有的Service层权限过滤逻辑迁移到Mapper层，提供了多种基于权限过滤的历史事件统计方法。

## 权限过滤逻辑

### 原有逻辑（Service层）
```java
@Override
public List<HistoryEvent> findByUserId(List<HistoryEvent> historyEvents, Integer userId) {
    if (null == userId) {
        return historyEvents;
    }
    List<Region> regions = regionService.findAllRegionsByUserId(userId);
    //如果勾选了所有区域权限组(RegionId固定为-1)，则不做区域权限过滤
    if (regions.stream().anyMatch(o -> o.getRegionId().equals(-1))) {
        return historyEvents;
    }
    List<Integer> regionIds = regions.stream().map(Region::getRegionId).toList();
    List<RegionMap> regionMaps = regionMapService.findByRegionIds(regionIds);
    //EquipmentId为-1代表具有该ResourceStructureId下的所有设备权限
    Set<Integer> resourceStructureIds = regionMaps.stream().filter(o -> o.getEquipmentId().equals(-1)).map(RegionMap::getResourceStructureId).collect(Collectors.toSet());
    Set<Integer> equipmentIds = regionMaps.stream().filter(o -> o.getEquipmentId() > 0).map(RegionMap::getEquipmentId).collect(Collectors.toSet());
    return historyEvents.stream().filter(o -> resourceStructureIds.contains(o.getResourceStructureId()) || equipmentIds.contains(o.getEquipmentId())).collect(Collectors.toList());
}
```

### 新逻辑（Mapper层）
现在权限过滤逻辑被封装在Service层的`getPermissionFilterResult`方法中，并传递给Mapper层进行数据库级别的过滤。

## 新增的Mapper方法

### 1. 统计历史事件总数量
```java
Long countHistoryEvents(@Param("startTime") Date startTime, 
                       @Param("endTime") Date endTime,
                       @Param("equipmentIds") Set<Integer> equipmentIds,
                       @Param("resourceStructureIds") Set<Integer> resourceStructureIds);
```

### 2. 按设备统计历史事件数量
```java
List<Map<String, Object>> countHistoryEventsByEquipment(@Param("startTime") Date startTime,
                                                       @Param("endTime") Date endTime,
                                                       @Param("equipmentIds") Set<Integer> equipmentIds,
                                                       @Param("resourceStructureIds") Set<Integer> resourceStructureIds);
```

### 3. 按事件等级统计历史事件数量
```java
List<Map<String, Object>> countHistoryEventsByEventLevel(@Param("startTime") Date startTime,
                                                        @Param("endTime") Date endTime,
                                                        @Param("equipmentIds") Set<Integer> equipmentIds,
                                                        @Param("resourceStructureIds") Set<Integer> resourceStructureIds);
```

### 4. 按设备类型统计历史事件数量
```java
List<Map<String, Object>> countHistoryEventsByEquipmentCategory(@Param("startTime") Date startTime,
                                                               @Param("endTime") Date endTime,
                                                               @Param("equipmentIds") Set<Integer> equipmentIds,
                                                               @Param("resourceStructureIds") Set<Integer> resourceStructureIds);
```

### 5. 按资源结构ID统计历史事件数量
```java
List<Map<String, Object>> countHistoryEventsByResourceStructureId(@Param("startTime") Date startTime,
                                                                  @Param("endTime") Date endTime,
                                                                  @Param("equipmentIds") Set<Integer> equipmentIds,
                                                                  @Param("resourceStructureIds") Set<Integer> resourceStructureIds);
```

## Service层方法

### 1. 统计历史事件总数量
```java
Long countHistoryEventsByUserId(Date startTime, Date endTime, Integer userId);
```

### 2. 按设备统计
```java
List<Map<String, Object>> countHistoryEventsByEquipmentAndUserId(Date startTime, Date endTime, Integer userId);
```

### 3. 按事件等级统计
```java
List<Map<String, Object>> countHistoryEventsByEventLevelAndUserId(Date startTime, Date endTime, Integer userId);
```

### 4. 按设备类型统计
```java
List<Map<String, Object>> countHistoryEventsByEquipmentCategoryAndUserId(Date startTime, Date endTime, Integer userId);
```

### 5. 按资源结构ID统计
```java
List<Map<String, Object>> countHistoryEventsByResourceStructureIdAndUserId(Date startTime, Date endTime, Integer userId);
```

## 使用示例

### Controller层调用
```java
@GetMapping("/count")
public Result<Long> countHistoryEvents(
        @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
        @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime) {
    
    Integer userId = UserUtil.getCurrentUserId();
    Long count = historyEventService.countHistoryEventsByUserId(startTime, endTime, userId);
    return Result.success(count);
}
```

### Service层调用
```java
@Autowired
private HistoryEventService historyEventService;

public void example() {
    Date startTime = new Date();
    Date endTime = new Date();
    Integer userId = 1;
    
    // 统计总数量
    Long totalCount = historyEventService.countHistoryEventsByUserId(startTime, endTime, userId);
    
    // 按设备统计
    List<Map<String, Object>> equipmentStats = historyEventService.countHistoryEventsByEquipmentAndUserId(startTime, endTime, userId);
    
    // 按等级统计
    List<Map<String, Object>> levelStats = historyEventService.countHistoryEventsByEventLevelAndUserId(startTime, endTime, userId);
}
```

## 权限处理说明

1. **全部权限（RegionId = -1）**：如果用户拥有所有区域权限，则不进行权限过滤，返回所有数据
2. **部分权限**：根据用户的区域权限，过滤出对应的设备ID和资源结构ID
3. **无权限**：如果用户没有任何权限，返回空结果

## 数据库支持

该功能支持以下数据库：
- PostgreSQL/OpenGauss
- MySQL
- DaMeng（达梦）

每个数据库都有对应的XML配置文件，确保SQL语法兼容性。
